<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.epcos</groupId>
    <artifactId>epcos-smart-base</artifactId>
    <version>1.0.0</version>
    <name>epcos-smart-base</name>
    <modules>
        <module>epcos-service</module>
        <module>epcos-api</module>
        <module>epcos-common</module>
    </modules>

    <packaging>pom</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <epcos.version>1.0.0</epcos.version>
        <nacos-client.version>1.4.1</nacos-client.version>
        <nacos-config.version>2.2.1.RELEASE</nacos-config.version>
        <maven-compiler-plugin.version>3.10.1</maven-compiler-plugin.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <skipTests>true</skipTests>
        <spring-boot.version>2.7.3</spring-boot.version>
        <spring-cloud.version>2021.0.4</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.0.4.0</spring-cloud-alibaba.version>
        <spring-boot-admin.version>2.7.5</spring-boot-admin.version>
        <spring-boot.mybatis>2.2.2</spring-boot.mybatis>
        <alibaba.nacos.version>2.1.1</alibaba.nacos.version>
        <swagger.fox.version>3.0.0</swagger.fox.version>
        <swagger.core.version>1.6.2</swagger.core.version>
        <tobato.version>1.27.2</tobato.version>
        <kaptcha.version>2.3.2</kaptcha.version>
        <pagehelper.boot.version>1.4.1</pagehelper.boot.version>
        <druid.version>1.2.8</druid.version>
        <dynamic-ds.version>3.5.0</dynamic-ds.version>
        <commons.io.version>2.11.0</commons.io.version>
        <commons.fileupload.version>1.4</commons.fileupload.version>
        <velocity.version>2.3</velocity.version>
        <fastjson.version>1.2.83</fastjson.version>
        <jjwt.version>0.9.1</jjwt.version>
        <minio.version>8.2.2</minio.version>
        <poi.version>4.1.2</poi.version>
        <pdfbox.version>3.0.1</pdfbox.version>
        <documents4j-local.version>1.0.3</documents4j-local.version>
        <commons-collections.version>3.2.2</commons-collections.version>
        <transmittable-thread-local.version>2.12.2</transmittable-thread-local.version>
        <mybatis-plus.version>3.5.1</mybatis-plus.version>
        <mybatis-plus-core.version>3.5.0</mybatis-plus-core.version>
        <itext7-core.version>7.2.5</itext7-core.version>
        <!--        <itext7-core.version>7.2.6</itext7-core.version>-->
        <jsoup.version>1.15.3</jsoup.version>
        <hutool-all.version>5.5.7</hutool-all.version>
        <hutool.version>5.8.4</hutool.version>
        <mybatis-plus-boot-starter.version>3.5.0</mybatis-plus-boot-starter.version>
        <mybatis-plus-generator.version>3.5.1</mybatis-plus-generator.version>
        <easyexcel.version>3.0.1</easyexcel.version>
        <esigntech-sdk.version>2.1.50</esigntech-sdk.version>
        <esigntech-tgtext.version>3.3.64.2150</esigntech-tgtext.version>
        <esigntech-sdk-utils.version>3.0.6.2150</esigntech-sdk-utils.version>
        <esigntech-sdk-utils-smUtil.version>1.3.3.2150</esigntech-sdk-utils-smUtil.version>
        <bcprov-jdk15on.version>1.70</bcprov-jdk15on.version>
        <html2pdf.version>4.0.5</html2pdf.version>
        <itext-asian.version>5.2.0</itext-asian.version>
        <jxls.version>2.12.0</jxls.version>
        <querydsl.version>5.0.0</querydsl.version>
        <org.mapstruct.version>1.5.5.Final</org.mapstruct.version>
        <commons-lang3.version>3.12.0</commons-lang3.version>
        <commons-lang.version>2.6</commons-lang.version>
        <cxf.version>3.5.6</cxf.version>
        <aspose-cells.version>23.11</aspose-cells.version>
        <documents4j-local.version>1.0.3</documents4j-local.version>
        <lucene.version>8.11.1</lucene.version>
        <!--        <knife4j.version>4.3.0</knife4j.version>-->


    </properties>
    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>
            <!-- SpringCloud 微服务 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringCloud Alibaba 微服务 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
                <exclusions>
                    <exclusion>
                        <groupId>io.seata</groupId>
                        <artifactId>seata-spring-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.nacos</groupId>
                        <artifactId>nacos-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>

            <dependency>
                <groupId>com.documents4j</groupId>
                <artifactId>documents4j-local</artifactId>
                <version>${documents4j-local.version}</version>
            </dependency>
            <dependency>
                <groupId>com.documents4j</groupId>
                <artifactId>documents4j-transformer-msoffice-word</artifactId>
                <version>${documents4j-local.version}</version>
            </dependency>


            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>${pdfbox.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>


            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>${commons-lang.version}</version>
            </dependency>

            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itext-asian</artifactId>
                <version>${itext-asian.version}</version>
            </dependency>

            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itext7-core</artifactId>
                <version>${itext7-core.version}</version>
                <type>pom</type>
            </dependency>

            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>html2pdf</artifactId>
                <version>${html2pdf.version}</version>
            </dependency>

            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>${bcprov-jdk15on.version}</version>
            </dependency>

            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk15on</artifactId>
                <version>${bcprov-jdk15on.version}</version>
            </dependency>

            <dependency>
                <groupId>esigntech-sdk</groupId>
                <artifactId>esigntech-sdk</artifactId>
                <version>${esigntech-sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>esigntech-tgtext</groupId>
                <artifactId>esigntech-tgtext</artifactId>
                <version>${esigntech-tgtext.version}</version>
            </dependency>

            <dependency>
                <groupId>esigntech-sdk-utils</groupId>
                <artifactId>esigntech-sdk-utils</artifactId>
                <version>${esigntech-sdk-utils.version}</version>
            </dependency>

            <dependency>
                <groupId>esigntech-sdk-smUtil</groupId>
                <artifactId>esigntech-sdk-utils-smUtil</artifactId>
                <version>${esigntech-sdk-utils-smUtil.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus-generator.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus-boot-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>${swagger.fox.version}</version>
            </dependency>

            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${swagger.fox.version}</version>
            </dependency>

            <!-- Alibaba Nacos 配置 -->
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>${alibaba.nacos.version}</version>
            </dependency>

            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-websocket</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <!-- FastDFS 分布式文件系统 -->
            <dependency>
                <groupId>com.github.tobato</groupId>
                <artifactId>fastdfs-client</artifactId>
                <version>${tobato.version}</version>
            </dependency>

            <!-- Mybatis 依赖配置 -->
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${spring-boot.mybatis}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-core</artifactId>
                <version>${mybatis-plus-core.version}</version>
            </dependency>

            <!-- Swagger 依赖配置 -->
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>

            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>

            <!-- 验证码 -->
            <dependency>
                <groupId>com.github.penggle</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>


            <!-- 文件上传工具类 -->
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons.fileupload.version}</version>
            </dependency>

            <!-- 代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- Collection 增强Java集合框架 -->
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons-collections.version}</version>
            </dependency>

            <!-- JSON 解析器和生成器 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- JWT -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <!-- 线程传递值 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-core</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- 核心模块 -->
            <dependency>
                <groupId>com.epcos</groupId>
                <artifactId>epcos-common-core</artifactId>
                <version>${epcos.version}</version>
            </dependency>

            <!-- 接口模块 -->
            <dependency>
                <groupId>com.epcos</groupId>
                <artifactId>epcos-common-swagger</artifactId>
                <version>${epcos.version}</version>
            </dependency>

            <!-- 安全模块 -->
            <dependency>
                <groupId>com.epcos</groupId>
                <artifactId>epcos-common-security</artifactId>
                <version>${epcos.version}</version>
            </dependency>

            <!-- 权限范围 -->
            <dependency>
                <groupId>com.epcos</groupId>
                <artifactId>epcos-common-datascope</artifactId>
                <version>${epcos.version}</version>
            </dependency>

            <!-- 多数据源 -->
            <dependency>
                <groupId>com.epcos</groupId>
                <artifactId>epcos-common-datasource</artifactId>
                <version>${epcos.version}</version>
            </dependency>

            <!-- 日志记录 -->
            <dependency>
                <groupId>com.epcos</groupId>
                <artifactId>epcos-common-log</artifactId>
                <version>${epcos.version}</version>
            </dependency>

            <!-- 搜索引擎 -->
            <dependency>
                <groupId>com.epcos</groupId>
                <artifactId>epcos-common-search</artifactId>
                <version>${epcos.version}</version>
            </dependency>


            <!-- 缓存服务 -->
            <dependency>
                <groupId>com.epcos</groupId>
                <artifactId>epcos-common-redis</artifactId>
                <version>${epcos.version}</version>
            </dependency>

            <dependency>
                <groupId>com.epcos</groupId>
                <artifactId>epcos-common-file</artifactId>
                <version>${epcos.version}</version>
            </dependency>

            <!-- 系统接口 -->
            <dependency>
                <groupId>com.epcos</groupId>
                <artifactId>epcos-api-system</artifactId>
                <version>${epcos.version}</version>
            </dependency>

            <dependency>
                <groupId>com.epcos</groupId>
                <artifactId>epcos-api-agent</artifactId>
                <version>${epcos.version}</version>
            </dependency>

            <dependency>
                <groupId>com.epcos</groupId>
                <artifactId>epcos-api-bidding</artifactId>
                <version>${epcos.version}</version>
            </dependency>

            <dependency>
                <groupId>com.epcos</groupId>
                <artifactId>epcos-api-epcfile</artifactId>
                <version>${epcos.version}</version>
            </dependency>

            <dependency>
                <groupId>com.epcos</groupId>
                <artifactId>epcos-api-seal</artifactId>
                <version>${epcos.version}</version>
            </dependency>

            <dependency>
                <groupId>com.epcos</groupId>
                <artifactId>epcos-api-pay</artifactId>
                <version>${epcos.version}</version>
            </dependency>

            <dependency>
                <groupId>com.epcos</groupId>
                <artifactId>epcos-common-jpa</artifactId>
                <version>${epcos.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jxls</groupId>
                <artifactId>jxls</artifactId>
                <version>${jxls.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jxls</groupId>
                <artifactId>jxls-poi</artifactId>
                <version>${jxls.version}</version>
            </dependency>

            <dependency>
                <groupId>com.querydsl</groupId>
                <artifactId>querydsl-jpa</artifactId>
                <version>${querydsl.version}</version>
            </dependency>

            <dependency>
                <groupId>com.querydsl</groupId>
                <artifactId>querydsl-apt</artifactId>
                <version>${querydsl.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-jdk8</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.cxf</groupId>
                <artifactId>cxf-rt-features-logging</artifactId>
                <version>${cxf.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.cxf</groupId>
                <artifactId>cxf-spring-boot-starter-jaxws</artifactId>
                <version>${cxf.version}</version>
            </dependency>

            <!-- lucene 全文检索引擎 -->
            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-core</artifactId>
                <version>${lucene.version}</version>
            </dependency>

            <!-- 中文分词 -->
            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-analyzers-smartcn</artifactId>
                <version>${lucene.version}</version>
            </dependency>

            <!-- 关键词高亮 -->
            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-highlighter</artifactId>
                <version>${lucene.version}</version>
            </dependency>

            <!-- 查询解析器 -->
            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-queryparser</artifactId>
                <version>${lucene.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- bootstrap 启动器 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.3.1</version>
                    <configuration>
                        <encoding>utf-8</encoding>
                        <nonFilteredFileExtensions>
                            <nonFilteredFileExtension>xls</nonFilteredFileExtension>
                            <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                        </nonFilteredFileExtensions>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>2.7.3</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <includeSystemScope>true</includeSystemScope>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>

    </build>


</project>
