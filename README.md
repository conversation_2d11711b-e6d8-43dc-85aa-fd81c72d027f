<h1 align="center" style="margin: 30px 0 30px; font-weight: bold;">epcos v1.0.0</h1>
<h4 align="center">基于 Vue/Element UI 和 Spring Boot/Spring Cloud & Alibaba 前后端分离的分布式微服务架构</h4>


## 平台简介

* 采用前后端分离的模式
* 后端采用Spring Boot、Spring Cloud & Alibaba。
* 注册中心、配置中心选型Nacos，权限认证使用Redis。
* 流量控制框架选型Sentinel，分布式事务选型Seata。

注：为什么建议使用spring-boot-devtools 热部署重启
spring-boot-devtools使用了两个类加载器ClassLoader，一个ClassLoader加载不会发生更改的类，另一个ClassLoader（restart
ClassLoader）加载会更改的类。 后台启动File Watcher，监测的目录中的文件发生变动时， 原来的restart
ClassLoader被丢弃，将会重新加载新的restart ClassLoader。 文件变动后第三方jar包不再重新加载，只加载自定义的类，加载的类比较少，所以建议devtools
热部署重启。

## 系统模块

~~~
com.epcos     
├── epcos-ui              // 前端框架 [80]
├── epcos-gateway         // 网关模块 [8080]
├── epcos-auth            // 认证中心 [9200]
├── epcos-api             // 接口模块
│       └── epcos-api-system                          // 系统接口
├── epcos-common          // 通用模块
│       └── epcos-common-core                         // 核心模块
│       └── epcos-common-datascope                    // 权限范围
│       └── epcos-common-datasource                   // 多数据源
│       └── epcos-common-log                          // 日志记录
│       └── epcos-common-redis                        // 缓存服务
│       └── epcos-common-security                     // 安全模块
│       └── epcos-common-swagger                      // 系统接口
├── epcos-modules         // 业务模块
│       └── epcos-auth                              // 系统权限 [9201]
│       └── epcos-gateway                           // 网管服务 [9300]
│       └── epcos-modules-agent                     // 代理服务 [9201]
│       └── epcos-modules-bidding                   // 采购服务 [9300]
│       └── epcos-modules-epcfile                   // 文件服务 [9201]
│       └── epcos-modules-review                    // 评审服务 [9300]
│       └── epcos-modules-system                    // 系统服务 [9201]
├── epcos-visual          // 图形化管理模块
│       └── epcos-visual-monitor                      // 监控中心 [9100]
├──pom.xml                // 公共依赖
~~~

## 架构图

<img src="doc/业务流程图/结构图/architect.png"/>

<img src="doc/业务流程图/结构图/javabean.png"/>

## 内置功能

1.  用户管理：用户是系统操作者，该功能主要完成系统用户配置。
2.  部门管理：配置系统组织机构（公司、部门、小组），树结构展现支持数据权限。
3.  岗位管理：配置系统用户所属担任职务。
4.  菜单管理：配置系统菜单，操作权限，按钮权限标识等。
5.  角色管理：角色菜单权限分配、设置角色按机构进行数据范围权限划分。
6.  字典管理：对系统中经常使用的一些较为固定的数据进行维护。
7.  参数管理：对系统动态配置常用参数。
8.  通知公告：系统通知公告信息发布维护。
9.  操作日志：系统正常操作日志记录和查询；系统异常信息日志记录和查询。
10. 登录日志：系统登录日志记录查询包含登录异常。
11. 在线用户：当前系统中活跃用户状态监控。
12. 定时任务：在线（添加、修改、删除)任务调度包含执行结果日志。
13. 代码生成：前后端代码的生成（java、html、xml、sql）支持CRUD下载 。
14. 系统接口：根据业务代码自动生成相关的api接口文档。
15. 服务监控：监视当前系统CPU、内存、磁盘、堆栈等相关信息。
16. 在线构建器：拖动表单元素生成相应的HTML代码。
17. 连接池监视：监视当前系统数据库连接池状态，可进行分析SQL找出系统性能瓶颈。

sh startup.sh -m standalone     
