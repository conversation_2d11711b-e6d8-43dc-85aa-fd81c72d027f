######################################################################
# Build Tools
.DS_Store
.DS_Store?
.gradle
/build/
!gradle/wrapper/gradle-wrapper.jar
target/
!.mvn/wrapper/maven-wrapper.jar
*.hprof
**/*/test
*/*/*/resources/application-dev.yml

######################################################################
# IDE

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### JRebel ###
rebel.xml
### NetBeans ###
nbproject/private/
build/*
nbbuild/
dist/
nbdist/
.nb-gradle/

######################################################################
# Others
*.log
*.xml.versionsBackup
*.swp
!epcos-common/epcos-common-websocket
!epcos-purchase/epcos-modules-websocket
docker/mysql/data/*
docker/mysql/logs/*
/epcos-purchase/epcos-modules-websocket/src/main/resources/static/test-stomp.html
!*/build/*.java
!*/build/*.html
!*/build/*.xml
docker/*/logs/
*/*/*/*/*/application-dev.yml
epcos-backstage/epcos-modules-system/src/main/resources/application-dev.yml
.kiro


/logs/
/epcos-purchase/epcos-modules-supplier/src/main/java/com/epcos/supplier/controller/TestC.java
/epcos-purchase/epcos-modules-supplier/src/main/java/com/epcos/supplier/controller/TestS.java
/epcos-purchase/epcos-modules-supplier/src/test/java/com/epcos/supplier/controller/
/epcos-purchase/epcos-modules-purchaser/src/main/java/com/epcos/purchase/controller/Test.java
/epcos-service/epcos-modules-system/src/main/java/com/epcos/system/gen/mysql/table/

/.xcodemap
/.vscode
/.lingma
/.chat
epcos-minio
