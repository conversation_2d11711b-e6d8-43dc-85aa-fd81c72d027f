<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.epcos</groupId>
        <artifactId>epcos-service</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>epcos-pay</artifactId>

    <dependencies>
        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- Mysql Connector -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.epcos</groupId>
            <artifactId>epcos-common-core</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.github.pagehelper</groupId>
                    <artifactId>pagehelper-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.epcos</groupId>
            <artifactId>epcos-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.epcos</groupId>
            <artifactId>epcos-common-log</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>seata-core</artifactId>
                    <groupId>io.seata</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.epcos</groupId>
            <artifactId>epcos-api-pay</artifactId>
        </dependency>
        <dependency>
            <groupId>com.epcos</groupId>
            <artifactId>epcos-api-bidding</artifactId>
        </dependency>
        <dependency>
            <groupId>com.epcos</groupId>
            <artifactId>epcos-common-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>5.3.2</version>
            <scope>compile</scope>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.alipay.sdk</groupId>-->
        <!--            <artifactId>alipay-sdk-java</artifactId>-->
        <!--            <version>4.40.133.ALL</version>-->
        <!--        </dependency>-->

    </dependencies>


    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>application-*.yml</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.xml</include>
                    <include>bootstrap.yml</include>
                    <include>application.yml</include>
                    <include>application-${epc}.yml</include>
                </includes>
            </resource>
        </resources>
    </build>

    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <epc>dev</epc>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <epc>test</epc>
            </properties>
        </profile>
        <profile>
            <id>xy</id>
            <properties>
                <epc>xy</epc>
            </properties>
        </profile>

        <profile>
            <id>prod</id>
            <properties>
                <epc>prod</epc>
            </properties>
        </profile>

        <profile>
            <id>lg</id>
            <properties>
                <epc>lg</epc>
            </properties>
        </profile>
        <profile>
            <id>ws</id>
            <properties>
                <epc>ws</epc>
            </properties>
        </profile>


        <profile>
            <id>wx</id>
            <properties>
                <epc>wx</epc>
            </properties>
        </profile>
    </profiles>

</project>