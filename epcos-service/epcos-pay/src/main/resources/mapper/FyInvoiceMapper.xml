<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epcos.pay.mapper.FyInvoiceMapper">

    <resultMap id="BaseResultMap" type="com.epcos.pay.domain.FyInvoiceDao">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="order_id" property="orderId" jdbcType="VARCHAR"/>
        <result column="invoice_id" property="invoiceId" jdbcType="VARCHAR"/>
        <result column="invoice_code" property="invoiceCode" jdbcType="VARCHAR"/>
        <result column="invoice_number" property="invoiceNumber" jdbcType="VARCHAR"/>
        <result column="invoice_type" property="invoiceType" jdbcType="VARCHAR"/>
        <result column="buyer_name" property="buyerName" jdbcType="VARCHAR"/>
        <result column="buyer_tax_no" property="buyerTaxNo" jdbcType="VARCHAR"/>
        <result column="buyer_address" property="buyerAddress" jdbcType="VARCHAR"/>
        <result column="buyer_bank" property="buyerBank" jdbcType="VARCHAR"/>
        <result column="seller_name" property="sellerName" jdbcType="VARCHAR"/>
        <result column="seller_tax_no" property="sellerTaxNo" jdbcType="VARCHAR"/>
        <result column="seller_address" property="sellerAddress" jdbcType="VARCHAR"/>
        <result column="seller_bank" property="sellerBank" jdbcType="VARCHAR"/>
        <result column="total_amount" property="totalAmount" jdbcType="DECIMAL"/>
        <result column="tax_amount" property="taxAmount" jdbcType="DECIMAL"/>
        <result column="invoice_status" property="invoiceStatus" jdbcType="VARCHAR"/>
        <result column="invoice_url" property="invoiceUrl" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="fy_response" property="fyResponse" jdbcType="LONGVARCHAR"/>
        <result column="create_at" property="createAt" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="update_at" property="updateAt" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="deleted" property="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, order_id, invoice_id, invoice_code, invoice_number, invoice_type,
        buyer_name, buyer_tax_no, buyer_address, buyer_bank,
        seller_name, seller_tax_no, seller_address, seller_bank,
        total_amount, tax_amount, invoice_status, invoice_url, remark,
        fy_response, create_at, create_by, update_at, update_by, deleted
    </sql>

    <!-- 根据订单号查询发票信息 -->
    <select id="selectByOrderId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM fy_invoice
        WHERE order_id = #{orderId,jdbcType=VARCHAR}
        AND deleted = 0
        LIMIT 1
    </select>

    <!-- 根据发票ID查询发票信息 -->
    <select id="selectByInvoiceId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM fy_invoice
        WHERE invoice_id = #{invoiceId,jdbcType=VARCHAR}
        AND deleted = 0
        LIMIT 1
    </select>

    <!-- 更新发票状态 -->
    <update id="updateInvoiceStatus">
        UPDATE fy_invoice
        SET invoice_status = #{invoiceStatus,jdbcType=VARCHAR},
            update_at = NOW(),
            update_by = #{updateBy,jdbcType=VARCHAR}
        WHERE id = #{id,jdbcType=BIGINT}
        AND deleted = 0
    </update>

    <!-- 更新发票信息 -->
    <update id="updateInvoiceInfo" parameterType="com.epcos.pay.domain.FyInvoiceDao">
        UPDATE fy_invoice
        <set>
            <if test="invoiceId != null and invoiceId != ''">
                invoice_id = #{invoiceId,jdbcType=VARCHAR},
            </if>
            <if test="invoiceCode != null and invoiceCode != ''">
                invoice_code = #{invoiceCode,jdbcType=VARCHAR},
            </if>
            <if test="invoiceNumber != null and invoiceNumber != ''">
                invoice_number = #{invoiceNumber,jdbcType=VARCHAR},
            </if>
            <if test="sellerName != null and sellerName != ''">
                seller_name = #{sellerName,jdbcType=VARCHAR},
            </if>
            <if test="sellerTaxNo != null and sellerTaxNo != ''">
                seller_tax_no = #{sellerTaxNo,jdbcType=VARCHAR},
            </if>
            <if test="sellerAddress != null and sellerAddress != ''">
                seller_address = #{sellerAddress,jdbcType=VARCHAR},
            </if>
            <if test="sellerBank != null and sellerBank != ''">
                seller_bank = #{sellerBank,jdbcType=VARCHAR},
            </if>
            <if test="taxAmount != null">
                tax_amount = #{taxAmount,jdbcType=DECIMAL},
            </if>
            <if test="invoiceStatus != null and invoiceStatus != ''">
                invoice_status = #{invoiceStatus,jdbcType=VARCHAR},
            </if>
            <if test="invoiceUrl != null and invoiceUrl != ''">
                invoice_url = #{invoiceUrl,jdbcType=VARCHAR},
            </if>
            <if test="fyResponse != null and fyResponse != ''">
                fy_response = #{fyResponse,jdbcType=LONGVARCHAR},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            update_at = NOW()
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
        AND deleted = 0
    </update>

</mapper>