<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epcos.pay.mapper.BizOrderMapper">



    <select id="getList" resultType="com.epcos.pay.domain.BizOrderDao">
        select *
        from biz_order
        <where>
            <if test="dto.supplierId != null">and supplier_id = #{dto.supplierId}</if>
            <if test="dto.buyItemName != null  and dto.buyItemName != ''">and buy_item_name like concat('%', #{dto.buyItemName}, '%')</if>
            <if test="dto.orderSt != null">and order_st = #{dto.orderSt}</if>
            <if test="dto.orderId != null  and dto.orderId != ''">and order_id like concat('%', #{dto.orderId}, '%') </if>
        </where>
    </select>
</mapper>
