server:
  port: 11008
  compression:
    enabled: false
  servlet:
    encoding:
      enabled: true

spring:
  application:
    name: epcos-pay
  profiles:
    active: '@epc@'
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      pool-name: HikariCP
      minimum-idle: 5
      idle-timeout: 300000
      maximum-pool-size: 10
      auto-commit: true
      max-lifetime: 1200000
      connection-timeout: 10000
      connection-test-query: SELECT 1
  redis:
    connect-timeout: 3000
    timeout: 5000
    lettuce:
      pool:
        enabled: true
        max-active: 50
        max-idle: 50
        min-idle: 2
        max-wait: 1000ms
        time-between-eviction-runs: 60000ms
  servlet:
    multipart:
      enabled: true
      max-file-size: 50MB
      max-request-size: 50MB
      resolve-lazily: false
      location: /tmp/
      file-size-threshold: 52428800 # 50MB

mybatis-plus:
  global-config:
    db-config:
      id-type: auto
  mapper-locations: classpath:mapper/*.xml

feign:
  okhttp:
    enabled: true
  httpclient:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 600000
        readTimeout: 600000
  compression:
    request:
      enabled: false
    response:
      enabled: true

# swagger配置
swagger:
  title: 支付模块





