spring:
  cloud:
    nacos:
      discovery:
        username: test
        password: test
        server-addr: ************:8848
        namespace: 516b92c8-098c-4ddd-b61e-909cccad3b5a
        group: DEFAULT_GROUP
        service: ${spring.application.name}
  redis:
    host: ************
    port: 6379
    password:
    database: 2
  datasource:
    url: ****************************************************************************************************************************************************************************************************************************************************************************
    username: root
    password: root123

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

domain: https://kb1047829kd6.vicp.fun
fy:
  mchnt-cd: 0001000F0040992
  ver: 1.0.0
  req-pri-key: MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAJMr8NnRV7ve7Y5FEBium/TsU0fK5NvzvFpsYxPAQhBXY+EN0Bi2JEg790C1njk9Q3U36u2JBDHAiDIomlgh6wWkJsFn7dghV/fCWSX1VVJ+dRINZy1432fRaJ8GqspvMneBpeLjBe94IwlWKpN+AOR+BNX8QL/uHmfCPlVQXos9AgMBAAECgYAzqbMs434m50UBMmFKKNF6kxNRGnpodBFktLO7FTybu/HF6TFp21a1PMe5IYhfk5AAsBZ6OCUOygWFhhdYZN+5W+dweF3kp1rLE4y5CjwqNlk/g22TAndf9znh/ltHFLvITToqu/eh/34tE1gyNxRbsi1olw/1wv8ZRjM3vtM9QQJBANvNwFq+CJHUyFzkXQB7+ycQFnY8wDq8Uw2Hv9ZMjgIntH7FSlJtdu5mAYPPo6f74slO5tFUMNP7EVppqsjYaNkCQQCraD6iKHo+OIlvvYIKiMXatJGD7N1GNhq5CrhUNPWLHwv/Ih2D3JJdF8IUZOPIJfUxTfM2fZYI+EVdsv6s4RcFAkAGjNYbnighOGcUJZYD6q3sVxVkRqEv3ubWs2HrH/Lna4l8caKqXCq8JfwLkod8/QugFiLYwBqIZqX4vMdjHtfZAkBsAl9dbWZCaPvpxp/4JWGPxDLhz9NLV/KU4bVvkoObq++yUHwKyGYOdVcd5MlIKOsNq5Hzp0Vw14lWVuF2bMxFAkBuNrZksvUULNIaWDKd4rQ6GVzUxXuIZW0ZE6atHYDiXPB4jVAjKRtLxZAV1qH9cr1zNJlcg+RbGYUdF9t4A9n5
  rsp-pub-key: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCTK/DZ0Ve73u2ORRAYrpv07FNHyuTb87xabGMTwEIQV2PhDdAYtiRIO/dAtZ45PUN1N+rtiQQxwIgyKJpYIesFpCbBZ+3YIVf3wlkl9VVSfnUSDWcteN9n0WifBqrKbzJ3gaXi4wXveCMJViqTfgDkfgTV/EC/7h5nwj5VUF6LPQIDAQAB
  pay-notify-url: ${domain}/order/pay/notify
  refund-notify-url: ${domain}/order/refund/notify
  page-notify-url: ${domain}/order/pay/notify
  redirect-page-url: http://www.epc1688.com/supplierProcess/step/supplierResult
  invoice-notify-url: ${domain}/invoice/notify
  api:
    url:
      order: https://aggpc-test.fuioupay.com/inteGate.fuiou
      order-query: https://aggpc-test.fuioupay.com/aggpaySynQry.fuiou
      channel-query: 待询问富友
      refund-query: https://refund-transfer-test.fuioupay.com/refund_transfer/aggreRefundQuery.fuiou
      fee-query: https://aggpc-test.fuioupay.com/aggpayFeeQry.fuiou
      order-close: https://aggpc-test.fuioupay.com/pc/close.fuiou
      order-refund: https://refund-transfer-test.fuioupay.com/refund_transfer/aggreRefund.fuiou
      invoice-issue: https://invoice-test.fuioupay.com/api/invoice/create
      invoice-query: https://invoice-test.fuioupay.com/api/invoice/query