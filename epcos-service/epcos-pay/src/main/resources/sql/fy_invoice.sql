-- 富友发票信息表
CREATE TABLE fy_invoice
(
    id                 BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    ins_cd             varchar(30)        not null comment '富友机构编号',
    mchnt_cd           varchar(50)        NOT NULL COMMENT '富友商户号',
    random_str         varchar(20)        NOT NULL COMMENT '随机数',
    merchant_name      varchar(200)       NOT NULL COMMENT '商户名称',
    channel            char(4)            not null comment '渠道：0004-富掌柜云票，0006-诺诺',
    order_id           VARCHAR(30) unique NOT NULL COMMENT '订单号',
    supplier_id        bigint             not null comment '供应商id',
    email              varchar(100) comment '收票人邮箱',
    mobilephone_no     char(12) comment '收票人手机号(与邮箱不能同时为空)',
    buyer_name         VARCHAR(200)       NOT NULL COMMENT '购买方名称',
    buyer_tax_id       VARCHAR(50) COMMENT '购买方纳税人识别号',
    buyer_bank_account char(12) COMMENT '购买方开户行账号',
    buyer_bank_name    VARCHAR(50) COMMENT '购买方开户行名称',
    buyer_address      VARCHAR(100) COMMENT '购买方地址',
    buyer_telephone_no char(12) COMMENT '购买方电话',
    cashier            char(8) comment '收款人',
    checker            char(8) comment '复核人',
    drawer             char(8)            not null comment '开票人',
    invoice_type       char(1)            NOT NULL COMMENT '发票类型：0-平推普票;1-平推专票;4-电票普票 5-电专票 6-全电普票 7-全电专票',
    request_time       char(14)           not null comment '请求时间-yyyyMMddHHmmss',
    fy_term_id         char(30) comment '终端号:非必填（若无值参加加密时值拼接空字符串:&fyTermId=）',
    remark             VARCHAR(100) COMMENT '备注',
    invoice_id         VARCHAR(64) COMMENT '发票ID',
    invoice_code       VARCHAR(32) COMMENT '发票代码',
    invoice_number     VARCHAR(32) COMMENT '发票号码',
    seller_name        VARCHAR(200) COMMENT '销售方名称',
    seller_tax_no      VARCHAR(32) COMMENT '销售方纳税人识别号',
    seller_address     VARCHAR(500) COMMENT '销售方地址电话',
    seller_bank        VARCHAR(500) COMMENT '销售方开户行及账号',
    total_amount       DECIMAL(10, 2)     NOT NULL COMMENT '发票总金额',
    tax_amount         DECIMAL(10, 2) COMMENT '税额',
    invoice_status     VARCHAR(2) DEFAULT '0' COMMENT '发票状态：0-开具中，1-开具成功，2-开具失败',
    invoice_url        VARCHAR(500) COMMENT '发票PDF下载地址',
    fy_response        TEXT COMMENT '富友响应数据',
    create_at          DATETIME   DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by          VARCHAR(100) COMMENT '创建人',
    update_at          DATETIME   DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_by          VARCHAR(100) COMMENT '更新人'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='富友发票信息表';