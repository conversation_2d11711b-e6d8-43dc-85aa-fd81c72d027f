package com.epcos.pay.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.epcos.bidding.supplier.api.RemoteSupplierService;
import com.epcos.common.core.constant.SecurityConstants;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.ServletUtils;
import com.epcos.common.core.utils.ip.IpUtils;
import com.epcos.common.redis.annotation.RedisLock;
import com.epcos.pay.api.domain.dto.BizOrderDto;
import com.epcos.pay.api.domain.dto.OrderCloseDto;
import com.epcos.pay.api.domain.dto.PayRefundDto;
import com.epcos.pay.api.domain.dto.SelOrderDto;
import com.epcos.pay.api.domain.vo.BizOrderVo;
import com.epcos.pay.config.FyConfig;
import com.epcos.pay.constons.Pay;
import com.epcos.pay.domain.BizOrderDao;
import com.epcos.pay.domain.FyRefundDao;
import com.epcos.pay.mapper.BizOrderMapper;
import com.epcos.pay.service.handler.notify.FyPayNotifyHandler;
import com.epcos.pay.service.handler.notify.FyRefundNotifyHandler;
import com.epcos.pay.service.handler.request.*;
import com.epcos.pay.util.FyUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;


@Slf4j
@Component
@AllArgsConstructor
public class PayService {

    private final BizOrderService bizOrderService;
    private final FyRefundService fyRefundService;
    private final FyOrderHandler fyOrderHandler;
    private final FyOrderQueryHandler fyOrderQueryHandler;
    private final FyOrderCloseHandler fyOrderCloseHandler;
    private final FyChannelQueryHandler fyChannelQueryHandler;
    private final FyFeeQueryHandler fyFeeQueryHandler;
    private final FyRefundQueryHandler fyRefundQueryHandler;
    private final FyRefundHandler fyRefundHandler;
    private final FyPayNotifyHandler fyPayNotifyHandler;
    private final FyRefundNotifyHandler fyRefundNotifyHandler;
    final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");

    private final FyConfig fyConfig;
    private final RemoteSupplierService remoteSupplierService;

    private final BizOrderMapper bizOrderMapper;

    // 查询订单
    public BizOrderVo query(String subpackageCode, Long supplierId) {
        if (!StringUtils.hasText(subpackageCode) || Objects.isNull(supplierId)) {
            throw new ServiceException("查询参数不能为空");
        }
        BizOrderDao dao = bizOrderService.findOne(subpackageCode, supplierId);
        if (Objects.isNull(dao)) {
            return null;
        }
        BizOrderVo vo = new BizOrderVo();
        BeanUtils.copyProperties(dao, vo);
        return vo;
    }

    // 订单创建
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> order(BizOrderDto dto) {
        String orderId = IdUtil.getSnowflakeNextIdStr();
        String date = dateFormatter.format(LocalDate.now());
//        FyPayOrderDao fyPayOrderDao = fyOrderHandler.handler(new FyOrderHandler.OrderParam(
//                orderId, date, dto.getOrderAmt(), dto.getPageNotifyUrl(), dto.getGoodsName(), dto.getRem()));
        BizOrderDao bizOrderDao = convertBizOrder(orderId, dto, date);
        bizOrderService.save(bizOrderDao);

        JSONObject requestJson = new JSONObject(FyUtil.getSortJsonConfig());
        // 组装请求参数
        buildRequest(requestJson, bizOrderDao, fyConfig.getPageNotifyUrl());
        // 添加签名
        String signDataStr = FyUtil.getSignDataStr(requestJson);
        String sign;
        try {
            sign = FyUtil.addSign(fyConfig.getReqPriKey(), signDataStr);
        } catch (Exception e) {
            log.error("生成签名异常", e);
            throw new ServiceException("生成签名异常");
        }
        Map<String, String> map = new HashMap<>();
        map.put("mchnt_cd", fyConfig.getMchntCd());
        map.put("order_date", date);
        map.put("order_id", orderId);
        map.put("back_notify_url", fyConfig.getPayNotifyUrl());
        map.put("page_notify_url", fyConfig.getPageNotifyUrl());
        map.put("sign", sign);
        map.put("url", fyConfig.getOrderUrl());
        map.put("order_amt", String.valueOf(dto.getOrderAmt()));
        map.put("goods_name", dto.getGoodsName());
        map.put("rem", dto.getRem());
        return map;
    }

    /**
     * 支付接口参数
     *
     * @param requestJson
     * @param dao
     * @param pageNotifyUrl
     */
    private void buildRequest(JSONObject requestJson, BizOrderDao dao, String pageNotifyUrl) {
        requestJson.set("mchnt_cd", fyConfig.getMchntCd());
        requestJson.set("order_date", dao.getOrderDate());
        requestJson.set("order_id", dao.getOrderId());
        requestJson.set("order_amt", dao.getOrderAmt());
        requestJson.set("page_notify_url", pageNotifyUrl);
        requestJson.set("back_notify_url", fyConfig.getPayNotifyUrl());
        requestJson.set("goods_name", dao.getGoodsName());
        requestJson.set("rem", dao.getRem());
        requestJson.set("pay_type", "");
    }

    /**
     * 支付接口参数
     *
     * @param requestJson
     * @param dao
     */
    private void buildUpdateOrder(JSONObject requestJson, BizOrderDao dao) {
        requestJson.set("mchnt_cd", fyConfig.getMchntCd());
        requestJson.set("order_date", dao.getOrderDate());
        requestJson.set("order_id", dao.getOrderId());
        requestJson.set("order_type", "PAY");
    }

    // 转换为业务订单信息
    private BizOrderDao convertBizOrder(String orderId, BizOrderDto dto, String orderDate) {
        BizOrderDao dao = new BizOrderDao();
        dao.setMchntCd(fyConfig.getMchntCd());
        dao.setPayType(dto.getPayType().getCode());
        dao.setBuyItemCode(dto.getBuyItemCode());
        dao.setBuyItemName(dto.getBuyItemName());
        dao.setSubpackageName(dto.getSubpackageName());
        dao.setSubpackageCode(dto.getSubpackageCode());
        dao.setSupplierId(dto.getSupplierId());
        dao.setSubject(dto.getSubject());
        dao.setGoodsName(dto.getGoodsName());
        dao.setOrderId(orderId);
        dao.setOrderAmt(dto.getOrderAmt());
        dao.setOrderDate(orderDate);
        dao.setOrderSt(Pay.OrderSt.WAIT_PAY);
        dao.setBody(dto.getBody().toString());
        dao.setRem(dto.getRem());
        dao.setIp(IpUtils.getIpAddr(ServletUtils.getRequest()));
        dao.setDeleted(0);
        dao.setCreateAt(new Date());
        dao.setCreateBy(dto.getCreateBy());
        return dao;
    }

    // 订单关闭
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> close(OrderCloseDto dto) {
        BizOrderDao bizOrderDao = bizOrderService.findByOrderId(dto.getOrderId());
        if (bizOrderDao == null) {
            throw new ServiceException("订单不存在");
        }
        if (bizOrderDao.getOrderSt() != 0) {
            log.error("订单已支付或支付失败无法关闭,bizOrderDao={}", bizOrderDao);
            throw new ServiceException("订单已支付或支付失败无法关闭");
        }

        FyOrderCloseHandler.OrderCloseResponse orderCloseResponse = fyOrderCloseHandler.handler(new FyOrderCloseHandler.OrderCloseParam(bizOrderDao.getOrderDate(), dto.getOrderId()));
        if (!"04".equals(orderCloseResponse.getOrderSt())) {
            log.error("订单关闭失败 bizOrderDao={}, response={}", bizOrderDao, orderCloseResponse);
            throw new ServiceException("订单关闭失败：" + orderCloseResponse.getRespDesc());
        }
        BizOrderDao upDao = new BizOrderDao();
        upDao.setId(bizOrderDao.getId());
        upDao.setCloseSt(Pay.CloseSt.CLOSED);
        upDao.setUpdateAt(new Date());
        upDao.setUpdateBy(dto.getUpdateBy());
        bizOrderService.updateById(upDao);

        Map<String, String> map = new HashMap<>();
        map.put("orderId", dto.getOrderId());
        map.put("subpackageCode", bizOrderDao.getSubpackageCode());
        map.put("supplierId", String.valueOf(bizOrderDao.getSupplierId()));
        map.put("remark", "订单关闭");
        map.put("status", String.valueOf(Pay.OrderSt.FAIL));
        map.put("updateBy", "订单关闭" + dto.getUpdateBy());
        // 通知业务处理
        asyncPayCallback(map);
        return R.ok(true, "关闭成功");
    }

    // 订单退款
    @Transactional(rollbackFor = Exception.class)
    public void refund(PayRefundDto dto) {
        BizOrderDao bizOrderDao = bizOrderService.findByOrderId(dto.getOrderId());
        if (bizOrderDao == null) {
            throw new ServiceException("订单不存在:+" + dto.getOrderId());
        }
        String refundOrderId = IdUtil.getSnowflakeNextIdStr();
        String refundOrderDate = dateFormatter.format(LocalDate.now());
        FyRefundHandler.RefundResponse refundResponse = fyRefundHandler.handler(new FyRefundHandler.RefundParam(refundOrderDate, refundOrderId,
                bizOrderDao.getOrderDate(), dto.getOrderId(), bizOrderDao.getOrderAmt(), dto.getRem()));
        // 退款成功后修改数据
        refundUpdateData(refundResponse);
    }

    // 支付通知
    @Transactional(rollbackFor = Exception.class)
    public void payNotify(Map<String, String[]> params) {
        JSONObject handler = fyPayNotifyHandler.handler(params);
        FyPayNotifyHandler.FyPayNotifyResult result = JSONUtil.toBean(handler, FyPayNotifyHandler.FyPayNotifyResult.class);
        String orderId = result.getOrderId();
        BizOrderDao bizOrderDao = bizOrderService.findByOrderId(orderId);
        if (bizOrderDao == null) {
            log.error("订单不存在 orderId={}, result={}", orderId, result);
            throw new ServiceException("订单不存在：orderId=" + orderId);
        }
        if (Pay.OrderSt.PAID == bizOrderDao.getOrderSt()) {
            log.error("订单已支付 orderId={}, result={}", orderId, result);
            return;
        }
        int orderStInt = Objects.equals("11", result.getOrderSt()) ? Pay.OrderSt.PAID : Pay.OrderSt.FAIL;
        BizOrderDao upDao = new BizOrderDao();
        upDao.setId(bizOrderDao.getId());
        upDao.setOrderSt(orderStInt);
        upDao.setRespDesc(result.getRespDesc());
        upDao.setUpdateAt(new Date());
        upDao.setUpdateBy("富友支付异步通知：" + result.getMchntCd());
        upDao.setRespBody(JSONUtil.toJsonStr(fyPayNotifyHandler.parseNotify(params)));
        bizOrderService.updateById(upDao);

        Map<String, String> map = new HashMap<>();
        map.put("orderId", orderId);
        map.put("subpackageCode", bizOrderDao.getSubpackageCode());
        map.put("supplierId", String.valueOf(bizOrderDao.getSupplierId()));
        map.put("remark", result.getRespDesc());
        map.put("status", String.valueOf(orderStInt));
        map.put("updateBy", "富友支付异步通知：" + result.getMchntCd());
        // 通知业务处理
        asyncPayCallback(map);
    }

    /**
     * 异步通知业务进行处理
     *
     * @param map
     */
    @Async
    public void asyncPayCallback(Map<String, String> map) {
        try {
            remoteSupplierService.payCallback(SecurityConstants.INNER, map);
        } catch (Exception e) {
            // 避免静默失败
            log.error("异步回调失败，map={}", map, e);
        }
    }


    // 订单退款通知
    @Transactional(rollbackFor = Exception.class)
    public void refundNotify(Map<String, String[]> params) {
        JSONObject handler = fyRefundNotifyHandler.handler(params);
        FyRefundNotifyHandler.FyRefundNotifyResult result = JSONUtil.toBean(handler, FyRefundNotifyHandler.FyRefundNotifyResult.class);
        FyRefundHandler.RefundResponse dto = new FyRefundHandler.RefundResponse();
        BeanUtils.copyProperties(result, dto);
        // 退款成功后修改数据
        refundUpdateData(dto);
    }


    /**
     * 退款成功后修改数据
     *
     * @param dto 退款成功(返回)参数
     */
    @Transactional
    @RedisLock(keyEl = "#dto.refundOrderId")
    public void refundUpdateData(FyRefundHandler.RefundResponse dto) {
        // 1. 修改 fy_refund表
        String refundOrderId = dto.getRefundOrderId();
        FyRefundDao fyRefundDao = fyRefundService.findByRefundOrderId(refundOrderId);
        if (fyRefundDao == null) {
            log.error("退款订单不存在 result={}", dto);
            throw new ServiceException("退款订单不存在：refundOrderId=" + refundOrderId);
        }
        if (Objects.equals(Pay.RefundSt.SUCCESS, fyRefundDao.getRefundSt())) {
            log.error("退款订单已退款 result={}, fyRefundDao={}", dto, fyRefundDao);
            return;
        }
        if (!Objects.equals(fyRefundDao.getRefundAmt(), Long.parseLong(dto.getRefundAmt()))) {
            log.error("退款订单金额不匹配 result={}, fyRefundDao={}", dto, fyRefundDao);
            throw new ServiceException("退款订单金额不匹配：refundOrderId=" + refundOrderId);
        }
        FyRefundDao upDao = new FyRefundDao();
        upDao.setId(fyRefundDao.getId());
        upDao.setRefundSt(Integer.parseInt(dto.getRefundSt()));
        upDao.setRespDesc(dto.getRespDesc());
        fyRefundService.updateById(upDao);

        // 2. 修改 biz_order表
        BizOrderDao byOrder = bizOrderService.findByOrderId(dto.getPayOrderId());
        byOrder.setOrderSt(dto.getRefundAmt().equals(byOrder.getOrderAmt().toString()) ? 3 : 2);
        bizOrderService.updateById(byOrder);
    }


    /**
     * 查询订单列表(本地数据库查询)
     *
     * @param dto
     * @return
     */
    public R<Page<BizOrderDao>> getList(SelOrderDto dto) {
//        QueryWrapper<BizOrderDao> qw = new QueryWrapper<>();
//        qw.lambda().eq(BizOrderDao::getDeleted, 0)
//                .eq(dto.getSupplierId() != null, BizOrderDao::getSupplierId, dto.getSupplierId())
//                .like(dto.getBuyItemName() != null, BizOrderDao::getBuyItemName, dto.getBuyItemName())
//                .eq(dto.getOrderSt() != null, BizOrderDao::getOrderSt, dto.getOrderSt())
//                .like(dto.getOrderId() != null, BizOrderDao::getOrderId, dto.getOrderId());
//        return bizOrderService.list(qw);
        Page<BizOrderDao> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        return R.ok(bizOrderMapper.getList(page, dto));
    }

    /**
     * 根据订单号查询订单(本地数据库查询)
     *
     * @param orderId
     * @return
     */
    public R<BizOrderDao> getByOrderId(String orderId) {
        QueryWrapper<BizOrderDao> qw = new QueryWrapper<>();
        qw.lambda().eq(BizOrderDao::getDeleted, 0)
                .like(BizOrderDao::getOrderId, orderId);

        BizOrderDao bizOrderDao = bizOrderService.getOne(qw);
        return R.ok(bizOrderDao);
    }

    /**
     * 更新订单状态(支付系统查询)
     *
     * @param orderId
     * @return
     */
    public R<Boolean> updateOrder(String orderId) {
        QueryWrapper<BizOrderDao> qw = new QueryWrapper<>();
        qw.lambda().eq(BizOrderDao::getDeleted, 0)
                .like(BizOrderDao::getOrderId, orderId);
        BizOrderDao dao = bizOrderService.getOne(qw);
        if (dao.getOrderSt() != 0) {
            return R.ok(false, "订单已有状态");
        }
        FyOrderQueryHandler.QueryParam queryParam = new FyOrderQueryHandler.QueryParam(dao.getOrderId(), dao.getOrderDate());
        FyOrderQueryHandler.QueryResponse result = fyOrderQueryHandler.handler(queryParam);
        int orderStInt = Objects.equals("11", result.getOrderSt()) ? Pay.OrderSt.PAID : Pay.OrderSt.FAIL;
        dao.setOrderSt(orderStInt);
        dao.setRespDesc("主动查询订单");
        dao.setUpdateAt(new Date());
        dao.setUpdateBy("主动查询订单：" + result.getMchntCd());
        dao.setRespBody(JSONUtil.toJsonStr(result));
        bizOrderService.updateById(dao);

        Map<String, String> map = new HashMap<>();
        map.put("orderId", orderId);
        map.put("subpackageCode", dao.getSubpackageCode());
        map.put("supplierId", String.valueOf(dao.getSupplierId()));
        map.put("remark", "主动查询订单");
        map.put("status", String.valueOf(orderStInt));
        map.put("updateBy", "主动查询订单：" + result.getMchntCd());
        // 通知业务处理
        asyncPayCallback(map);
        return R.ok(true, "更新成功");
    }
}
