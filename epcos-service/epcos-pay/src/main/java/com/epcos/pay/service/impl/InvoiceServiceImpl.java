package com.epcos.pay.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.pay.api.domain.dto.InvoiceRequestDto;
import com.epcos.pay.api.domain.vo.InvoiceResponseVo;
import com.epcos.pay.constons.Invoice;
import com.epcos.pay.constons.Pay;
import com.epcos.pay.domain.BizOrderDao;
import com.epcos.pay.domain.FyInvoiceDao;
import com.epcos.pay.service.BizOrderService;
import com.epcos.pay.service.FyInvoiceService;
import com.epcos.pay.service.InvoiceService;
import com.epcos.pay.service.handler.notify.FyInvoiceNotifyHandler;
import com.epcos.pay.service.handler.request.FyInvoiceHandler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 发票业务服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class InvoiceServiceImpl implements InvoiceService {

    private final BizOrderService bizOrderService;
    private final FyInvoiceService fyInvoiceService;
    private final FyInvoiceHandler fyInvoiceHandler;
    private final FyInvoiceNotifyHandler fyInvoiceNotifyHandler;

    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<InvoiceResponseVo> issueInvoice(InvoiceRequestDto dto) {
        // 1. 验证订单是否存在且已支付
        BizOrderDao bizOrder = validateOrder(dto.getOrderId());
        // 2. 检查是否已开具发票
        FyInvoiceDao existingInvoice = fyInvoiceService.findByOrderId(dto.getOrderId());
        if (existingInvoice != null) {
            log.error("订单已开具发票，orderId={}, invoiceId={}", dto.getOrderId(), existingInvoice.getId());
            throw new ServiceException("该订单已开具发票，不能重复开具");
        }
        // 3. 创建发票记录
        FyInvoiceDao invoice = createInvoiceRecord(dto, bizOrder);
        fyInvoiceService.save(invoice);

        try {
            // 5. 调用富友发票接口
            String orderDate = dateFormatter.format(LocalDate.now());
            FyInvoiceHandler.InvoiceParam param = new FyInvoiceHandler.InvoiceParam(
                    dto.getOrderId(),
                    orderDate,
                    dto.getInvoiceType(),
                    dto.getBuyerName(),
                    dto.getBuyerTaxNo(),
                    dto.getBuyerAddress(),
                    dto.getBuyerBank(),
                    dto.getTotalAmount(),
                    dto.getRemark()
            );

            FyInvoiceHandler.InvoiceResponse response = fyInvoiceHandler.handler(param);

            // 6. 更新发票信息
            updateInvoiceFromResponse(invoice, response);
            fyInvoiceService.updateById(invoice);

            // 7. 转换为响应VO
            InvoiceResponseVo responseVo = convertToResponseVo(invoice);
            return R.ok(responseVo, "发票开具成功");

        } catch (Exception e) {
            log.error("发票开具失败，orderId={}", dto.getOrderId(), e);
            // 更新发票状态为失败
            invoice.setInvoiceStatus(Invoice.InvoiceStatus.FAILED);
            invoice.setUpdateAt(new Date());
            invoice.setUpdateBy("系统异常更新");
            fyInvoiceService.updateById(invoice);
            
            throw new ServiceException("发票开具失败：" + e.getMessage());
        }
    }

    @Override
    public R<InvoiceResponseVo> getInvoiceByOrderId(String orderId) {
        FyInvoiceDao invoice = fyInvoiceService.findByOrderId(orderId);
        if (invoice == null) {
            return R.fail("未找到该订单的发票信息");
        }

        InvoiceResponseVo responseVo = convertToResponseVo(invoice);
        return R.ok(responseVo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleInvoiceNotify(Map<String, String[]> params) {
        try {
            JSONObject notifyData = fyInvoiceNotifyHandler.handler(params);
            FyInvoiceNotifyHandler.FyInvoiceNotifyResult result = 
                    JSONUtil.toBean(notifyData, FyInvoiceNotifyHandler.FyInvoiceNotifyResult.class);

            String orderId = result.getOrderId();
            FyInvoiceDao invoice = fyInvoiceService.findByOrderId(orderId);
            if (invoice == null) {
                log.error("发票通知处理失败，发票记录不存在，orderId={}", orderId);
                throw new ServiceException("发票记录不存在：orderId=" + orderId);
            }

            // 更新发票信息
            updateInvoiceFromNotify(invoice, result);
            fyInvoiceService.updateById(invoice);

            log.info("发票通知处理成功，orderId={}, invoiceStatus={}", orderId, result.getInvoiceStatus());

        } catch (Exception e) {
            log.error("发票通知处理异常", e);
            throw new ServiceException("发票通知处理异常：" + e.getMessage());
        }
    }

    /**
     * 验证订单状态
     */
    private BizOrderDao validateOrder(String orderId) {
        BizOrderDao bizOrder = bizOrderService.findByOrderId(orderId);
        if (bizOrder == null) {
            log.error("订单不存在，orderId={}", orderId);
            throw new ServiceException("订单不存在");
        }
        if (!Objects.equals(Pay.OrderSt.PAID, bizOrder.getOrderSt())) {
            log.error("订单未支付，无法开具发票，orderId={}, orderSt={}", orderId, bizOrder.getOrderSt());
            throw new ServiceException("订单未支付，无法开具发票");
        }
        return bizOrder;
    }

    /**
     * 创建发票记录
     */
    private FyInvoiceDao createInvoiceRecord(InvoiceRequestDto dto, BizOrderDao bizOrder) {
        FyInvoiceDao invoice = new FyInvoiceDao();
        invoice.setOrderId(dto.getOrderId());
        invoice.setInvoiceId();
        invoice.setInvoiceCode();
        invoice.setInvoiceNumber();
        invoice.setInvoiceType();
        invoice.setBuyerName();
        invoice.setBuyerTaxNo();
        invoice.setBuyerAddress();
        invoice.setBuyerBank();
        invoice.setSellerName();
        invoice.setSellerTaxNo();
        invoice.setSellerAddress();
        invoice.setSellerBank();
        invoice.setTotalAmount();
        invoice.setTaxAmount();
        invoice.setInvoiceStatus();
        invoice.setInvoiceUrl();
        invoice.setRemark();
        invoice.setFyResponse();
        invoice.setCreateAt();
        invoice.setCreateBy();
        invoice.setUpdateAt();
        invoice.setUpdateBy();
        invoice.setDeleted();



        invoice.setOrderId(dto.getOrderId());
        invoice.setInvoiceType(dto.getInvoiceType());
        invoice.setBuyerName(dto.getBuyerName());
        invoice.setBuyerTaxNo(dto.getBuyerTaxNo());
        invoice.setBuyerAddress(dto.getBuyerAddress());
        invoice.setBuyerBank(dto.getBuyerBank());
        invoice.setTotalAmount(dto.getTotalAmount());
        invoice.setInvoiceStatus(Invoice.InvoiceStatus.ISSUING);
        invoice.setRemark(dto.getRemark());
        invoice.setCreateAt(new Date());
        invoice.setCreateBy(dto.getCreateBy());
        invoice.setDeleted(0);
        return invoice;
    }

    /**
     * 从富友响应更新发票信息
     */
    private void updateInvoiceFromResponse(FyInvoiceDao invoice, FyInvoiceHandler.InvoiceResponse response) {
        invoice.setInvoiceId(response.getInvoiceId());
        invoice.setInvoiceCode(response.getInvoiceCode());
        invoice.setInvoiceNumber(response.getInvoiceNumber());
        invoice.setSellerName(response.getSellerName());
        invoice.setSellerTaxNo(response.getSellerTaxNo());
        invoice.setSellerAddress(response.getSellerAddress());
        invoice.setSellerBank(response.getSellerBank());
        
        if (response.getTaxAmount() != null && !response.getTaxAmount().isEmpty()) {
            invoice.setTaxAmount(new BigDecimal(response.getTaxAmount()));
        }
        
        invoice.setInvoiceUrl(response.getInvoiceUrl());
        invoice.setInvoiceStatus(response.getInvoiceStatus());
        invoice.setFyResponse(JSONUtil.toJsonStr(response));
        invoice.setUpdateAt(new Date());
        invoice.setUpdateBy("富友发票接口响应");
    }

    /**
     * 从富友通知更新发票信息
     */
    private void updateInvoiceFromNotify(FyInvoiceDao invoice, FyInvoiceNotifyHandler.FyInvoiceNotifyResult result) {
        if (result.getInvoiceId() != null) {
            invoice.setInvoiceId(result.getInvoiceId());
        }
        if (result.getInvoiceCode() != null) {
            invoice.setInvoiceCode(result.getInvoiceCode());
        }
        if (result.getInvoiceNumber() != null) {
            invoice.setInvoiceNumber(result.getInvoiceNumber());
        }
        if (result.getSellerName() != null) {
            invoice.setSellerName(result.getSellerName());
        }
        if (result.getSellerTaxNo() != null) {
            invoice.setSellerTaxNo(result.getSellerTaxNo());
        }
        if (result.getSellerAddress() != null) {
            invoice.setSellerAddress(result.getSellerAddress());
        }
        if (result.getSellerBank() != null) {
            invoice.setSellerBank(result.getSellerBank());
        }
        if (result.getTaxAmount() != null && !result.getTaxAmount().isEmpty()) {
            invoice.setTaxAmount(new BigDecimal(result.getTaxAmount()));
        }
        if (result.getInvoiceUrl() != null) {
            invoice.setInvoiceUrl(result.getInvoiceUrl());
        }
        if (result.getInvoiceStatus() != null) {
            invoice.setInvoiceStatus(result.getInvoiceStatus());
        }
        
        invoice.setFyResponse(JSONUtil.toJsonStr(result));
        invoice.setUpdateAt(new Date());
        invoice.setUpdateBy("富友发票通知");
    }

    /**
     * 转换为响应VO
     */
    private InvoiceResponseVo convertToResponseVo(FyInvoiceDao invoice) {
        InvoiceResponseVo vo = new InvoiceResponseVo();
        BeanUtils.copyProperties(invoice, vo);
        return vo;
    }
}