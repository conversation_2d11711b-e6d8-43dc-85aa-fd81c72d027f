package com.epcos.pay.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.pay.domain.BizOrderDao;
import com.epcos.pay.mapper.BizOrderMapper;
import org.springframework.stereotype.Service;

@Service
public class BizOrderService extends ServiceImpl<BizOrderMapper, BizOrderDao> {

    public BizOrderDao findByOrderId(String orderId) {
        return getOne(Wrappers.lambdaQuery(BizOrderDao.class).eq(BizOrderDao::getOrderId, orderId));
    }

    public BizOrderDao findOne(String subpackageCode, Long supplierId) {
        return getOne(Wrappers.lambdaQuery(BizOrderDao.class)
                .eq(BizOrderDao::getSubpackageCode, subpackageCode)
                .eq(BizOrderDao::getSupplierId, supplierId));
    }


}
