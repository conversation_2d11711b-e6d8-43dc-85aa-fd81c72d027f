package com.epcos.pay.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 富友退款订单表
 */
@Data
@TableName("fy_refund")
public class FyRefundDao implements Serializable {
    private static final long serialVersionUID = -7581306129730782882L;

    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("商户订单号是唯一,数字或字母组合")
    @NotBlank(message = "订单号,必填")
    @Length(max = 30, message = "订单号,最长30")
    private String orderId;

    @ApiModelProperty("退款订单号")
    @NotBlank(message = "退款订单号,必填")
    @Length(max = 30, message = "退款订单号,最长30")
    private String refundOrderId;

    @ApiModelProperty("退款金额，必须为整数，单位：分")
    @NotNull(message = "退款金额,必填")
    @Range(min = 1, message = "退款金额,最小1")
    private Long refundAmt;

    @ApiModelProperty("退款申请日期,格式:yyyyMMdd")
    @NotBlank(message = "退款申请日期,必填")
    private String refundOrderDate;

    @ApiModelProperty("退款状态: 1:退款成功 2：退款处理中，3：退款失败")
    @Range(min = 1, max = 3, message = "退款状态,最小1,最大3")
    private Integer refundSt;

    @ApiModelProperty("退款备注")
    @Length(max = 200, message = "退款备注,最长200")
    private String rem;

    @ApiModelProperty("响应描述")
    @Length(max = 1000, message = "响应描述,最长1000")
    private String respDesc;

    @ApiModelProperty("创建时间")
    private Date createAt;

    @ApiModelProperty("创建人")
    private String createBy;

}
