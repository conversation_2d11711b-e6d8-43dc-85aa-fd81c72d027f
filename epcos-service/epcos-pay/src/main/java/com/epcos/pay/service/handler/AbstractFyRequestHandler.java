package com.epcos.pay.service.handler;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.epcos.pay.config.FyConfig;
import com.epcos.pay.util.FyUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class AbstractFyRequestHandler<T, R> {

    protected final FyConfig fyConfig;
    protected final String url;
    protected final boolean noNeedResponse;

    public AbstractFyRequestHandler(FyConfig fyConfig, String url, boolean noNeedResponse) {
        this.fyConfig = fyConfig;
        this.url = url;
        this.noNeedResponse = noNeedResponse;
    }

    public R handler(T input) {
        // 参数校验
        FyUtil.checkParams(input);
        // 日志记录
        logRequest(input);
        JSONObject requestJson = new JSONObject(FyUtil.getSortJsonConfig());
        // 组装请求参数
        buildRequest(requestJson, input);
        // 添加签名
        FyUtil.addSign(requestJson, fyConfig.getReqPriKey());
        // 发送请求
        JSONObject response = sendRequest(requestJson);
        logResponse(response);
        // 响应校验
        FyUtil.checkResponse(response);
        // 验签
        FyUtil.sign(response, fyConfig.getRspPubKey());
        R r = processResponse(response, input);
        return postHandler(input, r);
    }

    protected abstract R postHandler(T input, R r);

    // 响应处理
    protected R processResponse(JSONObject response, T input) {
        return JSONUtil.toBean(response, new TypeReference<R>() {
        }, true);
    }

    // 发送请求
    protected JSONObject sendRequest(JSONObject requestJson) {
        return FyUtil.httpPost(url, requestJson.toString(), noNeedResponse);
    }

    // 组装请求参数
    protected abstract JSONObject buildRequest(JSONObject requestJson, T input);

    // 日志记录
    protected void logRequest(T input) {
        log.error("请求参数：input={}", input);
    }

    protected void logResponse(JSONObject response) {
        log.error("响应参数：response={}", response);
    }

}
