package com.epcos.pay.constons;

/**
 * 发票相关常量
 *
 * <AUTHOR>
 */
public class Invoice {

    /**
     * 发票类型
     */
    public static class InvoiceType {
        /** 增值税专用发票 */
        public static final String SPECIAL = "1";
        /** 增值税普通发票 */
        public static final String ORDINARY = "2";
    }

    /**
     * 发票状态
     */
    public static class InvoiceStatus {
        /** 开具中 */
        public static final String ISSUING = "0";
        /** 开具成功 */
        public static final String SUCCESS = "1";
        /** 开具失败 */
        public static final String FAILED = "2";
    }

    /**
     * 发票错误码
     */
    public static class ErrorCode {
        /** 订单不存在 */
        public static final String ORDER_NOT_FOUND = "INVOICE_001";
        /** 订单未支付 */
        public static final String ORDER_NOT_PAID = "INVOICE_002";
        /** 发票已开具 */
        public static final String INVOICE_ALREADY_ISSUED = "INVOICE_003";
        /** 富友接口调用失败 */
        public static final String FUYOU_API_ERROR = "INVOICE_004";
        /** 无效的发票类型 */
        public static final String INVALID_INVOICE_TYPE = "INVOICE_005";
        /** 金额不匹配 */
        public static final String AMOUNT_MISMATCH = "INVOICE_006";
    }
}