package com.epcos.pay.service;

import com.epcos.common.core.domain.R;
import com.epcos.pay.api.domain.dto.InvoiceRequestDto;
import com.epcos.pay.api.domain.vo.InvoiceResponseVo;
import com.epcos.pay.domain.FyInvoiceDao;

import java.util.Map;

/**
 * 发票业务服务接口
 *
 * <AUTHOR>
 */
public interface InvoiceService {

    /**
     * 开具发票
     *
     * @param dto 发票开具请求
     * @return 发票响应信息
     */
    R<InvoiceResponseVo> issueInvoice(InvoiceRequestDto dto);

    /**
     * 根据订单号查询发票详情
     *
     * @param orderId 订单号
     * @return 发票详情
     */
    R<InvoiceResponseVo> getInvoiceByOrderId(String orderId);

    /**
     * 处理富友发票回调通知
     *
     * @param params 通知参数
     */
    void handleInvoiceNotify(Map<String, String[]> params);
}