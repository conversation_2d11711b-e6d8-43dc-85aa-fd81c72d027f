package com.epcos.pay.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.epcos.pay.api.domain.dto.SelOrderDto;
import com.epcos.pay.domain.BizOrderDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BizOrderMapper extends BaseMapper<BizOrderDao> {


    /**
     * 查询订单列表
     *
     * @param page
     * @param dto
     * @return
     */
    Page<BizOrderDao> getList (@Param("page") IPage<BizOrderDao> page, @Param("dto") SelOrderDto dto);


}
