package com.epcos.pay.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.pay.mapper.FyPayOrderMapper;
import com.epcos.pay.domain.FyPayOrderDao;
import org.springframework.stereotype.Service;

@Service
public class FyPayOrderService extends ServiceImpl<FyPayOrderMapper, FyPayOrderDao> {

    public FyPayOrderDao findByOrderId(String orderId) {
        return getOne(Wrappers.<FyPayOrderDao>lambdaQuery()
                .eq(FyPayOrderDao::getOrderId, orderId));
    }

}
