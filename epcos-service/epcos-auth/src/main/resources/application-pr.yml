nacos:
  server-addr: epcdb:8848
  namespace: 2d9737c5-64b3-4d09-b738-6abce2d8cf34
  username: appnacos
  password: Epc_nacos!#

redis:
  host: epcdb
  port: 6379
  password: YjcPr123!redis@#
  timeout: 5000

# spring配置
spring:
  cloud:
    nacos:
      username: ${nacos.username}
      password: ${nacos.password}
      server-addr: ${nacos.server-addr}
      discovery:
        heart-beat-interval: 10000
        heart-beat-timeout: 30000
        ip-delete-timeout: 60000
        namespace: ${nacos.namespace}
  redis:
    host: ${redis.host}
    port: ${redis.port}
    password: ${redis.password}
    timeout: ${redis.timeout}
  servlet:
    multipart:
      enabled: true
      max-file-size: -1
      max-request-size: -1

swagger:
  enabled: false

license:
  sys-code: e62cf54c-9705-4239-a0c2-b22261f68c30
  sys-pwd: EC16B42ABE8A0D9F51A2837FDC5661BA

