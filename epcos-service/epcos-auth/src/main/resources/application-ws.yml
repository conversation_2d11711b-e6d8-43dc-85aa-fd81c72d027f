nacos:
  server-addr: ************:8848
  namespace: 2d9737c5-64b3-4d09-b738-6abce2d8cf34
  username: appnacos
  password: App@Whws#Nacos@_1688!

redis:
  host: ************
  port: 6379
  password: Epc#ws!@1688#epC!
  database: 1
  timeout: 5000

server:
  port: 9200

# spring配置
spring:
  cloud:
    nacos:
      username: ${nacos.username}
      password: ${nacos.password}
      server-addr: ${nacos.server-addr}
      discovery:
        heart-beat-interval: 10000
        heart-beat-timeout: 30000
        ip-delete-timeout: 60000
        namespace: ${nacos.namespace}
  redis:
    host: ${redis.host}
    port: ${redis.port}
    password: ${redis.password}
    database: ${redis.database}
    timeout: ${redis.timeout}
    lettuce:
      pool:
        enabled: true
        max-active: 50
        max-idle: 50
        min-idle: 2
        max-wait: 1000ms
        time-between-eviction-runs: 60000ms
  servlet:
    multipart:
      enabled: true
      max-file-size: -1
      max-request-size: -1

swagger:
  enabled: false

license:
  sys-code: b5e6e5fa-98ca-4fd7-a443-1c33428ee995
  sys-pwd: D9687C0CD2728C918251BF78B7972606
