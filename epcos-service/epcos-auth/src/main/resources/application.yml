spring:
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  cloud:
    sentinel:
      filter:
        # sentinel 在 springboot 2.6.x 不兼容问题的处理
        enabled: false

# feign 配置
feign:
  sentinel:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 600000
        readTimeout: 600000
  compression:
    request:
      enabled: false
    response:
      enabled: true

license:
  lic-file: lic-config/