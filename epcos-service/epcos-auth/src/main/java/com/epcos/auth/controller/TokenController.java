package com.epcos.auth.controller;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.epcos.auth.annotation.FailLimit;
import com.epcos.auth.form.LoginBody;
import com.epcos.auth.form.RegisterBody;
import com.epcos.auth.form.SendSmsDto;
import com.epcos.auth.lic.LicService;
import com.epcos.auth.lic.LicTipDto;
import com.epcos.auth.service.SysLoginService;
import com.epcos.auth.utils.WxLoginUtils;
import com.epcos.common.core.constant.Constants;
import com.epcos.common.core.constant.RoleConstants;
import com.epcos.common.core.constant.SecurityConstants;
import com.epcos.common.core.constant.UserConstants;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.enums.ClientEnum;
import com.epcos.common.core.enums.SmsEnum;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.JwtUtils;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.utils.StringUtils;
import com.epcos.common.core.web.domain.AjaxResult;
import com.epcos.common.core.web.domain.user.LoginUser;
import com.epcos.common.core.web.domain.user.SysUser;
import com.epcos.common.core.web.domain.user.UserOrgVO;
import com.epcos.common.redis.service.RedisService;
import com.epcos.common.redis.window.RateLimiter;
import com.epcos.common.security.annotation.RequiresPermissions;
import com.epcos.common.security.auth.AuthUtil;
import com.epcos.common.security.service.TokenService;
import com.epcos.common.sms.ISmsApi;
import com.epcos.common.sms.annotaion.SmsCodeDel;
import com.epcos.common.sms.annotaion.SmsCodeValid;
import com.epcos.system.api.RemoteSystemService;
import com.epcos.system.api.RemoteUserService;
import com.epcos.system.api.domain.SysDictData;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * token 控制
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class TokenController {
    @Autowired
    private TokenService tokenService;
    @Autowired
    private SysLoginService sysLoginService;
    @Autowired
    private RemoteUserService remoteUserService;
    @Autowired
    private RemoteSystemService remoteSystemService;
    @Autowired
    private ISmsApi smsApi;
    @Autowired
    private RedisService redisService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private LicService licService;

    @FailLimit(tags = "login", valueOrEl = "#form.username", needRecordLockCount = true)
    @SmsCodeDel(smsEnum = SmsEnum.SMS_LOGIN, mobileEL = "#form.username", mobileCodeEL = "#form.mobileCode")
    @PostMapping("login")
    public R<?> login(@RequestBody @Valid LoginBody form, HttpServletRequest request) {
        // 用户登录
        LoginUser userInfo = sysLoginService.login(form);
        Set<String> roles = userInfo.getRoles();
        boolean isAdmin = roles.contains(RoleConstants.SUPER_ADMIN)
                || roles.contains("zbbAdmin")
                || roles.contains("RZadmin");
        if (!isAdmin && licService.isLicExpired()) {
            if (roles.contains("supplier")) {
                return R.fail("系统登录失败，请先联系医院。");
            }
            return R.fail("系统未授权或已过期，请联系管理员");
        }
        // 获取登录token
        Map<String, Object> tokenMap = tokenService.createToken(userInfo);
        tokenMap.put("flag", sysLoginService.validUserPasswordIsSimple(form.getPassword()) || userInfo.getChangePassword());
        R<List<UserOrgVO>> r = remoteUserService.orgUserInfo(SecurityConstants.INNER, userInfo.getUserId());
        tokenMap.put("org", r.getData());
        if (isAdmin) {
            tokenMap.put("needUpLic", false);
            tokenMap.put("showLicTip", false);
            if (licService.isLicExpired()) {
                // 是否需要上传许可证或许可时期过期
                tokenMap.put("needUpLic", true);
            } else {
                boolean showLicTip = licService.needShowLicTip(userInfo.getUserId());
                // 是否需要弹窗提示
                tokenMap.put("showLicTip", showLicTip);
            }
            // lic信息
            tokenMap.put("licInfo", licService.getLicInfoVo());
        }
        return R.ok(tokenMap);
    }

    /**
     * @param sysCode    每一个医院部署的系统都有一套单独的系统识别码，根据系统识别码去生成对应的认证文件，比如：用武商的识别码生成的文件不能在江西的系统上传。
     * @param sysPwd     类似于登录密码在后台管理界面是不能更改的，只能在服务器里面更改系统验证码，本质就是登录的密码。系统识别码是登录名，系统验证码是密码
     * @param showLicTip true 显示授权提示，false 不显示授权提示
     * @param licFile    上传的lic文件
     */
    @RequiresPermissions("system:licUp:up")
    @ApiOperation("上传lic")
    @PostMapping("/lic/up")
    public R<Boolean> up(@RequestParam("sysCode") @NotBlank(message = "系统识别码必填") String sysCode,
                         @RequestParam("sysPwd") @NotBlank(message = "系统验证码必填") String sysPwd,
                         @RequestParam(value = "showLicTip", defaultValue = "true", required = false) Boolean showLicTip,
                         @RequestPart("licFile") @NotNull(message = "文件必传") MultipartFile licFile) throws Exception {
        if (!licService.sysValidate(sysCode, sysPwd, true)) {
            return R.fail("系统识别码或验证码错误");
        }
        if (!licFile.getOriginalFilename().endsWith(".lic")) {
            return R.fail("文件格式错误");
        }
        if (licFile.getSize() > 1024 * 10) {
            return R.fail("文件过大");
        }
        licService.upLic(SecurityUtils.getUserId(), licFile);
        if (!showLicTip) {
            licService.saveLicTip(SecurityUtils.getUserId(), new LicTipDto(true, true));
        }
        return R.ok();
    }

    /**
     * 当系统还有一个月到期时，每周一系统管理员登陆后弹窗提示系统到期日期，
     * 可以选择本月不在提示和本周不在提示，
     * 选择后根据选择不提示此弹窗，如果没有选择则每周一系统管理员每次登录都弹窗，
     * 当还剩最后一周到期时每天系统管理员登录都需要弹窗同时如果勾选了本月和本周不提示则均不弹窗
     */
    @ApiOperation("关掉系统过期提示")
    @PostMapping("/lic/tip/close")
    public R<Boolean> tipClose(@RequestBody @Valid LicTipDto dto) {
        licService.saveLicTip(SecurityUtils.getUserId(), dto);
        return R.ok();
    }

    /**
     * 获取微信二维码
     *
     * @return
     */
    @GetMapping("/getQRCode")
    public R<byte[]> getQRCode() {
        return R.ok(WxLoginUtils.getQRCode(), "获取二维码");
    }

    /**
     * 微信回调接口
     *
     * @param request
     * @return
     */
    @GetMapping(value = "/receive")
    public String getGzhLoginReceive(HttpServletRequest request) {
        return WxLoginUtils.wxLogin(request);
    }

    @PostMapping(value = "/receive")
    public String postGzhLoginReceive(HttpServletRequest request) {
        return WxLoginUtils.wxLogin(request);
    }


    @GetMapping("logout")
    public R<?> logout(HttpServletRequest request) {
        String token = SecurityUtils.getToken(request);
        if (StringUtils.isNotEmpty(token)) {
            String username = JwtUtils.getUserName(token);
            // 删除用户缓存记录
            AuthUtil.logoutByToken(token);
            // 记录用户退出日志
            sysLoginService.logout(username);
        }
        return R.ok();
    }

    @PostMapping("refresh")
    public R<?> refresh(HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser)) {
            // 刷新令牌有效期
            tokenService.refreshToken(loginUser);
            return R.ok();
        }
        return R.ok();
    }

    @SmsCodeValid(
            clients = {ClientEnum.ZL, ClientEnum.TH},
            smsEnum = SmsEnum.SMS_REGISTER,
            mobileEL = "#registerBody.username",
            mobileCodeEL = "#registerBody.mobileCode")
    @PostMapping("register")
    public R<?> register(@RequestBody @Validated RegisterBody registerBody) {
        if (sysLoginService.validUserPasswordIsSimple(registerBody.getPassword())) {
            return R.fail(UserConstants.PASSWORD_TIP);
        }
        // 用户注册
        sysLoginService.register(registerBody);
        return R.ok();
    }


//    /**
//     * 忘记密码(修改密码)
//     *
//     * @param registerBody
//     * @return
//     */
//    @SmsCodeValid(
//            clients = {ClientEnum.LG},
//            smsEnum = SmsEnum.SMS_FORGET_PASSWORD,
//            mobileEL = "#registerBody.username",
//            mobileCodeEL = "#registerBody.mobileCode")
//    @PostMapping("/forgetPassword")
//    public R<Boolean> forgetPassword(@RequestBody @Valid RegisterBody registerBody) {
//        return R.ok(true);
//    }

    @FailLimit(tags = "smsSend")
    @PostMapping("/smsSend")
    public R<Boolean> smsSend(@RequestBody @Valid SendSmsDto dto) {
        SmsEnum smsType = dto.getSmsType();
        String mobile = dto.getMobile();
        if (smsType == SmsEnum.SMS_REGISTER) {
            R<SysUser> userR = remoteUserService.simpleInfo(mobile, SecurityConstants.INNER);
            if (userR.hasFail()) {
                throw new ServiceException(userR.getMsg());
            }
            if (userR.getData() != null) {
                return R.fail("该手机号已注册");
            }
        } else if (smsType == SmsEnum.SMS_LOGIN) {
            R<List<SysUser>> sysUserR = remoteUserService.simpleInfos(mobile, SecurityConstants.INNER);
            if (sysUserR.hasFail()) {
                throw new ServiceException(sysUserR.getMsg());
            }
            List<SysUser> users = sysUserR.getData();
            if (CollectionUtils.isEmpty(users)) {
                return R.fail("该手机号未注册");
            }
            if (users.size() > 1) {
                throw new ServiceException("手机号重复，请使用用户名进行登录");
            }
        } else if (smsType == SmsEnum.SMS_FORGET_PASSWORD) {
            R<SysUser> r = remoteUserService.simpleInfo(mobile, SecurityConstants.INNER);
            if (r.hasFail()) {
                throw new ServiceException(r.getMsg());
            }
            SysUser sysUser = r.getData();
            if (sysUser == null) {
                return R.fail("该手机号未注册");
            }
        }
        RedisTemplate redisTemplate = redisService.redisTemplate;
        if (redisTemplate.hasKey(smsType.getSmsKey(mobile))) {
            throw new ServiceException("短信验证码已发送，请勿重复发送");
        }
        String code = RandomUtil.randomNumbers(6);
        int minutes = 2;
        if (smsType == SmsEnum.SMS_FORGET_PASSWORD) {
            AjaxResult r = remoteSystemService.getConfigKey(UserConstants.LG_SMS_PARAMETER);
            if ((Integer) r.get("code") != 200) {
                log.error("调用接口/config/configKey失败,configKey={},r={}", UserConstants.LG_SMS_PARAMETER, r);
                return R.fail("查询config参数异常,发送短信失败");
            }

            R<SysDictData> r1 = remoteSystemService.getDictValueOne(UserConstants.LG_SMS_TEMPLATE_ID, UserConstants.FORGET_PASSWORD);
            if (r1.hasFail() || r1.getData() == null) {
                log.error("调用接口/dict/type/getDictValueOne失败,dict_type={} , dict_label={},r={}", UserConstants.LG_SMS_TEMPLATE_ID, UserConstants.FORGET_PASSWORD, r);
                return R.fail("查询短信模版参数异常,发送短信失败");
            }

            String value = String.valueOf(r.get("data"));
            JSONObject jsonObject = JSONUtil.parseObj(value);
            String url = jsonObject.getStr("url");
            String appId = jsonObject.getStr("appId");
            String privateKey = jsonObject.getStr("privateKey");

            Map<String, String> map = new HashMap<>();
            map.put("url", url);
            map.put("appId", appId);
            map.put("privateKey", privateKey);
            map.put("templateId", r1.getData().getDictValue());
            map.put("type", "code");
            map.put("data", code);
            String jsonBody = JSONUtil.toJsonStr(map);
            smsApi.sendSms(mobile, jsonBody);
        } else {
            smsApi.sendSms(mobile, smsType.getInfo(code, minutes));
        }
        redisTemplate.opsForValue().set(smsType.getSmsKey(mobile), code, minutes, TimeUnit.MINUTES);
        return R.ok();
    }

    /**
     * one-time-nonce
     * 根据用户id生成nonce，提供给表单提交作校验，避免表单重复提交，过期时间
     */
    @GetMapping("/nonce")
    public R<String> nonce() {
        Long userId = SecurityUtils.getUserId();
        if (Objects.isNull(userId)) {
            return R.fail("请求用户为空");
        }
        String nonceKey = Constants.NONCE_KEY + userId;
        RateLimiter.applyRateLimit(stringRedisTemplate, nonceKey,
                10, 60, 10, 60, 300);
        String idStr = IdUtil.nanoId(16);
        redisService.setCacheObject(nonceKey, idStr, 10L, TimeUnit.SECONDS);
        return R.ok(idStr);
    }


}
