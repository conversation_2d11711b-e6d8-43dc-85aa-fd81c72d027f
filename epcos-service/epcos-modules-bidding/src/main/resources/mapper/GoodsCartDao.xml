<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.epcos.bidding.purchase.goods.repository.IGoodsCartMapper">

    <resultMap id="baseMap" type="com.epcos.bidding.purchase.goods.domain.dao.GoodsCartDao">
        <id column="id" property="id"/>
        <result column="id" property="id"/>
        <result column="buyer_id" property="buyerId"/>
        <result column="goods_id" property="goodsId"/>
        <result column="buy_count" property="buyCount"/>
        <result column="seller_id" property="sellerId"/>
        <result column="deleted" property="deleted"/>
        <result column="create_at" property="createAt"/>
        <result column="create_by" property="createBy"/>
        <result column="update_at" property="updateAt"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>

    <resultMap id="voMap" type="com.epcos.bidding.purchase.goods.domain.vo.GoodsCartVo">
        <result column="id" property="cartId"/>
        <result column="buyer_id" property="buyerId"/>
        <result column="goods_id" property="goodsId"/>
        <result column="buy_count" property="buyCount"/>
        <result column="create_at" property="createAt"/>
    </resultMap>

    <select id="selectPageBy" resultMap="voMap">
        select *
        from goods_cart
        where buyer_id = #{entity.buyerId}
          and deleted = 0
    </select>

</mapper>