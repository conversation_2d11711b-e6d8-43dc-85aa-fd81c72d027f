<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.epcos.bidding.purchase.project.wzlg.repository.buyitem.WZLGBuyItemMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.epcos.bidding.purchase.project.wzlg.domain.dao.buyitem.BuyItemWZLGDao"
               id="purchaseWZLGBuyItemMap">
        <result property="id" column="id"/>
        <result property="buyItemCode" column="buy_item_code"/>
        <result property="buyItemName" column="buy_item_name"/>
        <result property="buyClass" column="buy_class"/>
        <result property="applyTime" column="apply_time"/>
        <result property="applyDept" column="apply_dept"/>
        <result property="applyUnitPerson" column="apply_unit_person"/>
        <result property="handlePerson" column="handle_person"/>
        <result property="buyPurposeTime" column="buy_purpose_time"/>
        <result property="innerCode" column="inner_code"/>
        <result property="buyHeader" column="buy_header"/>
        <result property="buyBody" column="buy_body"/>
        <result property="buyRemark" column="buy_remark"/>
        <result property="createAt" column="create_at"/>
        <result property="createBy" column="create_by"/>
        <result property="deleted" column="deleted"/>
        <result property="updateAt" column="update_at"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <select id="selectJoinBuyItemPage"
            resultType="com.epcos.bidding.purchase.project.wzlg.domain.vo.buyitem.WZLGBuyItemPageVo">
        SELECT
        a.buy_item_code buyItemCode,
        a.buy_item_name buyItemName,
        a.end,
        a.purchase_method_code purchaseMethodCode,
        a.org_code orgCode,
        a.create_at createAt,
        a.purchase_method_type purchaseMethodType,
        a.dept_id deptId,
        c.inner_code,
        c.buy_class,
        c.buy_body
        FROM purchase_buy_item a
        INNER JOIN purchase_wzlg_buy_item c ON a.buy_item_code = c.buy_item_code
        INNER JOIN purchase_subpackage b ON a.buy_item_code = b.buy_item_code
        <where>
            <if test="dto.buyItemName!=null and dto.buyItemName!=''">
                AND a.buy_item_name LIKE concat('%',#{dto.buyItemName},'%')
            </if>
            <if test="dto.purchaseMethodCode!=null and dto.purchaseMethodCode!=''">
                AND a.purchase_method_code = #{dto.purchaseMethodCode}
            </if>
            <if test="dto.purchaseMethodCode!=null and dto.purchaseMethodCode!=''">
                AND a.purchase_method_code = #{dto.purchaseMethodCode}
            </if>
            <if test="dto.end!=null ">
                AND a.end = #{dto.end}
            </if>
            <if test="dto.userId != null">
                and a.user_id = #{dto.userId}
            </if>
            <if test="dto.deptId != null">
                and a.dept_id = #{dto.deptId}
            </if>
            <if test="orgCode!= null and orgCode != ''">
                and a.org_code = #{orgCode}
            </if>
            <if test="dto.innerCode != null and dto.innerCode != ''">
                and c.inner_code like concat('%',#{dto.innerCode},'%')
            </if>
        </where>
        group by a.id
        order by a.id desc
    </select>
</mapper>