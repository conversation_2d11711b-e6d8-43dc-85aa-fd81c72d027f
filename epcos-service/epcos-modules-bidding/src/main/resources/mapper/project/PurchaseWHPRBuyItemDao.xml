<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.epcos.bidding.purchase.project.whpr.repository.WHPRBuyItemMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.epcos.bidding.purchase.project.whpr.domain.dao.BuyItemWHPRDao"
               id="purchaseHealthBuyItemMap">
        <result property="id" column="id"/>
        <result property="buyItemCode" column="buy_item_code"/>
        <result property="buyItemName" column="buy_item_name"/>
        <result property="buyBudget" column="buy_budget"/>
        <result property="useDept" column="use_dept"/>
        <result property="buyClass" column="buy_class"/>
        <result property="buyPerson" column="buy_person"/>
        <result property="organizeType" column="organize_type"/>
        <result property="buyRemark" column="buy_remark"/>
        <result property="concatNumber" column="concat_number"/>
        <result property="createAt" column="create_at"/>
        <result property="createBy" column="create_by"/>
        <result property="deleted" column="deleted"/>
        <result property="updateAt" column="update_at"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <select id="selectJoinBuyItemPage"
            resultType="com.epcos.bidding.purchase.project.whpr.domain.vo.WHPRBuyItemPageVo">
        select
        a.buy_item_code buyItemCode,
        a.buy_item_name buyItemName,
        a.end,
        a.purchase_method_code purchaseMethodCode,
        a.org_code orgCode,
        a.create_at createAt,
        a.purchase_method_type purchaseMethodType
        from purchase_buy_item a
        inner join purchase_whpr_buy_item c
        on a.buy_item_code = c.buy_item_code
        inner join purchase_subpackage b
        on a.buy_item_code = b.buy_item_code
        <where>
            <if test="dto.buyItemName!=null and dto.buyItemName!=''">
                and a.buy_item_name like concat('%',#{dto.buyItemName},'%')
            </if>
            <if test="dto.purchaseMethodCode!=null and dto.purchaseMethodCode!=''">
                and a.purchase_method_code = #{dto.purchaseMethodCode}
            </if>
            <if test="dto.purchaseMethodCode!=null and dto.purchaseMethodCode!=''">
                and a.purchase_method_code = #{dto.purchaseMethodCode}
            </if>
            <if test="dto.end!=null ">
                and a.end = #{dto.end}
            </if>
<!--            <if test="dto.userId != null">-->
<!--                and a.user_id = #{dto.userId}-->
<!--            </if>-->
            <if test="dto.deptId != null">
                and a.dept_id = #{dto.deptId}
            </if>
            <if test="orgCode!= null and orgCode != ''">
                and a.org_code = #{orgCode}
            </if>
            <if test="dto.useDept != null and dto.useDept != ''">
                and c.use_dept = #{dto.useDept}
            </if>
            <if test="dto.buyClass != null and dto.buyClass != ''">
                and c.buy_class = #{dto.buyClass}
            </if>
            <if test="dto.buyBudget != null ">
                and c.buy_budget = #{dto.buyBudget}
            </if>
        </where>
        group by a.id
        order by a.id desc
    </select>

    <select id="selectNewInnerCode" parameterType="string" resultType="string">
        select inner_code
        from purchase_whpr_buy_item
        where `create_at` like concat('%', #{currentYear}, '%')
          and buy_class = #{buyClass}
        order by id desc limit 1
    </select>


</mapper>