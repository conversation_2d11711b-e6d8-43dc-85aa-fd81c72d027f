<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.epcos.bidding.purchase.project.jxzl.repository.JXZLBuyItemMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.epcos.bidding.purchase.project.jxzl.domain.dao.BuyItemJXZLDao"
               id="purchaseHealthBuyItemMap">
        <result property="id" column="id"/>
        <result property="allocateId" column="allocate_id"/>
        <result property="buyItemCode" column="buy_item_code"/>
        <result property="buyItemName" column="buy_item_name"/>
        <result property="buyClass" column="buy_class"/>
        <result property="buyPerson" column="buy_person"/>
        <result property="concatNumber" column="concat_number"/>
        <result property="organizeType" column="organize_type"/>
        <result property="applyTime" column="apply_time"/>
        <result property="buyBudget" column="buy_budget"/>
        <result property="projectBudget" column="project_budget"/>
        <result property="buyFixePrice" column="buy_fixe_price"/>
        <result property="innerCode" column="inner_code"/>
        <result property="applyDept" column="apply_dept"/>
        <result property="managementDept" column="management_dept"/>
        <result property="organizeDept" column="organize_dept"/>
        <result property="entrustAgent" column="entrust_agent"/>
        <result property="purchaseLimitTime" column="purchase_limit_time"/>
        <result property="buyRemark" column="buy_remark"/>
        <result property="createAt" column="create_at"/>
        <result property="createBy" column="create_by"/>
        <result property="deleted" column="deleted"/>
        <result property="updateAt" column="update_at"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <select id="selectJoinBuyItemPage"
            resultType="com.epcos.bidding.purchase.project.jxzl.domain.vo.JXZLBuyItemPageVo">
        SELECT
        a.buy_item_code buyItemCode,
        a.buy_item_name buyItemName,
        a.end,
        a.purchase_method_code purchaseMethodCode,
        a.org_code orgCode,
        a.create_at createAt,
        a.purchase_method_type purchaseMethodType,
        c.inner_code
        FROM purchase_buy_item a
        INNER JOIN purchase_jxzl_buy_item c ON a.buy_item_code = c.buy_item_code
        INNER JOIN purchase_subpackage b ON a.buy_item_code = b.buy_item_code
        <where>
            <if test="dto.buyItemName!=null and dto.buyItemName!=''">
                AND a.buy_item_name LIKE concat('%',#{dto.buyItemName},'%')
            </if>
            <if test="dto.purchaseMethodCode!=null and dto.purchaseMethodCode!=''">
                AND a.purchase_method_code = #{dto.purchaseMethodCode}
            </if>
            <if test="dto.purchaseMethodCode!=null and dto.purchaseMethodCode!=''">
                AND a.purchase_method_code = #{dto.purchaseMethodCode}
            </if>
            <if test="dto.end!=null ">
                AND a.end = #{dto.end}
            </if>
            <if test="dto.userId != null">
                and a.user_id = #{dto.userId}
            </if>
            <if test="dto.deptId != null">
                and a.dept_id = #{dto.deptId}
            </if>
            <if test="orgCode!= null and orgCode != ''">
                and a.org_code = #{orgCode}
            </if>
            <if test="dto.buyClass != null and dto.buyClass != ''">
                and c.buy_class = #{dto.buyClass}
            </if>
            <if test="dto.buyBudget != null ">
                and c.buy_budget = #{dto.buyBudget}
            </if>
            <if test="dto.innerCode != null and dto.innerCode != ''">
                and c.inner_code like concat('%',#{dto.innerCode},'%')
            </if>
        </where>
        group by a.id
        order by a.id desc
    </select>

    <select id="selectNewInnerCode" parameterType="string" resultType="string">
        select inner_code
        from purchase_jxzl_buy_item
        where `create_at` like concat('%', #{currentYear}, '%')
          and organize_type = #{organizeType}
          and buy_class = #{buyClass}
          and organize_dept = #{organizeDept}
        order by id desc limit 1
    </select>

    <select id="selectByInnerCode" parameterType="string" resultType="string">
        select inner_code
        from purchase_jxzl_buy_item
        where inner_code like concat('%', #{finalCode}, '%')
        order by id desc limit 1
    </select>


    <select id="selectInnerCodeByJoin" parameterType="string" resultType="string">
        select inner_code
        FROM purchase_buy_item a
                 INNER JOIN purchase_jxzl_buy_item b ON a.buy_item_code = b.buy_item_code
        WHERE a.`create_at` LIKE concat('%', #{currentYear}, '%')
          AND a.purchase_method_code = #{purchaseMethodCode}
        ORDER BY b.inner_code DESC limit 1
    </select>

</mapper>