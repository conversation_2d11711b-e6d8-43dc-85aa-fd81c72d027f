<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.epcos.bidding.purchase.project.bjxk.repository.BJXKBuyItemMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.epcos.bidding.purchase.project.bjxk.domain.dao.BuyItemBJXKDao"
               id="purchaseHealthBuyItemMap">
        <result property="id" column="id"/>
        <result property="dingTalkId" column="ding_talk_id"/>
        <result property="buyItemCode" column="buy_item_code"/>
        <result property="buyItemName" column="buy_item_name"/>
        <result property="apply" column="apply"/>
        <result property="applyDept" column="apply_dept"/>
        <result property="budgetNumber" column="budget_number"/>
        <result property="buyPurpose" column="buy_purpose"/>
        <result property="buyClass" column="buy_class"/>
        <result property="procurementType" column="procurement_type"/>
        <result property="goodsType" column="goods_type"/>
        <result property="middleAmount" column="middle_amount"/>
        <result property="paramsAtt" column="params_att"/>
        <result property="buyBudget" column="buy_budget"/>
        <result property="largeAmount" column="large_amount"/>
        <result property="capitalSource" column="capital_source"/>
        <result property="budgetType" column="budget_type"/>
        <result property="buyPerson" column="buy_person"/>
        <result property="classNum" column="class_num"/>
        <result property="meetingContent" column="meeting_content"/>
        <result property="buyRemark" column="buy_remark"/>
        <result property="createAt" column="create_at"/>
        <result property="createBy" column="create_by"/>
        <result property="deleted" column="deleted"/>
        <result property="updateAt" column="update_at"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <select id="selectJoinBuyItemPage"
            resultType="com.epcos.bidding.purchase.project.bjxk.domain.vo.BJXKBuyItemPageVo">
        SELECT
        a.buy_item_code buyItemCode,
        a.buy_item_name buyItemName,
        a.end,
        a.purchase_method_code purchaseMethodCode,
        a.org_code orgCode,
        a.create_at createAt,
        a.purchase_method_type purchaseMethodType
        FROM purchase_buy_item a
        INNER JOIN purchase_bjxk_buy_item c ON a.buy_item_code = c.buy_item_code
        INNER JOIN purchase_subpackage b ON a.buy_item_code = b.buy_item_code
        <where>
            <if test="dto.buyItemName!=null and dto.buyItemName!=''">
                AND a.buy_item_name LIKE concat('%',#{dto.buyItemName},'%')
            </if>
            <if test="dto.purchaseMethodCode!=null and dto.purchaseMethodCode!=''">
                AND a.purchase_method_code = #{dto.purchaseMethodCode}
            </if>
            <if test="dto.purchaseMethodCode!=null and dto.purchaseMethodCode!=''">
                AND a.purchase_method_code = #{dto.purchaseMethodCode}
            </if>
            <if test="dto.end!=null ">
                AND a.end = #{dto.end}
            </if>
            <if test="dto.userId != null">
                and a.user_id = #{dto.userId}
            </if>
            <if test="dto.deptId != null">
                and a.dept_id = #{dto.deptId}
            </if>
            <if test="orgCode!= null and orgCode != ''">
                and a.org_code = #{orgCode}
            </if>
            <if test="dto.buyClass != null and dto.buyClass != ''">
                and c.buy_class = #{dto.buyClass}
            </if>
            <if test="dto.buyBudget != null ">
                and c.buy_budget = #{dto.buyBudget}
            </if>
        </where>
        group by a.id
        order by a.id desc
    </select>

    <update id="updateByItemCode" parameterType="com.epcos.bidding.purchase.project.bjxk.domain.dao.BuyItemBJXKDao">
        UPDATE purchase_bjxk_buy_item
        <set>
            <if test="dto.buyItemName != null">buy_item_name = #{dto.buyItemName},</if>
            <if test="dto.apply != null">`apply` = #{dto.apply},</if>
            <if test="dto.applyDept != null">apply_dept = #{dto.applyDept},</if>
            <if test="dto.budgetNumber != null">budget_number = #{dto.budgetNumber},</if>
            <if test="dto.buyPurpose != null">buy_purpose = #{dto.buyPurpose},</if>
            <if test="dto.buyClass != null">buy_class = #{dto.buyClass},</if>
            <if test="dto.procurementType != null">procurement_type = #{dto.procurementType},</if>
            <if test="dto.goodsType != null">goods_type = #{dto.goodsType},</if>
            <if test="dto.middleAmount != null">middle_amount = #{dto.middleAmount},</if>
            <if test="dto.buyBudget != null">buy_budget = #{dto.buyBudget},</if>
            <if test="dto.largeAmount != null">large_amount = #{dto.largeAmount},</if>
            <if test="dto.capitalSource != null">capital_source = #{dto.capitalSource},</if>
            <if test="dto.budgetType != null">budget_type = #{dto.budgetType},</if>
            <if test="dto.buyPerson != null">buy_person = #{dto.buyPerson},</if>
            <if test="dto.classNum != null">class_num = #{dto.classNum},</if>
            <if test="dto.buyRemark != null">buy_remark = #{dto.buyRemark},</if>
            <if test="dto.updateAt != null">update_at = #{dto.updateAt},</if>
            <if test="dto.updateBy != null">update_by = #{dto.updateBy}</if>
        </set>
        WHERE buy_item_code = #{dto.buyItemCode}
    </update>

</mapper>