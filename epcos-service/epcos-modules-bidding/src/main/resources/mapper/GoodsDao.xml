<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.epcos.bidding.supplier.goods.repository.GoodsMapper">

    <resultMap id="baseMap" type="com.epcos.bidding.supplier.goods.domain.dao.GoodsDao">
        <id column="id" property="id"/>
        <result column="goods_category" property="goodsCategory"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="goods_name" property="goodsName"/>
        <result column="goods_unit_price" property="goodsUnitPrice"/>
        <result column="goods_brand" property="goodsBrand"/>
        <result column="goods_origin" property="goodsOrigin"/>
        <result column="goods_detail" property="goodsDetail"
                typeHandler="com.epcos.bidding.common.JsonToAttValVoListTypeHandler"/>
        <result column="goods_status" property="goodsStatus"/>
        <result column="audit_remark" property="auditRemark"/>
        <result column="deleted" property="deleted"/>
        <result column="create_at" property="createAt"/>
        <result column="create_by" property="createBy"/>
        <result column="update_at" property="updateAt"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>

    <select id="selectPageJoin" resultMap="baseMap">
        select
        *
        from goods ta
        where ta.deleted=0
        <if test="supplierId != null">
            and ta.supplier_id =#{supplierId}
        </if>
        <if test="dto != null and dto.goodsCategory != null and dto.goodsCategory != ''">
            and ta.goods_category = #{dto.goodsCategory}
        </if>
        <if test="dto != null and dto.supplierName != null and dto.supplierName != ''">
            and ta.supplier_name like concat ('%', #{dto.supplierName},'%')
        </if>
        <if test="dto != null and dto.goodsName != null and dto.goodsName != ''">
            and ta.goods_name like concat ('%', #{dto.goodsName},'%')
        </if>
        <if test="dto != null and dto.goodsBrand != null and dto.goodsBrand != ''">
            and ta.goods_brand like concat ('%', #{dto.goodsBrand},'%')
        </if>
        <if test="dto != null and dto.goodsOrigin != null and dto.goodsOrigin != ''">
            and ta.goods_origin like concat ('%', #{dto.goodsOrigin},'%')
        </if>
        <if test="dto != null and dto.goodsStatus != null">
            and ta.goods_status=#{dto.goodsStatus}
        </if>
        <if test="dto != null and dto.auditStatus != null">
            and ta.audit_status=#{dto.auditStatus}
        </if>
        <if test="dto != null and dto.auditRemark != null and dto.auditRemark != ''">
            and ta.audit_remark like concat ('%', #{dto.auditRemark},'%')
        </if>

        <if test="dto != null and dto.goodsUnitPriceRange!=null">
            and ta.goods_unit_price between #{dto.goodsUnitPriceRange[0]} and #{dto.goodsUnitPriceRange[1]}
        </if>
        <if test="dto != null and dto.createAtRange!=null">
            and ta.create_at between #{dto.createAtRange[0]} and #{dto.createAtRange[1]}
        </if>
        <choose>
            <when test="dto != null and dto.createAtOrder == true">
                order by ta.create_at asc
            </when>
            <otherwise>
                order by ta.create_at desc
            </otherwise>
        </choose>
        <choose>
            <when test="dto != null and dto.goodsUnitPriceOrder != null">
                <if test="dto.goodsUnitPriceOrder == true">
                    , ta.goods_unit_price asc
                </if>
                <if test="dto.goodsUnitPriceOrder == false">
                    , ta.goods_unit_price desc
                </if>
            </when>
        </choose>
    </select>

</mapper>