<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.epcos.bidding.purchase.project.wzlg.repository.subpackage.WZLGSubpackageMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.epcos.bidding.purchase.project.wzlg.domain.dao.subPackage.SubpackageWZLGDao"
               id="purchaseWzlgSubpackageMap">
        <result property="id" column="id"/>
        <result property="subpackageCode" column="subpackage_code"/>
        <result property="bidType" column="bid_type"/>
        <result property="subServerType" column="sub_server_type"/>
        <result property="subMachineryType" column="sub_machinery_type"/>
        <result property="subMoveType" column="sub_move_type"/>
        <result property="subMoveAmount" column="sub_move_amount"/>
        <result property="subProjectPerson" column="sub_project_person"/>
        <result property="subPhone" column="sub_phone"/>
        <result property="subMoveEvidence" column="sub_move_evidence"/>
        <result property="subAgentInfo" column="sub_agent_info"/>
        <result property="subInfoFile" column="sub_info_file"/>
        <result property="deleted" column="deleted"/>
        <result property="createBy" column="create_by"/>
        <result property="createAt" column="create_at"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateAt" column="update_at"/>
    </resultMap>


</mapper>