<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.epcos.bidding.purchase.process.repository.IAskAnswerMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.epcos.bidding.purchase.process.domain.dao.AskAnswerDao" id="askAnswerMap">
        <result property="id" column="id"/>
        <result property="createAt" column="create_at"/>
        <result property="createBy" column="create_by"/>
        <result property="deleted" column="deleted"/>
        <result property="updateAt" column="update_at"/>
        <result property="updateBy" column="update_by"/>
        <result property="askUserType" column="ask_user_type"/>
        <result property="askUserId" column="ask_user_id"/>
        <result property="askUserName" column="ask_user_name"/>
        <result property="answerUserId" column="answer_user_id"/>
        <result property="answerUserName" column="answer_user_name"/>
        <result property="answerUserType" column="answer_user_type"/>
        <result property="askContent" column="ask_content"/>
        <result property="answerContent" column="answer_content"/>
        <result property="subpackageCode" column="subpackage_code"/>
    </resultMap>

    <sql id="selectAskAnswerVo">
        select id,
               create_at,
               create_by,
               deleted,
               update_at,
               update_by,
               ask_user_type,
               ask_user_id,
               ask_user_name,
               answer_user_id,
               answer_user_name,
               answer_user_type,
               ask_content,
               answer_content,
               subpackage_code
        from purchase_ask_answer
    </sql>


    <select id="findSup" resultMap="askAnswerMap">
        <include refid="selectAskAnswerVo"/>
        where subpackage_code = #{subpackageCode}
        and ask_user_type = #{askUserType}
        <if test="askUserId != null">
            and ask_user_id = #{askUserId}
        </if>
        <if test="answerUserId != null">
            and answer_user_id = #{answerUserId}
        </if>
    </select>

    <select id="findJudge" resultMap="askAnswerMap">
        <include refid="selectAskAnswerVo"/>
        where subpackage_code = #{subpackageCode}
        and ask_user_type = 3
        and answer_user_id = #{userId}
    </select>

</mapper>