<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.epcos.bidding.purchase.claims.repository.ClaimsFileMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileDao" id="claimsFileMap">
        <result property="id" column="id"/>
        <result property="subpackageCode" column="subpackage_code"/>
        <result property="epcFile" column="epc_file"/>
        <result property="pdfFile" column="pdf_file"/>
        <result property="releaseStatus" column="release_status"/>
        <result property="appName" column="app_name"/>
        <result property="appVersion" column="app_version"/>
        <result property="zepcKey" column="zepc_key"/>
        <result property="deleted" column="deleted"/>
        <result property="createAt" column="create_at"/>
        <result property="createBy" column="create_by"/>
        <result property="updateAt" column="update_at"/>
        <result property="updateBy" column="update_by"/>
        <result property="releaseTime" column="release_time"/>
    </resultMap>


</mapper>