<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.epcos.bidding.attribute.repository.AttributeMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.epcos.bidding.attribute.domain.dao.AttributeDao" id="attributeMap">
        <result property="id" column="id"/>
        <result property="groupType" column="group_type"/>
        <result property="keyName" column="key_name"/>
        <result property="keyVal" column="key_val"/>
        <result property="keyType" column="key_type"/>
        <result property="required" column="required"/>
        <result property="regex" column="regex"/>
        <result property="remark" column="remark"/>
        <result property="displayed" column="displayed"/>
        <result property="disabled" column="disabled"/>
        <result property="sort" column="sort"/>
        <result property="deleted" column="deleted"/>
        <result property="createAt" column="create_at"/>
        <result property="createBy" column="create_by"/>
        <result property="updateAt" column="update_at"/>
        <result property="updateBy" column="update_by"/>
        <result property="keyGroup" column="key_group"/>
    </resultMap>


</mapper>