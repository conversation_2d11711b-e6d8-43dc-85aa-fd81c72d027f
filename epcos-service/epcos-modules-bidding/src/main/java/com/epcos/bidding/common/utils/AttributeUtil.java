package com.epcos.bidding.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.epcos.bidding.purchase.api.params.AttributeVo;
import com.epcos.bidding.purchase.api.params.dto.AttributeValVo;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.StringUtils;
import com.epcos.common.core.utils.sign.Base64;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 自定义属性工具类
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class AttributeUtil {

    private static final SerializerFeature WRITE_MAP_NULL_VALUE = SerializerFeature.WriteMapNullValue;

    public static String asJson(Object obj) {
        return Objects.isNull(obj) ? null : JSON.toJSONString(obj, WRITE_MAP_NULL_VALUE);
    }

    public static List<AttributeVo> asHead(String json) {
        if (StringUtils.isEmpty(json)) {
            return Collections.emptyList();
        }
        List<AttributeVo> attributeVos = JSON.parseObject(json, new TypeReference<List<AttributeVo>>() {
        });
        Collections.sort(attributeVos);
        return attributeVos;
    }

    public static LinkedHashMap<String, String> asMap(String json) {
        return StringUtils.isEmpty(json)
                ? new LinkedHashMap<>(0)
                : JSON.parseObject(json, new TypeReference<LinkedHashMap<String, String>>() {
        });
    }

    public static List<LinkedHashMap<String, String>> asBody(String json) {
        return StringUtils.isEmpty(json)
                ? new ArrayList<>()
                : JSON.parseObject(json, new TypeReference<List<LinkedHashMap<String, String>>>() {
        });
    }

    public static String convertJavaRegular(String regex) {
        if (StringUtils.isEmpty(regex)) {
            return regex;
        }
        if (regex.charAt(0) == '^' && regex.charAt(regex.length() - 1) == '$') {
            return regex;
        }
        return regex.charAt(0) == '/'
                ? regex.substring(1, regex.lastIndexOf('/'))
                : regex;
    }

    // quote header convert 客户端 data.json in head
    public static List<QuoteSheetHeaders> convertQuoteSheetHeaders(List<AttributeVo> heads) {
        if (CollectionUtils.isEmpty(heads)) {
            return Collections.emptyList();
        }
        Collections.sort(heads);
        return heads.stream().map(ac -> {
            QuoteSheetHeaders v = new QuoteSheetHeaders();
            v.setLabel(ac.getKeyName());
            v.setKey(ac.getKeyVal());
            v.setKeyType(ac.getKeyType());
            v.setRequired(ac.getRequired());
            v.setDisplayed(ac.getDisplayed());
            v.setVerifyRule(ac.getRegex());
            v.setVerifyMsg(ac.getRemark());
            v.setSort(ac.getSort());
            return v;
        }).collect(Collectors.toList());
    }

    /**
     * 验证表头和内容的必要性、正则表达式和Java类型。
     *
     * @param theBidder 是投标人
     * @param webForm   是否来自Web表单
     * @param heads     表头属性列表
     * @param bodyMaps  内容映射列表
     * @param filePath  文件路径
     */
    public static void validRequiredAndRegex(boolean theBidder, boolean webForm, List<AttributeVo> heads,
                                             List<LinkedHashMap<String, String>> bodyMaps, Path filePath) {
        decodeBase64(heads);
        if (!CollectionUtils.isEmpty(bodyMaps)) {
            for (Map<String, String> row : bodyMaps) {
                validateRow(theBidder, webForm, heads, row, filePath);
            }
        }
    }

    private static void validateRow(boolean theBidder, boolean webForm,
                                    List<AttributeVo> heads, Map<String, String> row, Path filePath) {
        if (CollectionUtils.isEmpty(heads) || CollectionUtils.isEmpty(row)) {
            return;
        }
        for (Map.Entry<String, String> entry : row.entrySet()) {
            AttributeVo head = findAttributeVo(heads, entry.getKey());
            validateEntry(theBidder, webForm, head, entry.getValue(), filePath);
        }
    }

    private static AttributeVo findAttributeVo(List<AttributeVo> heads, String key) {
        return heads.stream()
                .filter(f -> Objects.equals(f.getKeyVal(), key))
                .findAny()
                .orElseThrow(() -> {
                    log.error("在所有列中无法找到当前列。当前列：{}", key);
                    return new ServiceException("在所有列中无法找到当前列。当前列：" + key);
                });
    }

    private static void validateEntry(boolean theBidder, boolean webForm, AttributeVo head,
                                      String value, Path filePath) {
        String tip = generateTip(head, value, filePath);
        if (theBidder && Boolean.TRUE.equals(head.getRequired())) {
            validateRequired(webForm, head.getKeyType(), value, filePath, tip);
        }
        if (!Objects.equals("file", head.getKeyType())) {
            validateRegex(head.getRegex(), value, tip);
        }
    }

    private static void validateRegex(String regex, String value, String tip) {
        if (!StringUtils.isEmpty(regex)) {
            regex = convertJavaRegular(regex);
            if (!StringUtils.isEmpty(value) && !value.matches(regex)) {
                throw new ServiceException(tip + "不符合正则校验");
            }
        }
    }

    private static String generateTip(AttributeVo head, Object value, Path filePath) {
        return String.format("当前字段名：%s, 当前英文名：%s, 是否必填：%s, 正则：%s, 当前值：%s, 文件路径：%s, 错误信息：",
                head.getKeyName(), head.getKeyVal(), head.getRequired(), head.getRegex(), value, filePath);
    }

    private static void validateRequired(boolean webForm, String keyType, String value, Path filePath, String tip) {
        if (StringUtils.isEmpty(value)) {
            throw new ServiceException(tip + "值必填");
        }
        if (!webForm && "file".equals(keyType)) {
            validateFileExists(value, filePath, tip);
        }
    }

    private static void validateFileExists(String value, Path filePath, String tip) {
        if (Objects.isNull(filePath)) {
            throw new ServiceException(tip + "文件路径不正确");
        }
        Path targetFilePath = filePath.resolve(value);
        if (BidFileUtil.isNotExistFile(targetFilePath.toFile())) {
            throw new ServiceException(tip + "必须是一个存在的文件：" + targetFilePath);
        }
        if (BidFileUtil.isEncrypted(targetFilePath.toFile())) {
            throw new ServiceException(tip + "pdf文件【" + targetFilePath + "】有加密或只读，无法合并");
        }
    }

    // 修改head中base64编码为解码之后的
    public static List<AttributeVo> decodeBase64(List<AttributeVo> heads) {
        if (CollectionUtils.isEmpty(heads)) {
            return heads;
        }
        for (AttributeVo head : heads) {
            decodeBase64(head);
        }
        return heads;
    }

    private static void decodeBase64(AttributeVo head) {
        if (Base64.isBase64(head.getRegex())) {
            head.setRegex(cn.hutool.core.codec.Base64.decodeStr(head.getRegex()));
        }
    }

    public static void validRequiredAndRegex(List<AttributeValVo> attributeValVoList) {
        if (CollectionUtils.isEmpty(attributeValVoList)) {
            return;
        }
        attributeValVoList.forEach(i -> {
            AttributeUtil.decodeBase64(i);
            AttributeUtil.validRequiredAndRegex(i, i.getValue());
        });
    }

    public static void validRequiredAndRegex(AttributeVo head, Object value) {
        String tip = generateTip(head, value, null);
        if (Objects.isNull(value)) {
            if (Boolean.TRUE.equals(head.getRequired())) {
                throw new ServiceException(tip + "值必填");
            }
        } else {
            String val = value.toString();
            if (Objects.equals("file", head.getKeyType())) {
                if (!(value instanceof MultipartFile)) {
                    throw new ServiceException(tip + "必须是一个文件，当前值为：" + value);
                }
                val = ((MultipartFile) value).getOriginalFilename();
            }
            // 正则
            validateRegex(head.getRegex(), val, tip);
        }
    }

    // 处理特殊文件类型字段
    public static String replaceFileTypeField(String value, AttributeVo head) {
        if (Objects.nonNull(head) && Objects.equals("file", head.getKeyType())) {
            return "/".equals(value)
                    ? "未上传"
                    : (org.springframework.util.StringUtils.hasText(value) ? "已上传" : "未上传");
        }
        return value;
    }


}
