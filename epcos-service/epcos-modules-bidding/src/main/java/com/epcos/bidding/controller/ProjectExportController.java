package com.epcos.bidding.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.file.PathUtil;
import com.epcos.bidding.common.utils.excel.PoiExcelUtil;
import com.epcos.bidding.purchase.export.ProjectExportService;
import com.epcos.bidding.purchase.project.base.domain.dto.ProjectExportDto;
import com.epcos.bidding.purchase.project.base.domain.vo.BidQuoteVo;
import com.epcos.bidding.purchase.project.base.domain.vo.ProjectExportVo;
import com.epcos.bidding.purchase.project.base.domain.vo.SignUpBidderVo;
import com.epcos.bidding.purchase.project.base.domain.vo.SubpackageVo;
import com.epcos.common.core.constant.FileTypeNameConstants;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.file.FileUtils;
import com.epcos.common.file.html2pdf.HtmlUtil;
import com.epcos.common.redis.annotation.RedisLock;
import com.epcos.common.security.annotation.Logical;
import com.epcos.common.security.annotation.RequiresPermissions;
import com.epcos.epcfile.api.RemoteNonProjectFileService;
import com.epcos.epcfile.api.domain.vo.SysFileVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.aop.framework.AopContext;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Semaphore;
import java.util.stream.IntStream;

/**
 * 项目报表导出
 */
@Slf4j
@Api("项目报表导出")
@RestController
@RequestMapping("/project")
@AllArgsConstructor
public class ProjectExportController {

    private final ProjectExportService projectExportService;
    private final RemoteNonProjectFileService remoteNonProjectFileService;
    private final Semaphore semaphore = new Semaphore(1);

    @RedisLock(second = 30)
    @RequiresPermissions(value = {"project:report:export", "project:report:exportAll"}, logical = Logical.OR)
    @ApiOperation("项目报表导出")
    @PostMapping("/export")
    public R<Boolean> export(@RequestBody @Valid ProjectExportDto dto) {
        if (!semaphore.tryAcquire()) {
            return R.fail("当前有任务正在执行，请稍后再试");
        }
        try {
            ((ProjectExportController) AopContext.currentProxy()).generateExcelAndUpFile(dto);
        } catch (Exception e) {
            semaphore.release();
            log.error("项目报表导出失败, dto={}", dto, e);
            return R.fail("项目报表导出失败");
        }
        return R.ok();
    }

    @Async
    public void generateExcelAndUpFile(ProjectExportDto dto) {
        Path path = null;
        try {
            List<ProjectExportVo> voList = projectExportService.getProjectExportVoList(dto);
            if (CollectionUtils.isEmpty(voList)) {
                log.error("查询项目报表导出数据为空, dto={}", dto);
                return;
            }
            path = generateExcel(voList);
            R<Boolean> projectedReportDelete = remoteNonProjectFileService.projectReportDelete();
            if (projectedReportDelete.hasFail() || Boolean.FALSE.equals(projectedReportDelete.getData())) {
                log.error("删除项目报表失败，projectedReportDelete={}", projectedReportDelete);
                throw new ServiceException("删除项目报表失败：" + projectedReportDelete.getMsg());
            }
            R<SysFileVo> sysFileVoR = remoteNonProjectFileService.uploadNonProjectFile(
                    FileUtils.fileToMultipartFile(path.toFile()),
                    FileTypeNameConstants.PROJECT_REPORT_EXCEL);
            if (sysFileVoR.hasFail()) {
                log.error("项目报表导出文件上传失败, path={}, voList={}, sysFileVoR={}", path, voList, sysFileVoR);
                throw new ServiceException("项目报表导出文件上传失败：" + sysFileVoR.getMsg());
            }
        } finally {
            semaphore.release();
            Optional.ofNullable(path).ifPresent(PathUtil::del);
        }
    }

    @RequiresPermissions(value = {"project:report:export", "project:report:exportAll"}, logical = Logical.OR)
    @ApiOperation("下载项目报表导出")
    @GetMapping("/download")
    public ResponseEntity<Resource> download() {
        return remoteNonProjectFileService.dowProjectReport(FileTypeNameConstants.PROJECT_REPORT_EXCEL);
    }

    // 导出表格
    private Path generateExcel(List<ProjectExportVo> voList) {
        Path path = HtmlUtil.getTmpXlsxFilePath("项目报表_" + DateUtil.format(new Date(), "yyyy-MM-dd_HHmmss"));
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            XSSFSheet sheet = workbook.createSheet();

            CellStyle titleStyle = PoiExcelUtil.cellStyleTitle(workbook);
            titleStyle.setBorderBottom(BorderStyle.THIN);
            titleStyle.setBorderTop(BorderStyle.THIN);
            titleStyle.setBorderLeft(BorderStyle.THIN);
            titleStyle.setBorderRight(BorderStyle.THIN);
            CellStyle headStyle = PoiExcelUtil.cellStyleTableHead(workbook);
            CellStyle contentStyle = PoiExcelUtil.cellStyleContent(workbook);

            int projectColumn = 7;
            int subColumn = 7;
            int bidColumn = 8;
            int quColumn = 4;
            // 标题
            addTitleCell(sheet, titleStyle, projectColumn, subColumn, bidColumn, quColumn);
            // 表头
            addHeadCell(sheet, headStyle);
            for (ProjectExportVo p : voList) {
                // 项目-开始行
                int projectStartRow = sheet.getPhysicalNumberOfRows();
                // 标段包
                for (SubpackageVo sub : p.getSubpackageVoList()) {
                    int sectionRow = sheet.getPhysicalNumberOfRows();
                    // 报名投标供应商
                    for (SignUpBidderVo bid : sub.getSignUpBidderVoList()) {
                        int supplierRow = sheet.getPhysicalNumberOfRows();
                        // 报价
                        for (BidQuoteVo qu : bid.getBidQuoteVoList()) {
                            XSSFRow row = sheet.createRow(sheet.getPhysicalNumberOfRows());
                            int columnIndex = 0;
                            // 项目内容
                            columnIndex = setProjectCell(p, columnIndex, row, contentStyle);
                            // 标段内容
                            columnIndex = setSubpackageCell(sub, columnIndex, row, contentStyle);
                            // 供应商内容
                            columnIndex = setSupplierCell(bid, columnIndex, row, contentStyle);
                            // 中标人报价
//                            if (Boolean.TRUE.equals(bid.getIsWinningBidder())) {
                            // 报价内容
                            columnIndex = setCellVal(row, contentStyle, columnIndex, qu.getName());
                            columnIndex = setCellVal(row, contentStyle, columnIndex, qu.getQuantity());
                            columnIndex = setCellVal(row, contentStyle, columnIndex, qu.getUnitPrice());
                            columnIndex = setCellVal(row, contentStyle, columnIndex, qu.getQuotationTotalPrice());
//                            }
                        }
                        // 供应商-循环合并行
                        if (supplierRow < sheet.getLastRowNum()) {
                            IntStream.range(projectColumn + subColumn, projectColumn + subColumn + bidColumn).forEach(i -> {
                                sheet.addMergedRegion(new CellRangeAddress(supplierRow, sheet.getLastRowNum(), i, i));
                            });
                        }
                    }
                    // 标段-循环合并行
                    if (sectionRow < sheet.getLastRowNum()) {
                        IntStream.range(projectColumn, projectColumn + subColumn).forEach(i ->
                                sheet.addMergedRegion(new CellRangeAddress(sectionRow, sheet.getLastRowNum(), i, i)));
                    }
                }
                // 项目-循环合并行
                if (projectStartRow < sheet.getLastRowNum()) {
                    IntStream.range(0, projectColumn).forEach(i ->
                            sheet.addMergedRegion(new CellRangeAddress(projectStartRow, sheet.getLastRowNum(), i, i)));
                }
            }
            workbook.write(Files.newOutputStream(path, StandardOpenOption.CREATE));
        } catch (IOException e) {
            log.error("导出项目报表异常，path:{}", path, e);
            throw new ServiceException("导出项目报表异常");
        }
        return path;
    }

    private int setSupplierCell(SignUpBidderVo bid, int columnIndex, XSSFRow row, CellStyle contentStyle) {
        columnIndex = setCellVal(row, contentStyle, columnIndex, bid.getBidderName());
        columnIndex = setCellVal(row, contentStyle, columnIndex, bid.getLicNumber());
        columnIndex = setCellVal(row, contentStyle, columnIndex, bid.getCertificateName());
        columnIndex = setCellVal(row, contentStyle, columnIndex, bid.getCertificateCode());
        columnIndex = setCellVal(row, contentStyle, columnIndex, bid.getInfoReporterName());
        columnIndex = setCellVal(row, contentStyle, columnIndex, bid.getInfoReporterContactNumber());
        columnIndex = setCellVal(row, contentStyle, columnIndex, Objects.isNull(bid.getIsWinningBidder()) ? "" : bid.getIsWinningBidder() ? "是" : "不是");
        columnIndex = setCellVal(row, contentStyle, columnIndex, Optional.ofNullable(bid.getSendTime()).map(DateUtil::formatDateTime).orElse(""));
        return columnIndex;
    }

    private int setSubpackageCell(SubpackageVo sub, int columnIndex, XSSFRow row, CellStyle contentStyle) {
        columnIndex = setCellVal(row, contentStyle, columnIndex, sub.getSubpackageName());
        columnIndex = setCellVal(row, contentStyle, columnIndex, Optional.ofNullable(sub.getTenderPushTime()).map(DateUtil::formatDateTime).orElse(""));
        columnIndex = setCellVal(row, contentStyle, columnIndex, Optional.ofNullable(sub.getRegistrationEndTime()).map(DateUtil::formatDateTime).orElse(""));
        columnIndex = setCellVal(row, contentStyle, columnIndex, Optional.ofNullable(sub.getResponseFileEndTime()).map(DateUtil::formatDateTime).orElse(""));
        columnIndex = setCellVal(row, contentStyle, columnIndex, Optional.ofNullable(sub.getMeetingTime()).map(DateUtil::formatDateTime).orElse(""));
        columnIndex = setCellVal(row, contentStyle, columnIndex, Optional.ofNullable(sub.getWinPushTime()).map(DateUtil::formatDateTime).orElse(""));
        columnIndex = setCellVal(row, contentStyle, columnIndex, sub.getJudgeNameAndIdNumber());
        return columnIndex;
    }

    private int setProjectCell(ProjectExportVo p, int columnIndex, XSSFRow row, CellStyle contentStyle) {
        columnIndex = setCellVal(row, contentStyle, columnIndex, p.getProjectVo().getBuyItemName());
        columnIndex = setCellVal(row, contentStyle, columnIndex, p.getProjectVo().getBuyItemCode());
        columnIndex = setCellVal(row, contentStyle, columnIndex, p.getProjectVo().getBuyBudget());
        columnIndex = setCellVal(row, contentStyle, columnIndex, p.getProjectVo().getUseDept());
        columnIndex = setCellVal(row, contentStyle, columnIndex, p.getProjectVo().getManagementDept());
        columnIndex = setCellVal(row, contentStyle, columnIndex, p.getProjectVo().getBuyPerson());
        columnIndex = setCellVal(row, contentStyle, columnIndex, p.getProjectVo().getCreateBy());
        return columnIndex;
    }


    private int setCellVal(XSSFRow row, CellStyle contentStyle, int colIndex, String val) {
        XSSFCell cell = row.createCell(colIndex++);
        cell.setCellStyle(contentStyle);
        cell.setCellValue(val);
        return colIndex;
    }

    // 标题
    private void addTitleCell(XSSFSheet sheet, CellStyle titleStyle, int projectColumn, int subColumn, int bidColumn, int quColumn) {
        int colTitleIndex = 0;
        XSSFRow row = sheet.createRow(0);

        XSSFCell cell_title_1 = row.createCell(colTitleIndex);
        cell_title_1.setCellValue("项目信息");
        CellRangeAddress cellAddresses_title1 = new CellRangeAddress(0, 0, colTitleIndex, colTitleIndex + 6);
        sheet.addMergedRegion(cellAddresses_title1);
        setBordersForMergeCell(sheet, cellAddresses_title1, titleStyle);
        colTitleIndex += projectColumn;

        XSSFCell cell_title_2 = row.createCell(colTitleIndex);
        cell_title_2.setCellValue("标段（包）信息");
        CellRangeAddress cellAddresses_title2 = new CellRangeAddress(0, 0, colTitleIndex, colTitleIndex + 6);
        sheet.addMergedRegion(cellAddresses_title2);
        setBordersForMergeCell(sheet, cellAddresses_title2, titleStyle);
        colTitleIndex += subColumn;

        XSSFCell cell_title_3 = row.createCell(colTitleIndex);
        cell_title_3.setCellValue("投标供应商信息");
        CellRangeAddress cellAddresses_title3 = new CellRangeAddress(0, 0, colTitleIndex, colTitleIndex + 7);
        sheet.addMergedRegion(cellAddresses_title3);
        setBordersForMergeCell(sheet, cellAddresses_title3, titleStyle);
        colTitleIndex += bidColumn;

        XSSFCell cell_title_4 = row.createCell(colTitleIndex);
        cell_title_4.setCellValue("成交信息");
        CellRangeAddress cellAddresses_title4 = new CellRangeAddress(0, 0, colTitleIndex, colTitleIndex + 3);
        sheet.addMergedRegion(cellAddresses_title4);
        setBordersForMergeCell(sheet, cellAddresses_title4, titleStyle);
        colTitleIndex += quColumn;
    }

    /**
     * 为合并单元格区域中的每个单元格设置边框
     */
    private void setBordersForMergeCell(Sheet sheet, CellRangeAddress region, CellStyle titleStyle) {
        for (int row = region.getFirstRow(); row <= region.getLastRow(); row++) {
            Row sheetRow = sheet.getRow(row);
            if (sheetRow == null) {
                sheetRow = sheet.createRow(row);
            }
            for (int col = region.getFirstColumn(); col <= region.getLastColumn(); col++) {
                Cell cell = sheetRow.getCell(col);
                if (cell == null) {
                    cell = sheetRow.createCell(col);
                }
                cell.setCellStyle(titleStyle);
            }
        }
    }


    // 表头
    private void addHeadCell(XSSFSheet sheet, CellStyle headStyle) {
        int colHeadIndex = 0;
        XSSFRow rowHead = sheet.createRow(sheet.getPhysicalNumberOfRows());

        // 项目信息
        XSSFCell cell_head_a1 = rowHead.createCell(colHeadIndex++);
        cell_head_a1.setCellStyle(headStyle);
        cell_head_a1.setCellValue("项目名称");

        XSSFCell cell_head_a2 = rowHead.createCell(colHeadIndex++);
        cell_head_a2.setCellStyle(headStyle);
        cell_head_a2.setCellValue("项目编号");

        XSSFCell cell_head_a3 = rowHead.createCell(colHeadIndex++);
        cell_head_a3.setCellStyle(headStyle);
        cell_head_a3.setCellValue("预算价格");

        XSSFCell cell_head_a4 = rowHead.createCell(colHeadIndex++);
        cell_head_a4.setCellStyle(headStyle);
        cell_head_a4.setCellValue("使用科室");

        XSSFCell cell_head_a5 = rowHead.createCell(colHeadIndex++);
        cell_head_a5.setCellStyle(headStyle);
        cell_head_a5.setCellValue("归口部门");

        XSSFCell cell_head_a6 = rowHead.createCell(colHeadIndex++);
        cell_head_a6.setCellStyle(headStyle);
        cell_head_a6.setCellValue("项目负责人");

        XSSFCell cell_head_a7 = rowHead.createCell(colHeadIndex++);
        cell_head_a7.setCellStyle(headStyle);
        cell_head_a7.setCellValue("采购经办人");

        // 标段（包）信息
        XSSFCell cell_head_b1 = rowHead.createCell(colHeadIndex++);
        cell_head_b1.setCellStyle(headStyle);
        cell_head_b1.setCellValue("标段包名");

        XSSFCell cell_head_b2 = rowHead.createCell(colHeadIndex++);
        cell_head_b2.setCellStyle(headStyle);
        cell_head_b2.setCellValue("招标公告发布时间");

        XSSFCell cell_head_b3 = rowHead.createCell(colHeadIndex++);
        cell_head_b3.setCellStyle(headStyle);
        cell_head_b3.setCellValue("报名截止期");

        XSSFCell cell_head_b4 = rowHead.createCell(colHeadIndex++);
        cell_head_b4.setCellStyle(headStyle);
        cell_head_b4.setCellValue("投标截止期");

        XSSFCell cell_head_b5 = rowHead.createCell(colHeadIndex++);
        cell_head_b5.setCellStyle(headStyle);
        cell_head_b5.setCellValue("开标时间");

        XSSFCell cell_head_b6 = rowHead.createCell(colHeadIndex++);
        cell_head_b6.setCellStyle(headStyle);
        cell_head_b6.setCellValue("结果公示时间");

        XSSFCell cell_head_b7 = rowHead.createCell(colHeadIndex++);
        cell_head_b7.setCellStyle(headStyle);
        cell_head_b7.setCellValue("评委");

        // 投标供应商信息
        XSSFCell cell_head_c1 = rowHead.createCell(colHeadIndex++);
        cell_head_c1.setCellStyle(headStyle);
        cell_head_c1.setCellValue("单位名称");

        XSSFCell cell_head_c2 = rowHead.createCell(colHeadIndex++);
        cell_head_c2.setCellStyle(headStyle);
        cell_head_c2.setCellValue("社会统一信用代码");

        XSSFCell cell_head_c3 = rowHead.createCell(colHeadIndex++);
        cell_head_c3.setCellStyle(headStyle);
        cell_head_c3.setCellValue("法定代表人");

        XSSFCell cell_head_c4 = rowHead.createCell(colHeadIndex++);
        cell_head_c4.setCellStyle(headStyle);
        cell_head_c4.setCellValue("法定代表人证件号码");

        XSSFCell cell_head_c5 = rowHead.createCell(colHeadIndex++);
        cell_head_c5.setCellStyle(headStyle);
        cell_head_c5.setCellValue("项目负责人");

        XSSFCell cell_head_c6 = rowHead.createCell(colHeadIndex++);
        cell_head_c6.setCellStyle(headStyle);
        cell_head_c6.setCellValue("项目负责人电话");

        XSSFCell cell_head_c7 = rowHead.createCell(colHeadIndex++);
        cell_head_c7.setCellStyle(headStyle);
        cell_head_c7.setCellValue("是否中标人");

        XSSFCell cell_head_c8 = rowHead.createCell(colHeadIndex++);
        cell_head_c8.setCellStyle(headStyle);
        cell_head_c8.setCellValue("成交结果通知时间");

        // 成交信息
        XSSFCell cell_head_d1 = rowHead.createCell(colHeadIndex++);
        cell_head_d1.setCellStyle(headStyle);
        cell_head_d1.setCellValue("产品名称");

        XSSFCell cell_head_d2 = rowHead.createCell(colHeadIndex++);
        cell_head_d2.setCellStyle(headStyle);
        cell_head_d2.setCellValue("数量");

        XSSFCell cell_head_d3 = rowHead.createCell(colHeadIndex++);
        cell_head_d3.setCellStyle(headStyle);
        cell_head_d3.setCellValue("单价");

        XSSFCell cell_head_d4 = rowHead.createCell(colHeadIndex++);
        cell_head_d4.setCellStyle(headStyle);
        cell_head_d4.setCellValue("总价");
    }

}
