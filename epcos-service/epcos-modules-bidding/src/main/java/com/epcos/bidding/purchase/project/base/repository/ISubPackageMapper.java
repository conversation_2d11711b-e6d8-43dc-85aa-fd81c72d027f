package com.epcos.bidding.purchase.project.base.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.epcos.bidding.purchase.api.params.dto.BaseQueryDto;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 包 信息
 * @date 2024/4/23 12:28
 */
public interface ISubPackageMapper extends BaseMapper<SubpackageDao> {


    List<SubpackageDao> findTodayBidOPen(Page<Object> of, @Param("baseQueryDto") BaseQueryDto baseQueryDto);

    Long findTodayBidOPenCnt(@Param("isToday") Boolean isToday, @Param("baseQueryDto") BaseQueryDto baseQueryDto);

}
