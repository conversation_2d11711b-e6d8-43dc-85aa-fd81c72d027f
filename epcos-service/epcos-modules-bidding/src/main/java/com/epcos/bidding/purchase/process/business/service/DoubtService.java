package com.epcos.bidding.purchase.process.business.service;

import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.common.utils.BiddingBaseUtil;
import com.epcos.bidding.purchase.api.params.DoubtVo;
import com.epcos.bidding.purchase.api.params.dto.DoubtDto;
import com.epcos.bidding.purchase.api.params.dto.QueryDoubtDto;
import com.epcos.bidding.purchase.process.business.api.IDoubtApi;
import com.epcos.bidding.purchase.process.business.api.IDoubtTimeApi;
import com.epcos.bidding.purchase.process.domain.dao.DoubtDao;
import com.epcos.bidding.purchase.process.domain.dao.DoubtTimeDao;
import com.epcos.bidding.purchase.process.domain.vo.DoubtTimeVo;
import com.epcos.bidding.purchase.process.mapping.DoubtConvert;
import com.epcos.bidding.purchase.process.repository.IDoubtMapper;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.remote.RemoteToOtherServiceApi;
import com.epcos.common.core.constant.FileTypeNameConstants;
import com.epcos.common.core.constant.UserConstants;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.file.html2pdf.HtmlUtil;
import com.epcos.system.api.model.EsignVO;
import com.epcos.system.api.model.FUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/7 15:05
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DoubtService extends ServiceImpl<IDoubtMapper, DoubtDao> implements IDoubtApi {

    private final IBuyItemApi buyItemApi;
    private final IDoubtTimeApi doubtTimeApi;
    private final RemoteToOtherServiceApi remoteToOtherServiceApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean askReplyDoubt(DoubtDto dto) {
        //校验是否在提出质疑的时间之内
        DoubtTimeDao doubtTimeDao = doubtTimeApi.check(dto.getSubpackageCode(), dto.getDoubtType());
        String fileName = getFileCount(dto.getSubpackageCode(), dto.getDoubtUserType(), dto.getDoubtUserName(), dto.getId());
        String url = fileMethod(dto, FileTypeNameConstants.SUBMIT_RESULT_QUESTION, fileName);
        DoubtDao doubtDao = DoubtConvert.INSTANCE.convert(dto, doubtTimeDao.getId());
        if (Objects.nonNull(dto.getDoubtUserId())) {
            doubtDao.setDoubtFileKey(url);
        } else {
            doubtDao.setReplyFileKey(url);
        }
        return saveOrUpdate(doubtDao);
    }

    private String getFileCount(String subpackageCode, String doubtUserType, String doubtUserName, Long doubtId) {
        //没有提问默认为第一次
        int cnt = 1;
        List<DoubtDao> doubtDaoList = find(subpackageCode, doubtUserType);
        if (CollectionUtils.isEmpty(doubtDaoList)) {
            return doubtUserName + "第" + cnt + "次质疑文件";
        }
        int total = 0;
        if (Objects.isNull(doubtId)) {
            //没有id表示提问
            total = (int) doubtDaoList.stream()
                    .map(DoubtDao::getDoubtFileKey)
                    .filter(StringUtils::isNotEmpty)
                    .count() + cnt;
            return doubtUserName + "第" + total + "次质疑文件";
        }
        List<DoubtDao> collect = doubtDaoList.stream().filter(q -> q.getId().equals(doubtId)).collect(Collectors.toList());
        //计算回复质疑文件的次数，需要和提出质疑的次数保持一致
        for (int i = 0; i < doubtDaoList.size(); i++) {
            DoubtDao file = doubtDaoList.get(i);
            if (doubtId.equals(file.getId())) {
                total = i + cnt;
            }
        }
        return "对" + collect.get(0).getDoubtUserName() + "第" + total + "次回复文件";
    }

    private String fileMethod(DoubtDto dto, String fileType, String fileName) {
        Long userId = Objects.isNull(dto.getDoubtUserId()) ? dto.getReplyUserId() : dto.getDoubtUserId();
        Boolean isTender = remoteToOtherServiceApi.isTender(userId);
        if (isTender) {
            userId = remoteToOtherServiceApi.queryTenderId();
        }
        File pdfFile = HtmlUtil.toPdf(dto.getFileHtml());
        //文件重命名
        pdfFile = FileUtil.rename(pdfFile, fileName + ".pdf", true);
        BuyItemDao buyItemDao = buyItemApi.findBySubpackageCode(dto.getSubpackageCode());
        String url = BiddingBaseUtil.generateFileAndReturnUrl(pdfFile, buyItemDao, fileName, fileType, dto.getSubpackageCode(), userId);
        //盖章
//        EsignVO seal = remoteToOtherServiceApi.getSeal(userId);
        Boolean isSupplier = remoteToOtherServiceApi.isSupp(userId);
        if (isTender || isSupplier) {
//            FUtil.orgSealByCoordinate(url, String.valueOf(userId), null);
            FUtil.startSeal(url,null,String.valueOf(userId),null, UserConstants.FIRM_COORDINATE_SEAL_PARAMETER);

        } else {
//            FUtil.psnSealByCoordinate(url, String.valueOf(userId), null);
            FUtil.startSeal(url,null,String.valueOf(userId),null, UserConstants.PERSON_COORDINATE_SEAL_PARAMETER,dto.getFlowId(),dto.getAuthCode());

        }
        return url;
    }

    @Override
    public List<DoubtVo> doubtList(QueryDoubtDto dto) {
        List<DoubtTimeVo> bySubpackageCode = doubtTimeApi.findBySubpackageCode(dto.getSubpackageCode());
        List<DoubtDao> doubtDaoList = find(dto.getSubpackageCode(), dto.getDoubtUserType());
        List<DoubtVo> voList = doubtDaoList.stream().map(DoubtConvert.INSTANCE::convert).collect(Collectors.toList());
        voList.forEach(v -> v.setDoubtType(bySubpackageCode.get(0).getDoubtType()));
        return voList;
    }
}
