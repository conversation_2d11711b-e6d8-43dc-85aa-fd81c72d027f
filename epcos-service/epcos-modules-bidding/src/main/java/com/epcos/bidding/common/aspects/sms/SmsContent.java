package com.epcos.bidding.common.aspects.sms;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * 短信内容
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmsContent {

    private String buyItemName;

    private String subpackageName;

    /**
     * 短信内容时间 没有特指
     * 短信内容中包含的时间均可使用此字段
     */
    private Date date_1;

    /**
     * 短信内容时间 没有特指
     * 短信内容中包含的时间均可使用此字段
     */
    private Date date_2;

    /**
     * 手机号
     */
    private Set<String> mobiles = new HashSet<>();

    /**
     * 短信内容
     */
    private String content;

}
