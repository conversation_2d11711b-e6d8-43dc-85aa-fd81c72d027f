package com.epcos.bidding.purchase.goods.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/12 18:28
 */
@Data
public class UpdateCartDto implements Serializable {

    private static final long serialVersionUID = 979396328810186095L;

    @ApiModelProperty(value = "购物车清单")
    @NotEmpty
    private List<AddCartDetail> addCartDetailList;
}
