package com.epcos.bidding.common.aspects.filter.validator;

import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import com.epcos.bidding.purchase.api.params.vo.bulletin.BulletinAndItemTimeVo;
import com.epcos.common.core.exception.ServiceException;

import java.util.Optional;

/**
 * 比对 ResponseFileEndTime 是否已过
 */
public class ResponseFileEndTimeHasPassedValidator implements ResultPostHandlerFilterChain<BulletinAndItemTimeVo> {

    @Override
    public void postHandler(AspectContext context, BulletinAndItemTimeVo result) {
        Optional.ofNullable(result)
                .orElseThrow(() -> new ServiceException("采购项目时间为空"))
                .verifyResponseFileEndTimeIsNull()
                .verifyResponseFileEndTimeHasPassed();
    }

}
