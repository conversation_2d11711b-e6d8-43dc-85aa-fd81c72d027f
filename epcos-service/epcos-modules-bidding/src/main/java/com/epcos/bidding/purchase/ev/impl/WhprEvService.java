package com.epcos.bidding.purchase.ev.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.utils.BiddingBaseUtil;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.purchase.project.whpr.business.api.buyitem.WHPRBuyItemApi;
import com.epcos.bidding.purchase.project.whpr.domain.dao.BuyItemWHPRDao;
import com.epcos.bidding.workbench.vo.SpecialFieldVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.epcos.common.core.enums.ClientEnum.PR;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/21 15:48
 */
@Slf4j
@Service("pr")
public class WhprEvService extends AbEvService {

    private final WHPRBuyItemApi whprBuyItemApi;

    public WhprEvService(IBuyItemApi buyItemApi, ISubPackageApi subPackageApi, WHPRBuyItemApi whprBuyItemApi) {
        super(buyItemApi, subPackageApi);
        this.whprBuyItemApi = whprBuyItemApi;
    }

    @Override
    public String ev() {
        return PR.getCode();
    }

    @Override
    public boolean isDing() {
        return false;
    }


    @Override
    public String getTableName() {
        return "purchase_whpr_buy_item";
    }

    @Override
    public Map<String, String> queryBuyItemMap(Map<String, String> voMap, BuyItemDao buyItemInfo) {
        voMap = BiddingBaseUtil.convert(whprBuyItemApi.findOneByBuyItemCode(buyItemInfo.getBuyItemCode()));
        return voMap;
    }

    @Override
    public List<SpecialFieldVo> queryDiffInfo(List<String> buyItemCodes, Map<String, List<SubpackageDao>> subMap, List<SpecialFieldVo> voList) {
        List<BuyItemWHPRDao> whprDaoList = whprBuyItemApi.findOneByBuyItemCodeList(buyItemCodes);
        whprDaoList.forEach(dao -> subMap.get(dao.getBuyItemCode())
                .forEach(sub -> voList.add(new SpecialFieldVo(dao.getBuyItemCode(), sub.getSubpackageCode(), null, dao.getBuyClass(), null))));
        return voList;
    }

    @Override
    public Boolean delBuyItem(String buyItemCode, List<String> subpackageCodeList) {
        return whprBuyItemApi.delWHPRBuyItemInfo(buyItemCode);
    }

    @Override
    public List<String> queryAllBuyItemCode() {
        List<BuyItemWHPRDao> bjxkItemList = whprBuyItemApi.list(Wrappers.lambdaQuery(BuyItemWHPRDao.class)
                .select(BuyItemWHPRDao::getBuyItemCode));
        if (CollectionUtils.isEmpty(bjxkItemList)) {
            return Collections.emptyList();
        }
        List<String> codeList = bjxkItemList.stream().map(BuyItemWHPRDao::getBuyItemCode).collect(Collectors.toList());
        return codeList;
    }
}
