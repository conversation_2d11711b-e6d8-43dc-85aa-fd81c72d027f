package com.epcos.bidding.common.aspects;

import com.epcos.bidding.audit.api.AuditAttribute;
import com.epcos.bidding.audit.api.BizAndAudit;
import com.epcos.bidding.audit.api.BizAuditRelationDto;
import com.epcos.bidding.common.aspects.vo.GetItemVo;
import com.epcos.bidding.common.aspects.vo.GetSimpleItemVo;
import com.epcos.bidding.purchase.api.params.PurchaseBidOpeningVo;
import com.epcos.bidding.purchase.api.params.PurchaseQuoteFormVo;
import com.epcos.bidding.purchase.api.params.vo.bulletin.BulletinAndItemTimeVo;
import com.epcos.bidding.purchase.api.params.vo.buyitem.ItemUserInfo;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileDao;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierBidderDao;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierBiddingDao;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierDemoVideoDao;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierSignUpDao;
import com.epcos.common.core.context.SecurityContextHolder;
import com.epcos.common.core.web.domain.user.SysUser;
import com.epcos.system.api.model.EsignVO;
import com.epcos.system.api.model.SupplierCompanyVO;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * <AUTHOR>
 */
@Slf4j
public class GetUtil {

    public static final String GET_USER = "get_user";
    public static final String GET_ITEM = "get_item";
    public static final String GET_SIMPLE_ITEM = "get_simple_item";
    public static final String GET_BIDDER = "get_bidder";
    public static final String GET_SUPPLIER = "get_supplier";
    public static final String GET_ESIGN = "get_esign";
    public static final String GET_TIME = "get_time";
    public static final String GET_ITEM_USER = "get_item_user";
    public static final String GET_PURCHASER_QUOTE_FORM = "get_purchaser_quote_form";
    public static final String GET_CLAIMS_FILE = "get_claims_file";
    public static final String GET_SUPPLIER_BIDDING = "get_supplier_bidding";
    public static final String GET_SIGN_UP = "get_sign_up";
    public static final String GET_SUPPLIER_DEMO_VIDEO = "get_supplier_demo_video";
    public static final String GET_BID_OPENING = "get_bid_opening";
    public static final String GET_AUDIT_ATTRIBUTE = "get_audit_attribute";
    public static final String GET_BIZ_AND_AUDIT = "get_biz_and_audit";
    public static final String GET_AUDIT_RELATION = "get_audit_relation";
    public static final String HAS_FUNCTION = "has_function";

    public static SysUser getUser() {
        return get(GET_USER, SysUser.class);
    }

    public static GetItemVo getItemVo() {
        return get(GET_ITEM, GetItemVo.class);
    }

    public static GetSimpleItemVo getSimpleItemVo() {
        return get(GET_SIMPLE_ITEM, GetSimpleItemVo.class);
    }

    public static SupplierBidderDao getBidder() {
        return get(GET_BIDDER, SupplierBidderDao.class);
    }

    public static SupplierCompanyVO getSupplier() {
        return get(GET_SUPPLIER, SupplierCompanyVO.class);
    }

    public static List<SupplierCompanyVO> getSuppliers() {
        return get(GET_SUPPLIER, List.class);
    }

    public static EsignVO getEsign() {
        return get(GET_ESIGN, EsignVO.class);
    }

    public static BulletinAndItemTimeVo getTime() {
        return get(GET_TIME, BulletinAndItemTimeVo.class);
    }

    public static ItemUserInfo getItemUser() {
        return get(GET_ITEM_USER, ItemUserInfo.class);
    }

    public static PurchaseQuoteFormVo getPurchaserQuoteForm() {
        return get(GET_PURCHASER_QUOTE_FORM, PurchaseQuoteFormVo.class);
    }

    public static ClaimsFileDao getClaimsFile() {
        return get(GET_CLAIMS_FILE, ClaimsFileDao.class);
    }

    public static SupplierBiddingDao getSupplierBidding() {
        return get(GET_SUPPLIER_BIDDING, SupplierBiddingDao.class);
    }

    public static SupplierSignUpDao getSignUp() {
        return get(GET_SIGN_UP, SupplierSignUpDao.class);
    }

    public static SupplierDemoVideoDao getSupplierDemoVideo() {
        return get(GET_SUPPLIER_DEMO_VIDEO, SupplierDemoVideoDao.class);
    }

    public static PurchaseBidOpeningVo getBidOpening() {
        return get(GET_BID_OPENING, PurchaseBidOpeningVo.class);
    }

    public static List<AuditAttribute> getAuditAttribute() {
        return get(GET_AUDIT_ATTRIBUTE, List.class);
    }

    public static BizAndAudit getBizAndAudit() {
        return get(GET_BIZ_AND_AUDIT, BizAndAudit.class);
    }

    public static BizAuditRelationDto getAuditRelation() {
        return get(GET_AUDIT_RELATION, BizAuditRelationDto.class);
    }

    public static Boolean getHasFunction() {
        return get(HAS_FUNCTION, Boolean.class);
    }

    private static final int TIMEOUT = 5;
    private static final TimeUnit TIME_UNIT = TimeUnit.SECONDS;

    static <T> T get(String key, Class<T> tClass) {
        Object obj = SecurityContextHolder.getRaw(key);
        if (obj == null) {
            return null;
        }
        if (Future.class.isAssignableFrom(obj.getClass())) {
            Future<T> future = get(key);
            try {
                return future.get(TIMEOUT, TIME_UNIT);
            } catch (InterruptedException e) {
                log.error("获取对象，中断异常 key={},tClass={},future={}", key, tClass, future, e);
            } catch (ExecutionException e) {
                log.error("获取对象，执行异常 key={},tClass={},future={}", key, tClass, future, e);
            } catch (TimeoutException e) {
                log.error("获取对象，超时异常 timeout={},timeUnit={},key={},tClass={},future={}", TIMEOUT, TIME_UNIT, key, tClass, future, e);
            }
        }
        return SecurityContextHolder.get(key, tClass);
    }

    static <T> Future<T> get(String key) {
        return (Future) SecurityContextHolder.getRaw(key);
    }

    static Object getRaw(String key) {
        return SecurityContextHolder.getRaw(key);
    }

    static <T> void set(String key, T t) {
        SecurityContextHolder.set(key, t);
    }

    static void remove(String key) {
        SecurityContextHolder.remove(key);
    }


}
