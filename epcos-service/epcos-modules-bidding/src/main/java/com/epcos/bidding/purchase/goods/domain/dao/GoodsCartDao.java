package com.epcos.bidding.purchase.goods.domain.dao;

import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.bidding.common.BaseDao;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/12 17:19
 */
@ApiModel(description = "购物车")
@Data
@TableName("goods_cart")
public class GoodsCartDao extends BaseDao implements Serializable {

    private static final long serialVersionUID = -7914219821331635812L;

    @ApiModelProperty(value = "购买者id")
    private Long buyerId;

    @ApiModelProperty(value = "商品id")
    private Long goodsId;

    @ApiModelProperty(value = "购买数量")
    private Integer buyCount;

    @ApiModelProperty(value = "卖家id")
    private Long sellerId;
}
