package com.epcos.bidding.common.aspects.filter.validator;

import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierSignUpDao;

/**
 * 校验未签到
 */
public class SignUpNotSignInValidator implements ResultPostHandlerFilterChain<SupplierSignUpDao> {
    @Override
    public void postHandler(AspectContext context, SupplierSignUpDao result) {
        result.verifyNotSignIn();
    }
}
