package com.epcos.bidding.purchase.process.domain.vo;

import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/25 8:48
 */
@Data
public class SubpackageFileVo extends SuperPackageVo {

    private static final long serialVersionUID = 4976326455279122133L;

    @ApiModelProperty(value = "项目文件信息")
    private List<FileInfoVo> fileInfoVoList;
}
