package com.epcos.bidding.purchase.claims.business.api;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.IBaseService;
import com.epcos.bidding.common.utils.EvaluationMethod;
import com.epcos.bidding.purchase.api.params.EvaluationMethodVo;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileEvaluationMethodDao;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public interface IClaimsFileEvaluationMethodApi extends IBaseService<ClaimsFileEvaluationMethodDao> {

    @Override
    default LambdaQueryWrapper<ClaimsFileEvaluationMethodDao> queryWrapper(ClaimsFileEvaluationMethodDao dao) {
        LambdaQueryWrapper<ClaimsFileEvaluationMethodDao> query = Wrappers.lambdaQuery(ClaimsFileEvaluationMethodDao.class);
        if (Objects.nonNull(dao.getId())) {
            return query.eq(ClaimsFileEvaluationMethodDao::getId, dao.getId());
        }
        return query.eq(StringUtils.hasText(dao.getSubpackageCode()), ClaimsFileEvaluationMethodDao::getSubpackageCode, dao.getSubpackageCode())
                .eq(Objects.nonNull(dao.getReviewType()), ClaimsFileEvaluationMethodDao::getReviewType, dao.getReviewType())
                .eq(Objects.nonNull(dao.getSubjective()), ClaimsFileEvaluationMethodDao::getSubjective, dao.getSubjective());
    }

    default LambdaQueryWrapper<ClaimsFileEvaluationMethodDao> queryWrapper(String subpackageCode) {
        return queryWrapper(new ClaimsFileEvaluationMethodDao(subpackageCode));
    }

    default List<ClaimsFileEvaluationMethodDao> findBySubpackageCode(String subpackageCode) {
        return list(new ClaimsFileEvaluationMethodDao(subpackageCode));
    }

    void delAndCreate(EvaluationMethod evaluationMethod, String subpackageCode);

    void delete(String subpackageCode);

    EvaluationMethodVo findBySubpackageCodeVo(String subpackageCode);

    void importOther(String importSubpackageCode, String subpackageCode);

}
