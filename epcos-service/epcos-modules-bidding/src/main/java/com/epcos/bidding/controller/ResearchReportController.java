package com.epcos.bidding.controller;

import com.epcos.bidding.purchase.api.domian.reprot.ReportInfoVo;
import com.epcos.bidding.purchase.report.business.api.IResearchReportApi;
import com.epcos.bidding.purchase.report.domain.dao.ResearchReportDao;
import com.epcos.bidding.purchase.report.domain.dto.InitReportDto;
import com.epcos.common.core.constant.PurchaseConstants;
import com.epcos.common.core.constant.UserConstants;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.system.api.model.FUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/29 13:55
 */
@Api(tags = "调研报告")
@Slf4j
@RestController
@RequestMapping("/purchase.report")
@RequiredArgsConstructor
public class ResearchReportController {

    private final IResearchReportApi researchReportApi;

    @ApiOperation("初始化调研报告")
    @PostMapping(value = "/initReport")
    public R<Boolean> initReport(@RequestBody InitReportDto dto) {
        researchReportApi.initReport(dto);
        return R.ok();
    }


    @ApiOperation("查询调研报告信息")
    @GetMapping(value = "/reportInfo")
    public R<List<ReportInfoVo>> reportInfo(@RequestParam(value = "buyItemCode") String buyItemCode) {
        return R.ok(researchReportApi.reportInfoByItem(buyItemCode));
    }


    @ApiOperation("修改调研报告信息")
    @PostMapping(value = "/editReport")
    public R<ReportInfoVo> editReport(@RequestBody InitReportDto dto) {
        researchReportApi.editReport(dto);
        return R.ok();
    }


    @ApiOperation("采购人签字")
    @GetMapping(value = "/purchaseSign")
    public R<Boolean> purchaseSign(@RequestParam(value = "subpackageCode") String subpackageCode,
                                   @RequestParam(value = "flowId", required = false) String flowId,
                                   @RequestParam(value = "authCode", required = false) String authCode) {
        ResearchReportDao reportDao = researchReportApi.selectReport(subpackageCode);
//        EsignVO seal = remoteToOtherServiceApi.getSeal(SecurityUtils.getUserId());
//        FUtil.psnSealByKeyword(reportDao.getResearchReportKey(), PurchaseConstants.Bulletin.KEYWORDS, String.valueOf(SecurityUtils.getUserId()), null);
        FUtil.startSeal(reportDao.getResearchReportKey(), PurchaseConstants.Bulletin.KEYWORDS, String.valueOf(SecurityUtils.getUserId()), null, UserConstants.PERSON_KEY_SEAL_PARAMETER, flowId, authCode);
        researchReportApi.updateSignStatus(subpackageCode, SecurityUtils.getUserId(), "1");
        return R.ok();
    }


    @ApiOperation("删除调研报告")
    @GetMapping(value = "/clear")
    public R<Boolean> clear(@RequestParam(value = "subpackageCode") String subpackageCode) {
        researchReportApi.remove(subpackageCode);
        return R.ok();
    }
}
