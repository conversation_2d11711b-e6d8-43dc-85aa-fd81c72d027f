package com.epcos.bidding.purchase.project.base.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.epcos.bidding.purchase.api.params.dto.BaseQueryDto;
import com.epcos.bidding.purchase.extract.domain.vo.ExtractBuyItemVo;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.workbench.vo.SupplierRegistrationReviewVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 公用基础信息
 * @date 2024/4/23 12:28
 */
public interface IBuyItemBaseMapper extends BaseMapper<BuyItemDao> {

    IPage<ExtractBuyItemVo> selectExtract(Page<Object> of, @Param(value = "entity") BaseQueryDto entity,
                                          @Param(value = "tableName") String tableName);

    IPage<SupplierRegistrationReviewVo> selectBuyItemWithSupplierSignUp(Page<Object> of,
                                                                        @Param(value = "dto") BaseQueryDto dto,
                                                                        @Param("reviewStatus") List<Integer> reviewStatus);


    int countSupplierRegistrationReviews(@Param("userId") Long userId, @Param("reviewStatus") List<Integer> reviewStatus);

}
