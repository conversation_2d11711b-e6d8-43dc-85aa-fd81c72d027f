package com.epcos.bidding.common.aspects.filter.validator;

import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierSignUpDao;
import com.epcos.common.core.exception.ServiceException;

import java.util.Optional;

/**
 * 报名信息已存在
 */
public class SignUpExistsValidator implements ResultPostHandlerFilterChain<SupplierSignUpDao> {
    @Override
    public void postHandler(AspectContext context, SupplierSignUpDao result) {
        Optional.ofNullable(result)
                .ifPresent(supplierSignUpDao -> {
                    throw new ServiceException("您已报名");
                });
    }
}
