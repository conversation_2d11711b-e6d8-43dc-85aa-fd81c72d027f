package com.epcos.bidding.common.aspects.convert;

import com.epcos.bidding.purchase.opening.domain.dto.PurchaseBidOpeningDto;
import com.epcos.bidding.supplier.api.params.SupplierSignUpVo;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * 获取开标时需要通知的供应商id
 */
public class GetBidOpeningSupplierIdParamConvert implements GetParamConvert<PurchaseBidOpeningDto, Set<Long>> {


    @Override
    public Set<Long> doConvert(PurchaseBidOpeningDto dto) {
        return dto.getSignUpList()
                .stream()
                .map(SupplierSignUpVo::getSupplierId)
                .collect(Collectors.toSet());
    }


}
