package com.epcos.bidding.common.aspects.vo;

import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/24 9:23
 */
@Data
public class GetSimpleItemVo implements Serializable {

    private static final long serialVersionUID = -183311952232034454L;

    @ApiModelProperty(value = "采购项目code")
    private String buyItemCode;

    @ApiModelProperty(value = "采购项目名字")
    private String buyItemName;

    @ApiModelProperty(value = "招标项目创建时间的年月")
    private String yearMonthSplit;

    @ApiModelProperty(value = "包编号以及名称")
    private List<SuperPackageVo> superPackageVoList;
}
