package com.epcos.bidding.purchase.monitor.domain.dao;

import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.bidding.common.BaseDao;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/5 13:40
 */
@ApiModel(description = "监标人信息表")
@Data
@TableName("purchase_monitor_bid")
public class MonitorBidDao extends BaseDao<MonitorBidDao> implements Serializable {

    private static final long serialVersionUID = 2006360061965581455L;

    @ApiModelProperty(value = "采购项目编号(业务使用代码)")
    private String buyItemCode;

    @ApiModelProperty(value = "标段编号（业务使用代码）")
    private String subpackageCode;

    @ApiModelProperty(value = "标段名称")
    private String subpackageName;

    @ApiModelProperty(value = "监标人")
    private String monitorBidPerson;

    @ApiModelProperty(value = "监标人id")
    private Long monitorBidPersonId;

    @ApiModelProperty(value = "监标事项默认为两种值 ：1 监督开标  2监督评标")
    private String monitorBidType;

    @ApiModelProperty(value = "是否签字（0未签字  1已签字)")
    private Integer signStatus;

    @ApiModelProperty(value = "文件key")
    private String fileKey;
}
