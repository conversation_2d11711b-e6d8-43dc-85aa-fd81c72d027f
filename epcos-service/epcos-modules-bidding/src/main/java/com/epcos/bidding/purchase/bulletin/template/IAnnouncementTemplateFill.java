package com.epcos.bidding.purchase.bulletin.template;

import cn.hutool.core.collection.CollUtil;
import com.epcos.bidding.purchase.bulletin.domain.dto.TemplateDto;
import com.epcos.common.core.utils.StringUtils;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 公告模板填充
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/24 9:37
 */
public interface IAnnouncementTemplateFill {

    String filling(String template, TemplateDto dto);

    Pattern PATTERN = Pattern.compile("\\$\\{([^}]+)}");

    default String filling(String template, Map<String, String> map) {
        if (CollUtil.isNotEmpty(map)) {
            Matcher matcher = PATTERN.matcher(template);
            StringBuffer stringBuffer = new StringBuffer();
            while (matcher.find()) {
                String key = matcher.group(1);
                String value = map.get(key);
                if (StringUtils.isEmpty(value)) {
                    value = "";
                }
                matcher.appendReplacement(stringBuffer, Matcher.quoteReplacement(value));
            }
            matcher.appendTail(stringBuffer);
            return stringBuffer.toString();
        }
        return template;
    }
}
