package com.epcos.bidding.purchase.claims.domain.vo;

import com.epcos.common.core.annotation.desensitization.BoolToIntSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "包下采购文件")
public class ClaimsFilePageVo implements Serializable {
    private static final long serialVersionUID = -2648454837789750381L;

    @ApiModelProperty("包code")
    private String subpackageCode;

    @ApiModelProperty("包名")
    private String subpackageName;

    @ApiModelProperty("文件压缩包key")
    private String epcFile;

    @ApiModelProperty("文件pdf key")
    private String pdfFile;

    @ApiModelProperty("采购文件发布状态,0已上传 1已确认")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer releaseStatus;

    @ApiModelProperty("报价表头相等，1-相等，0-不相等")
    @JsonSerialize(using = BoolToIntSerializer.class)
    private Boolean areQuoteHeadEquals;

}
