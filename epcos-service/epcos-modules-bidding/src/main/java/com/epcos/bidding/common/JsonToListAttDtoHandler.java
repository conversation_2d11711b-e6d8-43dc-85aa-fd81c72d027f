package com.epcos.bidding.common;

import com.alibaba.fastjson.JSON;
import com.epcos.bidding.audit.api.dto.AttDto;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

public class JsonToListAttDtoHandler extends BaseTypeHandler<List<AttDto>> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<AttDto> parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, JSON.toJSONString(parameter));
    }

    @Override
    public List<AttDto> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return Optional.ofNullable(rs.getString(columnName))
                .map(i -> JSON.parseArray(i, AttDto.class))
                .orElse(Collections.emptyList());
    }

    @Override
    public List<AttDto> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return JSON.parseArray(rs.getString(columnIndex), AttDto.class);
    }

    @Override
    public List<AttDto> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return JSON.parseArray(cs.getString(columnIndex), AttDto.class);
    }
}
