package com.epcos.bidding.common.aspects.convert;

import com.epcos.bidding.purchase.api.params.dto.bargin.StartOrEndBargainDto;

import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/7 13:47
 */
public class BargainIdParamConvert implements GetParamConvert<StartOrEndBargainDto, Set<Long>> {
    @Override
    public Set<Long> doConvert(StartOrEndBargainDto dto) {
        return dto.getSupplierIds();
    }
}
