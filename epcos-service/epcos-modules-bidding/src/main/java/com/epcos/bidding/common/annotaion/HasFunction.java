package com.epcos.bidding.common.annotaion;

import com.epcos.bidding.common.enums.FunctionEnum;
import org.springframework.core.annotation.Order;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Order(150)
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface HasFunction {

    GetCommon common() default @GetCommon;

    /**
     * 功能点
     */
    FunctionEnum functionEnum();

    /**
     * 所属角色[1-采购人，2-供应商，3-专家]
     */
    String belongRole() default "";

}
