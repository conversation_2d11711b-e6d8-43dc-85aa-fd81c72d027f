package com.epcos.bidding.common.aspects.project;

import cn.hutool.core.text.CharSequenceUtil;
import com.epcos.bidding.common.annotaion.GetBidOpening;
import com.epcos.bidding.common.aspects.AbstractMethod;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.purchase.api.params.PurchaseBidOpeningVo;
import com.epcos.bidding.purchase.opening.business.api.IPurchaseBidOpeningApi;
import com.epcos.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 查询开标信息
 */
@Slf4j
@Aspect
@Component
public class BidOpeningAspectFilterChainPost extends AbstractMethod<GetBidOpening, PurchaseBidOpeningVo> {

    protected BidOpeningAspectFilterChainPost() {
        super(GetUtil.GET_BID_OPENING, "查询开标信息");
    }

    @Autowired
    private IPurchaseBidOpeningApi purchaseBidOpeningApi;

    @Override
    @Around(value = "@annotation(annotation)")
    public Object around(ProceedingJoinPoint point, GetBidOpening annotation) {
        return super.around(point, annotation);
    }

    @Override
    public PurchaseBidOpeningVo businessMethods(JoinPoint point, GetBidOpening annotation) {
        String codeEl = annotation.common().subpackageCodeEL();
        if (CharSequenceUtil.isEmpty(codeEl)) {
            log.error("参数值异常 @GetBidOpening，subpackageCodeEL={}", codeEl);
            throw new ServiceException("参数值异常 @GetBidOpening " + codeEl);
        }
        String subpackageCode = threadLocal.get().getSubpackageCode();
        if (CharSequenceUtil.isEmpty(subpackageCode)) {
            throw new ServiceException("查询开标信息,包code必填");
        }
        return purchaseBidOpeningApi.query(subpackageCode);
    }

}
