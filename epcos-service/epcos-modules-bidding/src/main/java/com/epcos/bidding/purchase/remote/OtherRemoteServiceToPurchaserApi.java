package com.epcos.bidding.purchase.remote;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.epcos.bidding.audit.domain.dao.AuditInfoDao;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.common.annotaion.GetCommon;
import com.epcos.bidding.common.annotaion.GetItem;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.common.aspects.vo.GetItemVo;
import com.epcos.bidding.purchase.api.domian.cliams.SupplierCommentVo;
import com.epcos.bidding.purchase.api.domian.reprot.ReportInfoVo;
import com.epcos.bidding.purchase.api.params.DoubtVo;
import com.epcos.bidding.purchase.api.params.JudgesVo;
import com.epcos.bidding.purchase.api.params.SubpackageVo;
import com.epcos.bidding.purchase.api.params.dto.*;
import com.epcos.bidding.purchase.api.params.dto.bargin.StartOrEndBargainDto;
import com.epcos.bidding.purchase.api.params.dto.monitor.CreateMonitorDto;
import com.epcos.bidding.purchase.api.params.vo.answer.AnswerVo;
import com.epcos.bidding.purchase.api.params.vo.buyitem.BuyItemVo;
import com.epcos.bidding.purchase.bargain.business.api.IPurchaseBargainApi;
import com.epcos.bidding.purchase.bargain.domain.vo.PurchaseBargainVo;
import com.epcos.bidding.purchase.bulletin.business.api.IBulletinApi;
import com.epcos.bidding.purchase.bulletin.domain.dao.BulletinDao;
import com.epcos.bidding.purchase.claims.business.api.IClaimsFileApi;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileDao;
import com.epcos.bidding.purchase.comment.business.api.ISupplierCommentApi;
import com.epcos.bidding.purchase.comment.domain.dao.SupplierCommentDao;
import com.epcos.bidding.purchase.ev.factory.EvFactory;
import com.epcos.bidding.purchase.extract.business.api.IExtractJudgeInnerApi;
import com.epcos.bidding.purchase.extract.domain.dao.ExtractJudgeInnerDao;
import com.epcos.bidding.purchase.monitor.business.api.IMonitorBidApi;
import com.epcos.bidding.purchase.process.business.api.IAskAnswerApi;
import com.epcos.bidding.purchase.process.business.api.IDoubtApi;
import com.epcos.bidding.purchase.process.business.api.IReviewBeforeApi;
import com.epcos.bidding.purchase.process.domain.dao.ReviewBeforeDao;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.purchase.project.mapping.DefaultVoAndDtoConvert;
import com.epcos.bidding.purchase.project.mapping.SubpackageConvert;
import com.epcos.bidding.purchase.report.business.api.IResearchReportApi;
import com.epcos.bidding.purchase.win.business.api.IWinBidResultApi;
import com.epcos.bidding.supplier.api.params.SupplierSignUpVo;
import com.epcos.bidding.supplier.sign.business.api.ISupplierSignApi;
import com.epcos.bidding.supplier.sign.domain.dto.SupplierSignQueryDto;
import com.epcos.common.core.constant.PurchaseConstants.BulletinAudit;
import com.epcos.common.core.constant.PurchaseConstants.Currency;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.domain.review.ReviewSummaryVo;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.bean.BeanUtils;
import com.epcos.common.core.web.page.TableDataVo;
import com.epcos.common.security.annotation.InnerAuth;
import com.epcos.system.api.domain.assmble.vo.FunctionKV;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.epcos.bidding.common.enums.BulletinTypeEnum.CHANGE_ANNOUNCEMENT;
import static com.epcos.bidding.common.enums.FunctionEnum.PURCHASER_BID_OPEN;

/**
 * <AUTHOR>
 * @version 1.0
 * @desc 其他微服务调用本服务接口
 * @date 2023/8/28 9:13
 */
@RequestMapping("/other/to")
@RestController
@Slf4j
@ApiIgnore
@Api(tags = "此类为其他服务调用采购人接口的api，不对页面使用")
@RequiredArgsConstructor
public class OtherRemoteServiceToPurchaserApi {

    private final IBuyItemApi buyItemApi;
    private final IBulletinApi bulletinApi;
    private final IClaimsFileApi claimsFileApi;
    private final IExtractJudgeInnerApi extractJudgeApi;
    private final ISubPackageApi subpackageApi;
    private final IAskAnswerApi askAnswerApi;
    private final IWinBidResultApi winBidResultApi;
    private final IDoubtApi doubtApi;
    private final ISupplierSignApi supplierSignApi;
    private final IReviewBeforeApi reviewBeforeApi;
    private final IPurchaseBargainApi purchaseBargainApi;
    private final ISupplierCommentApi supplierCommentApi;
    private final IResearchReportApi researchReportApi;
    private final IMonitorBidApi monitorBidApi;
    private final DefaultVoAndDtoConvert defaultVoAndDtoConvert;

    /**
     * 可根据采购项目编号或者包编号查询采购方式与功能
     *
     * @param buyItemCode    采购项目编号
     * @param subpackageCode 包编号
     * @param belongRole     所属角色[1-采购人，2-供应商，3-专家]，如需要供应商角色则传入"2"
     * @return
     */
    @InnerAuth
    @GetItem(querySubpackage = true, belongRole = "#belongRole", common = @GetCommon(buyItemCodeEL = "#buyItemCode", subpackageCodeEL = "#subpackageCode"))
    @GetMapping(value = "queryBuyItemInfo")
    public R<BuyItemVo> queryBuyItemInfo(@RequestParam(value = "buyItemCode", required = false) String buyItemCode,
                                         @RequestParam(value = "subpackageCode", required = false) String subpackageCode,
                                         @RequestParam(value = "belongRole", required = false) String belongRole) {
        if (StringUtils.isBlank(buyItemCode) && StringUtils.isBlank(subpackageCode)) {
            return R.fail("采购项目编号和包编号不能同时为空");
        }
        GetItemVo itemVo = GetUtil.getItemVo();
        return R.ok(itemVo.getBuyItemVo());
    }

    /**
     * 查询采购项目、采购项目对应的包、公告等部分信息
     * 采购功能对应的为专属评委的功能，即将功能所属角色为 3
     * 的功能过滤出来并传送
     *
     * @param subpackageCode 包编号
     * @return
     */
    @InnerAuth
    @GetMapping(value = "buyItemInfo")
    public R<SubpackageVo> buyItemInfo(@RequestParam(value = "subpackageCode") @NotBlank String subpackageCode) {
        //项目信息
        SubpackageDao subpackageDao = subpackageApi.findBySubpackageCode(subpackageCode);
        BuyItemDao buyItemDao = buyItemApi.findBySubpackageCode(subpackageCode);
        //查询公告
        List<BulletinDao> bulletinDaoList = bulletinApi.findBySubpackageCodeAndBulletinTypeInAndAuditStatusIn
                (null,
                        subpackageCode,
                        Collections.singletonList(BulletinAudit.PASS),
                        Collections.singletonList(CHANGE_ANNOUNCEMENT.getKey())
                );
        String changeNoticeKey = bulletinDaoList.stream()
                .map(BulletinDao::getBulletinContentKey)
                .collect(Collectors.joining(","));
        //采购文件
        ClaimsFileDao claimsFileDao = claimsFileApi.findBySubpackageCode(subpackageCode);
        List<FunctionKV> voList = buyItemDao.parsePurchaseFunctionJsonByRole("3");
        SubpackageVo vo = SubpackageConvert.INSTANCE.convert_1(subpackageDao, changeNoticeKey,
                JSONArray.toJSONString(voList), Objects.isNull(claimsFileDao) ? null : claimsFileDao.getPdfFile());
        BeanUtils.copyProperties(buyItemDao, vo);
        return R.ok(vo);
    }

    @InnerAuth
    @GetMapping(value = "getJudge")
    public R<List<JudgesVo>> getJudge(@RequestParam(value = "subpackageCode") @NotBlank String subpackageCode) {
        return R.ok(extractJudgeApi.queryBySub(subpackageCode));
    }

    @InnerAuth
    @GetMapping(value = "getSupplier")
    public R<SubSupplier> getSupplier(@RequestParam(value = "subpackageCode") @NotBlank String subpackageCode) {
        SubSupplier vo = new SubSupplier();
        BuyItemDao buyItemDao = buyItemApi.findBySubpackageCode(subpackageCode);
        List<String> functionTypeList = JSONArray.parseArray(buyItemDao.getPurchaseFunctionJson(), FunctionKV.class)
                .stream().map(FunctionKV::getPurchaseFunctionKey).collect(Collectors.toList());

        SupplierSignQueryDto queryDto = defaultVoAndDtoConvert.dtoAndVoConvert(subpackageCode, buyItemDao);
        List<SupplierSignUpVo> supplierSignUpVoList = supplierSignApi.query(queryDto, null);
        if (CollectionUtils.isEmpty(supplierSignUpVoList)) {
            throw new ServiceException("暂无可进入评审的供应商");
        }
        if (functionTypeList.contains(PURCHASER_BID_OPEN.getKey())) {
            //只要有开标功能就从开标之后获取供应商
            List<ReviewBeforeDao> beforeDaoList = reviewBeforeApi.find(subpackageCode, null);
            if (CollectionUtils.isEmpty(beforeDaoList)) {
                throw new ServiceException("没有可进入评审的供应商");
            }
            List<Long> supplierIdList = beforeDaoList.stream().map(ReviewBeforeDao::getSupplierId).collect(Collectors.toList());
            List<SupplierSignUpVo> signUpVoList = supplierSignUpVoList.stream().filter(s -> supplierIdList.contains(s.getSupplierId())).collect(Collectors.toList());
            vo = defaultVoAndDtoConvert.dtoAndVoConvert(vo, signUpVoList);
        } else {
            //只要没有开标，就直接从报名信息处获取供应商
            vo = defaultVoAndDtoConvert.dtoAndVoConvert(vo, supplierSignUpVoList);
        }
        return R.ok(vo);
    }

    /**
     * 若是专家之后的列表请传该评委的用户id
     *
     * @param judgeId 评委id
     * @return
     */
    @InnerAuth
    @GetMapping(value = "getJudgeList")
    public R<TableDataVo> getJudgeList(@RequestParam(value = "judgeId") @NotNull Long judgeId) {
        PageSortEntity<ExtractJudgeInnerDao> pageSort = new PageSortEntity<>();
        pageSort.setEntity(new ExtractJudgeInnerDao() {{
            setJudgeId(judgeId);
        }});
        pageSort.getSorts().putAll(ExtractJudgeInnerDao.sort());
        pageSort.setPageNum(1);
        pageSort.setPageSize(10);
        Page<ExtractJudgeInnerDao> page = extractJudgeApi.page(pageSort);
        List<ExtractJudgeInnerDao> voList = page.getRecords();
        return R.ok(new TableDataVo(voList, page.getTotal()));
    }

    /**
     * 答疑
     * 提问
     *
     * @param dto
     * @return
     */
    @InnerAuth
    @PostMapping(value = "/submitAsk")
    public R<Boolean> submitAsk(@RequestBody @Validated AskDto dto) {
        return R.ok(askAnswerApi.submitAsk(dto));
    }

    /**
     * 答疑
     * 回复
     *
     * @param dto
     * @return
     */
    @InnerAuth
    @PostMapping(value = "/replyAsk")
    public R<Boolean> replyAsk(@RequestBody @Validated ReplyDto dto) {
        return R.ok(askAnswerApi.replyAsk(dto));
    }

    @InnerAuth
    @GetMapping(value = "/answerList")
    public R<List<AnswerVo>> judgeAnswerList(@NotBlank String subpackageCode, @NotNull Long supplierId) {
        if (Objects.isNull(supplierId)) {
            log.error("答疑列表supplierId为空, 返回空列表");
            return R.ok(Collections.EMPTY_LIST);
        }
//        return R.ok(askAnswerApi.judgeAnswerList(subpackageCode, supplierId));
        return R.ok();
    }

    @InnerAuth
    @PostMapping(value = "/askDoubt")
    public R<Boolean> askDoubt(@RequestBody DoubtDto dto) {
        doubtApi.askReplyDoubt(dto);
        return R.ok();
    }

    @InnerAuth
    @PostMapping(value = "/doubtList")
    public R<List<DoubtVo>> doubtList(@RequestBody QueryDoubtDto dto) {
        return R.ok(doubtApi.doubtList(dto));
    }

    /**
     * 查询是否确认评审
     * 若已经确认评审则返回 true
     * 没有确认评审则返回 false
     *
     * @param subpackageCode 包code
     * @return
     */
    @InnerAuth
    @GetMapping(value = "/queryWhetherConfirmReview")
    public R<Boolean> queryWhetherConfirmReview(@NotBlank String subpackageCode) {
        boolean b = false;
        List<ReviewBeforeDao> voList = reviewBeforeApi.find(subpackageCode, null);
        if (!CollectionUtils.isEmpty(voList)) {
            if (String.valueOf(Currency.ONE).equals(voList.get(0).getConfirmReview())) {
                b = Boolean.TRUE;
            }
        }
        return R.ok(b);
    }

    /**
     * 评委 发起/结束议价
     * <p>
     * 详细请点击 实体类中查看
     * initiateId 评委调用时 必须要传
     *
     * @param dto
     * @return
     */
    @InnerAuth
    @ApiOperation(value = "评委 发起/结束议价")
    @PostMapping(value = "/startOrEndBargain")
    public R<Boolean> startOrEndBargain(@RequestBody @Validated StartOrEndBargainDto dto) {
        return R.ok(purchaseBargainApi.startOrEndBargain(dto));
    }

    /**
     * 评委 议价列表
     *
     * @param buyItemCode
     * @return
     */
    @InnerAuth
    @ApiOperation(value = "评委 议价列表")
    @GetMapping(value = "/list")
    public R<List<PurchaseBargainVo>> bargainList(@NotBlank String buyItemCode) {
        return R.ok(purchaseBargainApi.bargainList(buyItemCode));
    }

    @InnerAuth
    @ApiOperation(value = "查询供应商评论信息")
    @GetMapping(value = "/getSupplierCommentInfo")
    public R<List<SupplierCommentVo>> getSupplierCommentInfo(@RequestParam(value = "supplierId") Long supplierId) {
        List<SupplierCommentDao> commentDaoList = supplierCommentApi.list(
                Wrappers.lambdaQuery(SupplierCommentDao.class)
                        .eq(SupplierCommentDao::getSupplierId, supplierId)
        );
        List<SupplierCommentVo> voList = commentDaoList.stream()
                .map(commentDao -> {
                    SupplierCommentVo commentVo = new SupplierCommentVo();
                    BeanUtils.copyProperties(commentDao, commentVo);
                    return commentVo;
                }).collect(Collectors.toList());
        return R.ok(voList);
    }


    @InnerAuth
    @ApiOperation(value = "修改签字状态")
    @GetMapping(value = "/editSignStatus")
    public R<Boolean> editSignStatus(@RequestParam(value = "subpackageCode") String subpackageCode,
                                     @RequestParam(value = "judgeId") Long judgeId) {
        return R.ok(researchReportApi.updateSignStatus(subpackageCode, judgeId, "1"));
    }


    @InnerAuth
    @ApiOperation(value = "查询调研信息")
    @GetMapping(value = "/judgeView")
    public R<ReportInfoVo> judgeView(@RequestParam(value = "subpackageCode") String subpackageCode) {
        return R.ok(researchReportApi.reportInfoBySub(subpackageCode));
    }

    @InnerAuth
    @ApiOperation(value = "查询院内项目编号")
    @GetMapping(value = "/queryInnerCode")
    public R<String> queryInnerCode(@RequestParam(value = "subpackageCode") String subpackageCode) {
        BuyItemDao buyItemDao = buyItemApi.findBySubpackageCode(subpackageCode);
        return R.ok(EvFactory.getInstance().queryBuyItemMap(buyItemDao.getBuyItemCode()).get("innerCode"));
    }


    @InnerAuth
    @ApiOperation(value = "添加监标信息")
    @GetMapping(value = "/addMonitorInfo")
    public R<Boolean> addMonitorInfo(@RequestBody CreateMonitorDto dto) {
        return R.ok(monitorBidApi.addMonitorInfo(dto));
    }

    /**
     * 計算價格分數
     *
     * @param reviewInfo
     * @return
     */
    @InnerAuth
    @PostMapping(value = "/calculatePriceScore")
    public R<ReviewSummaryVo> calculatePriceScore(@RequestBody ReviewSummaryVo reviewInfo) {
        return R.ok(winBidResultApi.calculatePriceScore(reviewInfo.getSubpackageCode(), reviewInfo));
    }

    /**
     * 统计供应商中标次数
     *
     * @param supplierIds 供应商id集合
     * @return
     */
    @InnerAuth
    @PostMapping(value = "/calculateWinCnt")
    public R<Map<Long, Long>> calculateWinCnt(@RequestBody List<Long> supplierIds) {
        return R.ok(winBidResultApi.calculateWinCnt(supplierIds));
    }

    /**
     * 计算邀请了该供应商但未报名的次数
     *
     * @param supplierIds 供应商id集合
     * @return
     */
    @InnerAuth
    @PostMapping(value = "/calculateInviteNum")
    public R<Map<Long, Long>> calculateInviteNum(@RequestBody List<Long> supplierIds) {
        return R.ok(winBidResultApi.calculateInviteNum(supplierIds));
    }

    @InnerAuth
    @PostMapping(value = "/rollback")
    public R<Boolean> rollback(@RequestBody AuditInfoDao dto) {
        return R.ok(bulletinApi.rollback(dto));
    }
}
