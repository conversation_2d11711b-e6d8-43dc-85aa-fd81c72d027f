package com.epcos.bidding.common.aspects.filter.validator;

import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import com.epcos.bidding.supplier.sign.business.api.ISupplierSignUpApi;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierSignUpDao;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * PURCHASER_START_VALID_NUMBER_OF_CHECK_IN
 * 控制开标开始是否校验投标人签到满足3家
 */
@Slf4j
@Component
public class BidOpeningStartHasFunctionValidator implements ResultPostHandlerFilterChain<Boolean> {

    @Autowired
    private ISupplierSignUpApi supplierSignUpApi;

    @Override
    public void postHandler(AspectContext context, Boolean result) {
        if (Boolean.TRUE.equals(result)) {
            SupplierSignUpDao queryDao = new SupplierSignUpDao();
            if (!StringUtils.hasText(context.getSubpackageCode())) {
                throw new ServiceException("subpackageCode 参数必填");
            }
            queryDao.setSubpackageCode(context.getSubpackageCode());
            queryDao.setBidOpeningSign(true);
            long count = supplierSignUpApi.count(supplierSignUpApi.queryWrapper(queryDao));
            if (count < 3) {
                log.error("投标人签到人数不足3家: context={}, queryDao={}, count={}", context, queryDao, count);
                throw new ServiceException("投标人签到人数不足3家");
            }
        }
    }
}
