package com.epcos.bidding.common.annotaion;

import org.springframework.core.annotation.Order;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/9 14:28
 */
@Order(321)
@Retention(value = RetentionPolicy.RUNTIME)
@Target(value = ElementType.FIELD)
public @interface FieldMapping {


    /**
     * 字段名
     *
     * @return
     */
    String name();

    /**
     * 英文名 如果不传
     * 则默认获取属性名
     *
     * @return
     */
    String value() default "";

    /**
     * 是否忽略映射该字段
     * 默认为 false 即 默认不忽略映射
     *
     * @return
     */
    boolean isIgnore() default true;

    /**
     * 默认值为 void.class，表示不映射子类
     *
     * @return
     */
    Class<?> isSub() default void.class;

    /**
     * 是否填充
     * 默认为 false 即 默认不需要填充
     * 如果设置为true,则会自动填充表头
     *
     * @return
     */
    boolean isFill() default false;
}
