package com.epcos.bidding.purchase.win.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/9 17:04
 */
@Data
public class WinBuyItemVo implements Serializable {

    private static final long serialVersionUID = -5695295539284636555L;

    @ApiModelProperty(value = "采购项目编号")
    private String buyItemCode;

    @ApiModelProperty(value = "标段信息")
    private List<WinSubPackageVo> winSubPackageVoList;
}
