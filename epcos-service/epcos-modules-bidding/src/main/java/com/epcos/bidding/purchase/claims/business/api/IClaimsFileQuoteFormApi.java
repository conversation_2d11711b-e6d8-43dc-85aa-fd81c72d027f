package com.epcos.bidding.purchase.claims.business.api;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.IBaseService;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.api.params.PurchaseQuoteFormVo;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileQuoteFormDao;
import com.epcos.bidding.purchase.claims.domain.dto.ClaimsFileQuoteFormQueryDto;
import com.epcos.bidding.purchase.api.params.dto.QuoteFormCreatorDto;
import com.epcos.common.core.web.page.TableDataVo;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 采购报价单
 *
 * <AUTHOR>
 */
public interface IClaimsFileQuoteFormApi extends IBaseService<ClaimsFileQuoteFormDao> {

    @Override
    default LambdaQueryWrapper<ClaimsFileQuoteFormDao> queryWrapper(ClaimsFileQuoteFormDao dao) {
        LambdaQueryWrapper<ClaimsFileQuoteFormDao> query = Wrappers.lambdaQuery(ClaimsFileQuoteFormDao.class);
        if (Objects.nonNull(dao.getId())) {
            return query.eq(ClaimsFileQuoteFormDao::getId, dao.getId());
        }
        return query.eq(StringUtils.hasText(dao.getSubpackageCode()),
                ClaimsFileQuoteFormDao::getSubpackageCode, dao.getSubpackageCode());
    }

    default LambdaQueryWrapper<ClaimsFileQuoteFormDao> queryWrapper(String subpackageCode) {
        return queryWrapper(new ClaimsFileQuoteFormDao(subpackageCode));
    }

    default LambdaQueryWrapper<ClaimsFileQuoteFormDao> queryWrapper(Collection<String> subpackageCodes) {
        return Wrappers.lambdaQuery(ClaimsFileQuoteFormDao.class)
                .in(CollUtil.isNotEmpty(subpackageCodes),
                        ClaimsFileQuoteFormDao::getSubpackageCode, subpackageCodes);
    }

    default void del(String subpackageCode) {
        remove(queryWrapper(subpackageCode));
    }


    // 保存或更新
    void createOrUpdate(QuoteFormCreatorDto creator);

    // 查询报价表
    PurchaseQuoteFormVo query(Long id, String subpackageCode);

    PurchaseQuoteFormVo query(String subpackageCode);

    // 包编码 ： 报价项
    Map<String, List<PurchaseQuoteFormVo>> list(Set<String> subpackageCodes);

    // 分页查询
    TableDataVo<PurchaseQuoteFormVo> queryPage(PageSortEntity<ClaimsFileQuoteFormQueryDto> pageSort);

}
