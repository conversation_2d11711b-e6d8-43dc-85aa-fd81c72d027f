package com.epcos.bidding.common.annotaion;


import com.epcos.bidding.common.aspects.convert.GetParamConvert;
import com.epcos.bidding.common.aspects.convert.GetItemParamConvert;
import org.springframework.core.annotation.Order;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * 获取采购项目相关信息
 * return GetItemVo
 *
 * <AUTHOR>
 */
@Order(200)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface GetSimpleItem {

    GetCommon common() default @GetCommon;

    /**
     * 非基本类型的转换器
     */
    Class<? extends GetParamConvert> convert() default GetItemParamConvert.class;

}
