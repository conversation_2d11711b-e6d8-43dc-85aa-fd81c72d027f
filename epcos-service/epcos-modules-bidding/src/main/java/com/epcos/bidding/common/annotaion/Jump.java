package com.epcos.bidding.common.annotaion;

import com.epcos.bidding.common.aspects.convert.GetParamConvert;
import org.springframework.core.annotation.Order;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 需要跳转流程标记
 *
 * <AUTHOR>
 */
@Order(value = 600)
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Jump {


    /**
     * 包code
     */
    String subpackageCodeEL();


    /**
     * 流程所属角色[1-采购人，2-供应商，3-专家]
     */
    String processRole();

    /**
     * 接口地址-触发点
     * 当非http请求调用时，可以使用triggerPoint来指定触发点
     */
    String triggerPoint() default "";

    /**
     * 供应商id
     */
    String supplierIdEl() default "";

    /**
     * 转换供应商ids
     */
    Class<? extends GetParamConvert> toSupplierIdsConvert() default GetParamConvert.class;


}
