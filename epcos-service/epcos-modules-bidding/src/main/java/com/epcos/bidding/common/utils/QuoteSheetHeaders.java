package com.epcos.bidding.common.utils;

import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 对应客户端报价表属性描述
 */
@Data
public final class QuoteSheetHeaders {

    @NotBlank(message = "字段中文名不能为空")
    @Length(max = 255, message = "字段中文名最长255")
    private String label;

    @NotBlank(message = "字段英文名不能为空")
    @Length(max = 255, message = "字段英文名最长255")
    private String key;

    @NotBlank(message = "字段类型不能为空")
    @Length(max = 255, message = "字段类型最长255")
    private String keyType;

    @NotNull(message = "是否显示不能为null")
    private Boolean displayed;

    @NotNull(message = "是否必填不能null")
    private Boolean required;

    @NotBlank(message = "正则不能为空")
    @Length(max = 255, message = "正则最长255")
    private String verifyRule;

    @Length(max = 255, message = "备注最长255")
    private String verifyMsg;

    @Range(message = "排序【0-10000】", min = 0, max = 10000)
    @NotNull(message = "排序不能为null")
    private Integer sort;
}
