/**
 * Copyright 2022 bejson.com
 */
package com.epcos.bidding.common.utils;

import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * 客户端文件目录
 */
@Data
public final class MenuData {

    /**
     * 顺序
     */
    @NotNull(message = "【顺序】必填")
    @Range(min = 0, message = "【顺序】最小为0")
    private Integer order;
    /**
     * 章节名
     */
    @Length(max = 100, message = "【章节名】最长长度不得超过100个字符")
    @NotBlank(message = "【章节名】必填")
    private String chapterName;
    /**
     * 小章节
     */
    @Valid
    @NotEmpty(message = "【小章节】必填")
    private List<SectionList> sectionList;


}
