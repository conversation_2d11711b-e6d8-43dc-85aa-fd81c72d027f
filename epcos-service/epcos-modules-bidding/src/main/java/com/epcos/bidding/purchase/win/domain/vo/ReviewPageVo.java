package com.epcos.bidding.purchase.win.domain.vo;

import com.epcos.bidding.purchase.extract.domain.vo.ExtractLogVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/10 11:43
 */
@Data
public class ReviewPageVo {

    private static final long serialVersionUID = -1793561685715912700L;

    public ReviewPageVo() {
        this.whetherShowJudge = "0";
        this.confirmReview = "0";
    }

    @Deprecated
    @ApiModelProperty(value = "是否在专家列表上显示[0-不显示，1-显示]")
    private String whetherShowJudge;

    @ApiModelProperty(value = "是否确认会签[0-未确认，1-已确认]")
    private String confirmCounterSign;

    @ApiModelProperty(value = "是否确认评审[0-未确认，1-已确认]")
    private String confirmReview;

    @ApiModelProperty(value = "议价人[0-采购人，1-评委，2-没有议价]")
    private String isBargaining;

    @ApiModelProperty(value = "分数")
    private Double baseScore;

    @ApiModelProperty(value = "专家信息")
    private List<ExtractLogVo> judgeVoList;
}
