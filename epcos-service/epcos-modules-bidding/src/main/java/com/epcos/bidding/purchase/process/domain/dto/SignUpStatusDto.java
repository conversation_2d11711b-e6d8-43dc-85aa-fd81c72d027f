package com.epcos.bidding.purchase.process.domain.dto;

import com.epcos.bidding.supplier.api.params.SupplierSignUpStatusDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/9 11:05
 */
@Data
public class SignUpStatusDto extends SupplierSignUpStatusDto {

    private static final long serialVersionUID = -6929313598646017384L;

    @ApiModelProperty(value = "包编号")
    @NotBlank
    private String subpackageCode;
}
