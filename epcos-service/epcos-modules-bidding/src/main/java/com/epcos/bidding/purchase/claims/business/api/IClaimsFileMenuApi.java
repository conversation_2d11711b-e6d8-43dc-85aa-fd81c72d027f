package com.epcos.bidding.purchase.claims.business.api;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.IBaseService;
import com.epcos.bidding.common.utils.BidFileJson;
import com.epcos.bidding.purchase.api.params.MenuDataVo;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileMenuDao;
import org.springframework.util.StringUtils;

import java.nio.file.Path;
import java.util.List;
import java.util.Objects;

public interface IClaimsFileMenuApi extends IBaseService<ClaimsFileMenuDao> {

    @Override
    default LambdaQueryWrapper<ClaimsFileMenuDao> queryWrapper(ClaimsFileMenuDao dao) {
        LambdaQueryWrapper<ClaimsFileMenuDao> query = Wrappers.lambdaQuery(ClaimsFileMenuDao.class);
        if (Objects.nonNull(dao.getId())) {
            return query.eq(ClaimsFileMenuDao::getId, dao.getId());
        }
        return query.eq(StringUtils.hasText(dao.getSubpackageCode()), ClaimsFileMenuDao::getSubpackageCode, dao.getSubpackageCode())
                .eq(StringUtils.hasText(dao.getChapterName()), ClaimsFileMenuDao::getChapterName, dao.getChapterName())
                .eq(Objects.nonNull(dao.getChapterType()), ClaimsFileMenuDao::getChapterType, dao.getChapterType());
    }

    default LambdaQueryWrapper<ClaimsFileMenuDao> queryWrapper(String subpackageCode) {
        return queryWrapper(new ClaimsFileMenuDao(subpackageCode));
    }

    default List<ClaimsFileMenuDao> findBySubpackageCode(String subpackageCode) {
        return list(new ClaimsFileMenuDao(subpackageCode));
    }

    List<MenuDataVo> findBySubpackageCodeVo(String subpackageCode);

    void delete(String subpackageCode);

    // 删除之前附件并且保存新的
    void delAttachFileAndCreate(Path dirPath, BidFileJson bidFileJson,
                                String buyItemCode,String subpackageCode,String yearMonthSplit);

    void importOther(String importSubpackageCode, String subpackageCode);
}
