package com.epcos.bidding.purchase.ev.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.utils.BiddingBaseUtil;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.purchase.project.fjnx.business.api.buyitem.FJNXBuyItemApi;
import com.epcos.bidding.purchase.project.fjnx.domain.dao.BuyItemFJNXDao;
import com.epcos.bidding.workbench.vo.SpecialFieldVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.epcos.common.core.enums.ClientEnum.FJ;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/21 15:49
 */
@Slf4j
@Service("fj")
public class FjnxEvService extends AbEvService {

    private final FJNXBuyItemApi fjnxBuyItemApi;

    public FjnxEvService(IBuyItemApi buyItemApi, ISubPackageApi subPackageApi, FJNXBuyItemApi fjnxBuyItemApi) {
        super(buyItemApi, subPackageApi);
        this.fjnxBuyItemApi = fjnxBuyItemApi;
    }

    @Override
    public String ev() {
        return FJ.getCode();
    }

    @Override
    public boolean isDing() {
        return false;
    }


    @Override
    public String getTableName() {
        return "purchase_fjnx_buy_item";
    }

    @Override
    public Map<String, String> queryBuyItemMap(Map<String, String> voMap, BuyItemDao buyItemInfo) {
        voMap = BiddingBaseUtil.convert(fjnxBuyItemApi.findOneByBuyItemCode(buyItemInfo.getBuyItemCode()));
        return voMap;
    }

    @Override
    public List<SpecialFieldVo> queryDiffInfo(List<String> buyItemCodes, Map<String, List<SubpackageDao>> subMap, List<SpecialFieldVo> voList) {
        List<BuyItemFJNXDao> fjnxDaoList = fjnxBuyItemApi.findOneByBuyItemCodeList(buyItemCodes);
        fjnxDaoList.forEach(dao -> subMap.get(dao.getBuyItemCode())
                .forEach(sub -> voList.add(new SpecialFieldVo(dao.getBuyItemCode(), sub.getSubpackageCode(), null, dao.getBuyClass(), null))));
        return voList;
    }

    @Override
    public Boolean delBuyItem(String buyItemCode, List<String> subpackageCodeList) {
        return fjnxBuyItemApi.delFJNXBuyItemInfo(buyItemCode);
    }

    @Override
    public List<String> queryAllBuyItemCode() {
        List<BuyItemFJNXDao> bjxkItemList = fjnxBuyItemApi.list(Wrappers.lambdaQuery(BuyItemFJNXDao.class)
                .select(BuyItemFJNXDao::getBuyItemCode));
        if (CollectionUtils.isEmpty(bjxkItemList)) {
            return Collections.emptyList();
        }
        List<String> codeList = bjxkItemList.stream().map(BuyItemFJNXDao::getBuyItemCode).collect(Collectors.toList());
        return codeList;
    }
}
