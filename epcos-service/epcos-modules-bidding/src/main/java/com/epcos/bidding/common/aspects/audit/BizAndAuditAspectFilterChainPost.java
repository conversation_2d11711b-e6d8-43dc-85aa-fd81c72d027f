package com.epcos.bidding.common.aspects.audit;

import com.alibaba.fastjson.JSON;
import com.epcos.bidding.audit.api.AuditAttribute;
import com.epcos.bidding.audit.api.BizAndAudit;
import com.epcos.bidding.audit.api.BizAuditRelation;
import com.epcos.bidding.audit.api.BizAuditRelationDto;
import com.epcos.bidding.audit.api.ProcessUserInfoDto;
import com.epcos.bidding.audit.api.dto.AuditCreatorDto;
import com.epcos.bidding.audit.api.dto.AuditInfoDto;
import com.epcos.bidding.audit.api.dto.AuditPersonDto;
import com.epcos.bidding.audit.api.dto.AuditRecipientDto;
import com.epcos.bidding.common.annotaion.GetBizAndAudit;
import com.epcos.bidding.common.aspects.AbstractMethod;
import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.EvalSpelUtil;
import com.epcos.common.core.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 发起审批时，业务与审批信息
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class BizAndAuditAspectFilterChainPost extends AbstractMethod<GetBizAndAudit, BizAndAudit> {
    public BizAndAuditAspectFilterChainPost() {
        super(GetUtil.GET_BIZ_AND_AUDIT, "业务与审批信息");
    }

    @Override
    @Around(value = "@annotation(annotation)")
    public Object around(ProceedingJoinPoint joinPoint, GetBizAndAudit annotation) {
        return super.around(joinPoint, annotation);
    }

    @Override
    public BizAndAudit businessMethods(JoinPoint point, GetBizAndAudit annotation) {
        AspectContext context = threadLocal.get();
        Method method = context.getMethod();
        Object arg = context.getDto();
        String buyItemCode = context.getBuyItemCode();
        Long userId = StringUtils.hasText(annotation.userIdEL())
                ? EvalSpelUtil.get(method, point.getArgs(), annotation.userIdEL(), Long.class)
                : SecurityUtils.getUserId();
        if (!StringUtils.hasText(buyItemCode) || Objects.isNull(userId) || !(arg instanceof BizAuditRelation)) {
            log.error("@GetBizAndAudit 参数错误，buyItemCode={}, userId={}, arg={}", buyItemCode, userId, arg);
            throw new ServiceException("@GetBizAndAudit 参数错误");
        }
        BizAuditRelation audit = (BizAuditRelation) arg;
        if (Objects.isNull(audit.getAuditProcessDto())) {
            return new BizAndAudit();
        }
        // 从 @GetAuditAttribute 获取审批参数
        List<AuditAttribute> auditAttributeList = GetUtil.getAuditAttribute();
        if (CollectionUtils.isEmpty(auditAttributeList)) {
            // 从当前注解参数中获取忽略的字段，并获取审批参数
            auditAttributeList = AuditAttributeAspectFilterChainPost.getAuditAttributeList(
                    AuditAttributeAspectFilterChainPost.getIgnoreFields(annotation.ignoreField()), audit);
            if (CollectionUtils.isEmpty(auditAttributeList)) {
                throw new ServiceException("提交审批参数信息为空");
            }
        }
        String auditType = Optional.ofNullable(annotation.auditTypeEL())
                .filter(StringUtils::hasText)
                .map(el -> EvalSpelUtil.get(method, point.getArgs(), annotation.auditTypeEL(), String.class))
                .filter(StringUtils::hasText)
                .orElseGet(() -> annotation.auditType().getType());
        String subpackageCode = context.getSubpackageCode();
        BizAuditRelationDto bizAuditRelation = BizAuditRelationDto.builder()
                .auditType(auditType)
                .buyItemCode(buyItemCode)
                .subpackageCode(subpackageCode)
                .userId(userId)
                .data(JSON.toJSONString(arg))
                .className(arg.getClass().getName())
                .businessId(EvalSpelUtil.get(method, point.getArgs(), annotation.bizIdEL(), Long.class))
                .businessType(EvalSpelUtil.get(method, point.getArgs(), annotation.bizTypeEL(), String.class))
                .build();
        AuditCreatorDto auditCreator = getAuditCreator(buyItemCode, subpackageCode, auditType, audit, auditAttributeList);
        return new BizAndAudit(bizAuditRelation, auditCreator);
    }


    private AuditCreatorDto getAuditCreator(String buyItemCode, String subpackageCode,
                                            String auditType, BizAuditRelation audit,
                                            List<AuditAttribute> auditAttributeList) {
        AuditCreatorDto creatorDto = new AuditCreatorDto();
        AuditInfoDto auditInfoDto = new AuditInfoDto();
        auditInfoDto.setContentList(auditAttributeList);
        auditInfoDto.setBuyItemCode(buyItemCode);
        auditInfoDto.setSubpackageCode(subpackageCode);
        auditInfoDto.setAuditTitle(audit.getAuditProcessDto().getAuditTitle());
        auditInfoDto.setAuditType(auditType);
        auditInfoDto.setRemark(audit.getAuditProcessDto().getRemark());
        creatorDto.setAuditInfoDto(auditInfoDto);
        List<ProcessUserInfoDto> auditUserInfoList = audit.getAuditProcessDto().getAuditUserInfoList();
        if (!CollectionUtils.isEmpty(auditUserInfoList)) {
            List<AuditPersonDto> auditPersonDtoList = auditUserInfoList.stream()
                    .map(a -> {
                        AuditPersonDto auditPersonDto = new AuditPersonDto();
                        auditPersonDto.setUserId(a.getUserId());
                        auditPersonDto.setUserName(a.getNickName());
                        auditPersonDto.setRequirement(a.getRequirement());
                        return auditPersonDto;
                    }).collect(Collectors.toList());
            creatorDto.setAuditPersonList(auditPersonDtoList);
        }
        List<ProcessUserInfoDto> readOnlyUserInfoList = audit.getAuditProcessDto().getReadOnlyUserInfoList();
        if (!CollectionUtils.isEmpty(readOnlyUserInfoList)) {
            Set<AuditRecipientDto> auditRecipientDtoList = readOnlyUserInfoList.stream()
                    .map(a -> {
                        AuditRecipientDto recipientDto = new AuditRecipientDto();
                        recipientDto.setCopyId(a.getUserId());
                        recipientDto.setCopyName(a.getNickName());
                        return recipientDto;
                    }).collect(Collectors.toSet());
            creatorDto.setAuditRecipientList(auditRecipientDtoList);
        }
        return creatorDto;
    }


}
