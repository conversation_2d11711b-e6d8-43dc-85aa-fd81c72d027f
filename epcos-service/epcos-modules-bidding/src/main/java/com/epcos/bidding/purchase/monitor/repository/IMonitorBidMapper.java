package com.epcos.bidding.purchase.monitor.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.epcos.bidding.purchase.monitor.domain.dao.MonitorBidDao;
import com.epcos.bidding.purchase.monitor.domain.dto.RetrieveMonitorDto;
import com.epcos.bidding.purchase.monitor.domain.vo.MonitorVo;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/5 13:46
 */
public interface IMonitorBidMapper extends BaseMapper<MonitorBidDao> {


    IPage<MonitorVo> selectMonitorPage(Page of, @Param(value = "tableName") String tableName,
                                       @Param(value = "entity") RetrieveMonitorDto entity);
}
