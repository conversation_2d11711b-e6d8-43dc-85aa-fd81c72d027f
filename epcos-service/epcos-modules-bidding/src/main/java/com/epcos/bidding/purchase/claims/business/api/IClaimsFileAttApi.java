package com.epcos.bidding.purchase.claims.business.api;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.IBaseService;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileAttDao;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public interface IClaimsFileAttApi extends IBaseService<ClaimsFileAttDao> {
    @Override
    default LambdaQueryWrapper<ClaimsFileAttDao> queryWrapper(ClaimsFileAttDao dao) {
        LambdaQueryWrapper<ClaimsFileAttDao> query = Wrappers.lambdaQuery(ClaimsFileAttDao.class);
        if (Objects.nonNull(dao.getId())) {
            return query.eq(ClaimsFileAttDao::getId, dao.getId());
        }
        return query.eq(StringUtils.hasText(dao.getSubpackageCode()), ClaimsFileAttDao::getSubpackageCode, dao.getSubpackageCode())
                .eq(StringUtils.hasText(dao.getFileKey()), ClaimsFileAttDao::getFileKey, dao.getFileKey())
                .like(StringUtils.hasText(dao.getFileName()), ClaimsFileAttDao::getFileName, dao.getFileName());
    }
}
