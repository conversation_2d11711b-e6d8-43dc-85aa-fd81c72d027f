/**
 * Copyright 2022 bejson.com
 */
package com.epcos.bidding.common.utils;

import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 * 客户端文件章节内容
 */
@Data
public final class SectionList {

    /**
     * 顺序
     */
    @NotNull(message = "【顺序】必填")
    @Range(min = 0, message = "【顺序】最小为0")
    private Integer order;
    /**
     * 名字
     */
    @Length(max = 100, message = "【名字】最长长度不得超过100个字节")
    @NotBlank(message = "【名字】必填")
    private String sectionName;
    /**
     * 内容
     */
    @Length(max = 10 * 1024 * 1024, message = "【目录内容】最大10MB")
    private String sectionText;
    /**
     * 附件 mysql:text
     */
    @Length(max = 300, message = "【附件】最长长度不得超过300个字节")
    private String attachKey;
    /**
     * 章节类型: 1 编辑器文本，2 评审办法前附表(只能设置一个)，3 投标函附录/报价表(只能设置一个)，4 补充内容/附件
     */
    @Range(min = 1, message = "【章节类型】最小为1")
    @NotNull(message = "【章节类型】必填")
    private Integer chapterType;

    /**
     * 附件, key : 文件名
     */
    private Map<String, String> formatAttachKey;

    @Override
    public String toString() {
        return "SectionList{" +
                "attachKey='" + attachKey + '\'' +
                ", order=" + order +
                ", sectionName='" + sectionName + '\'' +
                ", chapterType=" + chapterType +
                ", formatAttachKey=" + formatAttachKey +
                '}';
    }
}
