package com.epcos.bidding.purchase.claims.domain.vo;

import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import com.epcos.bidding.purchase.api.params.vo.claims.TenderFileRequirementInfoVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TenderFileRequirementVo extends SuperPackageVo implements Serializable {
    private static final long serialVersionUID = -7792001231748920615L;

    @ApiModelProperty(value = "文件要求列表")
    List<TenderFileRequirementInfoVo> tenderFileRequirementInfoVoList;
}
