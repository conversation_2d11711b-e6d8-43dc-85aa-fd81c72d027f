package com.epcos.bidding.search.init;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.epcos.bidding.purchase.bulletin.business.api.IBulletinApi;
import com.epcos.bidding.purchase.bulletin.business.api.IBulletinTimeApi;
import com.epcos.bidding.purchase.bulletin.domain.dao.BulletinDao;
import com.epcos.bidding.purchase.bulletin.domain.dao.BulletinTimeDao;
import com.epcos.bidding.purchase.ev.factory.EvFactory;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.common.core.utils.DateUtils;
import com.epcos.common.search.LuceneHelper;
import com.epcos.common.search.annotation.LuceneDocumentHandler;
import com.epcos.common.search.properties.index.BulletinIndex;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.document.Document;
import org.apache.lucene.document.Field;
import org.apache.lucene.document.StringField;
import org.apache.lucene.document.TextField;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.epcos.common.search.properties.index.BulletinIndex.NAME_INDEX;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/9 14:18
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BulletinLucene implements LuceneDocumentHandler {

    private final LuceneHelper luceneHelper;
    private final IBuyItemApi buyItemApi;
    private final IBulletinApi bulletinApi;
    private final IBulletinTimeApi bulletinTimeApi;

    /**
     * demo
     */
    @Override
    @PostConstruct
    public void init() {

        log.error("==> 开始初始化 Lucene 索引...");

        List<String> buyItemCodes = EvFactory.getInstance().queryAllBuyItemCode();
        if (CollectionUtils.isEmpty(buyItemCodes)) {
            log.error("buyItemCodes is empty");
            return;
        }
        List<BulletinDao> bulletinList = bulletinApi.findByBuyItemCodes(buyItemCodes);
        if (CollectionUtils.isEmpty(bulletinList)) {
            log.error("bulletinList is empty");
            return;
        }
        List<Long> bulletinIdList = bulletinList.stream().map(BulletinDao::getId).collect(Collectors.toList());
        List<BulletinTimeDao> bulletinTimes = bulletinTimeApi.findByBulletinIdInOrderByIdDesc(bulletinIdList);
        List<Document> documents = Lists.newArrayList();

        bulletinList.forEach(bulletin -> {
            BuyItemDao buyItemInfo = buyItemApi.findBuyItemInfo(bulletin.getBuyItemCode());
            Document document = new Document();

            // ID字段使用StringField，因为不需要分词
            document.add(new StringField(BulletinIndex.id, String.valueOf(bulletin.getId()), Field.Store.YES));

            // 需要搜索的文本字段使用TextField，支持分词
            document.add(new TextField(BulletinIndex.bulletinName, bulletin.getBulletinName(), Field.Store.YES));
            document.add(new TextField(BulletinIndex.bulletinType, bulletin.getBulletinType(), Field.Store.YES));
            document.add(new TextField(BulletinIndex.bulletinTypeName, bulletin.getBulletinTypeName(), Field.Store.YES));
            document.add(new TextField(BulletinIndex.purchaseMethodType, buyItemInfo.getPurchaseMethodType(), Field.Store.YES));
            document.add(new TextField(BulletinIndex.purchaseMethodTypeName, buyItemInfo.getPurchaseMethodName(), Field.Store.YES));

            // 时间字段使用StringField，因为不需要分词
            document.add(new StringField(BulletinIndex.reviewTime, String.valueOf(bulletin.getReviewTime()), Field.Store.YES));
            document.add(new StringField(BulletinIndex.signUpTime, DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date()), Field.Store.YES));

            documents.add(document);
        });
        luceneHelper.createIndex(NAME_INDEX, documents);
        log.error("<== 初始化 Lucene 索引完成...");
    }

    @Override
    public void add(Method method, Object[] args, Object result) {
        log.error("你需要实现这个方法才能进行新增");
    }

    @Override
    public void update(Method method, Object[] args, Object result) {
        log.error("你需要实现这个方法才能进行修改");
    }

    @Override
    public void delete(Method method, Object[] args, Object result) {
        log.error("你需要实现这个方法才能进行删除");
    }
}
