package com.epcos.bidding.purchase.opening.domain.dao;

import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.bidding.common.SubpackageCodeEntity;
import com.epcos.common.core.constant.PurchaseConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@TableName("purchase_bid_opening")
public class PurchaseBidOpeningDao extends SubpackageCodeEntity {
    public PurchaseBidOpeningDao(String subpackageCode, Integer status) {
        this.subpackageCode = subpackageCode;
        this.status = status;
    }

    @ApiModelProperty("0-开标开始，1-唱标，2-开标完成")
    private Integer status;

    @ApiModelProperty("开标记录文件")
    private String recordFile;

    public PurchaseBidOpeningDao start() {
        this.setStatus(PurchaseConstants.BidOpenState.START);
        return this;
    }

    public PurchaseBidOpeningDao sing() {
        this.setStatus(PurchaseConstants.BidOpenState.SING);
        return this;
    }

    public PurchaseBidOpeningDao complete() {
        this.setStatus(PurchaseConstants.BidOpenState.FINISH);
        return this;
    }


}
