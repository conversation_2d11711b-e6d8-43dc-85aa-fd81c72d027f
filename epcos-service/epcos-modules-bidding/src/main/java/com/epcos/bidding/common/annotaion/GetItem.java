package com.epcos.bidding.common.annotaion;


import com.epcos.bidding.common.aspects.convert.GetItemParamConvert;
import com.epcos.bidding.common.aspects.convert.GetParamConvert;
import org.springframework.core.annotation.Order;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * 获取采购项目相关信息
 * return GetItemVo
 *
 * <AUTHOR>
 */
@Order(205)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface GetItem {

    GetCommon common() default @GetCommon;

    /**
     * 是否查询包信息
     */
    boolean querySubpackage() default false;

    /**
     * 所属角色[1-采购人，2-供应商，3-专家]
     */
    String belongRole() default "";

    /**
     * 非基本类型的转换器
     */
    Class<? extends GetParamConvert> convert() default GetItemParamConvert.class;

    /**
     * 非基本类型参数位置
     */
    int convertParamIndex() default 0;


}
