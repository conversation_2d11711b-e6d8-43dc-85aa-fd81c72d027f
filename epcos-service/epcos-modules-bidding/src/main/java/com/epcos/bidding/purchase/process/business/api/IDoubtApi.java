package com.epcos.bidding.purchase.process.business.api;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.IBaseService;
import com.epcos.bidding.purchase.api.params.DoubtVo;
import com.epcos.bidding.purchase.api.params.dto.DoubtDto;
import com.epcos.bidding.purchase.api.params.dto.QueryDoubtDto;
import com.epcos.bidding.purchase.process.domain.dao.DoubtDao;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/7 15:02
 */
public interface IDoubtApi extends IBaseService<DoubtDao> {

    @Override
    default LambdaQueryWrapper<DoubtDao> queryWrapper(DoubtDao dao) {
        LambdaQueryWrapper<DoubtDao> query = Wrappers.lambdaQuery(DoubtDao.class);
        if (Objects.nonNull(dao.getId())) {
            return query.eq(DoubtDao::getId, dao.getId());
        }
        return query
                .eq(StringUtils.hasText(dao.getSubpackageCode()), DoubtDao::getSubpackageCode, dao.getSubpackageCode())
                .eq(Objects.nonNull(dao.getDoubtTimeId()), DoubtDao::getDoubtTimeId, dao.getDoubtTimeId())
                .eq(StringUtils.hasText(dao.getDoubtUserType()), DoubtDao::getDoubtUserType, dao.getDoubtUserType())
                .eq(Objects.nonNull(dao.getDoubtUserId()), DoubtDao::getDoubtUserId, dao.getDoubtUserId())
                .like(StringUtils.hasText(dao.getDoubtUserName()), DoubtDao::getDoubtUserName, dao.getDoubtUserName())
                .eq(Objects.nonNull(dao.getReplyUserId()), DoubtDao::getReplyUserId, dao.getReplyUserId())
                .orderByDesc(DoubtDao::getId);
    }

    default LambdaQueryWrapper<DoubtDao> queryWrapper(String subpackageCode, String doubtUserType) {
        DoubtDao doubtDao = new DoubtDao();
        doubtDao.setSubpackageCode(subpackageCode);
        doubtDao.setDoubtUserType(doubtUserType);
        return queryWrapper(doubtDao);
    }

    default List<DoubtDao> find(String subpackageCode, String doubtUserType) {
        return list(queryWrapper(subpackageCode, doubtUserType));
    }

    default Boolean del(List<String> subpackageCodeList) {
        return remove(Wrappers.lambdaQuery(DoubtDao.class)
                .in(DoubtDao::getSubpackageCode, subpackageCodeList));
    }

    Boolean askReplyDoubt(DoubtDto dto);

    List<DoubtVo> doubtList(QueryDoubtDto dto);
}
