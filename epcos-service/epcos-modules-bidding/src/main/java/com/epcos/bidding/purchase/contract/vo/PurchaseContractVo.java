package com.epcos.bidding.purchase.contract.vo;


import com.epcos.bidding.supplier.contract.domain.vo.SupplierContractVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/4 15:27
 */
@Data
public class PurchaseContractVo extends SupplierContractVo {

    private static final long serialVersionUID = 6883694149424052481L;

    @ApiModelProperty(value = "结果通知书pdf_key")
    private String bidNotice;

    @ApiModelProperty(value = "院内编号")
    private String innerCode;
}
