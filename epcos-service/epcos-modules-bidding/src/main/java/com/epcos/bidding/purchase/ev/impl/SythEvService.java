package com.epcos.bidding.purchase.ev.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.utils.BiddingBaseUtil;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.purchase.project.syth.business.api.buyitem.SYTHBuyItemApi;
import com.epcos.bidding.purchase.project.syth.domain.dao.BuyItemSYTHDao;
import com.epcos.bidding.workbench.vo.SpecialFieldVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.epcos.common.core.enums.ClientEnum.TH;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/21 15:47
 */
@Slf4j
@Service("th")
public class SythEvService extends AbEvService {

    private final SYTHBuyItemApi sythBuyItemApi;

    public SythEvService(IBuyItemApi buyItemApi, ISubPackageApi subPackageApi, SYTHBuyItemApi sythBuyItemApi) {
        super(buyItemApi, subPackageApi);
        this.sythBuyItemApi = sythBuyItemApi;
    }

    @Override
    public String ev() {
        return TH.getCode();
    }

    @Override
    public boolean isDing() {
        return false;
    }


    @Override
    public String getTableName() {
        return "purchase_syth_buy_item";
    }

    @Override
    public Map<String, String> queryBuyItemMap(Map<String, String> voMap, BuyItemDao buyItemInfo) {
        voMap = BiddingBaseUtil.convert(sythBuyItemApi.findOneByBuyItemCode(buyItemInfo.getBuyItemCode()));
        return voMap;
    }

    @Override
    public List<SpecialFieldVo> queryDiffInfo(List<String> buyItemCodes, Map<String, List<SubpackageDao>> subMap, List<SpecialFieldVo> voList) {
        List<BuyItemSYTHDao> sythDaoList = sythBuyItemApi.findOneByBuyItemCodeList(buyItemCodes);
        sythDaoList.forEach(dao -> subMap.get(dao.getBuyItemCode())
                .forEach(sub -> voList.add(new SpecialFieldVo(dao.getBuyItemCode(), sub.getSubpackageCode(), null, dao.getBuyClass(), null))));
        return voList;
    }

    @Override
    public Boolean delBuyItem(String buyItemCode, List<String> subpackageCodeList) {
        return sythBuyItemApi.delSYTHBuyItemInfo(buyItemCode);
    }

    @Override
    public List<String> queryAllBuyItemCode() {
        List<BuyItemSYTHDao> bjxkItemList = sythBuyItemApi.list(Wrappers.lambdaQuery(BuyItemSYTHDao.class)
                .select(BuyItemSYTHDao::getBuyItemCode));
        if (CollectionUtils.isEmpty(bjxkItemList)) {
            return Collections.emptyList();
        }
        List<String> codeList = bjxkItemList.stream().map(BuyItemSYTHDao::getBuyItemCode).collect(Collectors.toList());
        return codeList;
    }
}
