package com.epcos.bidding.common.crossapi;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.api.params.AttributeVo;
import com.epcos.bidding.purchase.api.params.PurchaseQuoteFormVo;
import com.epcos.bidding.purchase.api.params.dto.AskDto;
import com.epcos.bidding.purchase.api.params.dto.ReplyDto;
import com.epcos.bidding.purchase.api.params.dto.SupplierInfoVo;
import com.epcos.bidding.purchase.api.params.vo.bulletin.BulletinAndItemTimeVo;
import com.epcos.bidding.purchase.api.params.vo.bulletin.TimeNodeVo;
import com.epcos.bidding.purchase.bargain.business.api.IPurchaseBargainApi;
import com.epcos.bidding.purchase.bargain.domain.dao.PurchaseBargainDao;
import com.epcos.bidding.purchase.bulletin.business.api.IBulletinApi;
import com.epcos.bidding.purchase.bulletin.domain.dao.BulletinDao;
import com.epcos.bidding.purchase.bulletin.domain.dto.BulletinSupplierDto;
import com.epcos.bidding.purchase.claims.business.api.IClaimsFileQuoteFormApi;
import com.epcos.bidding.purchase.process.business.api.IAskAnswerApi;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.remote.dto.SupplierBulletinPageDto;
import com.epcos.bidding.purchase.remote.vo.SupplierBulletinVo;
import com.epcos.common.core.constant.PurchaseConstants;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.epcos.bidding.common.enums.BulletinTypeEnum.BIDDING_ANNOUNCEMENT;
import static com.epcos.bidding.common.enums.BulletinTypeEnum.WIN_RESULT_PUBLICITY;
import static com.epcos.bidding.common.enums.PurchaseMethodEnum.INVITED_BIDDING;
import static com.epcos.bidding.common.enums.PurchaseMethodEnum.LG_YQZB;
import static com.epcos.common.core.constant.PurchaseConstants.BulletinAudit.PASS;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/28 11:00
 */
@Service
@RequiredArgsConstructor
public class ProvidedForSupplierImpl implements ProvidedForSupplierApi {

    private final IBuyItemApi buyItemApi;
    private final IAskAnswerApi askAnswerApi;
    private IPurchaseBargainApi purchaseBargainApi;
    private IBulletinApi bulletinApi;
    private final IClaimsFileQuoteFormApi claimsFileQuoteFormService;

    @Autowired
    public void setPurchaseBargainApi(IPurchaseBargainApi purchaseBargainApi) {
        this.purchaseBargainApi = purchaseBargainApi;
    }

    @Autowired
    public void setBulletinApi(IBulletinApi bulletinApi) {
        this.bulletinApi = bulletinApi;
    }

    /**
     * @param buyItemCodeList 采购项目code
     * @return Map<String, List < TimeNodeVo>>  key为包code
     */
    @Override
    public Map<String, List<TimeNodeVo>> getTimeNodeList(List<String> buyItemCodeList) {
        return bulletinApi.getTimeNodeList(buyItemCodeList);
    }


    @Override
    public Boolean submitAsk(AskDto dto) {
        return askAnswerApi.submitAsk(dto);
    }

    @Override
    public Boolean replyAsk(ReplyDto dto) {
        return askAnswerApi.replyAsk(dto);
    }

    @Override
    public BulletinAndItemTimeVo bulletinItemTimeInfo(String subpackageCode) {
        return bulletinApi.bulletinItemTimeInfo(subpackageCode);
    }

    @Override
    public Boolean whetherInvite(String subpackageCode, Long supplierId) {
        boolean invite = false;
        BuyItemDao buyItemDao = buyItemApi.findBySubpackageCode(subpackageCode);
        if (Objects.isNull(buyItemDao)) {
            return null;
        }
        if (!INVITED_BIDDING.getKey().equals(buyItemDao.getPurchaseMethodType())) {
            return null;
        }
        if (!LG_YQZB.getKey().equals(buyItemDao.getPurchaseMethodType())) {
            return null;
        }
        List<BulletinDao> bulletinDaoList = bulletinApi.findByBuyItemCodeAndBulletinTypeAndAuditStatusIn(buyItemDao.getBuyItemCode(),
                BIDDING_ANNOUNCEMENT.getKey(), Collections.singletonList(PASS));
        if (CollectionUtils.isEmpty(bulletinDaoList)) {
            invite = false;
        }
        List<BulletinSupplierDto> supplierDtoList = JSONArray.parseArray(bulletinDaoList.get(0).getInviteSupplier(), BulletinSupplierDto.class);
        if (CollectionUtils.isEmpty(supplierDtoList)) {
            invite = false;
        }
        List<BulletinSupplierDto> voList = supplierDtoList.stream().filter(s -> subpackageCode.equals(s.getSubpackageCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(voList)) {
            return false;
        }
        List<SupplierInfoVo> supplierInfoVoList = voList.get(0).getSupplierInfoVoList();
        List<Long> supplierIdList = supplierInfoVoList.stream().map(SupplierInfoVo::getSupplierId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(supplierIdList)) {
            invite = true;
        }
        if (supplierIdList.contains(supplierId)) {
            invite = true;
        }
        return invite;
    }

    @Override
    public Boolean whetherWrite(String subpackageCode) {
        BuyItemDao buyItemDao = buyItemApi.findBySubpackageCode(subpackageCode);
        List<BulletinDao> bulletinDaoList = bulletinApi.findByBuyItemCodeAndBulletinTypeAndAuditStatusIn(
                buyItemDao.getBuyItemCode(),
                BIDDING_ANNOUNCEMENT.getKey(),
                Collections.singletonList(PASS)
        );
        if (CollectionUtils.isEmpty(bulletinDaoList)) {
            return Boolean.FALSE;
        }
        BulletinDao bulletinDao = bulletinDaoList.get(0);
        return bulletinDao.getWhetherWrite() == PurchaseConstants.Currency.ONE ? Boolean.TRUE : Boolean.FALSE;
    }

    @Override
    public List<AttributeVo> querySupplierAddition(String subpackageCode) {
        BuyItemDao buyItemDao = buyItemApi.findBySubpackageCode(subpackageCode);
        List<BulletinDao> bulletinDaoList = bulletinApi.findByBuyItemCodeAndBulletinTypeAndAuditStatusIn(
                buyItemDao.getBuyItemCode(), BIDDING_ANNOUNCEMENT.getKey(), Collections.singletonList(PASS));
        if (CollUtil.isEmpty(bulletinDaoList)) {
            return null;
        }
        BulletinDao bulletinDao = bulletinDaoList.get(0);
        List<BulletinSupplierDto> voList = JSONArray.parseArray(bulletinDao.getSupplierAddition(), BulletinSupplierDto.class);
        if (CollectionUtils.isEmpty(voList)) {
            return null;
        }
        return voList.get(0).getAttributeVoList();
    }

    @Override
    public Boolean queryComplete(String subpackageCode) {
        List<BulletinDao> bulletinDaoList = bulletinApi.findBySubpackageCodeAndBulletinTypeInAndAuditStatusIn(null, subpackageCode, Collections.singletonList(WIN_RESULT_PUBLICITY.getKey()),
                Collections.singletonList(PASS));
        if (CollectionUtils.isEmpty(bulletinDaoList)) {
            return false;
        }
        return true;
    }

    @Override
    public List<BulletinDao> queryBulletin(String subpackageCode, List<String> bulletinTypeList, List<String> auditStatusList) {
        List<BulletinDao> voList;
        BuyItemDao buyItemDao = buyItemApi.findBySubpackageCode(subpackageCode);
        List<BulletinDao> bulletinDaoList = bulletinApi.findByBuyItemCodeAndBulletinType(
                buyItemDao.getBuyItemCode(),
                PASS);
        if (CollectionUtils.isEmpty(bulletinTypeList)) {
            voList = bulletinApi.findBySubpackageCodeAndBulletinTypeInAndAuditStatusIn(null, subpackageCode, null, auditStatusList);
        } else {
            voList = bulletinApi.findBySubpackageCodeAndBulletinTypeInAndAuditStatusIn(null, subpackageCode, bulletinTypeList, auditStatusList);
        }
        voList.addAll(bulletinDaoList);
        return voList;
    }

    @Override
    public List<PurchaseBargainDao> getBargain(String subpackageCode, Integer round, Long supplierId) {
        return purchaseBargainApi.find(subpackageCode, round, supplierId);
    }

    @Override
    public IPage<SupplierBulletinVo> supplierBulletinPage(PageSortEntity<SupplierBulletinPageDto> dto) {
        return bulletinApi.supplierBulletinPage(dto);
    }

    @Override
    public PurchaseQuoteFormVo queryShopInfo(String subpackageCode) {
        Map<String, List<PurchaseQuoteFormVo>> quoteMap = claimsFileQuoteFormService
                .list(Collections.singletonList(subpackageCode).stream().collect(Collectors.toSet()));
        List<PurchaseQuoteFormVo> quoteFormVoList = quoteMap.get(subpackageCode);
        return quoteFormVoList.get(0);
    }
}
