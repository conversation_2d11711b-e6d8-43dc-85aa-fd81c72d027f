package com.epcos.bidding.search.service;

import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.home.domain.dto.HomePageDto;
import com.epcos.bidding.purchase.home.domain.vo.PageHomeVo;
import com.epcos.common.core.web.page.TableDataVo;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/14 11:12
 */
public interface ISearchApi {


    TableDataVo<PageHomeVo> homePage(PageSortEntity<HomePageDto> dto);
}
