package com.epcos.bidding.purchase.project.base.business.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.common.aspects.params.GetItemParam;
import com.epcos.bidding.common.aspects.params.GetSimpleItemParam;
import com.epcos.bidding.common.aspects.vo.GetItemVo;
import com.epcos.bidding.common.aspects.vo.GetSimpleItemVo;
import com.epcos.bidding.purchase.api.params.dto.BaseQueryDto;
import com.epcos.bidding.purchase.api.params.vo.buyitem.BuyItemVo;
import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import com.epcos.bidding.purchase.extract.domain.vo.ExtractBuyItemVo;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.purchase.project.base.domain.dto.BuyItemBaseDto;
import com.epcos.bidding.purchase.project.base.repository.IBuyItemBaseMapper;
import com.epcos.bidding.workbench.vo.SupplierRegistrationReviewVo;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.DateUtils;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.utils.StringUtils;
import com.epcos.common.core.utils.bean.BeanUtils;
import com.epcos.common.core.web.domain.user.SysUser;
import com.epcos.system.api.domain.assmble.dto.PurchaseTimeNodeDto;
import com.epcos.system.api.domain.assmble.vo.BulletinKV;
import com.epcos.system.api.domain.assmble.vo.FunctionKV;
import com.epcos.system.api.domain.assmble.vo.MethodKV;
import com.epcos.system.api.domain.assmble.vo.PurchaseMethodInfoVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.epcos.common.core.constant.PurchaseConstants.Currency.BUY_ITEM;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 公用基础信息
 * @date 2024/4/23 14:08
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BuyItemService extends ServiceImpl<IBuyItemBaseMapper, BuyItemDao> implements IBuyItemApi {

    private final IBuyItemBaseMapper buyItemBaseMapper;
    private final ISubPackageApi subPackageApi;

    @Override
    public BuyItemDao insertBuyItemInfo(BuyItemBaseDto baseDto, PurchaseMethodInfoVo purchaseMethodInfoToJson) {
        BuyItemDao buyItemDao = new BuyItemDao();
        BeanUtils.copyProperties(baseDto, buyItemDao);
        fillMethodInfo(purchaseMethodInfoToJson, buyItemDao);
        String buyItemCode = BUY_ITEM + UUID.randomUUID().toString().replaceAll("-", "");
        buyItemDao.setBuyItemCode(buyItemCode);
        buyItemDao.setYearMonthSplit(DateUtils.dateTimeNow("YYYYMM"));
        buyItemDao.setUserId(SecurityUtils.getUserId());
        SysUser sysUser = SecurityUtils.getLoginUser().getSysUser();
        buyItemDao.setDeptId(sysUser.getDeptId());
//        buyItemDao.setRoleId(sysUser.getRoleId());
        buyItemBaseMapper.insert(buyItemDao);
        return buyItemDao;
    }

    private void fillMethodInfo(PurchaseMethodInfoVo purchaseMethodInfoToJson, BuyItemDao buyItemDao) {
        MethodKV methodKV = purchaseMethodInfoToJson.getMethodKVList().get(0);
        BeanUtils.copyProperties(methodKV, buyItemDao);
        if (Objects.isNull(purchaseMethodInfoToJson.getTimeNode())) {
            throw new ServiceException("该采购方式未配置时间节点 请在后台先进行配置");
        }
        buyItemDao.setPurchaseTimeJson(JSONObject.toJSONString(purchaseMethodInfoToJson.getTimeNode()));
        if (Objects.isNull(purchaseMethodInfoToJson.getBulletinKVList())) {
            throw new ServiceException("该采购方式未配置公告种类 请在后台先进行配置");
        }
        buyItemDao.setPurchaseBulletinJson(JSONArray.toJSONString(purchaseMethodInfoToJson.getBulletinKVList()));
        if (Objects.isNull(purchaseMethodInfoToJson.getFunctionKVList())) {
            throw new ServiceException("该采购方式未配置功能点 请在后台先进行配置");
        }
        buyItemDao.setPurchaseFunctionJson(JSONArray.toJSONString(purchaseMethodInfoToJson.getFunctionKVList()));
    }

    @Override
    public BuyItemDao updateBaseInfo(BuyItemBaseDto dto, PurchaseMethodInfoVo purchaseMethodInfoToJson) {
        BuyItemDao buyItemDao = new BuyItemDao();
        fillMethodInfo(purchaseMethodInfoToJson, buyItemDao);
        BeanUtils.copyProperties(dto, buyItemDao);
        update(buyItemDao, updateWrapper(dto.getBuyItemCode()));
        return buyItemDao;
    }

    /**
     * 根据标段code查询采购项目信息
     *
     * @param subpackageCode
     * @return
     */
    @Override
    public BuyItemDao findBySubpackageCode(String subpackageCode) {
        SubpackageDao subpackageDao = subPackageApi.findBySubpackageCode(subpackageCode);
        return findBuyItemInfo(subpackageDao.getBuyItemCode());
    }

    @Override
    public BuyItemVo getBuyItemVo(String subpackageCode, String belongRole) {
        BuyItemDao buyItemDao = findBySubpackageCode(subpackageCode);
        return daoConvertVo(buyItemDao, belongRole);
    }

    @Override
    public GetItemVo getItemVo(GetItemParam param) {
        BuyItemDao buyItemInfo = queryItemInfo(param.getBuyItemCode(), param.getSubpackageCode());
        BuyItemVo buyItemVO = daoConvertVo(buyItemInfo, param.getBelongRole());
        GetItemVo vo = new GetItemVo(buyItemVO);
        List<SubpackageDao> subpackageByBuyItemCode = subPackageApi.findByBuyItemCode(buyItemInfo.getBuyItemCode());
        vo.setSubpackageDaoList(subpackageByBuyItemCode);
        return vo;
    }

    @Override
    public GetSimpleItemVo getSimpleItemVo(GetSimpleItemParam param) {
        BuyItemDao buyItemInfo = queryItemInfo(param.getBuyItemCode(), param.getSubpackageCode());
        GetSimpleItemVo vo = new GetSimpleItemVo();
        BeanUtils.copyProperties(buyItemInfo, vo);
        List<SubpackageDao> subpackageDaoList = subPackageApi.findByBuyItemCode(buyItemInfo.getBuyItemCode());
        List<SuperPackageVo> voList = subpackageDaoList.stream().map(s -> {
            SuperPackageVo packageVo = new SuperPackageVo();
            BeanUtils.copyProperties(s, packageVo);
            return packageVo;
        }).collect(Collectors.toList());
        vo.setSuperPackageVoList(voList);
        return vo;
    }

    private BuyItemDao queryItemInfo(String buyItemCode, String subpackageCode) {
        BuyItemDao buyItemInfo;
        if (StringUtils.hasText(buyItemCode)) {
            buyItemInfo = findBuyItemInfo(buyItemCode);
        } else {
            buyItemInfo = findBySubpackageCode(subpackageCode);
        }
        return buyItemInfo;
    }

    private BuyItemVo daoConvertVo(BuyItemDao buyItemInfo, String belongRole) {
        BuyItemVo vo = new BuyItemVo();
        BeanUtils.copyProperties(buyItemInfo, vo);
        vo.setTimeNodeDto(JSONObject.parseObject(buyItemInfo.getPurchaseTimeJson(), PurchaseTimeNodeDto.class));
        vo.setBulletinKVList(JSONArray.parseArray(buyItemInfo.getPurchaseBulletinJson(), BulletinKV.class));
        List<FunctionKV> functionKVList = JSONArray.parseArray(buyItemInfo.getPurchaseFunctionJson(), FunctionKV.class);
        if (StringUtils.isEmpty(belongRole)) {
            vo.setFunctionKVList(functionKVList);
        } else {
            List<FunctionKV> voList = functionKVList.stream().filter(f -> f.getBelongRole()
                    .contains(belongRole)).collect(Collectors.toList());
            vo.setFunctionKVList(voList);
        }
        return vo;
    }

    @Override
    public IPage<ExtractBuyItemVo> selectExtract(PageSortEntity<BaseQueryDto> dto, String tableName) {
        BaseQueryDto entity = dto.getEntity();
        return buyItemBaseMapper.selectExtract(
                Page.of(dto.getPageNum(), dto.getPageSize()),
                entity,
                tableName
        );
    }

    @Override
    public IPage<SupplierRegistrationReviewVo> selectBuyItemWithSupplierSignUp(Integer pageNum, Integer pageSize,
                                                                               BaseQueryDto dto, List<Integer> reviewStatus) {
        return buyItemBaseMapper.selectBuyItemWithSupplierSignUp(Page.of(pageNum, pageSize), dto, reviewStatus);
    }

    @Override
    public int countSupplierRegistrationReviews(Long userId, List<Integer> reviewStatus) {
        return buyItemBaseMapper.countSupplierRegistrationReviews(userId, reviewStatus);
    }
}
