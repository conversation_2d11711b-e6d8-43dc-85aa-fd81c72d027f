package com.epcos.bidding.purchase.project.fjnx.business.api.buyitem;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.IBaseService;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.project.fjnx.domain.dao.BuyItemFJNXDao;
import com.epcos.bidding.purchase.project.fjnx.domain.dto.FJNXBuyItemQueryDto;
import com.epcos.bidding.purchase.project.fjnx.domain.dto.FJNXCreateBuyItemDto;
import com.epcos.bidding.purchase.project.fjnx.domain.vo.FJNXBuyItemPageVo;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 福建省农信版本
 * @date 2024/4/23 14:24
 */
public interface FJNXBuyItemApi extends IBaseService<BuyItemFJNXDao> {

    @Override
    default LambdaQueryWrapper<BuyItemFJNXDao> queryWrapper(BuyItemFJNXDao dao) {
        LambdaQueryWrapper<BuyItemFJNXDao> query = Wrappers.lambdaQuery(BuyItemFJNXDao.class);
        if (Objects.nonNull(dao.getId())) {
            return query.eq(BuyItemFJNXDao::getId, dao.getId());
        }
        return query.eq(StringUtils.hasText(dao.getBuyItemCode()), BuyItemFJNXDao::getBuyItemCode, dao.getBuyItemCode())
                .like(StringUtils.hasText(dao.getBuyItemName()), BuyItemFJNXDao::getBuyItemName, dao.getBuyItemName())
                .eq(StringUtils.hasText(dao.getBuyClass()), BuyItemFJNXDao::getBuyClass, dao.getBuyClass());
    }

    default LambdaQueryWrapper<BuyItemFJNXDao> queryWrapper(String buyItemCode) {
        return queryWrapper(new BuyItemFJNXDao(buyItemCode));
    }

    default LambdaQueryWrapper<BuyItemFJNXDao> queryWrapper(String buyItemCode, String innerCode) {
        return Wrappers.lambdaQuery(BuyItemFJNXDao.class)
                .eq(StringUtils.hasText(buyItemCode), BuyItemFJNXDao::getBuyItemCode, buyItemCode)
                .eq(BuyItemFJNXDao::getInnerCode, innerCode);
    }

    default LambdaUpdateWrapper<BuyItemFJNXDao> updateWrapper(String buyItemCode) {
        return Wrappers.lambdaUpdate(BuyItemFJNXDao.class)
                .eq(StringUtils.hasText(buyItemCode), BuyItemFJNXDao::getBuyItemCode, buyItemCode);
    }

    void insertFJNXBuyItem(FJNXCreateBuyItemDto dto, String buyItemCode);


    IPage<FJNXBuyItemPageVo> selectJoinBuyItemPage(PageSortEntity<FJNXBuyItemQueryDto> dto);

    BuyItemFJNXDao findOneByBuyItemCode(String buyItemCode);

    List<BuyItemFJNXDao> findOneByBuyItemCodeList(List<String> buyItemCodeList);

    void updateFJNXBuyItemInfo(FJNXCreateBuyItemDto dto);

    Boolean delFJNXBuyItemInfo(String buyItemCode);
}
