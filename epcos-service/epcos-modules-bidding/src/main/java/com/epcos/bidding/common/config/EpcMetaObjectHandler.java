package com.epcos.bidding.common.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.epcos.common.core.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/19 17:19
 */
@Slf4j
@Component
public class EpcMetaObjectHandler implements MetaObjectHandler {

    /**
     * 插入时的填充策略
     *
     * @param metaObject
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        this.setFieldValByName("createAt", new Date(), metaObject);
        this.setFieldValByName("createBy", SecurityUtils.getNickName(), metaObject);
    }

    /**
     * 更新时的填充策略
     *
     * @param metaObject
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        this.setFieldValByName("updateAt", new Date(), metaObject);
        this.setFieldValByName("updateBy", SecurityUtils.getNickName(), metaObject);
    }
}

