package com.epcos.bidding.common.utils.excel;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

/**
 * @author:moyu
 * @Description:作用于excel样式的工具类
 * @date 2021-05-28 11:05
 * @version:1.0
 */
public class ExcelStyleUtil {

    /**
     * excel中sheet名不能包含这些字符
     */
    private static String regex = "(\\*|/|:|\\\\|\\[|\\]|\\?)";
    /**
     * excel表名的最大长度
     */
    private static Integer length = 31;

    /**
     * 将excel中sheet名不能包含这些字符 统一替换成 _
     *
     * @param sheetName 传进来的sheet表名
     * @return
     */
    public static String replaceSheetName(String sheetName) {
        if (sheetName.length() > length) {
            sheetName.substring(0, 31);
        }
        return sheetName.replaceAll(regex, "_");
    }

    /**
     * 设置表格字体的对齐方式
     *
     * @param cellStyle 样式对象
     * @param standard  水平方向的字体对齐方式
     * @param vertical  垂直方向的字体对齐方式
     */
    public static void setStandAndVerticalStyle(CellStyle cellStyle, HorizontalAlignment standard, VerticalAlignment vertical) {
        cellStyle.setAlignment(standard);
        cellStyle.setVerticalAlignment(vertical);
    }


    /**
     * 合并单元格
     *
     * @param sheet    工作表对象
     * @param firstRow 合并的开始行
     * @param lastRow  合并的结束行
     * @param firstCol 合并的开始列
     * @param lastCol  合并的结束列
     */
    public static void mergeCell(Sheet sheet, int firstRow, int lastRow, int firstCol, int lastCol) {
        sheet.addMergedRegion(new CellRangeAddress(firstRow, lastRow, firstCol, lastCol));
    }


    /**
     * 设置字体样式
     *
     * @param font     字体对象
     * @param isBold   字体是否加粗
     * @param fontSize 字体字号大小
     * @param fontName 字体样式 如：宋体
     * @return
     */
    public static void setTitleStyle(Font font, Boolean isBold, short fontSize, String fontName) {

        font.setBold(isBold);
        font.setFontHeightInPoints(fontSize);
        font.setFontName(fontName);
    }

    /**
     * 设置字体大小
     *
     * @param font     字体对象
     * @param fontSize 字体大小
     */
    public static void setTitleStyle(Font font, short fontSize) {
        font.setFontHeightInPoints(fontSize);
    }

    /**
     * 设置表格填充色
     *
     * @param cellStyle  样式对象
     * @param prospect   设置前景色填充颜色注意：确保将前景色设置为背景颜色之前
     * @param background 设置背景填充颜色
     */
    public static void setExcelFillColor(CellStyle cellStyle, short prospect, short background) {
        cellStyle.setFillForegroundColor(prospect);
        cellStyle.setFillBackgroundColor(background);
    }

    /**
     * 设置表格是否有边框
     * 设置表格边框的粗细程度
     *
     * @param cellStyle   样式对象
     * @param bottomStyle 下边框
     * @param leftStyle   左边框
     * @param rightStyle  右边框
     * @param topStyle    上边框
     * @return
     */
    public static void setCellBorder(CellStyle cellStyle, BorderStyle bottomStyle,
                                     BorderStyle leftStyle, BorderStyle rightStyle, BorderStyle topStyle) {

        cellStyle.setBorderBottom(bottomStyle);
        cellStyle.setBorderLeft(leftStyle);
        cellStyle.setBorderTop(topStyle);
        cellStyle.setBorderRight(rightStyle);

    }

    /**
     * 设置表格边框线颜色
     *
     * @param cellStyle   样式对象
     * @param bottomColor 下边框
     * @param leftColor   左边框
     * @param rightColor  右边框
     * @param topColor    上边框
     */
    public static void setCellBorderColor(CellStyle cellStyle, short bottomColor,
                                          short leftColor, short rightColor, short topColor) {

        cellStyle.setTopBorderColor(topColor);
        cellStyle.setBottomBorderColor(bottomColor);
        cellStyle.setLeftBorderColor(leftColor);
        cellStyle.setRightBorderColor(rightColor);
    }
}
