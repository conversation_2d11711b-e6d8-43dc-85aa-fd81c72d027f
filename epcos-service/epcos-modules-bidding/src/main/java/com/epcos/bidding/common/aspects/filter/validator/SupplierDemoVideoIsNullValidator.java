package com.epcos.bidding.common.aspects.filter.validator;

import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierDemoVideoDao;
import com.epcos.common.core.exception.ServiceException;

import java.util.Optional;

public class SupplierDemoVideoIsNullValidator implements ResultPostHandlerFilterChain<SupplierDemoVideoDao> {
    @Override
    public void postHandler(AspectContext context, SupplierDemoVideoDao result) {
        Optional.ofNullable(result)
                .orElseThrow(() -> new ServiceException("获取供应商演示视频为空"))
                .verifyVideoIsNull();
    }

}
