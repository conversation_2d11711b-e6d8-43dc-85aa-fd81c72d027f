package com.epcos.bidding.purchase.project.base.domain.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
public class BidOpenTask implements Serializable {
    private static final long serialVersionUID = -7739815833616798371L;

    public BidOpenTask(String subpackageCode, String buyItemCode,
                       Integer failCount, Date bidOpenTime,
                       String status) {
        this.subpackageCode = subpackageCode;
        this.buyItemCode = buyItemCode;
        this.failCount = failCount;
        this.bidOpenTime = bidOpenTime;
        this.status = status;
    }

    private Long userId;
    private String username;
    private String nickName;

    private String subpackageCode;
    private String buyItemCode;
    private Integer failCount;
    private Date bidOpenTime;
    private String status;
    private String msg;

    private Date successTime;
    private Date lastRetryTime;

    public static class Status {
        // 等待中
        public static final String PENDING = "pending";
        // 成功
        public static final String SUCCESS = "success";
        // 重试中
        public static final String RETRYING = "retrying";
        // 失败
        public static final String FAILED = "failed";
        // 错误
        public static final String ERROR = "error";
    }
}
