package com.epcos.bidding.common.annotaion;

import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * get 相关注解中的通用参数
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface GetCommon {

    /**
     * 异步执行
     */
    boolean async() default false;

    /**
     * 采购项目code
     * el 表达式取值
     */
    String buyItemCodeEL() default "";

    /**
     * 包code
     * el 表达式取值
     */
    String subpackageCodeEL() default "";

    /**
     * 拿到相关返回后的后续处理
     */
    Class<? extends ResultPostHandlerFilterChain>[] afters() default {ResultPostHandlerFilterChain.class};

}
