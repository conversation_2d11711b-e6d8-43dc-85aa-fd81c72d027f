package com.epcos.bidding.purchase.project.bjxk.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.epcos.bidding.purchase.project.bjxk.domain.dao.BuyItemBJXKDao;
import com.epcos.bidding.purchase.project.bjxk.domain.dto.BJXKBuyItemQueryDto;
import com.epcos.bidding.purchase.project.bjxk.domain.vo.BJXKBuyItemPageVo;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 北京胸科医院版本
 * @date 2024/12/15 14:28
 */
public interface BJXKBuyItemMapper extends BaseMapper<BuyItemBJXKDao> {

    IPage<BJXKBuyItemPageVo> selectJoinBuyItemPage(IPage page, @Param("dto") BJXKBuyItemQueryDto dto, @Param("orgCode") String orgCode);

    int updateByItemCode(@Param("dto")BuyItemBJXKDao dao);
}
