package com.epcos.bidding.common.utils;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.File;
import java.util.List;

@Data
@NoArgsConstructor
public class BidFileOutLine {

    public BidFileOutLine(String bigTitle, String title, File file) {
        this.bigTitle = bigTitle;
        this.title = title;
        this.file = file;
    }

    // 大标题
    private String bigTitle;

    // 标题
    private String title;

    // 标题下的文件
    private File file;

    // 子标题
    private List<BidFileOutLine> childList;

    // 排序
    private Integer order;
}
