package com.epcos.bidding.common.annotaion;

import org.springframework.core.annotation.Order;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/9 14:35
 */
@Order(333)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface FieldMap {

    /**
     * excel列名映射的类
     *
     * @return
     */
    Class<?> value();

    /**
     * 是否需要填充自定义字段
     *
     * @return
     */
    boolean required() default true;

    /**
     * 键数组，用于存储列的名称
     *
     * @return
     */
    String[] keys() default {"序号"};

    /**
     * 值数组，表示每个键的对应值
     *
     * @return
     */
    String[] values() default {"number"};
}
