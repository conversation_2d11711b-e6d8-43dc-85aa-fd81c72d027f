package com.epcos.bidding.purchase.project.bjxk.business.service.buyitem;

import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.common.utils.BiddingBaseUtil;
import com.epcos.bidding.purchase.project.bjxk.business.api.buyitem.BJXKBuyItemApi;
import com.epcos.bidding.purchase.project.bjxk.domain.dao.BuyItemBJXKDao;
import com.epcos.bidding.purchase.project.bjxk.domain.dto.BJXKBuyItemQueryDto;
import com.epcos.bidding.purchase.project.bjxk.domain.dto.BJXKCreateBuyItemDto;
import com.epcos.bidding.purchase.project.bjxk.domain.vo.BJXKBuyItemPageVo;
import com.epcos.bidding.purchase.project.bjxk.repository.BJXKBuyItemMapper;
import com.epcos.common.core.domain.AttachmentDto;
import com.epcos.common.core.utils.DateUtils;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.utils.bean.BeanUtils;
import com.epcos.dingtalk.domain.dto.AttachFileDto;
import com.epcos.system.api.model.FUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.epcos.common.core.constant.FileTypeNameConstants.CREATE_BUY_ITEM;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 江西省南昌市肿瘤医院版本
 * @date 2024/4/23 14:26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BJXKBuyItemService extends ServiceImpl<BJXKBuyItemMapper, BuyItemBJXKDao> implements BJXKBuyItemApi {


    private final BJXKBuyItemMapper bjxkBuyItemMapper;

    @Override
    public void insertBJXKBuyItem(BJXKCreateBuyItemDto dto, String buyItemCode) {
        BuyItemBJXKDao bjxkDao = new BuyItemBJXKDao();
        BeanUtils.copyProperties(dto, bjxkDao);
        bjxkDao.setBuyItemCode(buyItemCode);
        //创建项目中的附件上传
        String paramJson = attUp(dto.getParamsAttList(), buyItemCode);
        String meetingJson = attUp(dto.getMeetingAttList(), buyItemCode);
        bjxkDao.setParamsAtt(paramJson);
        bjxkDao.setMeetingContent(meetingJson);
        bjxkBuyItemMapper.insert(bjxkDao);
    }

    /**
     * 上传项目中的文件
     *
     * @param attachmentDtoList
     * @param buyItemCode
     * @return
     */
    private String attUp(List<AttachFileDto> attachmentDtoList, String buyItemCode) {
        //上传文件
        if (ArrayUtil.isNotEmpty(attachmentDtoList)) {
            List<AttachmentDto> attList = attachmentDtoList.stream()
                    .map(att -> {
                        AttachmentDto attachmentDto = new AttachmentDto();
                        BeanUtils.copyProperties(att, attachmentDto);
                        MultipartFile file;
                        if (Objects.nonNull(att.getFile())) {
                            file = att.getFile();
                        } else {
                            CommonsMultipartFile multipartFile = BiddingBaseUtil.downFile(att.getUrl());
                            file = multipartFile;
                        }
                        if (Objects.nonNull(file)) {
                            String key = FUtil.upFile(buyItemCode, DateUtils.getDateToM(), file, CREATE_BUY_ITEM,
                                    null, SecurityUtils.getUserId());
                            attachmentDto.setUrl(key);
                        }
                        return attachmentDto;
                    }).collect(Collectors.toList());
            String fileJson = JSONArray.toJSONString(attList);
            return fileJson;
        }
        return null;
    }

    private String attUps(List<AttachFileDto> paramAttList, String buyItemCode) {
        if (CollectionUtils.isNotEmpty(paramAttList)) {
            List<AttachmentDto> paramAttVoList = paramAttList.stream()
                    .map(a -> {
                        AttachmentDto attachmentDto = new AttachmentDto(a.getName(), a.getUrl());
                        if (Objects.nonNull(a.getFile())) {
                            String url = FUtil.upFile(buyItemCode, DateUtils.getDateToM(), a.getFile(),
                                    CREATE_BUY_ITEM, null, SecurityUtils.getUserId());
                            attachmentDto.setUrl(url);
                        }
                        return attachmentDto;
                    }).collect(Collectors.toList());
            return JSONArray.toJSONString(paramAttVoList);
        }
        return null;
    }


    @Override
    public IPage<BJXKBuyItemPageVo> selectJoinBuyItemPage(PageSortEntity<BJXKBuyItemQueryDto> dto) {
        BJXKBuyItemQueryDto entity = dto.getEntity();
        return bjxkBuyItemMapper.selectJoinBuyItemPage(
                Page.of(dto.getPageNum(), dto.getPageSize()),
                entity, entity.getOrgCode()
        );
    }

    @Override
    public BuyItemBJXKDao findOneByBuyItemCode(String buyItemCode) {
        return getOne(queryWrapper(new BuyItemBJXKDao(buyItemCode)));
    }

    @Override
    public List<BuyItemBJXKDao> findOneByBuyItemCodeList(List<String> buyItemCodeList) {
        return list(Wrappers.lambdaQuery(BuyItemBJXKDao.class)
                .in(BuyItemBJXKDao::getBuyItemCode, buyItemCodeList));
    }

    @Override
    public void updateBJXKBuyItemInfo(BJXKCreateBuyItemDto dto) {
        BuyItemBJXKDao bjxkDao = new BuyItemBJXKDao();
        BeanUtils.copyProperties(dto, bjxkDao);
        //文件更新业务逻辑 TODO 此处文件未删除之前的旧文件，且，重复的文件会一直上传，需要做md5算法进行排除
        String paramJson = attUps(dto.getParamsAttList(), bjxkDao.getBuyItemCode());
        String meetingJson = attUps(dto.getMeetingAttList(), bjxkDao.getBuyItemCode());
        bjxkDao.setParamsAtt(paramJson);
        bjxkDao.setMeetingContent(meetingJson);
        bjxkBuyItemMapper.updateByItemCode(bjxkDao);
    }

    @Override
    public Boolean delBJXKBuyItemInfo(String buyItemCode) {
        return remove(updateWrapper(buyItemCode));
    }
}
