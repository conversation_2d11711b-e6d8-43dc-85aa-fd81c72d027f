package com.epcos.bidding.purchase.win.business.api;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.IBaseService;
import com.epcos.bidding.purchase.api.params.vo.buyitem.ItemSubpackageVo;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.purchase.win.domain.dao.WinBidResultDao;
import com.epcos.bidding.purchase.win.domain.dto.*;
import com.epcos.bidding.purchase.win.domain.vo.ReviewPageVo;
import com.epcos.bidding.purchase.win.domain.vo.WinBidVo;
import com.epcos.bidding.purchase.win.domain.vo.WinSubPackageVo;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.domain.review.ReviewSummaryVo;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/9 14:56
 */
public interface IWinBidResultApi extends IBaseService<WinBidResultDao> {

    @Override
    default LambdaQueryWrapper<WinBidResultDao> queryWrapper(WinBidResultDao dao) {
        LambdaQueryWrapper<WinBidResultDao> query = Wrappers.lambdaQuery(WinBidResultDao.class);
        if (Objects.nonNull(dao.getId())) {
            return query.eq(WinBidResultDao::getId, dao.getId());
        }
        return query
                .eq(StringUtils.hasText(dao.getSubpackageCode()), WinBidResultDao::getSubpackageCode, dao.getSubpackageCode())
                .eq(Objects.nonNull(dao.getSupplierId()), WinBidResultDao::getSupplierId, dao.getSupplierId())
                .like(StringUtils.hasText(dao.getSubpackageName()), WinBidResultDao::getSubpackageName, dao.getSubpackageName())
                .like(StringUtils.hasText(dao.getSupplierCompanyName()), WinBidResultDao::getSupplierCompanyName, dao.getSupplierCompanyName())
                .eq(StringUtils.hasText(dao.getRecommendCandidate()), WinBidResultDao::getRecommendCandidate, dao.getRecommendCandidate())
                .eq(StringUtils.hasText(dao.getWinBid()), WinBidResultDao::getWinBid, dao.getWinBid())
                .like(StringUtils.hasText(dao.getRemark()), WinBidResultDao::getRemark, dao.getRemark())
                .eq(StringUtils.hasText(dao.getIsWin()), WinBidResultDao::getIsWin, dao.getIsWin())
                .eq(StringUtils.hasText(dao.getAgainSend()), WinBidResultDao::getAgainSend, dao.getAgainSend())
                .orderByDesc(WinBidResultDao::getId);
    }

    default LambdaQueryWrapper<WinBidResultDao> queryWrapper(String subpackageCode, Long supplierId) {
        WinBidResultDao winBidResultDao = new WinBidResultDao();
        winBidResultDao.setSubpackageCode(subpackageCode);
        winBidResultDao.setSupplierId(supplierId);
        return queryWrapper(winBidResultDao);
    }

    default LambdaQueryWrapper<WinBidResultDao> queryWrapper(String subpackageCode, Long supplierId, String winBid) {
        WinBidResultDao winBidResultDao = new WinBidResultDao();
        winBidResultDao.setSubpackageCode(subpackageCode);
        winBidResultDao.setSupplierId(supplierId);
        winBidResultDao.setWinBid(winBid);
        return queryWrapper(winBidResultDao);
    }

    default LambdaUpdateWrapper<WinBidResultDao> updateWrapper(String subpackageCode, Long supplierId) {
        return Wrappers.lambdaUpdate(WinBidResultDao.class)
                .eq(StringUtils.hasText(subpackageCode), WinBidResultDao::getSubpackageCode, subpackageCode)
                .eq(Objects.nonNull(supplierId), WinBidResultDao::getSupplierId, supplierId);
    }


    default WinBidResultDao findOne(String subpackageCode, Long supplierId) {
        return getOne(queryWrapper(subpackageCode, supplierId));
    }

    default Boolean del(List<String> subpackageCodeList) {
        return remove(Wrappers.lambdaQuery(WinBidResultDao.class)
                .in(WinBidResultDao::getSubpackageCode, subpackageCodeList));
    }


    default List<WinBidResultDao> find(String subpackageCode) {
        return list(queryWrapper(subpackageCode, null));
    }

    List<ItemSubpackageVo<ReviewPageVo>> confirmReviewPage(String buyItemCode);

    Boolean againVote(String subpackageCode);

    List<ItemSubpackageVo<WinSubPackageVo>> winPage(String buyItemCode);

    ReviewSummaryVo calculatePriceScore(String subpackageCode, ReviewSummaryVo reviewInfo);

    Boolean confirmCounterSign(ConfirmCounterDto dto);

    Boolean confirmReview(ConfirmReviewDto dto);

    Boolean sendNotice(ResultNoticeDto dto);

    void completed(String buyItemCode);

    Boolean confirmReviewResult(String subpackageCode);

    Boolean selectWin(SelectWinDto dto);

    WinBidVo findBy(String subpackageCode, Long supplierId);

    List<WinBidResultDao> findBy(String subpackageCode, Long supplierId, String winBid);

    List<WinBidVo> findBy(String subpackageCode);

    Boolean againConfirmReview(String subpackageCode);

    Map<Long, Long> calculateWinCnt(List<Long> supplierIds);

    /**
     * 生成评审报告
     *
     * @param reviewReportDto
     * @return
     */
    R<Boolean> confirmReviewReport(ReviewReportDto reviewReportDto);

    /**
     * 查询项目评审报告集合
     *
     * @param buyItemCode
     * @return
     */
    R<List<SubpackageDao>> getReviewReport(String buyItemCode);

    /**
     * 计算邀请了该供应商但未报名的次数
     *
     * @param supplierIds
     * @return
     */
    Map<Long, Long> calculateInviteNum(List<Long> supplierIds);
}
