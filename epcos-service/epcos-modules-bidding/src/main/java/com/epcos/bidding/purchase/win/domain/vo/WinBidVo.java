package com.epcos.bidding.purchase.win.domain.vo;

import com.epcos.bidding.audit.api.vo.AuditInfoVo;
import com.epcos.bidding.purchase.win.domain.dao.WinBidResultDao;
import com.epcos.bidding.supplier.contract.domain.vo.SupplierContractVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/9 17:13
 */
@Data
public class WinBidVo extends WinBidResultDao implements Serializable {

    private static final long serialVersionUID = -5695295539284636555L;

    @ApiModelProperty("中标结果通知书是否发送,true=完成")
    private Boolean resultNoticeSendingStatus;

    @ApiModelProperty("结果公告审核状态,true=完成")
    private Boolean resultAnnouncementReviewStatus;

    @ApiModelProperty("招标文件审核状态,true=完成")
    private SupplierContractVo supplierContractVo;

    @ApiModelProperty("审批信息")
    private AuditInfoVo auditInfoVo;
}
