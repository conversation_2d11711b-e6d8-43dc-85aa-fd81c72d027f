package com.epcos.bidding.common.annotaion;

import org.springframework.core.annotation.Order;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 获取投标人信息
 * return SupplierBidderDao
 *
 * <AUTHOR>
 */
@Order(300)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface GetBidder {

    GetCommon common() default @GetCommon;

    /**
     * 供应商id
     * el 表达式取值
     */
    String supplierIdEL() default "";

}
