package com.epcos.bidding.common.utils;

import com.epcos.bidding.common.SubpackageCodeEntity;
import com.epcos.common.core.annotation.Gzip;
import com.epcos.common.core.web.validator.CreateGroup;
import com.epcos.common.core.web.validator.UpdateGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class FileMenuEntityShared extends SubpackageCodeEntity {

    @ApiModelProperty(value = "父主键")
    protected Long pid;

    @NotNull(message = "目录顺序,必填", groups = CreateGroup.class)
    @Range(min = 0, max = 255, message = "目录顺序【范围0-255】", groups = {CreateGroup.class, UpdateGroup.class})
    @ApiModelProperty(value = "目录顺序")
    protected Integer orderNum;

    @NotBlank(message = "目录名字,必填", groups = CreateGroup.class)
    @Length(max = 100, message = "目录名字【最长100】", groups = {CreateGroup.class, UpdateGroup.class})
    @ApiModelProperty(value = "目录名字")
    protected String chapterName;

    @Gzip
    @Length(max = 10*1024*1024, message = "目录内容【最大10MB】", groups = {CreateGroup.class, UpdateGroup.class})
    @ApiModelProperty(value = "目录内容，压缩并base64转码存储")
    protected String chapterContext;

    @Length(max = 5000, message = "目录中文件url及文件名【最长5000】", groups = {CreateGroup.class, UpdateGroup.class})
    @ApiModelProperty(value = "目录中文件keys")
    protected String attachKey;

    @Range(min = 1, max = 255, message = "目录类型【范围1-255】", groups = {CreateGroup.class, UpdateGroup.class})
    @ApiModelProperty(value = "目录类型")
    protected Integer chapterType;
}
