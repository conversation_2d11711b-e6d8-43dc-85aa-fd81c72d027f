package com.epcos.bidding.purchase.project.bjxk;

import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

public class BodyMapsUtil {

    public static Map<String, List<LinkedHashMap<String, String>>> parseBodyMaps(HttpServletRequest request) {
        // 存储最终的结果：每个 subpackageDtoList 分别有其对应的 bodyMaps 列表
        Map<String, List<LinkedHashMap<String, String>>> groupedBodyMaps = new HashMap<>();

        // 解析普通参数
        Map<String, String[]> parameterMap = request.getParameterMap();
        parameterMap.forEach((key, values) -> {
            if (key.contains("claimsFileQuoteFormCreatorDto.bodyMaps[")) {
                // 提取 subpackageDtoList 索引、uniqueKey、bodyMaps 索引及字段名
                String uniqueKey = extractUniqueKey(parameterMap, key);
                int bodyMapsIndex = extractIndex(key, "bodyMaps");
                String fieldKey = extractFieldKey(key);

                // 获取或初始化当前 uniqueKey 对应的 bodyMaps 列表
                groupedBodyMaps.putIfAbsent(uniqueKey, new ArrayList<>());
                List<LinkedHashMap<String, String>> bodyMaps = groupedBodyMaps.get(uniqueKey);

                // 确保 bodyMaps 的大小
                while (bodyMaps.size() <= bodyMapsIndex) {
                    bodyMaps.add(new LinkedHashMap<>());
                }

                // 添加普通参数值
                bodyMaps.get(bodyMapsIndex).put(fieldKey, values[0]);
            }
        });

        // 解析文件参数
        if (request instanceof MultipartHttpServletRequest) {
            MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
            fileMap.forEach((key, file) -> {
                if (key.contains("claimsFileQuoteFormCreatorDto.bodyMaps[")) {
                    // 提取 subpackageDtoList 索引、uniqueKey、bodyMaps 索引及字段名
                    String uniqueKey = extractUniqueKey(parameterMap, key);
                    int bodyMapsIndex = extractIndex(key, "bodyMaps");
                    String fieldKey = extractFieldKey(key);

                    // 获取或初始化当前 uniqueKey 对应的 bodyMaps 列表
                    groupedBodyMaps.putIfAbsent(uniqueKey, new ArrayList<>());
                    List<LinkedHashMap<String, String>> bodyMaps = groupedBodyMaps.get(uniqueKey);

                    // 确保 bodyMaps 的大小
                    while (bodyMaps.size() <= bodyMapsIndex) {
                        bodyMaps.add(new LinkedHashMap<>());
                    }

                    // 添加文件参数值
                    bodyMaps.get(bodyMapsIndex).put(fieldKey, file.toString());
                }
            });
        }

        return groupedBodyMaps;
    }

    // 提取 uniqueKey，例如从 "subpackageDtoList[0]" 中获取对应的 uniqueKey 值
    private static String extractUniqueKey(Map<String, String[]> parameterMap, String key) {
        // 从字段路径中提取 subpackageDtoList 索引
        int start = key.indexOf("subpackageDtoList[") + "subpackageDtoList[".length();
        int end = key.indexOf(']', start);
        String subpackageIndex = key.substring(start, end);

        // 构造 uniqueKey 的参数名
        String uniqueKeyParam = "subpackageDtoList[" + subpackageIndex + "].subpackageName";
        String[] uniqueKeyValues = parameterMap.get(uniqueKeyParam);

        // 返回 uniqueKey 的值
        return uniqueKeyValues != null && uniqueKeyValues.length > 0 ? uniqueKeyValues[0] : "defaultKey";
    }

    // 提取 bodyMaps 索引，例如从 "subpackageDtoList[0].claimsFileQuoteFormCreatorDto.bodyMaps[1].age" 提取 1
    private static int extractIndex(String key, String arrayName) {
        int start = key.indexOf(arrayName + "[") + arrayName.length() + 1;
        int end = key.indexOf(']', start);
        return Integer.parseInt(key.substring(start, end));
    }

    // 提取字段名，例如从 "subpackageDtoList[0].claimsFileQuoteFormCreatorDto.bodyMaps[1].name" 提取 "name"
    private static String extractFieldKey(String key) {
        int start = key.lastIndexOf('.') + 1;
        return key.substring(start);
    }
}
