package com.epcos.bidding.common.utils;

import com.epcos.bidding.common.SubpackageCodeEntity;
import com.epcos.common.core.web.validator.CreateGroup;
import com.epcos.common.core.web.validator.UpdateGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

@Data
public class FileEvaluationMethodRemarkEntityShared extends SubpackageCodeEntity {


    @NotBlank(message = "备注内容，不能为空", groups = CreateGroup.class)
    @Length(max = 65535, message = "备注内容【最长：65535】", groups = {CreateGroup.class, UpdateGroup.class})
    @ApiModelProperty(value = "备注内容")
    protected String remark;
}
