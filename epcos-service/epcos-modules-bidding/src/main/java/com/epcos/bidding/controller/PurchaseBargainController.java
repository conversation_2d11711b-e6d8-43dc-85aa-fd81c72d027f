package com.epcos.bidding.controller;

import com.epcos.bidding.common.annotaion.Msg;
import com.epcos.bidding.common.aspects.convert.BargainIdParamConvert;
import com.epcos.bidding.purchase.api.params.dto.bargin.StartOrEndBargainDto;
import com.epcos.bidding.purchase.bargain.business.api.IPurchaseBargainApi;
import com.epcos.bidding.purchase.bargain.domain.dto.UpBargainTimeDto;
import com.epcos.bidding.purchase.bargain.domain.vo.PurchaseBargainVo;
import com.epcos.bidding.purchase.bulletin.business.api.IBulletinApi;
import com.epcos.bidding.purchase.bulletin.domain.dao.BulletinDao;
import com.epcos.common.core.constant.RoleConstants;
import com.epcos.common.core.domain.R;
import com.epcos.common.log.annotation.Log;
import com.epcos.common.log.enums.BusinessType;
import com.epcos.common.redis.publish.BusinessTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.epcos.bidding.common.enums.BulletinTypeEnum.WIN_RESULT_PUBLICITY;
import static com.epcos.common.core.constant.PurchaseConstants.BulletinAudit.PASS;
import static com.epcos.common.core.constant.PurchaseConstants.BulletinAudit.WAIT_VERIFY;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/18 13:55
 */
@Api(tags = "采购人发起议价")
@RestController
@RequestMapping("/purchase/bargain")
@RequiredArgsConstructor
public class PurchaseBargainController {

    private final IPurchaseBargainApi purchaseBargainApi;
    private final IBulletinApi bulletinApi;

    @ApiOperation(value = "发起/结束议价")
    @Log(title = "发起/结束议价[接口：replyAsk]", businessType = BusinessType.INSERT)
    @Msg(
            fromRole = RoleConstants.PURCHASER,
            businessType = BusinessTypeEnum.START_OR_END_BARGAIN,
            msg = "发起/结束议价",
            subpackageCodeEL = "#dto.subpackageCode",
            toUserIdsConvert = BargainIdParamConvert.class
    )
    @PostMapping(value = "/startOrEndBargain")
    public R<Boolean> startOrEndBargain(@RequestBody @Validated StartOrEndBargainDto dto) {
        dto.verifySustainedTime();
        //发送结果公示之后不可再议价
        List<BulletinDao> bulletinDaoList = bulletinApi.findBySubpackageCodeAndBulletinTypeInAndAuditStatusIn(null,
                dto.getSubpackageCode(), Collections.singletonList(WIN_RESULT_PUBLICITY.getKey()), Arrays.asList(WAIT_VERIFY, PASS));
        if (!CollectionUtils.isEmpty(bulletinDaoList)) {
            return R.fail("当前子包已经发送结果公示，不可再议价");
        }
        return R.ok(purchaseBargainApi.startOrEndBargain(dto));
    }

    @ApiOperation(value = "议价列表")
    @GetMapping(value = "/list")
    public R<List<PurchaseBargainVo>> bargainList(@NotBlank String buyItemCode) {
        return R.ok(purchaseBargainApi.bargainList(buyItemCode));
    }

    @ApiOperation(value = "查询议价最大轮数")
    @GetMapping(value = "/findMaxRound")
    public R<Integer> findMaxRound(@RequestParam("subpackageCode") String subpackageCode) {
        return R.ok(purchaseBargainApi.findMaxRound(subpackageCode));
    }


    @ApiOperation(value = "批量修改议价截止期以及服务需求（议价备注）")
    @PostMapping(value = "/updateBargainTime")
    public R<Boolean> updateBargainTime(@RequestBody List<UpBargainTimeDto> dtoList) {
        return R.ok(purchaseBargainApi.updateBargainTime(dtoList));
    }
}
