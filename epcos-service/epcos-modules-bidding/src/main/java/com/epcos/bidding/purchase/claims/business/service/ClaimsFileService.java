package com.epcos.bidding.purchase.claims.business.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.audit.api.BizAuditRelationDto;
import com.epcos.bidding.audit.api.vo.AuditInfoVo;
import com.epcos.bidding.audit.business.api.IAuditInfoApi;
import com.epcos.bidding.audit.domain.dao.AuditInfoDao;
import com.epcos.bidding.audit.event.AuditResultEvent;
import com.epcos.bidding.biz.business.api.IBizAuditRelationApi;
import com.epcos.bidding.common.annotaion.Jump;
import com.epcos.bidding.common.annotaion.Msg;
import com.epcos.bidding.common.utils.BidFileJson;
import com.epcos.bidding.common.utils.BidFileUtil;
import com.epcos.bidding.purchase.api.domian.cliams.ClaimsFileReleaseAndStampDto;
import com.epcos.bidding.purchase.api.params.EpcFileContentVo;
import com.epcos.bidding.purchase.claims.business.api.IClaimsFileApi;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileDao;
import com.epcos.bidding.purchase.claims.repository.ClaimsFileMapper;
import com.epcos.common.core.constant.RoleConstants;
import com.epcos.common.core.constant.UserConstants;
import com.epcos.common.core.domain.AuditStatusDto;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.redis.publish.BusinessTypeEnum;
import com.epcos.system.api.model.FUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

import static com.epcos.common.core.constant.PurchaseConstants.UserType.PURCHASER;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ClaimsFileService extends ServiceImpl<ClaimsFileMapper, ClaimsFileDao> implements IClaimsFileApi {

    private final IBizAuditRelationApi iBizAuditRelationApi;
    private final IAuditInfoApi iAuditInfoApi;
    private final ApplicationContext ac;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdate(String subpackageCode,
                             String zipUrl, String pdfUrl,
                             BidFileJson bidFileJson) {
        ClaimsFileDao claimsFileDao = Optional.ofNullable(findBySubpackageCode(subpackageCode))
                .map(f -> {
                    delExistsFile(f);
                    f.setEpcFile(zipUrl);
                    f.setPdfFile(pdfUrl);
                    f.setAppName(bidFileJson.getAppName());
                    f.setAppVersion(bidFileJson.getVersion());
                    f.setZepcKey(bidFileJson.getZepcKey());
                    return f;
                }).orElseGet(() -> new ClaimsFileDao(subpackageCode, zipUrl, pdfUrl,
                        bidFileJson.getAppName(), bidFileJson.getVersion(), bidFileJson.getZepcKey())
                        .init());
        checkResLog(saveOrUpdate(claimsFileDao, updateWrapper(subpackageCode)), claimsFileDao);
        return claimsFileDao.getId();
    }

    private void delExistsFile(ClaimsFileDao dao) {
        Optional.ofNullable(dao)
                .ifPresent(d -> {
                    Optional.ofNullable(d.getEpcFile()).ifPresent(FUtil::delNonArchivedFileByFileKey);
                    Optional.ofNullable(d.getPdfFile()).ifPresent(FUtil::delFile);
                });
    }

    @Override
    public EpcFileContentVo findBySubpackageCodeVo(String subpackageCode) {
        return Optional.ofNullable(findBySubpackageCode(subpackageCode))
                .map(i ->
                        BidFileUtil.convert(i, null, null))
                .orElse(null);
    }

    @Override
    public ClaimsFileDao findBySubpackageCode(String subpackageCode) {
        return getOne(queryWrapper(subpackageCode));
    }

    @Override
    public List<ClaimsFileDao> findBySubpackageCodes(Set<String> subpackageCodes) {
        return list(queryWrapper(subpackageCodes));
    }

    @Msg(
            fromRole = RoleConstants.PURCHASER,
            businessType = BusinessTypeEnum.PROCUREMENT_RESPONSE_FILE,
            msg = "采购文件已正式发布",
            buyItemCodeEL = "#dto.buyItemCode",
            subpackageCodeEL = "#dto.subpackageCode"
    )
    @Override
    @Jump(subpackageCodeEL = "#dto.subpackageCode", processRole = PURCHASER)
    public void updateReleaseStatus(ClaimsFileReleaseAndStampDto dto) {
//        FUtil.orgSealByCoordinate(dto.getPdfFile(), dto.getOrgCode(), null);
        FUtil.startSeal(dto.getPdfFile(),null,dto.getOrgCode(),null, UserConstants.FIRM_COORDINATE_SEAL_PARAMETER);

        ClaimsFileDao release = new ClaimsFileDao().release();
        checkResLog(update(release,
                        updateWrapper(dto.getSubpackageCode())),
                release, dto.getSubpackageCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importOther(String importSubpackageCode, String subpackageCode) {
        ClaimsFileDao old = findBySubpackageCode(importSubpackageCode);
        if (Objects.isNull(old)) {
            log.error("传入包code没有采购文件信息：importSubpackageCode={}, subpackageCode={}, dao={}",
                    importSubpackageCode, subpackageCode, old);
            throw new ServiceException("传入包code没有采购文件信息：" + importSubpackageCode);
        }
        old.init().setSubpackageCode(subpackageCode);
        old.setId(null);
        saveOrUpdate(subpackageCode, old.getEpcFile(),
                old.getPdfFile(), old.convertBidFileJson());
    }

    /**
     * 钉钉
     * 修改采购文件审批状态，撤回-终止
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAuditStatus(AuditStatusDto dto) {
        BizAuditRelationDto build = BizAuditRelationDto.builder()
                .businessType(dto.getAuditCode())
                .build();
        AuditInfoVo infoVo = iBizAuditRelationApi.queryAuditInfoVo(build);
        if (Objects.isNull(infoVo)) {
            log.error("钉钉修改采购文件审批状态-查询审批为空-异常 dto={}", dto);
            throw new ServiceException("钉钉修改采购文件审批状态-查询审批为空-异常");
        }
        AuditInfoDao auditInfoDao = new AuditInfoDao();
        BeanUtils.copyProperties(infoVo, auditInfoDao, "status", "remark");
        auditInfoDao.setId(infoVo.getId());
        auditInfoDao.setStatus(dto.getAuditStatus());
        auditInfoDao.setRemark(dto.getRemark());
        iAuditInfoApi.updateById(auditInfoDao);
        ac.publishEvent(new AuditResultEvent(auditInfoDao));
    }


}
