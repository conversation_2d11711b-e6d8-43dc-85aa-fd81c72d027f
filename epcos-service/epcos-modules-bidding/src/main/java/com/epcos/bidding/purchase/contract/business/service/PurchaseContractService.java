package com.epcos.bidding.purchase.contract.business.service;

import com.epcos.bidding.audit.api.BizAuditRelationDto;
import com.epcos.bidding.audit.business.api.IAuditApi;
import com.epcos.bidding.biz.business.api.IBizAuditRelationApi;
import com.epcos.bidding.common.AuditTypeEnum;
import com.epcos.bidding.purchase.contract.business.api.IPurchaseContractApi;
import com.epcos.bidding.supplier.contract.business.api.ISupplierContractApi;
import com.epcos.bidding.supplier.contract.domain.dao.SupplierContractDao;
import com.epcos.bidding.supplier.contract.domain.dto.SupplierContractAuditDto;
import com.epcos.bidding.supplier.contract.domain.dto.SupplierContractUpdateDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/4 14:58
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PurchaseContractService implements IPurchaseContractApi {

    private final ISupplierContractApi supplierContractApi;
    private final IAuditApi auditApi;
    private final IBizAuditRelationApi bizAuditRelationApi;

    /**
     * 0-完成模板,1-提交至采购人确认,2-采购人确认，3-供应商确认
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean editContractTemplate(SupplierContractUpdateDto dto) {
        supplierContractApi.updateByPurchase(dto);
        supplierContractApi.updateStatusById(dto.getId(), 2);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean backContractTemplate(Long userId, SupplierContractAuditDto dto) {
        SupplierContractDao dao = supplierContractApi.getById(dto.getId());
        SupplierContractUpdateDto dto2 = new SupplierContractUpdateDto();
        dto2.setId(dto.getId());
        dto2.setAuditStatus(dto.getAuditStatus());
        supplierContractApi.updateByPurchase(dto2);
        Optional.ofNullable(
                bizAuditRelationApi.queryAuditCode(
                        BizAuditRelationDto.builder()
                                .auditType(AuditTypeEnum.WINNING_CONTRACT.getType())
                                .buyItemCode(dao.getBuyItemCode())
                                .subpackageCode(dao.getSubpackageCode())
                                .userId(userId)
                                .businessId(dao.getId())
                                .build()
                )
        ).ifPresent(auditApi::cancel);
        return Boolean.TRUE;
    }
}
