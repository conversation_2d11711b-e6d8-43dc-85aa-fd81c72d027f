package com.epcos.bidding.purchase.claims.domain.vo;

import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/23 9:44
 */
@Data
public class TenderFileInfoVo extends SuperPackageVo {

    private static final long serialVersionUID = 2601671288016063555L;
    @ApiModelProperty(value = "文件要求及驳回列表")
    List<CurrentTenderFileVo> currentTenderFileVoList;
}
