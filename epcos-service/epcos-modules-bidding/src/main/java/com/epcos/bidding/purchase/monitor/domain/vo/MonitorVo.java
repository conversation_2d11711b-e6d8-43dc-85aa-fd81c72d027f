package com.epcos.bidding.purchase.monitor.domain.vo;

import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperBuyItemVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/7 9:41
 */
@Data
public class MonitorVo extends SuperBuyItemVo {


    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "标段编号（业务使用代码）")
    private String subpackageCode;

    @ApiModelProperty(value = "标段名称")
    private String subpackageName;

    @ApiModelProperty(value = "监标人")
    private String monitorBidPerson;

    @ApiModelProperty(value = "监标人id")
    private Long monitorBidPersonId;

    @ApiModelProperty(value = "监标事项默认为两种值 ：1 监督开标  2监督评标")
    private String monitorBidType;

    @ApiModelProperty(value = "是否签字（0未签字  1已签字)")
    private Integer signStatus;

    @ApiModelProperty(value = "文件key")
    private String fileKey;
}
