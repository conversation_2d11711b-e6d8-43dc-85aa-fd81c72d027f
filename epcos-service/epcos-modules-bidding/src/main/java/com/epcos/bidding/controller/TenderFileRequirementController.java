package com.epcos.bidding.controller;

import com.epcos.bidding.purchase.claims.business.api.ITenderFileRequirementApi;
import com.epcos.bidding.purchase.claims.domain.dto.*;
import com.epcos.bidding.purchase.claims.domain.vo.TenderFileInfoVo;
import com.epcos.bidding.purchase.claims.domain.vo.TenderFileRequirementVo;
import com.epcos.common.core.domain.R;
import com.epcos.common.log.annotation.Log;
import com.epcos.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@Api(tags = "采购要求")
@RestController
@RequiredArgsConstructor
@RequestMapping("/tender.file/requirement")
public class TenderFileRequirementController {

    private final ITenderFileRequirementApi tenderFileRequirementApi;

    @ApiOperation("新增要求")
    @Log(title = "新增要求[接口：add]", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public R<Boolean> add(@RequestBody @Valid TenderFileRequirementAddDto dto) {
        tenderFileRequirementApi.add(dto);
        return R.ok();
    }

    @ApiOperation("修改要求")
    @Log(title = "修改要求[接口：updates]", businessType = BusinessType.UPDATE)
    @PostMapping("/updates")
    public R<Boolean> updates(@RequestBody @Valid List<TenderFileRequirementUpdateDto> dtos) {
        tenderFileRequirementApi.updates(dtos);
        return R.ok();
    }

    @ApiOperation("删除要求")
    @Log(title = "删除要求[接口：dels]", businessType = BusinessType.DELETE)
    @PostMapping("/dels")
    public R<Boolean> dels(@RequestBody @Valid List<TenderFileRequirementDelDto> dtos) {
        tenderFileRequirementApi.dels(dtos);
        return R.ok();
    }

    @ApiOperation("列表查询要求")
    @PostMapping("/list")
    public R<List<TenderFileRequirementVo>> list(@RequestBody @Valid TenderFileRequirementQueryDto dto) {
        List<TenderFileRequirementVo> res = tenderFileRequirementApi.list(dto);
        return R.ok(res);
    }

    @ApiOperation("查询驳回要求")
    @PostMapping("/queryReject")
    public R<TenderFileInfoVo> queryReject(@RequestBody @Valid TenderFileRequirementQueryDto dto) {
        return R.ok(tenderFileRequirementApi.queryReject(dto));
    }

    @ApiOperation("驳回")
    @Log(title = "驳回[接口：rejection]", businessType = BusinessType.UPDATE)
    @PostMapping("/rejection")
    public R<Boolean> rejection(@RequestBody @Valid TenderFileRejectionDto dto) {
        tenderFileRequirementApi.rejection(dto);
        return R.ok();
    }


}
