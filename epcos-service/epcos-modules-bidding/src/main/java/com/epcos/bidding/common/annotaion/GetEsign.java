package com.epcos.bidding.common.annotaion;

import org.springframework.core.annotation.Order;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 获取用户印章
 * return EsignAccountAndSealIdVO
 *
 * <AUTHOR>
 */
@Order(500)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface GetEsign {

    GetCommon common() default @GetCommon;

    /**
     * 用户id el
     */
    String userIdEL() default "";

    /**
     * 组织code el
     */
    String orgCodeEL() default "";

}
