package com.epcos.bidding.purchase.claims.business.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.purchase.api.domian.cliams.TenderFileRejectionDao;
import com.epcos.bidding.purchase.api.params.vo.claims.BidFileRequirementVo;
import com.epcos.bidding.purchase.api.params.vo.claims.TenderFileRequirementInfoVo;
import com.epcos.bidding.purchase.claims.business.api.ITenderFileRequirementApi;
import com.epcos.bidding.purchase.claims.domain.dao.TenderFileRequirementDao;
import com.epcos.bidding.purchase.claims.domain.dto.*;
import com.epcos.bidding.purchase.claims.domain.vo.CurrentTenderFileVo;
import com.epcos.bidding.purchase.claims.domain.vo.RequirementVo;
import com.epcos.bidding.purchase.claims.domain.vo.TenderFileInfoVo;
import com.epcos.bidding.purchase.claims.domain.vo.TenderFileRequirementVo;
import com.epcos.bidding.purchase.claims.repository.TenderFileRejectionMapper;
import com.epcos.bidding.purchase.claims.repository.TenderFileRequirementMapper;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.supplier.answer.business.api.IBidFileRequirementApi;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor
public class TenderFileRequirementService extends ServiceImpl<TenderFileRequirementMapper, TenderFileRequirementDao> implements ITenderFileRequirementApi {

    private final TenderFileRequirementMapper requirementMapper;
    private final TenderFileRejectionMapper rejectionMapper;
    private final ISubPackageApi subpackageApi;
    private final IBidFileRequirementApi bidFileRequirementApi;

    @Transactional
    public void add(TenderFileRequirementAddDto dto) {
        dto.getRequestsDetailList().forEach(r -> {
            TenderFileRequirementDao requirement = new TenderFileRequirementDao();
            requirement.setSubpackageCode(dto.getSubpackageCode());
            BeanUtils.copyProperties(r, requirement);
            requirementMapper.insert(requirement);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void updates(List<TenderFileRequirementUpdateDto> dtos) {
        dtos.forEach(i -> {
            TenderFileRequirementDao requirement = new TenderFileRequirementDao();
            BeanUtils.copyProperties(i, requirement);
            requirementMapper.updateById(requirement);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void dels(List<TenderFileRequirementDelDto> dtos) {
        List<RequirementVo> requirementVoList = bidFileRequirementApi.query(dtos.get(0).getId());
        if (!CollectionUtils.isEmpty(requirementVoList)) {
            throw new ServiceException("该条要求不可删除");
        }
        requirementMapper.deleteByIds(dtos.stream().map(TenderFileRequirementDelDto::getId).collect(Collectors.toList()));
        dtos.forEach(d -> rejectionMapper.deleteById(d.getId()));
    }

    @Transactional(rollbackFor = Exception.class)
    public void delBySubpackageCode(List<String> subpackageCodeList) {
        List<BidFileRequirementVo> fileRequirementVoList = bidFileRequirementApi.query(subpackageCodeList);
        if (!CollectionUtils.isEmpty(fileRequirementVoList)){
            List<Long> collect = fileRequirementVoList.stream().map(BidFileRequirementVo::getRequirementId).collect(Collectors.toList());
            requirementMapper.deleteByIds(collect);
            collect.forEach(d -> rejectionMapper.deleteById(d));
        }
    }

    public List<TenderFileRequirementVo> list(TenderFileRequirementQueryDto dto) {
        List<SubpackageDao> subpackageList = subpackageApi.findByBuyItemCode(dto.getBuyItemCode());
        List<TenderFileRequirementVo> voList = new ArrayList<>();
        for (SubpackageDao sub : subpackageList) {
            dto.setSubpackageCode(sub.getSubpackageCode());
            TenderFileRequirementVo vo = query(dto);
            vo.setSubpackageCode(sub.getSubpackageCode());
            vo.setSubpackageName(sub.getSubpackageName());
            List<BidFileRequirementVo> bidFileRequirementVoList =
                    bidFileRequirementApi.query(Collections.singletonList(vo.getSubpackageCode()));
            Map<String, List<BidFileRequirementVo>> bidFileRequireMap = bidFileRequirementVoList.stream()
                    .collect(Collectors.groupingBy(BidFileRequirementVo::getSubpackageCode));
            if (!CollectionUtils.isEmpty(vo.getTenderFileRequirementInfoVoList())) {
                vo.getTenderFileRequirementInfoVoList().forEach(i -> i.setBidFileRequirementList(bidFileRequireMap.get(dto.getSubpackageCode())));
            }
            voList.add(vo);
        }
        return voList;
    }

    public TenderFileRequirementVo query(TenderFileRequirementQueryDto dto) {
        TenderFileRequirementDao requirement = new TenderFileRequirementDao();
        TenderFileRequirementVo vo = new TenderFileRequirementVo();
        BeanUtils.copyProperties(dto, requirement);
        List<TenderFileRequirementDao> list = requirementMapper.select(requirement);
        if (CollectionUtils.isEmpty(list)) {
            return vo;
        }
        vo.setSubpackageCode(dto.getSubpackageCode());
        List<TenderFileRequirementInfoVo> infoVoList = list.stream()
                .map(i -> {
                    TenderFileRequirementInfoVo infoVo = new TenderFileRequirementInfoVo();
                    BeanUtils.copyProperties(i, infoVo);
                    return infoVo;
                }).collect(Collectors.toList());
        vo.setTenderFileRequirementInfoVoList(infoVoList);
        return vo;
    }


    @Transactional(rollbackFor = Exception.class)
    public void rejection(TenderFileRejectionDto dto) {
        TenderFileRejectionDao tenderFileRejection = new TenderFileRejectionDao();
        tenderFileRejection.setRequirementId(dto.getRequirementId());
        tenderFileRejection.setSupplierId(dto.getSupplierId());
        tenderFileRejection.setReason(dto.getReason());
        tenderFileRejection.setBidFileKey(dto.getBidFileKey());
        tenderFileRejection.setCreateId(SecurityUtils.getUserId());
        tenderFileRejection.setCreateName(SecurityUtils.getNickName());
        rejectionMapper.insert(tenderFileRejection);
        BidFileRejectionDto bidFileRejectionDto = new BidFileRejectionDto();
        bidFileRejectionDto.setSubpackageCode(dto.getSubpackageCode());
        bidFileRejectionDto.setSupplierId(dto.getSupplierId());
        bidFileRejectionDto.setRequirementIdList(Collections.singletonList(dto.getRequirementId()));
        bidFileRequirementApi.rejection(bidFileRejectionDto);
    }

    public List<TenderFileRejectionDao> queryRejectRecord(RejectRecordDto dto) {
        List<TenderFileRejectionDao> rejectionList = null;
        if (CollectionUtils.isEmpty(dto.getRequirementIdList())) {
            List<TenderFileRequirementDao> requirementList = requirementMapper.selectByCode(dto.getSubpackageCode());
            if (!CollectionUtils.isEmpty(requirementList)) {
                rejectionList = rejectionMapper.selectByConditions(
                        requirementList.stream().map(TenderFileRequirementDao::getId).collect(Collectors.toList()),
                        dto.getSupplierId()
                );
            }
        } else {
            rejectionList = rejectionMapper.selectByConditions(dto.getRequirementIdList(), dto.getSupplierId());
        }
        Map<Long, TenderFileRejectionDao> latestByRequirementIdMap = rejectionList.stream().collect(Collectors.toMap(
                        TenderFileRejectionDao::getRequirementId,
                        tenderFileRejection -> tenderFileRejection,
                        (existing, replacement) -> existing.getCreateAt().after(replacement.getCreateAt()) ? existing : replacement
                )
        );
        return new ArrayList<>(latestByRequirementIdMap.values());
    }

    public TenderFileInfoVo queryReject(TenderFileRequirementQueryDto dto) {
        TenderFileRequirementDao requirement = new TenderFileRequirementDao();
        TenderFileInfoVo vo = new TenderFileInfoVo();
        BeanUtils.copyProperties(dto, requirement);
        List<TenderFileRequirementDao> list = requirementMapper.select(requirement);
        if (CollectionUtils.isEmpty(list)) {
            return vo;
        }
        List<Long> requirementIdList = list.stream().map(TenderFileRequirementDao::getId).collect(Collectors.toList());
        vo.setSubpackageCode(dto.getSubpackageCode());
        List<RequirementVo> requirementVoList = bidFileRequirementApi.query(dto.getSubpackageCode(), dto.getSupplierId());
        List<TenderFileRejectionDao> rejectionList = rejectionMapper.selectByConditions(requirementIdList, dto.getSupplierId());
        Map<Long, List<TenderFileRejectionDao>> rejectMap = rejectionList.stream().collect(Collectors.groupingBy(TenderFileRejectionDao::getRequirementId));
        List<CurrentTenderFileVo> infoVoList = list.stream().map(i -> {
            CurrentTenderFileVo infoVo = new CurrentTenderFileVo();
            BeanUtils.copyProperties(i, infoVo);
            infoVo.setRejectionList(CollectionUtils.isEmpty(rejectMap.get(i.getId())) ? Collections.EMPTY_LIST : rejectMap.get(i.getId()));
            if (!CollectionUtils.isEmpty(requirementVoList)) {
                Map<Long, List<RequirementVo>> RequirementVoMap = requirementVoList.stream().collect(Collectors.groupingBy(RequirementVo::getRequirementId));
                infoVo.setStatus(Objects.isNull(RequirementVoMap.get(i.getId()).get(0).getStatus()) ? 0 : RequirementVoMap.get(i.getId()).get(0).getStatus());
                infoVo.setCurrentBidFileKey(RequirementVoMap.get(i.getId()).get(0).getBidFileKey());
            }
            return infoVo;
        }).collect(Collectors.toList());
        vo.setCurrentTenderFileVoList(infoVoList);
        return vo;
    }
}
