package com.epcos.bidding.purchase.report.domain.dao;

import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.bidding.common.BaseDao;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/29 13:38
 */
@Data
@TableName("purchase_research_report")
public class ResearchReportDao extends BaseDao implements Serializable {

    private static final long serialVersionUID = 7094376717647315457L;

    @ApiModelProperty(value = "标段code")
    private String subpackageCode;

    @ApiModelProperty(value = "调研报告文本html")
    private String researchReportText;

    @ApiModelProperty(value = "调研报告key")
    private String researchReportKey;

    @ApiModelProperty(value = "签字状态（0-未签字, 1-已签字）")
    private String signStatus;

    @ApiModelProperty(value = "签字人ID")
    private Long signUserId;

    @ApiModelProperty(value = "签字人名字")
    private String signUserName;
}
