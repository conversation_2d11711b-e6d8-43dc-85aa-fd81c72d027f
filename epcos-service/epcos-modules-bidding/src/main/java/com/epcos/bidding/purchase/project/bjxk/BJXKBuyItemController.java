package com.epcos.bidding.purchase.project.bjxk;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import com.epcos.bidding.purchase.project.bjxk.business.api.BJXKBuyJoinSubApi;
import com.epcos.bidding.purchase.project.bjxk.domain.dto.BJXKBuyItemQueryDto;
import com.epcos.bidding.purchase.project.bjxk.domain.dto.BJXKCreateBuyItemDto;
import com.epcos.bidding.purchase.project.bjxk.domain.vo.BJXKBuyItemInfoVo;
import com.epcos.bidding.purchase.project.bjxk.domain.vo.BJXKBuyItemPageVo;
import com.epcos.common.core.domain.AttachmentDto;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.utils.StringUtils;
import com.epcos.common.core.web.page.TableDataVo;
import com.epcos.common.core.web.page.TableSupport;
import com.epcos.common.log.annotation.Log;
import com.epcos.common.log.enums.BusinessType;
import com.epcos.common.security.annotation.Logical;
import com.epcos.common.security.annotation.RequiresPermissions;
import com.epcos.dingtalk.api.RemoteDingtalk;
import com.epcos.dingtalk.domain.dao.DingtalkProjectRequestDao;
import com.epcos.dingtalk.domain.dto.AttachFileDto;
import com.epcos.dingtalk.domain.dto.DingTalkProjectUpdateDto;
import com.epcos.dingtalk.domain.dto.DingtalkProjectRequestDto;
import com.epcos.dingtalk.domain.vo.DingtalkProjectRequestVo;
import com.epcos.epcfile.api.domain.vo.SysFileVo;
import com.epcos.system.api.model.FUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.epcos.common.core.constant.FileTypeNameConstants.DINGTALK_OA_ATT;
import static com.epcos.common.core.constant.FileTypeNameConstants.DING_TALK_MEETING_ATT;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/23 13:53
 */
@RequestMapping("/bjxk.buyItem")
@RestController
@Slf4j
@Api(tags = "采购项目与标段--北京胸科医院版本")
@RequiredArgsConstructor
public class BJXKBuyItemController {

    private final BJXKBuyJoinSubApi bjxkBuyJoinSubApi;
    private final RemoteDingtalk remoteDingtalk;

    @ApiOperation("查询钉钉采购项目申请列表")
    @GetMapping("/dingtalk/projectRequestPage")
    public TableDataVo<DingtalkProjectRequestVo> dingtalkProjectRequestPage(DingtalkProjectRequestDto dto) {
        return remoteDingtalk.getProjectRequestPage(TableSupport.getPageNumOrDefault(), TableSupport.getPageSizeOrDefault(), dto);
    }

    @ApiOperation("查询钉钉申购列表详细信息")
    @GetMapping("/dingtalk/dingtalkProjectInfo")
    public R<BJXKBuyItemInfoVo> dingtalkProjectInfo(@RequestParam(value = "dingTalkId") Long dingTalkId) {
        DingtalkProjectRequestDao resultDao = remoteDingtalk.getById(dingTalkId);
        BJXKBuyItemInfoVo vo = new BJXKBuyItemInfoVo();
        if (StringUtils.hasText(resultDao.getParamsAtt())) {
            List<AttachmentDto> paramsAttList = JSONArray.parseArray(resultDao.getParamsAtt(), AttachmentDto.class);
            vo.setParamsAttList(paramsAttList);
        }
        if (StringUtils.hasText(resultDao.getMeetingContent())) {
            List<AttachmentDto> meetingAttList = JSONArray.parseArray(resultDao.getMeetingContent(), AttachmentDto.class);
            vo.setMeetingAttList(meetingAttList);
        }
        BeanUtils.copyProperties(resultDao, vo);
        vo.setDingTalkId(resultDao.getId());
        return R.ok(vo);
    }

    @ApiOperation("修改钉钉申购详细信息")
    @PostMapping("/dingtalk/dingtalkEditInfo")
    public R<Boolean> dingtalkEditInfo(DingTalkProjectUpdateDto dto) {
        DingtalkProjectRequestDao dao = new DingtalkProjectRequestDao();
        BeanUtils.copyProperties(dto, dao);
        dao.setId(dto.getDingTalkId());
        String paramsJson = dingUp(dto.getParamsAttList(), DINGTALK_OA_ATT, dto.getProcessInstanceId());
        String meetingJson = dingUp(dto.getMeetingAttList(), DING_TALK_MEETING_ATT, dto.getProcessInstanceId());
        dao.setParamsAtt(paramsJson);
        dao.setMeetingContent(meetingJson);
        return R.ok(remoteDingtalk.updateById(dao));
    }

    private String dingUp(List<AttachFileDto> attList, String fileType, String instanceId) {
        String jsonString = null;
        if (!CollectionUtils.isEmpty(attList)) {
            List<SysFileVo> fileVoList = attList.stream().map(att -> {
                SysFileVo sysFileVo = new SysFileVo();
                BeanUtils.copyProperties(att, sysFileVo);
                if (Objects.nonNull(att.getFile())) {
                    String url = FUtil.upDingFile(att.getFile(), fileType, instanceId);
                    sysFileVo.setUrl(url);
                }
                return sysFileVo;
            }).collect(Collectors.toList());
            jsonString = JSONArray.toJSONString(fileVoList);
        }
        return jsonString;
    }

    @ApiOperation("确认修改详情")
    @GetMapping("/dingtalk/dingtalkEnterInfo")
    public R<Boolean> dingtalkEnterInfo(@RequestParam(value = "dingTalkId") Long dingTalkId) {
        DingtalkProjectRequestDao dingtalkProjectRequestDao = new DingtalkProjectRequestDao();
        dingtalkProjectRequestDao.setId(dingTalkId);
        dingtalkProjectRequestDao.setConfirm(1);
        return R.ok(remoteDingtalk.updateById(dingtalkProjectRequestDao));
    }


    @ApiOperation("创建采购项目与标段")
    @Log(title = "创建采购项目与标段(BJXK)", businessType = BusinessType.INSERT)
    @PostMapping("/createBuyItem")
    public R<Boolean> createBJXKBuyItem(@ModelAttribute BJXKCreateBuyItemDto dto, HttpServletRequest request) {
        if (Objects.nonNull(dto.getDingTalkId())) {
            DingtalkProjectRequestDao dingtalk = remoteDingtalk.getById(dto.getDingTalkId());
            if (Objects.nonNull(dingtalk) && dingtalk.getStatus() == 1) {
                return R.fail("该钉钉项目已被创建");
            }
        }
        Map<String, List<LinkedHashMap<String, String>>> bodyMap = BodyMapsUtil.parseBodyMaps(request);
        dto.setQuote(bodyMap, dto.getSubpackageDtoList());
        bjxkBuyJoinSubApi.createBJXKBuyItem(dto);
        return R.ok();
    }


//    @Watch(name = "buyItemCode")
    @ApiOperation("查询项目列表")
    @RequiresPermissions(value = {"purchaser:project:query", "purchaser:project:queryDept", "purchaser:project:queryAll"}, logical = Logical.OR)
    @PostMapping("/buyItemPage")
    public TableDataVo<BJXKBuyItemPageVo> bjxkBuyItemPage(@RequestBody PageSortEntity<BJXKBuyItemQueryDto> dto) {
        BJXKBuyItemQueryDto entity = dto.getEntity();
//        if (!StringUtils.hasText(entity.getOrgCode())) {
//            return new TableDataVo(Collections.EMPTY_LIST, Collections.EMPTY_LIST.size());
//        }
        entity.defaultValue("purchaser:project:queryAll");
        IPage<BJXKBuyItemPageVo> page = bjxkBuyJoinSubApi.bjxkBuyItemPage(dto);
        List<BJXKBuyItemPageVo> voList = page.getRecords();
        return new TableDataVo(voList, page.getTotal());
    }

    @ApiOperation("查询项目详细信息")
    @GetMapping("/queryBuyItemInfo")
    @ApiImplicitParam(name = "buyItemCode", value = "采购项目code", paramType = "query", dataType = "String", required = true)
    public R<BJXKBuyItemInfoVo> queryBJXKBuyItemInfo(@RequestParam(value = "buyItemCode") @NotBlank String buyItemCode) {
        return R.ok(bjxkBuyJoinSubApi.queryBJXKBuyItemInfo(buyItemCode));
    }

    @ApiOperation("根据项目code查询包信息")
    @GetMapping("/subInfo")
    @ApiImplicitParam(name = "buyItemCode", value = "采购项目code", paramType = "query", dataType = "String", required = true)
    public R<List<SuperPackageVo>> subInfo(@RequestParam(value = "buyItemCode") @NotBlank String buyItemCode) {
        return R.ok(bjxkBuyJoinSubApi.findByBuyItemCode(buyItemCode));
    }

    @ApiOperation("修改项目详细信息")
    @Log(title = "修改项目详细信息(BJXK)", businessType = BusinessType.UPDATE)
    @PostMapping("/updateBuyItemInfo")
    public R<Boolean> updateBJXKBuyItemInfo(@ModelAttribute BJXKCreateBuyItemDto dto, HttpServletRequest request) {
        Map<String, List<LinkedHashMap<String, String>>> bodyMap = BodyMapsUtil.parseBodyMaps(request);
        dto.setQuote(bodyMap, dto.getSubpackageDtoList());
        bjxkBuyJoinSubApi.updateBJXKBuyItemInfo(dto);
        return R.ok();
    }

    @ApiOperation(value = "删除项目信息")
    @Log(title = "删除项目信息(BJXK)", businessType = BusinessType.DELETE)
    @RequiresPermissions(value = {"purchaser:project:remove"})
    @GetMapping(value = "/delBuyItemInfo")
    public R<Boolean> delBJXKBuyItemInfo(@RequestParam(value = "buyItemCode") @NotBlank String buyItemCode) {
        return R.ok(bjxkBuyJoinSubApi.delBJXKBuyItemInfo(buyItemCode));
    }
}
