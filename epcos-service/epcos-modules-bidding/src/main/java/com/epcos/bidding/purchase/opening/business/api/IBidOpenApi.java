package com.epcos.bidding.purchase.opening.business.api;

import com.epcos.bidding.purchase.bulletin.domain.vo.SupplierSignVo;
import com.epcos.bidding.purchase.opening.domain.dto.PurchaseBidOpeningDto;

import java.util.List;

public interface IBidOpenApi {

    void sing(PurchaseBidOpeningDto dto);

    void start(PurchaseBidOpeningDto dto);

    void complete(PurchaseBidOpeningDto dto);

    void restart(PurchaseBidOpeningDto dto);

    List<SupplierSignVo> bidderList(String subpackageCode);
}
