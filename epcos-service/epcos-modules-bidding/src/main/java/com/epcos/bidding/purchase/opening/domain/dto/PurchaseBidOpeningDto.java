package com.epcos.bidding.purchase.opening.domain.dto;

import com.epcos.bidding.supplier.api.params.SupplierSignUpVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PurchaseBidOpeningDto implements Serializable {
    private static final long serialVersionUID = 6074227917570486738L;

    @ApiModelProperty("项目code")
    @NotBlank(message = "项目code,必填")
    private String buyItemCode;

    @ApiModelProperty("包编码")
    @NotBlank(message = "包编码，必填")
    @Length(max = 50, message = "包编码【最长：50】")
    private String subpackageCode;

    @ApiModelProperty("开标供应商")
    @NotEmpty(message = "开标供应商，不能为空")
    private List<SupplierSignUpVo> signUpList;

}
