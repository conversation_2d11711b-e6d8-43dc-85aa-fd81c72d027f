package com.epcos.bidding.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/31 16:32
 */
@Getter
public enum BulletinTimeEnum {

    RESPONSE_FILE_END_TIME("responseFileEndTime", "响应文件递交截止时间/投标文件递交截止时间"),
    PROCUREMENT_START_TIME("procurementStartTime", "采购开始时间（项目创建时间）"),
    REGISTRATION_END_TIME("registrationEndTime", "公示期（报名截止时间）"),
    MEETING_TIME("meetingTime", "开标时间"),
    REVIEW_START_TIME("reviewStartTime", "评审开始时间"),
    REVIEW_END_TIME("reviewEndTime", "评审结束时间"),
    CANDIDATE_TIME("candidateTime", "候选人公告时间"),
    RESULT_PUBLICITY_TIME("resultPublicityTime", "结果公示时间"),
    RESULT_NOTIFICATION_TIME("resultNotificationTime", "结果通知时间");


    private final String key;
    private final String desc;


    BulletinTimeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
