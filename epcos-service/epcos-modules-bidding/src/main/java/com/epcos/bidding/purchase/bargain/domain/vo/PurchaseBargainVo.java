package com.epcos.bidding.purchase.bargain.domain.vo;

import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import com.epcos.bidding.supplier.api.params.MultiSupplierQuoteFormVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/18 15:17
 */
@Data
@NoArgsConstructor
public class PurchaseBargainVo extends SuperPackageVo {

    private static final long serialVersionUID = -7335245151861834805L;

    @ApiModelProperty(value = "议价状态：1 发起  2 结束")
    private String launchStatus;

    @ApiModelProperty(value = "当前轮数")
    private Integer round;

    @ApiModelProperty(value = "总轮数")
    private List<RequireDetailVo> requireDetailList;

    @ApiModelProperty(value = "标段名称")
    private String subpackageName;

    @ApiModelProperty(value = "议价人[0-采购人议价，1-评委议价,2-没有议价]")
    private String isBargaining;

    @ApiModelProperty(value = "报价信息")
    private MultiSupplierQuoteFormVo supplierQuoteFormVo;


    public PurchaseBargainVo(String subpackageName) {
        this.subpackageName = subpackageName;
    }
}
