package com.epcos.bidding.common.aspects.project;

import com.epcos.bidding.common.annotaion.GetSimpleItem;
import com.epcos.bidding.common.aspects.AbstractMethod;
import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.common.aspects.params.GetSimpleItemParam;
import com.epcos.bidding.common.aspects.vo.GetSimpleItemVo;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/24 9:25
 */
@Slf4j
@Aspect
@Component
public class SimpleItemAspectFilterChainPost extends AbstractMethod<GetSimpleItem, GetSimpleItemVo> {

    @Autowired
    private IBuyItemApi buyItemApi;

    public SimpleItemAspectFilterChainPost() {
        super(GetUtil.GET_SIMPLE_ITEM, "获取采购项目相关信息");
    }

    @Override
    @Around(value = "@annotation(getSimpleItem)")
    public Object around(ProceedingJoinPoint point, GetSimpleItem getSimpleItem) {
        return super.around(point, getSimpleItem);
    }

    @Override
    public GetSimpleItemVo businessMethods(JoinPoint point, GetSimpleItem getSimpleItem) {
        GetSimpleItemParam param;
        try {
            param = getSimpleItemParam(point, getSimpleItem);
        } catch (InstantiationException | IllegalAccessException e) {
            log.error("检查 @GetSimpleItem 使用规则: GetSimpleItem={}", getSimpleItem, e);
            throw new ServiceException("检查 @GetSimpleItem 使用规则: " + e.getMessage());
        }
        return buyItemApi.getSimpleItemVo(param);
    }

    private GetSimpleItemParam getSimpleItemParam(JoinPoint point, GetSimpleItem anno) throws InstantiationException, IllegalAccessException {
        AspectContext context = threadLocal.get();
        String buyItemCode = context.getBuyItemCode();
        String subpackageCode = context.getSubpackageCode();
        if (!StringUtils.hasText(buyItemCode) && !StringUtils.hasText(subpackageCode)) {
            throw new ServiceException("getItem方法必须指定buyItemCodeEL或者subpackageCodeEL");
        }
        return new GetSimpleItemParam(buyItemCode, subpackageCode);
    }


}
