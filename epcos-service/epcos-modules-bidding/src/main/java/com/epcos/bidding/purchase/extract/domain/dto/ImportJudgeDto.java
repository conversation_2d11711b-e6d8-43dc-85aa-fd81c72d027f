package com.epcos.bidding.purchase.extract.domain.dto;

import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperBuyItemVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/20 10:09
 */
@Data
public class ImportJudgeDto extends SuperBuyItemVo {

    private static final long serialVersionUID = 3581940666362517635L;

    @ApiModelProperty("包名称")
    @NotBlank
    private String subpackageName;

    @ApiModelProperty("采购编号")
    @NotBlank
    private String buyItemCode;
}
