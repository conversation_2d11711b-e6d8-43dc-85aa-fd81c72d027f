package com.epcos.bidding.common;

/**
 * @Title:
 * @Description: This is a top-level abstract interface, mainly used for the conversion between objects and objects.
 * It accepts two generic parameters, namely Source and Target.
 * There are two default interfaces under this interface.
 * If you want to perform some conversions between objects, you only need to implement this interface.
 * @author:moyu
 * @version:1.0
 * @since 2023-05-25 9:38
 */
public interface DefaultMapper<Source, Target> {

    /**
     * This is an abstract method for converting a non-database object into a database object.
     * This method accepts an object of type Source as a parameter and returns an object of type Target.
     * The method will convert fields with the same property types and names in the object.
     *
     * @param source source object
     * @return
     */
    Target tObjConvertEntity(Source source);

    /**
     * This is an abstract method for converting a database object to a non-database object.
     * This method accepts an Target type parameter object and returns a Source type object.
     * The method will convert fields with the same property types and names in the object.
     *
     * @param target target object
     * @return
     */
    Source entityConvertTObj(Target target);
}
