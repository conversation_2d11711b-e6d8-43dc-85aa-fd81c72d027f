package com.epcos.bidding.purchase.project.fjnx.domain.vo;

import com.epcos.bidding.purchase.project.base.domain.vo.BuyItemBaseVo;
import com.epcos.bidding.purchase.project.base.domain.vo.SubPackageTimeVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 福建省农信版本
 * @date 2023/8/15 11:08
 */
@Data
@NoArgsConstructor
@ApiModel(description = "福建省农信版本")
public class FJNXBuyItemPageVo extends BuyItemBaseVo {

    @ApiModelProperty(value = "标段信息")
    private List<SubPackageTimeVo> subpackageTimeVoList;
}
