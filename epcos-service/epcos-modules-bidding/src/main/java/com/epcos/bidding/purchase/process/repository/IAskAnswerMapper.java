package com.epcos.bidding.purchase.process.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.epcos.bidding.purchase.process.domain.dao.AskAnswerDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/28 9:59
 */
public interface IAskAnswerMapper extends BaseMapper<AskAnswerDao> {

    List<AskAnswerDao> findSup(@Param(value = "subpackageCode") String subpackageCode, @Param(value = "askUserId") Long askUserId,
                               @Param(value = "answerUserId") Long answerUserId, @Param(value = "askUserType") String askUserType);

    List<AskAnswerDao> findJudge(@Param(value = "subpackageCode") String subpackageCode);
}
