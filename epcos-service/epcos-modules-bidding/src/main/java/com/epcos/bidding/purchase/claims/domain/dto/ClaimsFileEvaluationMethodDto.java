package com.epcos.bidding.purchase.claims.domain.dto;

import com.epcos.bidding.audit.api.BizAuditRelation;
import com.epcos.bidding.common.utils.EvaluationMethod;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileEvaluationMethodDao;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */

@Data
@ApiModel(description = "评审项")
public class ClaimsFileEvaluationMethodDto implements Serializable {

    /**
     * 标段code
     */
    private String subpackageCode;

    /**
     * 评审项
     */
    private EvaluationMethod evaluationMethod;

}
