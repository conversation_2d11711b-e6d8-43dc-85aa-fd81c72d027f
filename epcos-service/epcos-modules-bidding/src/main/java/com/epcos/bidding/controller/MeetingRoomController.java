package com.epcos.bidding.controller;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.epcos.bidding.common.annotaion.NotNullUserId;
import com.epcos.bidding.room.business.api.IMeetingRoomApi;
import com.epcos.bidding.room.business.api.IMeetingRoomScheduleApi;
import com.epcos.bidding.room.domain.dao.MeetingRoomDao;
import com.epcos.bidding.room.domain.dao.MeetingRoomScheduleDao;
import com.epcos.bidding.room.domain.dto.*;
import com.epcos.bidding.room.domain.vo.MeetingRoomScheduleVo;
import com.epcos.bidding.room.domain.vo.MeetingRoomVo;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.web.page.TableDataVo;
import com.epcos.common.redis.annotation.RedisLock;
import com.epcos.common.security.annotation.Logical;
import com.epcos.common.security.annotation.RequiresPermissions;
import com.epcos.common.core.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@Api(tags = "会议室")
@AllArgsConstructor
@RestController
@RequestMapping("/meetingRoom")
public class MeetingRoomController {

    private final IMeetingRoomApi meetingRoomApi;
    private final IMeetingRoomScheduleApi meetingRoomScheduleApi;

    @RedisLock(second = 30)
    @RequiresPermissions(value = "meeting:room:add")
    @ApiOperation("添加")
    @PostMapping("/add")
    public R<Boolean> add(@RequestBody @Valid MeetingRoomAddDto dto) {
        MeetingRoomDao dao = new MeetingRoomDao();
        BeanUtils.copyProperties(dto, dao);
        boolean save = meetingRoomApi.save(dao);
        return R.ok(save, Objects.toString(dao.getId()));
    }

    @RedisLock(second = 30)
    @RequiresPermissions(value = "meeting:room:edit")
    @ApiOperation("修改")
    @PostMapping("/update")
    public R<Boolean> update(@RequestBody @Valid MeetingRoomUpdateDto dto) {
        MeetingRoomDao updateDao = new MeetingRoomDao();
        BeanUtils.copyProperties(dto, updateDao);
        updateDao.setId(dto.getRoomId());
        boolean res = meetingRoomApi.updateById(updateDao);
        return R.ok(res);
    }

    @RedisLock(second = 30)
    @RequiresPermissions(value = "meeting:room:edit")
    @ApiOperation("启用，禁用")
    @PostMapping("/enabled")
    public R<Boolean> enabled(@RequestBody @Valid MeetingRoomEnabledDto dto) {
        MeetingRoomDao updateDao = new MeetingRoomDao();
        updateDao.setId(dto.getRoomId());
        updateDao.setStatus(dto.getStatus());
        boolean res = meetingRoomApi.updateById(updateDao);
        return R.ok(res);
    }

    @RequiresPermissions(value = "meeting:room:query")
    @ApiOperation("查询会议室")
    @GetMapping("/query")
    public R<MeetingRoomVo> query(@RequestParam("roomId") Long roomId) {
        MeetingRoomVo res = Optional.ofNullable(meetingRoomApi.getById(roomId))
                .map(i -> {
                    MeetingRoomVo vo = new MeetingRoomVo();
                    BeanUtils.copyProperties(i, vo);
                    vo.setRoomId(i.getId());
                    return vo;
                })
                .orElse(null);
        return R.ok(res);
    }

    @RedisLock(second = 30)
    @RequiresPermissions(value = "meeting:room:remove")
    @ApiOperation("删除会议室")
    @PostMapping("/delete")
    public R<Boolean> delete(@RequestBody Set<Long> roomIds) {
        meetingRoomApi.delete(roomIds);
        return R.ok();
    }

    @RequiresPermissions(value = {"meeting:room:list", "room:reservation:list"}, logical = Logical.OR)
    @ApiOperation("分页查询")
    @PostMapping("/page")
    public TableDataVo<MeetingRoomVo> page(@RequestBody @Valid MeetingRoomPageDto dto) {
        MeetingRoomDao queryDao = new MeetingRoomDao();
        BeanUtils.copyProperties(dto, queryDao);
        Page<MeetingRoomDao> page = meetingRoomApi.page(
                Page.of(dto.getPageNum(), dto.getPageSize()),
                meetingRoomApi.queryWrapper(queryDao));
        List<MeetingRoomVo> list = page.getRecords().stream().map(i -> {
            MeetingRoomVo vo = new MeetingRoomVo();
            BeanUtils.copyProperties(i, vo);
            vo.setRoomId(i.getId());
            return vo;
        }).collect(Collectors.toList());
        return new TableDataVo<>(list, page.getTotal());
    }

    @RequiresPermissions(value = "meeting:room:audit")
    @ApiOperation("预约审核")
    @PostMapping("/schedule/audit")
    public R<Boolean> scheduleAudit(@RequestBody @Valid MeetingRoomScheduleAuditDto dto) {
        MeetingRoomScheduleDao updateDao = new MeetingRoomScheduleDao();
        updateDao.setId(dto.getRoomScheduleId());
        updateDao.setAudit(dto.getAudit());
        updateDao.setStatement(dto.getStatement());
        boolean res = meetingRoomScheduleApi.updateById(updateDao);
        return R.ok(res);
    }

    @RequiresPermissions(value = {"meeting:room:list", "room:reservation:list"}, logical = Logical.OR)
    @ApiOperation("预约分页查询")
    @GetMapping("/schedule/page")
    public TableDataVo<MeetingRoomScheduleVo> schedulePage(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                           @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                           @RequestParam(value = "roomId") Long roomId,
                                                           @RequestParam(value = "audit", required = false) Integer audit
    ) {
        Page<MeetingRoomScheduleDao> page = meetingRoomScheduleApi.page(
                Page.of(pageNum, pageSize),
                meetingRoomScheduleApi.queryWrapper(roomId, audit));
        List<MeetingRoomScheduleVo> list = page.getRecords().stream().map(i -> {
            MeetingRoomScheduleVo vo = new MeetingRoomScheduleVo();
            BeanUtils.copyProperties(i, vo);
            vo.setRoomScheduleId(i.getId());
            return vo;
        }).collect(Collectors.toList());
        return new TableDataVo<>(list, page.getTotal());
    }


    /**
     * 审批编号 202410311124000086531
     *
     * @param dto
     * @return
     */
    @RedisLock(second = 30)
    @NotNullUserId
    @RequiresPermissions("room:reservation:add")
    @ApiOperation("添加预约")
    @PostMapping("/schedule/add")
    public R<Boolean> scheduleAdd(@RequestBody @Valid MeetingRoomScheduleAddDto dto) {
        MeetingRoomScheduleDao dao = new MeetingRoomScheduleDao();
        BeanUtils.copyProperties(dto, dao);
        dao.setUserId(SecurityUtils.getUserId());
//        if (meetingRoomScheduleApi.count(meetingRoomScheduleApi.queryWrapper(dao)) > 0) {
//            return R.fail("当前时间段已经有预约");
//        }
        check(dto);
        dao.setNikeName(SecurityUtils.getNickName());
        dao.setPhone(SecurityUtils.getLoginUser().getSysUser().getPhonenumber());
        boolean save = meetingRoomScheduleApi.save(dao);
        return R.ok(save);
    }

    /**
     * 检查当前时间段是否有预约
     *
     * @param dto 预约信息
     */
    public void check(MeetingRoomScheduleAddDto dto) {
        List<MeetingRoomScheduleDao> roomList = meetingRoomScheduleApi.list(Wrappers.lambdaQuery(MeetingRoomScheduleDao.class)
                .eq(MeetingRoomScheduleDao::getRoomId, dto.getRoomId())
                .gt(MeetingRoomScheduleDao::getEndTime, dto.getStartTime()));
        if (!CollectionUtils.isEmpty(roomList)) {
            for (MeetingRoomScheduleDao room : roomList) {
                boolean overlapping = isOverlapping(room.getStartTime(), room.getEndTime(), dto.getStartTime(), dto.getEndTime());
                if (overlapping) {
                    throw new ServiceException("当前时间段已经有预约");
                }
            }
        }
    }

    /**
     * 判断两个时间段是否有重叠
     *
     * @param date1 第一个时间段的开始时间
     * @param date2 第一个时间段的结束时间
     * @param date3 第二个时间段的开始时间
     * @param date4 第二个时间段的结束时间
     * @return
     */
    public boolean isOverlapping(Date date1, Date date2, Date date3, Date date4) {
        // 确保 date1 <= date2 和 date3 <= date4
        if (date1.after(date2) || date3.after(date4)) {
            throw new IllegalArgumentException("起始日期不能晚于结束日期");
        }
        // 检查是否有重叠
        return !date1.after(date4) && !date2.before(date3);
    }


    @NotNullUserId
    @RequiresPermissions("room:reservation:query")
    @ApiOperation("查询我的预约")
    @PostMapping("/schedule/myList")
    public R<List<MeetingRoomScheduleVo>> scheduleMyList() {
        MeetingRoomScheduleDao queryDao = new MeetingRoomScheduleDao();
        queryDao.setUserId(SecurityUtils.getUserId());
        List<MeetingRoomScheduleVo> list = meetingRoomScheduleApi.list(queryDao)
                .stream()
                .map(i -> {
                    MeetingRoomScheduleVo vo = new MeetingRoomScheduleVo();
                    BeanUtils.copyProperties(i, vo);
                    vo.setRoomScheduleId(i.getId());
                    Optional.ofNullable(meetingRoomApi.getById(i.getRoomId()))
                            .ifPresent(s -> vo.setName(s.getName()));
                    return vo;
                }).collect(Collectors.toList());
        return R.ok(list);
    }

    /**
     * 取消即删除
     *
     * @param roomScheduleIds 预约计划id
     * @return
     */
    @ApiOperation("取消预约")
    @GetMapping("/cancel")
    public R<Boolean> cancel(@RequestParam(value = "roomScheduleIds") Long[] roomScheduleIds) {
        return R.ok(meetingRoomScheduleApi.remove(meetingRoomScheduleApi.deleteWrapper(roomScheduleIds)));
    }

}
