package com.epcos.bidding.purchase.monitor.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/7 9:42
 */
@Data
public class MonitorInfoVo {

    @ApiModelProperty(value = "标段编号")
    private String subpackageCode;

    @ApiModelProperty(value = "标段名称")
    private String subpackageName;

    @ApiModelProperty(value = "监标人")
    private String monitorBidPerson;

    @ApiModelProperty(value = "监标人id")
    private Long monitorBidPersonId;
}
