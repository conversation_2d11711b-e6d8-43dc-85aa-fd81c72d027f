package com.epcos.bidding.common.aspects.filter.validator;

import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileDao;

/**
 * 采购文件未发布
 */
public class ClaimsFileNotPublishedValidator implements ResultPostHandlerFilterChain<ClaimsFileDao> {
    @Override
    public void postHandler(AspectContext context, ClaimsFileDao result) {
        result.verifyNotPublished();
    }

}
