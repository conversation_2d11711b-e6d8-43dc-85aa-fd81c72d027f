package com.epcos.bidding.purchase.bargain.business.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.common.annotaion.GetCommon;
import com.epcos.bidding.common.annotaion.GetItem;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.common.aspects.vo.GetItemVo;
import com.epcos.bidding.purchase.api.params.AttributeVo;
import com.epcos.bidding.purchase.api.params.dto.bargin.StartOrEndBargainDto;
import com.epcos.bidding.purchase.bargain.business.api.IPurchaseBargainApi;
import com.epcos.bidding.purchase.bargain.domain.dao.PurchaseBargainDao;
import com.epcos.bidding.purchase.bargain.domain.dto.UpBargainTimeDto;
import com.epcos.bidding.purchase.bargain.domain.vo.PurchaseBargainVo;
import com.epcos.bidding.purchase.bargain.mapping.PurchaseBargainConvert;
import com.epcos.bidding.purchase.bargain.repository.IPurchaseBargainMapper;
import com.epcos.bidding.purchase.process.business.api.IReviewBeforeApi;
import com.epcos.bidding.purchase.process.domain.dao.ReviewBeforeDao;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.purchase.project.mapping.DefaultVoAndDtoConvert;
import com.epcos.bidding.purchase.remote.RemoteSupplierApi;
import com.epcos.bidding.purchase.remote.RemoteToOtherServiceApi;
import com.epcos.bidding.supplier.api.params.MultiSupplierQuoteFormVo;
import com.epcos.bidding.supplier.api.params.SupplierQuoteFormVo;
import com.epcos.bidding.supplier.api.params.SupplierSignUpVo;
import com.epcos.bidding.supplier.sign.business.api.ISupplierBiddingApi;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.DateUtils;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.utils.StringUtils;
import com.epcos.system.api.domain.assmble.vo.FunctionKV;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.epcos.bidding.common.enums.FunctionEnum.*;
import static com.epcos.common.core.constant.PurchaseConstants.Currency.ONE;
import static com.epcos.common.core.constant.PurchaseConstants.Currency.TWO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/18 14:03
 */
@Slf4j
@Service
public class PurchaseBargainService extends ServiceImpl<IPurchaseBargainMapper, PurchaseBargainDao> implements IPurchaseBargainApi {


    private final IReviewBeforeApi reviewBeforeApi;
    private final RemoteSupplierApi remoteSupplierApi;
    private final RemoteToOtherServiceApi remoteToOtherServiceApi;
    private final DefaultVoAndDtoConvert defaultVoAndDtoConvert;
    @Autowired
    private ISupplierBiddingApi iSupplierBiddingApi;

    public PurchaseBargainService(IReviewBeforeApi reviewBeforeApi, RemoteSupplierApi remoteSupplierApi,
                                  RemoteToOtherServiceApi remoteToOtherServiceApi, DefaultVoAndDtoConvert defaultVoAndDtoConvert) {
        this.reviewBeforeApi = reviewBeforeApi;
        this.remoteSupplierApi = remoteSupplierApi;
        this.remoteToOtherServiceApi = remoteToOtherServiceApi;
        this.defaultVoAndDtoConvert = defaultVoAndDtoConvert;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean startOrEndBargain(StartOrEndBargainDto dto) {
        if ("1".equals(dto.getLaunchStatus())) {
            bargainValidate(dto);
            Date afterDate = DateUtils.getAfterDate(dto.getSustainedTime());
            PurchaseBargainDao convert = PurchaseBargainConvert.INSTANCE.convert(afterDate, dto);
            return save(convert);
        }
        PurchaseBargainDao purchaseBargainDao = new PurchaseBargainDao();
        purchaseBargainDao.setLaunchStatus(dto.getLaunchStatus());
        return update(purchaseBargainDao, updateWrapper(dto.getSubpackageCode(), dto.getRound()));
    }

    private void bargainValidate(StartOrEndBargainDto dto) {
        if (Objects.isNull(dto.getInitiateId())) {
            dto.setInitiateId(SecurityUtils.getUserId());
        }
        //判断前一轮议价是否结束
        List<PurchaseBargainDao> roundList = find(dto.getSubpackageCode());
        if (!CollectionUtils.isEmpty(roundList)) {
            roundList.sort(Comparator.comparing(PurchaseBargainDao::getRound));
            Integer lastRound = roundList.get(0).getRound();
            if (lastRound.equals(dto.getRound())) {
                throw new ServiceException("当前轮已开启, 请勿重复操作");
            }
            if ((dto.getLaunchStatus().equals(roundList.get(0).getLaunchStatus()))) {
                throw new ServiceException("请先结束第" + (dto.getRound() - 1) + "轮议价");
            }
        }
    }

    @Override
    @GetItem(common = @GetCommon(buyItemCodeEL = "#buyItemCode"), querySubpackage = true)
    public List<PurchaseBargainVo> bargainList(String buyItemCode) {

        //获取项目及标段信息
        GetItemVo itemVo = GetUtil.getItemVo();
        // 采购功能英文名称集合
        List<String> functionKeyList = itemVo.getBuyItemVo().getFunctionKVList()
                .stream()
                .map(FunctionKV::getPurchaseFunctionKey)
                .collect(Collectors.toList());

        // 标段code集合
        List<String> subCodeList = itemVo.getSubpackageDaoList()
                .stream()
                .map(SubpackageDao::getSubpackageCode)
                .collect(Collectors.toList());

        // 根据标段查询答疑表
        List<ReviewBeforeDao> beforeDaoList = reviewBeforeApi.find(subCodeList);
        // 根据标段code分组
        Map<String, List<ReviewBeforeDao>> beforeMap = beforeDaoList.stream()
                .collect(Collectors.groupingBy(ReviewBeforeDao::getSubpackageCode));

        List<PurchaseBargainVo> voList = itemVo.getSubpackageDaoList()
                .stream().map(sub -> {
                    List<SupplierSignUpVo> upVoList = remoteSupplierApi.getSignUp(sub.getSubpackageCode());
                    List<SupplierSignUpVo> filterUpList = filterUpList(upVoList, functionKeyList);
                    PurchaseBargainVo vo = new PurchaseBargainVo(sub.getSubpackageName());
                    vo.setSubpackageCode(sub.getSubpackageCode());
                    if (CollectionUtils.isEmpty(filterUpList)) {
                        return vo;
                    }
                    List<PurchaseBargainDao> bargainList = find(sub.getSubpackageCode());
                    //是否有客户端
                    boolean hasClient = functionKeyList.contains(PURCHASER_PURCHASE_FILE.getKey());
                    assemble(filterUpList, sub.getSubpackageCode(), vo, hasClient);
                    defaultVoAndDtoConvert.dtoAndVoConvert(sub.getSubpackageCode(), vo, bargainList, beforeMap.get(sub.getSubpackageCode()), hasClient);
                    return vo;
                }).collect(Collectors.toList());
        return voList;
    }

    /**
     * 如果有开标功能，议价的供应商就需要筛选出能进入评审的
     * 如果没有开标表功能，但是有设置了是否合格供应商，则需要筛选出合格的供应商
     * 吐过都没有，则返回原列表
     *
     * @param upVoList
     * @param functionKeyList
     * @return
     */
    private List<SupplierSignUpVo> filterUpList(List<SupplierSignUpVo> upVoList, List<String> functionKeyList) {
        if (functionKeyList.contains(PURCHASER_BID_OPEN.getKey())) {
            return upVoList.stream().filter(up -> String.valueOf(ONE).equals(up.getEnterTheReview())).collect(Collectors.toList());
        } else if (functionKeyList.contains(PURCHASER_SET_SUPPLIER_QUALIFIED.getKey())) {
            return upVoList.stream().filter(up -> Objects.nonNull(up.getQualified()) && up.getQualified()).collect(Collectors.toList());
        }
        return upVoList;
    }


    private void assemble(List<SupplierSignUpVo> filterUpList, String subpackageCode, PurchaseBargainVo vo, boolean hasClient) {
        List<SupplierQuoteFormVo> voList = new ArrayList<>();

        //
        Set<Long> supplierIds = filterUpList.stream().map(i -> i.getSupplierId()).collect(Collectors.toSet());
        Map<Long, Boolean> queriedBidOpeningDecrypt = iSupplierBiddingApi.queryBidOpeningDecrypt(subpackageCode, supplierIds);


        MultiSupplierQuoteFormVo formVo = new MultiSupplierQuoteFormVo();
        for (SupplierSignUpVo s : filterUpList) {
            formVo = remoteSupplierApi.getBargainInfo(subpackageCode, s.getSupplierId(), null);
            List<String> fileHeadList = formVo.getHeads().stream().filter(f -> "file".equals(f.getKeyType())).map(AttributeVo::getKeyVal).collect(Collectors.toList());
            for (SupplierQuoteFormVo i : formVo.getSupplierQuoteFormList()) {
                i.getRoundQuoteFormList().stream().forEach(r->{
                    for (LinkedHashMap<String, String> b : r.getBodyMaps()) {
                        b.entrySet().stream().filter(e -> fileHeadList.contains(e.getKey())).forEach(e -> {
                            if (Boolean.FALSE.equals(queriedBidOpeningDecrypt.get(i.getSupplierId()))) {
                                b.put(e.getKey(), null);
                            }
                        });
                    }
                });
            }


            //没有客户端的情况
            if (!hasClient) {
                notClient(formVo, s, voList);
            } else {
                if (CollectionUtils.isEmpty(formVo.getSupplierQuoteFormList())) {
                    voList.add(fillQuote(s));
                } else {
                    voList.addAll(formVo.getSupplierQuoteFormList());
                }
                voList.forEach(sup -> {
                    if (StringUtils.isBlank(sup.getSupplierCompanyName())) {
                        sup.setSupplierCompanyName(remoteToOtherServiceApi.getSysUserInfo(sup.getSupplierId()).getNickName());
                    }
                });
            }
        }
        voList.forEach(f -> filterUpList.forEach(u -> {
            if (f.getSupplierId().equals(u.getSupplierId())) {
                f.setSupplierCompanyName(u.getSupplierSignUpInfo().getBidderName());
            }
        }));
        formVo.setSupplierQuoteFormList(voList);
        vo.setSupplierQuoteFormVo(formVo);
    }

    private void notClient(MultiSupplierQuoteFormVo formVo, SupplierSignUpVo supplierSignUpVo, List<SupplierQuoteFormVo> voList) {
        //没有发起过议价
        List<Long> quoteSupplierIdList = formVo.getSupplierQuoteFormList().stream()
                .map(SupplierQuoteFormVo::getSupplierId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(formVo.getSupplierQuoteFormList())) {
            voList.add(fillQuote(supplierSignUpVo));
        } else {
            Map<Long, SupplierQuoteFormVo> formVoMap = formVo.getSupplierQuoteFormList().stream()
                    .collect(Collectors.toMap(SupplierQuoteFormVo::getSupplierId, Function.identity()));
            if (quoteSupplierIdList.contains(supplierSignUpVo.getSupplierId())) {
                voList.add(formVoMap.getOrDefault(supplierSignUpVo.getSupplierId(), null));
            } else {
                voList.add(fillQuote(supplierSignUpVo));
            }
        }
    }

    private SupplierQuoteFormVo fillQuote(SupplierSignUpVo supplier) {
        SupplierQuoteFormVo supplierQuoteFormVo = new SupplierQuoteFormVo();
        supplierQuoteFormVo.setSupplierId(supplier.getSupplierId());
        supplierQuoteFormVo.setSupplierCompanyName(supplier.getSupplierSignUpInfo().getBidderName());
        supplierQuoteFormVo.setRoundQuoteFormList(Collections.emptyList());
        return supplierQuoteFormVo;
    }

    @Override
    public PurchaseBargainDao findBy(String subpackageCode) {
        List<PurchaseBargainDao> daoList = find(subpackageCode);
        if (CollectionUtils.isEmpty(daoList)) {
            return null;
        }
        return daoList.get(0);
    }

    @Override
    public void delBargainInfo(String subpackageCode) {
        //清除供应商议价数据
        remoteSupplierApi.delSupplierBargainInfo(subpackageCode);
        //清除 采购人议价数据
        remove(updateWrapper(subpackageCode, null));
    }

    @Override
    public Boolean updateBargainTime(List<UpBargainTimeDto> dtoList) {
        for (UpBargainTimeDto dto : dtoList) {
            //查询最新论的报价
            PurchaseBargainDao bargainDao = findBy(dto.getSubpackageCode());
            if (Objects.isNull(bargainDao)) {
                return Boolean.TRUE;
            }
            if (Integer.parseInt(bargainDao.getLaunchStatus()) == TWO) {
                return Boolean.TRUE;
            }
            bargainDao.setCountdown(DateUtils.getAfterDate(dto.getCountdown()));
            bargainDao.setServiceRequire(dto.getServiceRequire());
            updateById(bargainDao);
            return Boolean.TRUE;
        }
        return Boolean.TRUE;
    }

    @Override
    public Integer findMaxRound(String subpackageCode) {
        List<PurchaseBargainDao> bargainDaos = find(subpackageCode);
        return bargainDaos.stream()
                .max(Comparator.comparing(PurchaseBargainDao::getRound))
                .map(PurchaseBargainDao::getRound)
                .orElse(null);
    }

    @Override
    public List<PurchaseBargainDao> findMaxBargain(String subpackageCode) {
        List<PurchaseBargainDao> bargainDaos = find(subpackageCode, "2");
        return bargainDaos.stream()
                .collect(Collectors.groupingBy(PurchaseBargainDao::getRound))
                .entrySet()
                .stream()
                .max(Map.Entry.comparingByKey())
                .map(Map.Entry::getValue)
                .orElse(Collections.emptyList());

    }
}
