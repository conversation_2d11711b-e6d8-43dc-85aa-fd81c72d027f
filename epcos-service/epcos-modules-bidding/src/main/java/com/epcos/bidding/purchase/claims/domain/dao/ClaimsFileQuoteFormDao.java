package com.epcos.bidding.purchase.claims.domain.dao;

import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.bidding.common.SubpackageCodeEntity;
import com.epcos.common.core.web.validator.CreateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 采购人标段报价表
 */
@Data
@ApiModel(description = "采购人标段报价表")
@NoArgsConstructor
@TableName("claims_file_quote_form")
public class ClaimsFileQuoteFormDao extends SubpackageCodeEntity {

    @ApiModelProperty("报价表头")
    @NotBlank(message = "报价表头，必填", groups = CreateGroup.class)
    private String headJson;

    @ApiModelProperty("报价内容")
    @NotBlank(message = "报价内容，必填", groups = CreateGroup.class)
    private String bodyJson;

    public ClaimsFileQuoteFormDao(String subpackageCode) {
        this(subpackageCode, null, null);
    }

    public ClaimsFileQuoteFormDao(String subpackageCode, String headJson) {
        this(subpackageCode, headJson, null);
    }

    public ClaimsFileQuoteFormDao(String subpackageCode, String headJson, String bodyJson) {
        this.subpackageCode = subpackageCode;
        this.headJson = headJson;
        this.bodyJson = bodyJson;
    }

    public ClaimsFileQuoteFormDao bySubpackageCode(String subpackageCode) {
        ClaimsFileQuoteFormDao claimsFileQuoteFormDao = new ClaimsFileQuoteFormDao();
        claimsFileQuoteFormDao.setSubpackageCode(subpackageCode);
        return claimsFileQuoteFormDao;
    }

}
