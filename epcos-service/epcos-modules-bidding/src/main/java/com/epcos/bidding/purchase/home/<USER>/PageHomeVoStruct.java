package com.epcos.bidding.purchase.home.mapping;

import com.alibaba.fastjson.JSONArray;
import com.epcos.bidding.common.DefaultMapper;
import com.epcos.bidding.purchase.api.params.AttributeVo;
import com.epcos.bidding.purchase.bulletin.domain.dao.BulletinDao;
import com.epcos.common.core.domain.AttachmentDto;
import com.epcos.bidding.purchase.bulletin.domain.dto.BulletinSupplierDto;
import com.epcos.bidding.purchase.home.domain.vo.BulletinSimpleVo;
import com.epcos.bidding.purchase.home.domain.vo.PageHomeInfoVo;
import com.epcos.bidding.purchase.home.domain.vo.PageHomeSimpleVo;
import com.epcos.bidding.purchase.home.domain.vo.PageHomeVo;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.common.core.utils.StringUtils;
import com.epcos.common.core.utils.bean.BeanUtils;
import com.epcos.system.api.domain.SysOrganize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.epcos.bidding.common.enums.BulletinTypeEnum.BIDDING_ANNOUNCEMENT;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/8 16:17
 */
@Mapper(componentModel = "spring")
public interface PageHomeVoStruct extends DefaultMapper<PageHomeVo, BulletinDao> {

    @Mapping(source = "id", target = "bulletinId")
    PageHomeVo asVo(Long id, BulletinDao bulletinDao);

    default PageHomeInfoVo asVo(List<SysOrganize> orgInfo, List<SubpackageDao> subpackageDaoList,
                                BulletinDao bulletinDao, BuyItemDao buyItemDao, List<BulletinDao> otherBulletinList,
                                String innerCode) {
        PageHomeInfoVo infoVo = new PageHomeInfoVo();
        //获取服务器当前时间
        infoVo.setServerTime(new Date());
        BeanUtils.copyProperties(bulletinDao, infoVo);
        if (StringUtils.hasText(bulletinDao.getAnnexKey())) {
            infoVo.setAttachmentDtoList(JSONArray.parseArray(bulletinDao.getAnnexKey(), AttachmentDto.class));
        }
        List<PageHomeSimpleVo> voList = subpackageDaoList.stream().map(s -> {
            //如果是招标公告
            if (BIDDING_ANNOUNCEMENT.getKey().equals(bulletinDao.getBulletinType())
                    || s.getSubpackageCode().equals(bulletinDao.getSubpackageCode())) {
                PageHomeSimpleVo simpleVo = new PageHomeSimpleVo();
                List<AttributeVo> supplierAddtionList = new ArrayList<>();
                if (StringUtils.hasText(bulletinDao.getSupplierAddition())) {
                    List<BulletinSupplierDto> bulletinSupplierDtoList = JSONArray.parseArray(bulletinDao.getSupplierAddition(), BulletinSupplierDto.class);
                    if (!CollectionUtils.isEmpty(bulletinSupplierDtoList)) {
                        bulletinSupplierDtoList.forEach(sup -> {
                            if (sup.getSubpackageCode().equals(s.getSubpackageCode())) {
                                supplierAddtionList.addAll(sup.getAttributeVoList());
                            }
                        });
                    }
                }
                simpleVo.setAttributeVoList(supplierAddtionList);
                simpleVo.setRegistrationEndTime(s.getRegistrationEndTime());
                simpleVo.setSubpackageCode(s.getSubpackageCode());
                simpleVo.setSubpackageName(s.getSubpackageName());
                return simpleVo;
            }
            return null;
        }).filter(v -> Objects.nonNull(v)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(otherBulletinList)) {
            List<BulletinSimpleVo> simpleVoList = otherBulletinList.stream().map(b -> {
                BulletinSimpleVo simpleVo = new BulletinSimpleVo();
                BeanUtils.copyProperties(b, simpleVo);
                simpleVo.setBulletinId(b.getId());
                return simpleVo;
            }).collect(Collectors.toList());
            infoVo.setBulletinSimpleVoList(simpleVoList);
        }
        infoVo.setSimpleVoList(voList);
        infoVo.setOrgCode(orgInfo.get(0).getOrgCode());
        infoVo.setTenderName(orgInfo.get(0).getOrgName());
        infoVo.setBuyItemCode(bulletinDao.getBuyItemCode());
        infoVo.setCreateAt(buyItemDao.getCreateAt());
        infoVo.setYearMonthSplit(buyItemDao.getYearMonthSplit());
        infoVo.setInnerCode(innerCode);
        infoVo.setBuyItemName(buyItemDao.getBuyItemName());
        return infoVo;
    }

}
