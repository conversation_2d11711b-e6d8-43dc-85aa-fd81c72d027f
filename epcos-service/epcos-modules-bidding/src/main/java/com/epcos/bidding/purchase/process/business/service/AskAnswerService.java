package com.epcos.bidding.purchase.process.business.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.purchase.api.params.dto.AskDto;
import com.epcos.bidding.purchase.api.params.dto.ReplyDto;
import com.epcos.bidding.purchase.api.params.vo.answer.AnswerVo;
import com.epcos.bidding.purchase.process.business.api.IAskAnswerApi;
import com.epcos.bidding.purchase.process.domain.dao.AskAnswerDao;
import com.epcos.bidding.purchase.process.mapping.AskAnswerConvert;
import com.epcos.bidding.purchase.process.repository.IAskAnswerMapper;
import com.epcos.bidding.purchase.remote.RemoteToOtherServiceApi;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/28 9:57
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AskAnswerService extends ServiceImpl<IAskAnswerMapper, AskAnswerDao> implements IAskAnswerApi {

    private final RemoteToOtherServiceApi remoteToOtherServiceApi;
    private final IAskAnswerMapper askAnswerMapper;


    @Override
    public Boolean submitAsk(AskDto dto) {
        return save(AskAnswerConvert.INSTANCE.convert(dto));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean replyAsk(ReplyDto dto) {
        AskAnswerDao askAnswerDao = new AskAnswerDao();
        BeanUtils.copyProperties(dto, askAnswerDao);
        return updateById(askAnswerDao);
    }


    @Override
    public List<AnswerVo> answerList(String subpackageCode, Long userId, String askUserType) {
        List<AskAnswerDao> answerDaoList = new ArrayList<>();
        Boolean tender = remoteToOtherServiceApi.isTender(userId);
        Boolean supp = remoteToOtherServiceApi.isSupp(userId);
        if (tender) {
            answerDaoList = askAnswerMapper.findSup(subpackageCode, null, null, askUserType);
        }
        if (supp) {
            if ("1".equals(askUserType)) {
                answerDaoList = askAnswerMapper.findSup(subpackageCode, userId, null, askUserType);
            } else {
                answerDaoList = askAnswerMapper.findSup(subpackageCode, null, userId, askUserType);
            }
        }
        if (!tender && !supp) {
            answerDaoList = askAnswerMapper.findSup(subpackageCode, null, null, askUserType);
        }
        List<AnswerVo> collect = answerDaoList.stream()
                .map(AskAnswerConvert.INSTANCE::convert)
                .collect(Collectors.toList());
        return collect;
    }
}
