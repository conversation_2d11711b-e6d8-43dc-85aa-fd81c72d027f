package com.epcos.bidding.common.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/1 10:37
 */
public enum BulletinTypeEnum {

    BIDDING_ANNOUNCEMENT("bidding_announcement", "招标公告"),
    CLARIFY_ANNOUNCEMENT("clarify_announcement", "澄清公告"),
    CHANGE_ANNOUNCEMENT("change_announcement", "变更公告"),
    ABANDON_ANNOUNCEMENT("abandon_announcement", "废标公告"),
    WIN_CANDIDATE_PUBLICITY("win_candidate_publicity", "中标候选人公示"),
    WIN_RESULT_PUBLICITY("win_result_publicity", "中标结果公示"),
    WIN_RESULT_NOTICE("win_result_notice", "中标结果通知书"),
    NOT_WIN_RESULT_NOTICE("not_win_result_notice", "未中标结果通知书");


    private final String key;
    private final String desc;

    public final String getKey() {
        return key;
    }

    public final String getDesc() {
        return desc;
    }

    BulletinTypeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
