package com.epcos.bidding.purchase.process.domain.dao;

import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.bidding.common.SubpackageCodeEntity;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import static com.epcos.common.core.constant.PurchaseConstants.Currency.ONE;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/12 8:49
 */
@ApiModel(description = "答疑表")
@Data
@TableName("purchase_review_before")
public class ReviewBeforeDao extends SubpackageCodeEntity implements Serializable {


    @ApiModelProperty(value = "供应商id")
    private Long supplierId;

    @ApiModelProperty(value = "采购人确认评标结果【0-未确认，1-确认】")
    private String bidEnterExpert;

    @ApiModelProperty(value = "是否确认会签[0-未确认，1-已确认]")
    private String confirmCounterSign;

    @ApiModelProperty(value = "是否确认评审[0-未确认，1-已确认]")
    private String confirmReview;

    @ApiModelProperty(value = "确认评审时间")
    private Date confirmReviewTime;

    @ApiModelProperty(value = "评审结束时间")
    private Date reviewEndTime;

    @Deprecated
    @ApiModelProperty(value = "是否允许在评委列表上显示[0-不显示，1-显示]")
    private String whetherShowJudge;

    @ApiModelProperty(value = "是否允许进入评审,0-不允许，1-允许")
    private String enterTheReview;

    @ApiModelProperty(value = "议价人[0-采购人议价，1-评委议价,2-没有议价]")
    private String isBargaining;

    public ReviewBeforeDao verifyCounterSign() {
        if (StringUtils.hasText(getConfirmCounterSign()) && String.valueOf(ONE).equals(getConfirmCounterSign())) {
            throw new ServiceException("已确认会签 请勿重复点击");
        }
        return this;
    }

    public ReviewBeforeDao verifyConfirmReview() {
        if (StringUtils.hasText(getConfirmReview()) && String.valueOf(ONE).equals(getConfirmReview())) {
            throw new ServiceException("已确认评审 请勿重复点击");
        }
        return this;
    }

}
