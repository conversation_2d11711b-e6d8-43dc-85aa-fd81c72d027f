package com.epcos.bidding.purchase.project.bjxk.domain.vo;

import com.epcos.bidding.purchase.api.params.SubPackageInfoVo;
import com.epcos.bidding.purchase.project.base.domain.vo.BuyItemBaseVo;
import com.epcos.common.core.domain.AttachmentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/23 15:05
 */
@Data
@ApiModel(description = "北京胸科医院版本")
public class BJXKBuyItemInfoVo extends BuyItemBaseVo {

    private static final long serialVersionUID = 8000748135096267522L;

    @ApiModelProperty(value = "钉钉表id")
    private Long dingTalkId;

    @ApiModelProperty(value = "申请人")
    private String apply;

    @ApiModelProperty(value = "申请科室")
    private String applyDept;

    @ApiModelProperty(value = "预算号/课题号")
    private String budgetNumber;

    @ApiModelProperty(value = "采购目的")
    private String buyPurpose;

    @ApiModelProperty(value = "采购类型：货物/服务")
    private String buyClass;

    @ApiModelProperty(value = "采购标的")
    private String procurementType;

    @ApiModelProperty(value = "货物采购类型：医用设备采购/医用设备维修配件及维修服务采购/信息类办公设备及维修配件采购/其他")
    private String goodsType;

    @ApiModelProperty(value = "是否超过5万")
    private String middleAmount;

    @ApiModelProperty(value = "详细参数附件")
    private List<AttachmentDto> paramsAttList;

    @ApiModelProperty(value = "预算总金额（元）")
    private BigDecimal buyBudget;

    @ApiModelProperty(value = "是否涉及大额资金（20万元以上）")
    private String largeAmount;

    @ApiModelProperty(value = "资金来源：研究所/医院/工会")
    private String capitalSource;

    @ApiModelProperty(value = "预算类型：财政项目/科研项目/自有资金")
    private String budgetType;

    @ApiModelProperty(value = "项目/课题负责人")
    private String buyPerson;

    @ApiModelProperty(value = "课题号")
    private String classNum;

    @ApiModelProperty(value = "院长会议纪要文件")
    private List<AttachmentDto> meetingAttList;

    /**
     * 这里 不能随意更换 subpackageDtoList 名字 页面对应回显 需要 这个名字
     */
    @ApiModelProperty(value = "标段信息")
    private List<SubPackageInfoVo> subpackageDtoList;
}
