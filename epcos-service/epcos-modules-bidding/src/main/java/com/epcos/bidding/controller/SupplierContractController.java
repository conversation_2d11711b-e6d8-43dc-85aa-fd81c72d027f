package com.epcos.bidding.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.epcos.bidding.common.annotaion.NotNullUserId;
import com.epcos.bidding.supplier.contract.business.api.ISupplierContractApi;
import com.epcos.bidding.supplier.contract.domain.dao.SupplierContractDao;
import com.epcos.bidding.supplier.contract.domain.dto.*;
import com.epcos.bidding.supplier.contract.domain.vo.SupplierContractVo;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.web.page.TableDataVo;
import com.epcos.common.log.annotation.Log;
import com.epcos.common.log.enums.BusinessType;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.redis.annotation.RedisLock;
import com.epcos.system.api.model.FUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Api(tags = "供应商合同")
@RestController
@RequiredArgsConstructor
@RequestMapping("/contract")
public class SupplierContractController {

    private final ISupplierContractApi supplierContractApi;

    @RedisLock(second = 30)
    @NotNullUserId
    @ApiOperation(value = "新增合同模板")
    @Log(title = "新增合同模板[接口：add]", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public R<Boolean> add(@RequestBody @Valid SupplierContractCreateDto dto) {
        return R.ok(supplierContractApi.save(dto.convert()));
    }

    @RedisLock(second = 30)
    @ApiOperation(value = "修改合同模板")
    @Log(title = "修改合同模板[接口：update]", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public R<Boolean> update(@RequestBody @Valid SupplierContractUpdateDto dto) {
        supplierContractApi.update(dto);
        return R.ok();
    }

    @RedisLock(second = 30)
    @ApiOperation("发布合同模板至采购人审核")
    @Log(title = "发布合同模板至采购人审核[接口：publish]", businessType = BusinessType.UPDATE)
    @GetMapping("/publish")
    public R<Boolean> publish(@RequestParam("id") Long id) {
        supplierContractApi.publish(id);
        return R.ok();
    }

    @RedisLock(second = 30)
    @ApiOperation("供应商确认合同模板")
    @Log(title = "供应商确认合同模板[接口：confirm]", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm")
    public R<Boolean> confirm(@RequestBody @Valid SupplierContractConfirmDto dto) {
        supplierContractApi.updateById(dto.convert());
        return R.ok();
    }

    @RedisLock(second = 30)
    @ApiOperation("删除合同模板")
    @Log(title = "删除合同模板[接口：delete]", businessType = BusinessType.DELETE)
    @GetMapping("/delete")
    public R<Boolean> deleteById(@RequestParam("id") Long id) {
        Optional.ofNullable(supplierContractApi.getById(id))
                .ifPresent(i -> {
                    if (Objects.nonNull(i.getContractUrl())) {
                        FUtil.delFile(i.getContractUrl());
                    }
                    if (Objects.nonNull(i.getAttachment())) {
                        FUtil.delFile(i.getAttachment());
                    }
                });
        supplierContractApi.removeById(id);
        return R.ok();
    }

    @NotNullUserId
    @ApiOperation("分页查询合同")
    @GetMapping("/page")
    public TableDataVo<SupplierContractVo> page(@ModelAttribute("dto") SupplierContractQueryDto dto) {
        IPage<SupplierContractDao> page = supplierContractApi.page(dto, SecurityUtils.getUserId());
        List<SupplierContractVo> voList = page.getRecords().stream().map(i -> {
            SupplierContractVo vo = new SupplierContractVo();
            BeanUtils.copyProperties(i, vo);
            return vo;
        }).collect(Collectors.toList());
        return new TableDataVo<>(voList, page.getTotal());
    }

    @RedisLock(second = 30)
    @ApiOperation("上传合同pdf")
    @Log(title = "上传合同pdf[接口：upAttachment]", businessType = BusinessType.INSERT)
    @PostMapping("/upAttachment")
    public R<Boolean> upAttachment(@RequestBody @Valid SupplierContractUpAttachmentDto dto) {
        supplierContractApi.updateById(dto.convert());
        return R.ok();
    }

}
