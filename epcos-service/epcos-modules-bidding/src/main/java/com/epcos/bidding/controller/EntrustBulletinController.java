package com.epcos.bidding.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.epcos.bidding.common.annotaion.NotNullUserId;
import com.epcos.bidding.purchase.entrust.business.api.IEntrustBulletinApi;
import com.epcos.bidding.purchase.entrust.domain.EntrustBulletinEnum;
import com.epcos.bidding.purchase.entrust.domain.dao.EntrustBulletinDao;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.web.page.TableSupport;
import com.epcos.common.log.annotation.Log;
import com.epcos.common.log.enums.BusinessType;
import com.epcos.common.security.annotation.Logical;
import com.epcos.common.security.annotation.RequiresPermissions;
import com.epcos.common.core.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.Objects;

@Api(tags = "委托公告")
@RestController
@RequestMapping("/entrust/bulletin")
@AllArgsConstructor
public class EntrustBulletinController {

    private final IEntrustBulletinApi entrustBulletinApi;

    @ApiOperation("主页")
    @GetMapping("/public/homepage")
    R<IPage<EntrustBulletinDao>> homepage(@Valid EntrustBulletinDao dto) {
        dto.setShowStatus(EntrustBulletinEnum.BULLETIN_SHOW.getCode());
        dto.setAuditStatus(EntrustBulletinEnum.AUDIT_PASS.getCode());
        Page<EntrustBulletinDao> page = entrustBulletinApi.page(page(), entrustBulletinApi.queryWrapper(dto));
        return R.ok(page);
    }

    @NotNullUserId
    @RequiresPermissions("entrust:bulletin:add")
    @ApiOperation("发布")
    @Log(title = "发布[接口：publish]", businessType = BusinessType.INSERT)
    @PostMapping("/publish")
    R<Boolean> publish(@RequestBody @Valid EntrustBulletinDao dto) {
        dto.setCreateUserId(SecurityUtils.getUserId());
        dto.setCreateUserName(SecurityUtils.getNickName());
        dto.init();
        return R.ok(entrustBulletinApi.save(dto));
    }

    @RequiresPermissions(value = {"entrust:bulletin:edit", "entrust:bulletinAudit:edit"}, logical = Logical.OR)
    @ApiOperation("修改公告")
    @Log(title = "修改公告[接口：up]", businessType = BusinessType.UPDATE)
    @PostMapping("/up")
    R<Boolean> up(@RequestBody @Valid EntrustBulletinDao dto) {
        return R.ok(entrustBulletinApi.updateById(dto.validIdNotNull().init()));
    }

    @RequiresPermissions("entrust:bulletin:remove")
    @ApiOperation("删除公告")
    @Log(title = "删除公告[接口：del]", businessType = BusinessType.DELETE)
    @PostMapping("/del")
    R<Boolean> del(@RequestBody @Valid EntrustBulletinDao dto) {
        dto.validIdNotNull();
        if (EntrustBulletinEnum.AUDIT_PASS.getCode() == entrustBulletinApi.getById(dto.getId()).getAuditStatus()) {
            return R.fail("该公告已经通过审核，不可删除");
        }
        return R.ok(entrustBulletinApi.removeById(dto.getId()));
    }

    @NotNullUserId
    @RequiresPermissions("entrust:bulletin:query")
    @ApiOperation("创建者查询公告分页列表")
    @GetMapping("/page")
    R<IPage<EntrustBulletinDao>> page(@Valid EntrustBulletinDao dto) {
        dto.setCreateUserId(SecurityUtils.getUserId());
        Page<EntrustBulletinDao> page = entrustBulletinApi.page(page(), entrustBulletinApi.queryWrapper(dto));
        return R.ok(page);
    }

    private Page page() {
        return Page.of(TableSupport.getPageNumOrDefault(), TableSupport.getPageSizeOrDefault());
    }

    @RequiresPermissions("entrust:bulletinAudit:query")
    @ApiOperation("审核者查询所有公告分页列表")
    @GetMapping("/pageByReviewer")
    R<IPage<EntrustBulletinDao>> pageByReviewer(@Valid EntrustBulletinDao dto) {
        return R.ok(entrustBulletinApi.page(page(), entrustBulletinApi.orderAuditStatusWrapper(dto)));
    }

    @RequiresPermissions("entrust:bulletinAudit:audit")
    @ApiOperation("审核")
    @Log(title = "审核[接口：audit]", businessType = BusinessType.UPDATE)
    @PostMapping("/audit")
    R<Boolean> audit(@RequestBody @Valid EntrustBulletinDao dto) {
        dto.validIdNotNull().validAuditStatusIsPassOrFail();
        EntrustBulletinDao updateDao = new EntrustBulletinDao();
        updateDao.setAuditStatus(dto.getAuditStatus());
        updateDao.setId(dto.getId());
        if (Objects.equals(dto.getAuditStatus(), EntrustBulletinEnum.AUDIT_PASS.getCode())) {
            updateDao.setFailureDescription("");
        } else if (Objects.equals(dto.getAuditStatus(), EntrustBulletinEnum.AUDIT_FAIL.getCode())) {
            updateDao.setFailureDescription(dto.getFailureDescription());
        }
        updateDao.setAuditUserId(SecurityUtils.getUserId());
        updateDao.setAuditUserName(SecurityUtils.getNickName());
        updateDao.setAuditTime(new Date());
        return R.ok(entrustBulletinApi.updateById(updateDao));
    }

    @RequiresPermissions("entrust:bulletinAudit:show")
    @ApiOperation("是否展示")
    @Log(title = "是否展示[接口：show]", businessType = BusinessType.UPDATE)
    @PostMapping("/show")
    R<Boolean> show(@RequestBody @Valid EntrustBulletinDao dto) {
        dto.validIdNotNull().validShowStatusNotNull();
        EntrustBulletinDao updateDao = new EntrustBulletinDao();
        updateDao.setId(dto.getId());
        updateDao.setShowStatus(dto.getShowStatus());
        return R.ok(entrustBulletinApi.updateById(updateDao));
    }

    @RequiresPermissions("entrust:bulletinAudit:remove")
    @ApiOperation("审核者删除公告")
    @Log(title = "审核者删除公告[接口：delByReviewer]", businessType = BusinessType.DELETE)
    @PostMapping("/delByReviewer")
    R<Boolean> delByReviewer(@RequestBody @Valid EntrustBulletinDao dto) {
        return R.ok(entrustBulletinApi.removeById(dto.validIdNotNull().getId()));
    }


}
