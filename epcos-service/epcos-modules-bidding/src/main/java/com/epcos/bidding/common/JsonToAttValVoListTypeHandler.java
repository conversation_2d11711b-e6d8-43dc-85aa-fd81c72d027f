package com.epcos.bidding.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.epcos.bidding.purchase.api.params.dto.AttributeValVo;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

public class JsonToAttValVoListTypeHandler extends BaseTypeHandler<List<AttributeValVo>> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<AttributeValVo> parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, JSON.toJSONString(parameter));
    }

    @Override
    public List<AttributeValVo> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        List<AttributeValVo> res = Optional.ofNullable(rs.getString(columnName))
                .map(i -> JSON.parseObject(i, new TypeReference<List<AttributeValVo>>() {
                }))
                .orElse(Collections.emptyList());
//        convertKeyGroups(res);
        return res;
    }

    private void convertKeyGroups(List<AttributeValVo> res) {
        res.forEach(i -> {
            String keyGroup = i.getKeyGroup();
            if (Objects.nonNull(keyGroup)) {
                HashSet<String> set = new HashSet<>(Arrays.asList(keyGroup.split(",")));
                i.setKeyGroups(set);
            } else {
                i.setKeyGroups(new HashSet<>());
            }
        });
    }

    @Override
    public List<AttributeValVo> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        List<AttributeValVo> res = JSON.parseObject(rs.getString(columnIndex), new TypeReference<List<AttributeValVo>>() {
        });
//        convertKeyGroups(res);
        return res;
    }

    @Override
    public List<AttributeValVo> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        List<AttributeValVo> res = JSON.parseObject(cs.getString(columnIndex),
                new TypeReference<List<AttributeValVo>>() {
                }
        );
//        convertKeyGroups(res);
        return res;
    }
}
