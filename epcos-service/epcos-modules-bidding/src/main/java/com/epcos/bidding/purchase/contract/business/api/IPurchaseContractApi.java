package com.epcos.bidding.purchase.contract.business.api;

import com.epcos.bidding.supplier.contract.domain.dto.SupplierContractAuditDto;
import com.epcos.bidding.supplier.contract.domain.dto.SupplierContractUpdateDto;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/20 16:23
 */
public interface IPurchaseContractApi {

    Boolean editContractTemplate(SupplierContractUpdateDto dto);

    Boolean backContractTemplate(Long userId,SupplierContractAuditDto dto);
}
