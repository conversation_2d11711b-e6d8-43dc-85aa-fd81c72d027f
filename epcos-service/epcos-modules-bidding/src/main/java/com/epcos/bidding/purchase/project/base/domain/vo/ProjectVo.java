package com.epcos.bidding.purchase.project.base.domain.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class ProjectVo implements Serializable {
    private static final long serialVersionUID = 5578747676165785545L;

    /**
     * 成交价格     节支率     使用科室负责人     归口负责人
     */
    // 项目名称
    private String buyItemName;

    // 项目编号
    private String buyItemCode;

    // 预算价格
    private String buyBudget;

    // 使用科室
    private String useDept;

    // 归口部门
    private String managementDept;

    // 项目负责人
    private String buyPerson;

    // 采购经办人
    private String createBy;





}
