package com.epcos.bidding.purchase.claims.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.epcos.bidding.purchase.api.domian.cliams.TenderFileRejectionDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/10 16:43
 */
public interface TenderFileRejectionMapper extends BaseMapper<TenderFileRejectionDao> {

    int insert(TenderFileRejectionDao rejection);

    List<TenderFileRejectionDao> selectById(Long id);

    List<TenderFileRejectionDao> selectByIds(@Param(value = "ids") List<Long> ids);

    List<TenderFileRejectionDao> selectAll();

    int deleteById(Long id);

    List<TenderFileRejectionDao> selectByConditions(@Param(value = "ids") List<Long> ids, @Param(value = "supplierId") Long supplierId);
}
