package com.epcos.bidding.common.aspects.project;

import cn.hutool.core.text.CharSequenceUtil;
import com.epcos.bidding.common.annotaion.GetClaimsFile;
import com.epcos.bidding.common.aspects.AbstractMethod;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.purchase.claims.business.api.IClaimsFileApi;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileDao;
import com.epcos.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 获取采购文件信息
 */
@Slf4j
@Aspect
@Component
public class ClaimsFileAspectFilterChainPost extends AbstractMethod<GetClaimsFile, ClaimsFileDao> {

    public ClaimsFileAspectFilterChainPost() {
        super(GetUtil.GET_CLAIMS_FILE, "获取采购文件信息");
    }

    @Autowired
    private IClaimsFileApi claimsFileApi;


    @Override
    public ClaimsFileDao businessMethods(JoinPoint point, GetClaimsFile annotation) {
        String codeEl = annotation.common().subpackageCodeEL();
        if (CharSequenceUtil.isEmpty(codeEl)) {
            log.error("参数值异常 @GetClaimsFile，subpackageCodeEL={}", codeEl);
            throw new ServiceException("参数值异常 @GetClaimsFile " + codeEl);
        }
        String subpackageCode = threadLocal.get().getSubpackageCode();
        if (CharSequenceUtil.isEmpty(subpackageCode)) {
            throw new ServiceException("获取采购文件信息,包code必填");
        }
        return claimsFileApi.findBySubpackageCode(subpackageCode);
    }

    @Override
    @Around(value = "@annotation(annotation)")
    public Object around(ProceedingJoinPoint point, GetClaimsFile annotation) {
        return super.around(point, annotation);
    }


}
