package com.epcos.bidding.common.aspects.filter.validator;

import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import com.epcos.bidding.purchase.api.params.vo.bulletin.BulletinAndItemTimeVo;
import com.epcos.common.core.exception.ServiceException;

import java.util.Date;
import java.util.Optional;

/**
 * 校验 ResponseFileEndTime 如果不为空，就进行校验时间是否截止
 */
public class ResponseFileEndTimeNonNullHasPassedValidator implements ResultPostHandlerFilterChain<BulletinAndItemTimeVo> {
    @Override
    public void postHandler(AspectContext context, BulletinAndItemTimeVo result) {
        if (Optional.ofNullable(result)
                .map(BulletinAndItemTimeVo::getResponseFileEndTime)
                .map(d -> d.before(new Date())).orElse(false)) {
            throw new ServiceException("响应时间已截止");
        }
    }

}
