package com.epcos.bidding.purchase.monitor.mapping;

import com.epcos.bidding.purchase.api.params.dto.monitor.CreateMonitorDto;
import com.epcos.bidding.purchase.monitor.domain.dao.MonitorBidDao;
import com.epcos.bidding.purchase.monitor.domain.dto.UpdateMonitorDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/6 11:36
 */
@Mapper
public interface MonitorBidConvert {

    MonitorBidConvert INSTANCE = Mappers.getMapper(MonitorBidConvert.class);


    MonitorBidDao convert(CreateMonitorDto createMonitorDto);

    MonitorBidDao convert(UpdateMonitorDto updateMonitorDto);
}
