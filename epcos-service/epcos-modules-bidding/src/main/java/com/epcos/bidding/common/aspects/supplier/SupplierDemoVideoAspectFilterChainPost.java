package com.epcos.bidding.common.aspects.supplier;

import cn.hutool.core.text.CharSequenceUtil;
import com.epcos.bidding.common.annotaion.GetSupplierDemoVideo;
import com.epcos.bidding.common.aspects.AbstractMethod;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.supplier.sign.business.api.ISupplierDemoVideoApi;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierDemoVideoDao;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.EvalSpelUtil;
import com.epcos.common.core.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Objects;

/**
 * 查询供应商演示视频
 */
@Slf4j
@Aspect
@Component
public class SupplierDemoVideoAspectFilterChainPost extends AbstractMethod<GetSupplierDemoVideo, SupplierDemoVideoDao> {

    public SupplierDemoVideoAspectFilterChainPost() {
        super(GetUtil.GET_SUPPLIER_DEMO_VIDEO, "查询供应商演示视频");
    }

    @Autowired
    private ISupplierDemoVideoApi supplierDemoVideoApi;

    @Override
    public SupplierDemoVideoDao businessMethods(JoinPoint point, GetSupplierDemoVideo annotation) {
        String subpackageCode = threadLocal.get().getSubpackageCode();
        if (CharSequenceUtil.isEmpty(subpackageCode)) {
            throw new ServiceException("subpackageCode 参数必填：" + subpackageCode);
        }
        Method method = ((MethodSignature) point.getSignature()).getMethod();
        String supplierIdEL = annotation.supplierIdEL();
        Long supplierId;
        if (CharSequenceUtil.isEmpty(supplierIdEL)) {
            supplierId = SecurityUtils.getUserId();
        } else {
            supplierId = EvalSpelUtil.get(method, point.getArgs(), supplierIdEL, Long.class);
        }
        if (Objects.isNull(supplierId)) {
            throw new ServiceException("查询供应商演示视频，参数：supplierId 必填");
        }
        return supplierDemoVideoApi.findOne(subpackageCode, supplierId);
    }

    @Override
    @Around(value = "@annotation(annotation)")
    public Object around(ProceedingJoinPoint point, GetSupplierDemoVideo annotation) {
        return super.around(point, annotation);
    }

}
