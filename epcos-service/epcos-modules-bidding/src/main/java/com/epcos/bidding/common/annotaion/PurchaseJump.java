package com.epcos.bidding.common.annotaion;

import org.springframework.core.annotation.Order;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/1 9:13
 */
@Order(value = 601)
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface PurchaseJump {

    GetCommon common() default @GetCommon;

}
