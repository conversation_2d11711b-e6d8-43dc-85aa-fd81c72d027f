package com.epcos.bidding.purchase.report.business.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.common.utils.BiddingBaseUtil;
import com.epcos.bidding.purchase.api.domian.reprot.ReportInfoVo;
import com.epcos.bidding.purchase.api.domian.reprot.SignInfoVo;
import com.epcos.bidding.purchase.api.params.JudgesVo;
import com.epcos.bidding.purchase.extract.business.api.IExtractJudgeInnerApi;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.purchase.report.business.api.IResearchReportApi;
import com.epcos.bidding.purchase.report.domain.dao.ResearchReportDao;
import com.epcos.bidding.purchase.report.domain.dto.InitReportDto;
import com.epcos.bidding.purchase.report.repository.IResearchReportMapper;
import com.epcos.common.core.constant.FileTypeNameConstants;
import com.epcos.common.core.utils.StringUtils;
import com.epcos.common.file.html2pdf.HtmlUtil;
import com.epcos.common.core.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/29 14:00
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ResearchReportImpl extends ServiceImpl<IResearchReportMapper, ResearchReportDao> implements IResearchReportApi {

    private final IExtractJudgeInnerApi extractJudgeInnerApi;
    private final ISubPackageApi subPackageApi;
    private final IBuyItemApi buyItemApi;

    @Override
    public LambdaQueryWrapper<ResearchReportDao> queryWrapper(ResearchReportDao dao) {
        return null;
    }


    @Override
    public void initReport(InitReportDto dto) {
        BuyItemDao buyItemDao = buyItemApi.findBySubpackageCode(dto.getSubpackageCode());
        List<ResearchReportDao> reportDaoList = selectReportList(dto.getSubpackageCode());

        //添加埋点信息
        String html = dto.getResearchReportText();
        StringBuilder builder = new StringBuilder(html);
        StringBuilder tmpHtml = builder.append("<div style='opacity: 0;'>");
        reportDaoList.forEach(vo ->
                tmpHtml.append("<span style='margin-right: 20px; margin-bottom: 20px;'>")
                        .append(vo.getSignUserId())
                        .append("epcos")
                        .append("</span>"));
        tmpHtml.append("</div>");

        //html生成pdf
        File pdfFile = HtmlUtil.toPdf(tmpHtml.toString());
        String fileKey = BiddingBaseUtil.generateFileAndReturnUrl(
                pdfFile,
                buyItemDao,
                "询价及评审报告",
                FileTypeNameConstants.PURCHASE_RESEARCH_REPORT,
                dto.getSubpackageCode(),
                SecurityUtils.getUserId()
        );
        update(Wrappers.lambdaUpdate(ResearchReportDao.class)
                .set(ResearchReportDao::getResearchReportText, tmpHtml.toString())
                .set(ResearchReportDao::getResearchReportKey, fileKey)
                .eq(ResearchReportDao::getSubpackageCode, dto.getSubpackageCode())
                .isNotNull(ResearchReportDao::getResearchReportText));
    }

    @Override
    public List<ReportInfoVo> reportInfoByItem(String buyItemCode) {

        List<SubpackageDao> subpackageDaoList = subPackageApi.findByBuyItemCode(buyItemCode);

        List<String> subpackgeCodeList = subpackageDaoList.stream()
                .map(SubpackageDao::getSubpackageCode)
                .collect(Collectors.toList());

        List<ResearchReportDao> reportDaoList = list(Wrappers.lambdaQuery(ResearchReportDao.class)
                .in(ResearchReportDao::getSubpackageCode, subpackgeCodeList));

        Map<String, List<ResearchReportDao>> reportMap = reportDaoList.stream()
                .collect(Collectors.groupingBy(ResearchReportDao::getSubpackageCode));

        List<ReportInfoVo> voList = subpackageDaoList.stream().map(subpackageDao -> {

            ReportInfoVo vo = new ReportInfoVo();
            vo.setSubpackageCode(subpackageDao.getSubpackageCode());
            vo.setSubpackageName(subpackageDao.getSubpackageName());
            List<ResearchReportDao> researchReportDaoList = reportMap.get(subpackageDao.getSubpackageCode());
            if (!CollectionUtils.isEmpty(researchReportDaoList)) {
                ResearchReportDao reportDao = researchReportDaoList.stream()
                        .filter(r -> StringUtils.hasText(r.getResearchReportText()))
                        .collect(Collectors.toList())
                        .get(0);

                BeanUtils.copyProperties(reportDao, vo);

                List<SignInfoVo> signInfoVoList = researchReportDaoList.stream()
                        .map(r -> {
                            SignInfoVo signInfoVo = new SignInfoVo();
                            BeanUtils.copyProperties(r, signInfoVo);
                            return signInfoVo;
                        }).collect(Collectors.toList());

                vo.setSignInfoVoList(signInfoVoList);
            }
            return vo;
        }).collect(Collectors.toList());
        return voList;
    }

    @Override
    public ReportInfoVo reportInfoBySub(String subpackageCode) {
        ReportInfoVo vo = new ReportInfoVo();
        List<ResearchReportDao> reportDaoList = list(Wrappers.lambdaQuery(ResearchReportDao.class)
                .eq(ResearchReportDao::getSubpackageCode, subpackageCode));
        if (CollectionUtils.isEmpty(reportDaoList)) {
            return vo;
        }
        ResearchReportDao reportDao = reportDaoList.stream()
                .filter(r -> StringUtils.hasText(r.getResearchReportText()))
                .collect(Collectors.toList())
                .get(0);
        BeanUtils.copyProperties(reportDao, vo);

        List<SignInfoVo> signInfoVoList = reportDaoList.stream()
                .map(r -> {
                    SignInfoVo signInfoVo = new SignInfoVo();
                    BeanUtils.copyProperties(r, signInfoVo);
                    return signInfoVo;
                }).collect(Collectors.toList());
        vo.setSignInfoVoList(signInfoVoList);

        return vo;
    }

    @Override
    public void editReport(InitReportDto dto) {

        remove(dto.getSubpackageCode());

        List<JudgesVo> judgesVoList = extractJudgeInnerApi.queryBySub(dto.getSubpackageCode());
        //保存评委相关信息
        if (CollectionUtils.isEmpty(judgesVoList)) {
            throw new RuntimeException("未查询到评委信息");
        }
        List<ResearchReportDao> reportDaoList = judgesVoList.stream()
                .map(judgesVo -> {
                    ResearchReportDao reportDao = new ResearchReportDao();
                    reportDao.setSignUserId(judgesVo.getJudgeId());
                    reportDao.setSignUserName(judgesVo.getJudgeName());
                    reportDao.setSubpackageCode(dto.getSubpackageCode());
                    return reportDao;
                }).collect(Collectors.toList());
        //保存发起人信息
        ResearchReportDao reportDao = new ResearchReportDao();
        reportDao.setSubpackageCode(dto.getSubpackageCode());
        reportDao.setResearchReportText(dto.getResearchReportText());
        reportDao.setSignUserId(SecurityUtils.getUserId());
        reportDao.setSignUserName(SecurityUtils.getNickName());
        reportDaoList.add(reportDao);
        saveBatch(reportDaoList);
    }

    @Override
    public Boolean updateSignStatus(String subpackageCode, Long signUserId, String signStatus) {

        return update(Wrappers.lambdaUpdate(ResearchReportDao.class)
                .set(ResearchReportDao::getSignStatus, signStatus)
                .eq(ResearchReportDao::getSubpackageCode, subpackageCode)
                .eq(ResearchReportDao::getSignUserId, signUserId)
        );
    }
}
