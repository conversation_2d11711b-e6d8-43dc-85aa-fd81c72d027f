package com.epcos.bidding.purchase.remote.dto;

import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/4 16:26
 */
@Data
@NoArgsConstructor
public class NotifyJudgeDto extends SuperPackageVo {

    @ApiModelProperty(value = "0 是采购议价  1 是评委议价  2-没有议价")
    private String isBargaining;

    @ApiModelProperty(value = "是否线上评审 : 1-是  ， 0-不评审")
    private Integer isReview;

    @ApiModelProperty(value = "组长id")
    private Long groupId;

    public NotifyJudgeDto(String subpackageCode) {
        super(subpackageCode, null);
    }

    public NotifyJudgeDto(String subpackageCode, String isBargaining) {
        super(subpackageCode, null);
        this.isBargaining = isBargaining;
    }

    public NotifyJudgeDto(String subpackageCode, Integer isReview) {
        super(subpackageCode, null);
        this.isReview = isReview;
    }

    public NotifyJudgeDto(String subpackageCode, Long groupId) {
        super(subpackageCode, null);
        this.groupId = groupId;
    }


}
