package com.epcos.bidding.purchase.win;

import com.epcos.bidding.common.annotaion.*;
import com.epcos.bidding.purchase.api.params.vo.buyitem.ItemSubpackageVo;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.purchase.win.business.api.IWinBidResultApi;
import com.epcos.bidding.purchase.win.domain.dto.*;
import com.epcos.bidding.purchase.win.domain.vo.ReviewPageVo;
import com.epcos.bidding.purchase.win.domain.vo.WinSubPackageVo;
import com.epcos.common.core.domain.R;
import com.epcos.common.log.annotation.Log;
import com.epcos.common.log.enums.BusinessType;
import com.epcos.common.redis.annotation.RedisLock;
import com.epcos.common.security.annotation.RequiresPermissions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

import static com.epcos.common.core.constant.PurchaseConstants.UserType.PURCHASER;
import static com.epcos.common.core.constant.PurchaseConstants.UserType.SUPPLIER;
import static com.epcos.common.log.enums.BusinessType.INSERT;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/9 14:51
 */
@Api(tags = "中标结果", description = "中标结果")
@RestController
@RequestMapping("/purchase/win")
@RequiredArgsConstructor
public class WinBidResultController {

    private final IWinBidResultApi winBidResultApi;
    private final ISubPackageApi subpackageApi;

    @RedisLock
    @RequiresPermissions("process:reviewReport:edit")
    @Log(title = "采购人编写评审报告", businessType = INSERT)
    @ApiOperation("采购人编写评审报告")
    @PostMapping("/confirmReviewReport")
    public R<Boolean> confirmReviewReport(@RequestBody @Valid ReviewReportDto reviewReportDto) {
        return winBidResultApi.confirmReviewReport(reviewReportDto);
    }

    @RequiresPermissions("project:process:query")
    @ApiOperation("查询项目评审报告集合")
    @GetMapping("/getReviewReport")
    public R<List<SubpackageDao>> getReviewReport(@RequestParam("buyItemCode") String buyItemCode) {
        return winBidResultApi.getReviewReport(buyItemCode);
    }

    /*=================================================== 确认评审 ===================================================*/

    @ApiOperation(value = "确认评审页面")
    @PurchaseJump(common = @GetCommon(buyItemCodeEL = "#buyItemCode"))
    @GetMapping(value = "/confirmReviewPage")
    @ApiImplicitParam(name = "buyItemCode", value = "项目code", paramType = "query", dataTypeClass = String.class)
    public R<List<ItemSubpackageVo<ReviewPageVo>>> confirmReviewPage(@NotBlank String buyItemCode) {
        return R.ok(winBidResultApi.confirmReviewPage(buyItemCode));
    }

    @ApiOperation(value = "重新投票")
    @GetMapping(value = "/againVote")
    @ApiImplicitParam(name = "subpackageCode", value = "包code", paramType = "query", dataTypeClass = String.class)
    public R<Boolean> againVote(@NotBlank String subpackageCode) {
        return R.ok(winBidResultApi.againVote(subpackageCode));
    }

    @ApiOperation(value = "确认会签")
    @PurchaseJump(common = @GetCommon(subpackageCodeEL = "#dto.subpackageCode"))
    @PostMapping(value = "/confirmCounterSign")
    public R<Boolean> confirmCounterSign(@RequestBody ConfirmCounterDto dto) {
        return R.ok(winBidResultApi.confirmCounterSign(dto));
    }

    @ApiOperation(value = "确认评审")
    @PurchaseJump(common = @GetCommon(subpackageCodeEL = "#dto.subpackageCode"))
    @Jump(subpackageCodeEL = "#dto.subpackageCode", processRole = PURCHASER)
    @PostMapping(value = "/confirmReview")
    public R<Boolean> confirmReview(@RequestBody ConfirmReviewDto dto) {
        return R.ok(winBidResultApi.confirmReview(dto));
    }

    @ApiOperation(value = "重新评审")
    @Log(title = "重新评审", businessType = BusinessType.UPDATE)
    @GetMapping(value = "/againConfirmReview")
    @ApiImplicitParam(name = "subpackageCode", value = "包code", paramType = "query", dataTypeClass = String.class)
    public R<Boolean> againConfirmReview(@NotBlank String subpackageCode) {
        return R.ok(winBidResultApi.againConfirmReview(subpackageCode));
    }


    /**
     * 保存投标报价基准分
     *
     * @return
     */
    @ApiOperation(value = "保存投标报价基准分")
    @Log(title = "保存投标报价基准分", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/saveSource")
    public R<Boolean> saveSource(@RequestBody SaveSourceDto dto) {
        return R.ok(subpackageApi.saveSource(dto));
    }

    /*=================================================== 成交结果页面 ===================================================*/


    @ApiOperation(value = "成交结果页面")
    @GetMapping(value = "/winPage")
    @ApiImplicitParam(name = "buyItemCode", value = "项目code", paramType = "query", dataTypeClass = String.class)
    public R<List<ItemSubpackageVo<WinSubPackageVo>>> winPage(@NotBlank String buyItemCode) {
        return R.ok(winBidResultApi.winPage(buyItemCode));
    }


    @RedisLock
    @ApiOperation(value = "同意评审结果")
    @Log(title = "同意评审结果", businessType = BusinessType.UPDATE)
    @GetMapping(value = "/confirmReviewResult")
    @PurchaseJump(common = @GetCommon(subpackageCodeEL = "#subpackageCode"))
    @ApiImplicitParam(name = "subpackageCode", value = "包code", paramType = "query", dataTypeClass = String.class)
    public R<Boolean> confirmReviewResult(@NotBlank String subpackageCode) {
        return R.ok(winBidResultApi.confirmReviewResult(subpackageCode));
    }

    @ApiOperation(value = "选定中标人")
    @Log(title = "选定中标人", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/selectWin")
    @GetItem(common = @GetCommon(subpackageCodeEL = "#dto.subpackageCode"))
    public R<Boolean> selectWin(@RequestBody SelectWinDto dto) {
        return R.ok(winBidResultApi.selectWin(dto));
    }


    @ApiOperation(value = "发送通知书")
    @Log(title = "发送通知书", businessType = BusinessType.UPDATE)
    @Jump(subpackageCodeEL = "#dto.subpackageCode", supplierIdEl = "#dto.supplierId", processRole = SUPPLIER)
    @GetSimpleItem(common = @GetCommon(subpackageCodeEL = "#dto.subpackageCode"))
    @PostMapping(value = "/sendNotice")
    public R<Boolean> sendNotice(@RequestBody ResultNoticeDto dto) {
        // 查询包信息
        SubpackageDao subpackageDao = subpackageApi.findBySubpackageCode(dto.getSubpackageCode());
        dto.setBuyItemCode(subpackageDao.getBuyItemCode());
        return R.ok(winBidResultApi.sendNotice(dto));
    }


    @ApiOperation(value = "点击项目完成")
    @RequiresPermissions("purchaser:project:complete")
    @Log(title = "点击项目完成", businessType = BusinessType.UPDATE)
    @GetSimpleItem(common = @GetCommon(buyItemCodeEL = "#buyItemCode"))
    @GetMapping(value = "/completeItem")
    @ApiImplicitParam(name = "buyItemCode", value = "项目code", paramType = "query", dataTypeClass = String.class)
    public R<Boolean> completeItem(@NotBlank String buyItemCode) {
        winBidResultApi.completed(buyItemCode);
        return R.ok();
    }
}
