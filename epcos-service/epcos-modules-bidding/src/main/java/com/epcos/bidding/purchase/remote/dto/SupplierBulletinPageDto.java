package com.epcos.bidding.purchase.remote.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/17 9:51
 */
@Data
public class SupplierBulletinPageDto implements Serializable {

    private static final long serialVersionUID = -8163634404300751497L;

    @ApiModelProperty(value = "供应商id", hidden = true)
    private Long supplierId;

    @ApiModelProperty(value = "项目名称")
    private String buyItemName;

    @ApiModelProperty(value = "公告名称")
    private String bulletinName;

    /**
     * 由采购人自己设置指定的值
     */
    @ApiModelProperty(value = "公告状态", hidden = true)
    private String auditStatus;
}
