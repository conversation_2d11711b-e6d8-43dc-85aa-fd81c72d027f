/**
 * Copyright 2022 bejson.com
 */
package com.epcos.bidding.common.utils;

import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * 客户端文件评审内容
 */
@Data
public final class ReviewItem {


    /**
     * 评审类型：1符合性评审 or 评分表
     * 2-技术
     * 3-商务
     * 4-价格
     */
    @Range(min = 1, max = 4, message = "【评审类型：1符合性评审 or 评分表】传入数值不符合预设值")
    @NotNull(message = "【评审类型：1符合性评审 or 评分表】必填")
    private Integer reviewType;
    /**
     * 评审模块
     */
    @Length(max = 500, message = "【评审模块】最长长度不得超过500个字符")
    @NotBlank(message = "【评审模块】必填")
    private String reviewItem;
    /**
     * 评审规则
     */
    @Length(max = 65525, message = "【评审规则】最长长度不得超过65525个字符")
    @NotBlank(message = "【评审规则】必填")
    private String reviewCriteria;
    /**
     * 1-主观分,0-客观分
     */
    @Range(min = 0, max = 1, message = "【1-主观分,0-客观分】传入数值不符合预设值")
    @NotNull(message = "【1-主观分,0-客观分】必填")
    private Integer isSubjective;
    /**
     * 打分
     */
    @Digits(integer = 8, fraction = 2, message = "【打分】必须是8位整数，2位小数以内的范围")
    private Double reviewScore;

    @Length(max = 50, message = "uuid最长不得超过50字符")
    @NotBlank(message = "评审uuid,不得为空")
    private String uuid;

    // 得分点定位
    private List<String> scoreChapter;

    public String getConvertReviewType() {
        if (Objects.equals(2, reviewType)) {
            return "技术";
        } else if (Objects.equals(3, reviewType)) {
            return "商务";
        } else if (Objects.equals(4, reviewType)) {
            return "价格";
        }
        return reviewType.toString();
    }

    public String getConvertIsSubjective() {
        return Objects.equals(isSubjective, 1) ? "主观分" : "客观分";
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ReviewItem that = (ReviewItem) o;
        return Objects.equals(reviewType, that.reviewType) &&
                Objects.equals(reviewItem, that.reviewItem) &&
                Objects.equals(reviewCriteria, that.reviewCriteria) &&
                Objects.equals(isSubjective, that.isSubjective) &&
                Objects.equals(reviewScore, that.reviewScore) &&
                Objects.equals(uuid, that.uuid);
    }

    @Override
    public int hashCode() {
        return Objects.hash(reviewType, reviewItem, reviewCriteria, isSubjective, reviewScore, uuid);
    }

    public String convertReviewScore() {
        long round = Math.round(reviewScore);
        if (round - reviewScore == 0) {
            return String.valueOf(round);
        }
        return String.valueOf(reviewScore);
    }

    public String convertReviewType() {
        if (Objects.equals(2, reviewType)) {
            return "技术";
        } else if (Objects.equals(3, reviewType)) {
            return "商务";
        } else if (Objects.equals(4, reviewType)) {
            return "价格";
        }
        return reviewType.toString();
    }

    public String convertIsSubjective() {
        return Objects.equals(isSubjective, 1) ? "主观分" : "客观分";
    }
}
