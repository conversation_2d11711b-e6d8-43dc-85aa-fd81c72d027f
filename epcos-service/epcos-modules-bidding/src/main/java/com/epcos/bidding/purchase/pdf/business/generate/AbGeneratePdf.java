package com.epcos.bidding.purchase.pdf.business.generate;

import com.epcos.common.core.domain.pdf.Pdf;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.factory.pdf.IGeneratePdf;
import com.epcos.common.core.utils.DateUtils;
import com.epcos.common.file.html2pdf.HtmlUtil;
import com.epcos.common.file.pdf.Itext7PdfUtil;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.nio.file.Path;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/6 9:14
 */
@Slf4j
public abstract class AbGeneratePdf implements IGeneratePdf {

    @Override
    public Path generatePdf(Pdf vo) {
        //设置文件名
        String fileName = HtmlUtil.getBusinessFileName(getFileName(), String.valueOf(System.currentTimeMillis()), ".pdf");
        Path path = HtmlUtil.getTmpPdfFilePath(fileName);
        try (final PdfWriter pdfWriter = new PdfWriter(path.toFile());
             final PdfDocument pdfDocument = new PdfDocument(pdfWriter);
             final Document document = new Document(pdfDocument, PageSize.A4.rotate())) {
            document.setFont(Itext7PdfUtil.pdfFont());
            generateTitle(document);
            Itext7PdfUtil.blankLinesInTheDocument(document, 1);
            generateTableContent(document, vo);
        } catch (IOException e) {
            log.error("导出pdf失败. e:{}", e);
            throw new ServiceException("导出pdf失败");
        }
        return path;
    }

    protected abstract void generateTitle(Document document);

    protected String getFileName() {
        return DateUtils.getDate();
    }

    protected abstract void generateTableContent(Document document, Pdf vo);
}
