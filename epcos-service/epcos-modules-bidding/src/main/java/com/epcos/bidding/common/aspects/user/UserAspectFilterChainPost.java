package com.epcos.bidding.common.aspects.user;


import cn.hutool.core.text.CharSequenceUtil;
import com.epcos.bidding.common.annotaion.GetUser;
import com.epcos.bidding.common.aspects.AbstractMethod;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.common.core.constant.SecurityConstants;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.EvalSpelUtil;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.web.domain.user.SysUser;
import com.epcos.system.api.RemoteUserService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
@Aspect
public class UserAspectFilterChainPost extends AbstractMethod<GetUser, SysUser> {

    public UserAspectFilterChainPost() {
        super(GetUtil.GET_USER, "获取用户信息");
    }

    @Autowired
    private RemoteUserService remoteUserService;


    @Override
    @Around(value = "@annotation(getUser)")
    public Object around(ProceedingJoinPoint point, GetUser getUser) {
        return super.around(point, getUser);
    }

    @Override
    public SysUser businessMethods(JoinPoint point, GetUser annotation) {
        String userIdEl = annotation.userIdEl();
        Long userId;
        if (CharSequenceUtil.isBlank(userIdEl)) {
            userId = SecurityUtils.getUserId();
        } else {
            userId = EvalSpelUtil.get(((MethodSignature) point.getSignature()).getMethod(),
                    point.getArgs(), userIdEl, Long.class);
        }
        if (Objects.isNull(userId)) {
            log.error("参数值异常，用户id为空，@GetUser={}, userId={}", annotation, userId);
            throw new ServiceException("参数值异常，用户id为空");
        }
        R<SysUser> userInfoR = remoteUserService.getInfoById(userId, SecurityConstants.INNER);
        if (userInfoR.hasFail()) {
            log.error("远程调用用户信息异常，userId={},userInfoR={}", userId, userInfoR);
            throw new ServiceException(userInfoR.getMsg());
        }
        return userInfoR.getData();
    }
}
