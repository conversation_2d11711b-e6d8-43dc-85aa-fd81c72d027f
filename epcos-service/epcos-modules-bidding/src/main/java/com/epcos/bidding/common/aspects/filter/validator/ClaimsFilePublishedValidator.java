package com.epcos.bidding.common.aspects.filter.validator;

import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileDao;

/**
 * 采购文件已发布
 */
public class ClaimsFilePublishedValidator implements ResultPostHandlerFilterChain<ClaimsFileDao> {
    @Override
    public void postHandler(AspectContext context, ClaimsFileDao result) {
        result.verifyPublished();
    }
}
