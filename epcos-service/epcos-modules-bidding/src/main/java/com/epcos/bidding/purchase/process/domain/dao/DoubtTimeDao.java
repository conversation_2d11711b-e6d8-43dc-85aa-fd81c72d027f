package com.epcos.bidding.purchase.process.domain.dao;

import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.bidding.common.SubpackageCodeEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/7 15:30
 */
@ApiModel(description = "质疑表")
@Data
@TableName("purchase_doubt_time")
public class DoubtTimeDao extends SubpackageCodeEntity implements Serializable {


    @ApiModelProperty(value = "采购项目编号")
    private String buyItemCode;

    @ApiModelProperty(value = "标段名称")
    private String subpackageName;

    @ApiModelProperty(value = "质疑类型  1-开标之前 （供应商向招标人） 2 - 开标过程中（供应商向招标人） 3 -评标过程中（专家向供应商 澄清 ） 4- 中标公示期（供应商向招标人）")
    private String doubtType;

    @ApiModelProperty(value = "发起质疑开始时间")
    private Date startTime;

    @ApiModelProperty(value = "质疑回复截止时间")
    private Date endTime;

}
