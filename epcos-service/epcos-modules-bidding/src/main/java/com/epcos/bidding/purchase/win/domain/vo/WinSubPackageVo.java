package com.epcos.bidding.purchase.win.domain.vo;

import com.epcos.common.core.domain.review.ReviewSummaryVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/9 17:11
 */
@Data
public class WinSubPackageVo implements Serializable {

    private static final long serialVersionUID = 188617088844916473L;

    @ApiModelProperty(value = "是否已确定选择中标人[0-未选择，1-已经选择]")
    private Integer selected;

    @ApiModelProperty(value = "审批类型")
    private String auditType;

    @ApiModelProperty(value = "候选公告信息")
    private ResultBulletinVo candidateInfo;

    @ApiModelProperty(value = "中标结果公告信息")
    private ResultBulletinVo winInfo;

    @ApiModelProperty(value = "中标信息")
    private List<WinBidVo> winBidVoList;

    @ApiModelProperty(value = "评审结果")
    private ReviewSummaryVo summaryVo;

    @ApiModelProperty("评审类型：  1-评审 ， 0-调研   2-投票")
    private Integer reviewMethod;
}
