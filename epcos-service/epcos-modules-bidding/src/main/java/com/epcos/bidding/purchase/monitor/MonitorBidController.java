package com.epcos.bidding.purchase.monitor;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.api.params.dto.monitor.CreateMonitorDto;
import com.epcos.bidding.purchase.monitor.business.api.IMonitorBidApi;
import com.epcos.bidding.purchase.monitor.domain.dao.MonitorBidDao;
import com.epcos.bidding.purchase.monitor.domain.dto.RetrieveMonitorDto;
import com.epcos.bidding.purchase.monitor.domain.dto.UpdateMonitorDto;
import com.epcos.bidding.purchase.monitor.domain.vo.MonitorInfoVo;
import com.epcos.bidding.purchase.monitor.domain.vo.MonitorVo;
import com.epcos.bidding.purchase.remote.RemoteToOtherServiceApi;
import com.epcos.common.core.constant.UserConstants;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.utils.StringUtils;
import com.epcos.common.core.web.page.TableDataVo;
import com.epcos.common.log.annotation.Log;
import com.epcos.common.log.enums.BusinessType;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.system.api.model.EsignVO;
import com.epcos.system.api.model.FUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/5 13:54
 */
@Api(tags = "监标人")
@AllArgsConstructor
@RestController
@RequestMapping("/monitor")
public class MonitorBidController {

    private final IMonitorBidApi monitorBidApi;
    private final RemoteToOtherServiceApi remoteToOtherServiceApi;


    @ApiOperation(value = "添加监标人")
    @Log(title = "添加监标人", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/addMonitor")
    public R<Boolean> addMonitor(@RequestBody CreateMonitorDto dto) {
        return R.ok(monitorBidApi.addMonitor(dto));
    }


    @ApiOperation(value = "添加监标信息")
    @Log(title = "添加监标信息", businessType = BusinessType.INSERT)
    @PostMapping(value = "/addMonitorInfo")
    public R<Boolean> addMonitorInfo(@RequestBody CreateMonitorDto dto) {
        return R.ok(monitorBidApi.addMonitorInfo(dto));
    }


    @ApiOperation(value = "修改监标人")
    @Log(title = "修改监标人", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/editMonitor")
    public R<Boolean> editMonitor(@RequestBody UpdateMonitorDto dto) {
        return R.ok(monitorBidApi.editMonitor(dto));
    }


    @ApiOperation(value = "监标人详情")
    @GetMapping(value = "/monitorInfo")
    public R<List<MonitorInfoVo>> monitorInfo(@RequestParam(value = "buyItemCode") String buyItemCode) {
        return R.ok(monitorBidApi.monitorInfo(buyItemCode));
    }


    @ApiOperation(value = "监标人列表")
    @PostMapping(value = "/monitorPage")
    public TableDataVo<MonitorVo> monitorPage(@RequestBody PageSortEntity<RetrieveMonitorDto> dto) {
        IPage<MonitorVo> page = monitorBidApi.monitorPage(dto);
        List<MonitorVo> voList = page.getRecords();
        return new TableDataVo(voList, page.getTotal());
    }


    @ApiOperation(value = "监标人签字")
    @GetMapping(value = "/signMonitor")
    public R<Boolean> signMonitor(@RequestParam(value = "monitorId") Long monitorId,
                                  @RequestParam(value = "flowId", required = false) String flowId,
                                  @RequestParam(value = "authCode", required = false) String authCode) {
        MonitorBidDao monitorBidDao = monitorBidApi.getById(monitorId);
        if (StringUtils.hasText(monitorBidDao.getFileKey())) {
//            EsignVO seal = remoteToOtherServiceApi.getSeal(SecurityUtils.getUserId());
            if (FUtil.signatureIsOff()) {
//                FUtil.psnSealByCoordinate(monitorBidDao.getFileKey(), String.valueOf(SecurityUtils.getUserId()),null);
                FUtil.startSeal(monitorBidDao.getFileKey(), null, String.valueOf(SecurityUtils.getUserId()), null, UserConstants.PERSON_COORDINATE_SEAL_PARAMETER, flowId, authCode);
            }
        }
        return R.ok(Boolean.TRUE);
    }
}
