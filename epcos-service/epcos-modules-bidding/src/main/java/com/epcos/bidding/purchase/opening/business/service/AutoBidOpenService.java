package com.epcos.bidding.purchase.opening.business.service;

import com.epcos.bidding.purchase.opening.domain.dto.PurchaseBidOpeningDto;
import com.epcos.bidding.purchase.project.base.domain.dto.BidOpenTask;
import com.epcos.bidding.supplier.api.params.SupplierSignUpVo;
import com.epcos.common.core.constant.CacheConstants;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.redis.lock.RedisLock;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
@AllArgsConstructor
public class AutoBidOpenService {

    private final BidOpenService bidOpenService;
    private final RedisTemplate redisTemplate;
    private final RedisLock redisLock;

    // 增加服务关停时间处理
    @PostConstruct
    public void init() {
        checkBidOpenTask();
    }

    // 增加key监听异常处理
    @Scheduled(cron = "0 0 * * * *")
    public void scheduled() {
        checkBidOpenTask();
    }

    private void checkBidOpenTask() {
        Set<String> keys = redisTemplate.keys(CacheConstants.AUTO_BID_OPEN_HASH + "*");
        if (keys.isEmpty()) {
            return;
        }
        long currentTimeMillis = System.currentTimeMillis();
        for (String key : keys) {
            Map<String, Object> map = redisTemplate.opsForHash().entries(key);
            if (map.isEmpty()) {
                continue;
            }
            String buyItemCode = (String) map.get("buyItemCode");
            String subpackageCode = (String) map.get("subpackageCode");
            String status = (String) map.get("status");
            Date bidOpenTime = (Date) map.get("bidOpenTime");
            Integer failCount = (Integer) map.get("failCount");
            if (bidOpenTime.getTime() <= currentTimeMillis && !BidOpenTask.Status.SUCCESS.equals(status)) {
                autoBidOpen(subpackageCode, buyItemCode, ++failCount);
            }
        }
    }

    // 自动开标
    public void autoBidOpen(String subpackageCode, String buyItemCode, Integer failCount) {
        String lockKey = CacheConstants.AUTO_BID_OPEN_LOCK + subpackageCode;
        String hashKey = CacheConstants.AUTO_BID_OPEN_HASH + subpackageCode;
        PurchaseBidOpeningDto dto = null;
        int cacheDay = 60;
        try {
            String lock = redisLock.lock(lockKey, subpackageCode, 30);
            if (Objects.isNull(lock)) {
                log.error("自动开标-获取锁失败，其他线程处理，跳过本次处理，lockKey={}", lockKey);
                return;
            }
            // 检查任务状态
            String status = getRedisHashValue(hashKey, "status", String.class);
            if (BidOpenTask.Status.SUCCESS.equals(status)) {
                log.info("自动开标-任务已完成，跳过本次处理，lockKey={}", lockKey);
                return;
            }
            List<SupplierSignUpVo> signVoList = bidOpenService.bidderList(subpackageCode)
                    .stream()
                    .map(i -> ((SupplierSignUpVo) i))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(signVoList)) {
                redisTemplate.opsForHash().put(hashKey, "status", BidOpenTask.Status.FAILED);
                redisTemplate.opsForHash().put(hashKey, "msg", "当前项目无有效报名供应商，系统无法完成自动开标。");
                redisTemplate.expire(hashKey, cacheDay, TimeUnit.DAYS);
                return;
            }
            dto = new PurchaseBidOpeningDto();
            dto.setBuyItemCode(buyItemCode);
            dto.setSubpackageCode(subpackageCode);
            dto.setSignUpList(signVoList);
            bidOpenService.start(dto);
            // 更新状态为已完成
            redisTemplate.opsForHash().put(hashKey, "status", BidOpenTask.Status.SUCCESS);
            redisTemplate.opsForHash().put(hashKey, "successTime", new Date());
            redisTemplate.expire(hashKey, 1, TimeUnit.DAYS);
        } catch (ServiceException e) {
            log.error("自动开标-业务检查异常-无法自动开标-设置【{}】天过期-可在【redis】【{}】检查异常信息：dto={}",
                    cacheDay, hashKey, dto, e);
            redisTemplate.expire(hashKey, cacheDay, TimeUnit.DAYS);
            redisTemplate.opsForHash().put(hashKey, "msg", e.getMessage());
            redisTemplate.opsForHash().put(hashKey, "status", BidOpenTask.Status.FAILED);
        } catch (Exception e) {
            log.error("自动开标-非预期异常-重试数值={}, hashKey={}, dto={}", failCount, hashKey, dto, e);
            redisTemplate.opsForHash().put(hashKey, "failCount", failCount);
            redisTemplate.opsForHash().put(hashKey, "status", BidOpenTask.Status.RETRYING);
            redisTemplate.opsForHash().put(hashKey, "msg", "自动开标异常，进行重试");
            redisTemplate.opsForHash().put(hashKey, "lastRetryTime", new Date());
            int maxRetry = 5;
            if (failCount <= maxRetry) {
                // 退避重试，1分钟，2分钟，4分钟，8分钟，16分钟
                long delaySeconds = (long) Math.min(Math.pow(2, failCount - 1) * 60, 60 * 60);
                String retryKey = CacheConstants.AUTO_BID_OPEN + subpackageCode;
                redisTemplate.opsForValue().set(retryKey, subpackageCode, delaySeconds, TimeUnit.SECONDS);
            } else {
                log.error("自动开标失败-超过 {} 次重数，不再重试，lockKey={}", maxRetry, lockKey);
                redisTemplate.opsForHash().put(hashKey, "status", BidOpenTask.Status.ERROR);
            }
        } finally {
            redisLock.release(lockKey, subpackageCode);
        }
    }

    public <T> T getRedisHashValue(String key, String hashKey, Class<T> type) {
        return type.cast(redisTemplate.opsForHash().get(key, hashKey));
    }


    public String getAutoBidOpenMsg(String subpackageCode) {
        return getRedisHashValue(CacheConstants.AUTO_BID_OPEN_HASH + subpackageCode, "msg", String.class);
    }

}
