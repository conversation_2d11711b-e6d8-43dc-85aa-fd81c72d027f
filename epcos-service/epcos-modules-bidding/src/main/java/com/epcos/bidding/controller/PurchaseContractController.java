package com.epcos.bidding.controller;

import com.epcos.bidding.audit.api.BizAuditRelationDto;
import com.epcos.bidding.biz.business.api.IBizAuditRelationApi;
import com.epcos.bidding.common.AuditTypeEnum;
import com.epcos.bidding.common.SubpackageCodeAndSupplierIdEntity;
import com.epcos.bidding.common.annotaion.GetBizAndAudit;
import com.epcos.bidding.common.annotaion.GetCommon;
import com.epcos.bidding.common.annotaion.GetSimpleItem;
import com.epcos.bidding.common.annotaion.NotNullUserId;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.purchase.api.params.vo.buyitem.ItemSubpackageVo;
import com.epcos.bidding.purchase.contract.business.api.IPurchaseContractApi;
import com.epcos.bidding.supplier.api.params.SupplierContractApprovalDto;
import com.epcos.bidding.supplier.contract.business.api.ISupplierContractApi;
import com.epcos.bidding.supplier.contract.domain.dao.SupplierContractDao;
import com.epcos.bidding.supplier.contract.domain.dto.SupplierContractAuditDto;
import com.epcos.bidding.supplier.contract.domain.dto.SupplierContractUpdateDto;
import com.epcos.bidding.supplier.contract.domain.vo.SupplierContractAndAuditVo;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.web.controller.BaseController;
import com.epcos.common.log.annotation.Log;
import com.epcos.common.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/20 16:23
 */
@Api(tags = "合同管理")
@Slf4j
@RestController
@RequestMapping("/purchase.contract")
@RequiredArgsConstructor
public class PurchaseContractController extends BaseController {

    private final IPurchaseContractApi contractService;
    private final ISupplierContractApi supplierContractApi;
    private final IBizAuditRelationApi bizAuditRelationApi;

    @NotNullUserId
    @ApiOperation("查询项目下所有包合同与审批")
    @GetMapping("/list")
    @GetSimpleItem(common = @GetCommon(async = true, buyItemCodeEL = "#buyItemCode"))
    R<List<ItemSubpackageVo<List<SupplierContractAndAuditVo>>>> query(@RequestParam("buyItemCode")
                                                                      @NotBlank(message = "采购项目编码必填")
                                                                      String buyItemCode) {
        SupplierContractDao queryDao = new SupplierContractDao();
        queryDao.setBuyItemCode(buyItemCode);
        Map<String, Map<Long, SupplierContractDao>> contractMap = supplierContractApi.list(queryDao)
                .stream().collect(Collectors.groupingBy(SupplierContractDao::getSubpackageCode,
                        Collectors.toMap(SubpackageCodeAndSupplierIdEntity::getSupplierId,
                                Function.identity())));
        List<ItemSubpackageVo<List<SupplierContractAndAuditVo>>> voList = GetUtil.getSimpleItemVo()
                .getSuperPackageVoList().stream().map(i -> {
                    ItemSubpackageVo<List<SupplierContractAndAuditVo>> vo = new ItemSubpackageVo<>(i.getSubpackageCode(), i.getSubpackageName());
                    List<SupplierContractAndAuditVo> vos = Optional.ofNullable(contractMap.get(i.getSubpackageCode()))
                            .map(it -> it.keySet().stream().map(userId -> {
                                                SupplierContractAndAuditVo contractAndAuditVo = new SupplierContractAndAuditVo();
                                                Optional.ofNullable(supplierContractApi.findOneVo(i.getSubpackageCode(), userId))
                                                        .ifPresent(contractAndAuditVo::setSupplierContractVo);
                                                Optional.ofNullable(
                                                        bizAuditRelationApi.queryAuditVo(
                                                                BizAuditRelationDto.builder()
                                                                        .auditType(AuditTypeEnum.WINNING_CONTRACT.getType())
                                                                        .buyItemCode(buyItemCode)
                                                                        .subpackageCode(i.getSubpackageCode())
                                                                        .userId(SecurityUtils.getUserId())
                                                                        .businessId(it.get(userId).getId())
                                                                        .build()
                                                        )
                                                ).ifPresent(a -> contractAndAuditVo.setAuditProcessDto(a.convert()));
                                                return contractAndAuditVo;
                                            }
                                    ).collect(Collectors.toList())
                            ).orElse(Collections.emptyList());
                    vo.setData(vos);
                    return vo;
                }).collect(Collectors.toList());
        return R.ok(voList);
    }

    @NotNullUserId
    @GetBizAndAudit(auditType = AuditTypeEnum.WINNING_CONTRACT,
            common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode"),
            bizIdEL = "#dto.contractId", ignoreField = {"orgCode"})
    @ApiOperation(value = "审核合同")
    @Log(title = "审核合同[接口：auditContract]", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/auditContract")
    public R<Boolean> auditContract(@RequestBody @Valid SupplierContractApprovalDto dto) {
        bizAuditRelationApi.initiateAndRecord(
                GetUtil.getBizAndAudit().getBizAuditRelation(),
                GetUtil.getBizAndAudit().getAuditCreator());
        return R.ok();
    }

    @ApiOperation(value = "修改合同模板")
    @Log(title = "修改合同模板[接口：editContractTemplate]", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/editContractTemplate")
    public R<Boolean> editContractTemplate(@RequestBody @Valid SupplierContractUpdateDto dto) {
        return R.ok(contractService.editContractTemplate(dto));
    }

    @NotNullUserId
    @ApiOperation(value = "退回合同模板")
    @Log(title = "退回合同模板[接口：backContractTemplate]", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/backContractTemplate")
    public R<Boolean> backContractTemplate(@RequestBody @Valid SupplierContractAuditDto dto) {
        return R.ok(contractService.backContractTemplate(SecurityUtils.getUserId(), dto));
    }
}
