package com.epcos.bidding.common.aspects.supplier;

import cn.hutool.core.text.CharSequenceUtil;
import com.epcos.bidding.common.annotaion.GetSignUp;
import com.epcos.bidding.common.aspects.AbstractMethod;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.supplier.sign.business.api.ISupplierSignUpApi;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierSignUpDao;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.EvalSpelUtil;
import com.epcos.common.core.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Objects;

/**
 * 查询供应商报名信息
 */
@Slf4j
@Aspect
@Component
public class SignUpAspectFilterChainPost extends AbstractMethod<GetSignUp, SupplierSignUpDao> {

    public SignUpAspectFilterChainPost() {
        super(GetUtil.GET_SIGN_UP, "查询供应商报名信息");
    }

    @Autowired
    private ISupplierSignUpApi supplierSignUpApi;

    @Override
    @Around(value = "@annotation(getSignUp)")
    public Object around(ProceedingJoinPoint point, GetSignUp getSignUp) {
        return super.around(point, getSignUp);
    }

    @Override
    public SupplierSignUpDao businessMethods(JoinPoint point, GetSignUp annotation) {
        String subpackageCode = threadLocal.get().getSubpackageCode();
        if (CharSequenceUtil.isBlank(subpackageCode)) {
            throw new ServiceException("subpackageCode 参数必填：" + subpackageCode);
        }
        Method method = ((MethodSignature) point.getSignature()).getMethod();
        Long supplierId;
        if (CharSequenceUtil.isBlank(annotation.supplierIdEL())) {
            supplierId = SecurityUtils.getUserId();
        } else {
            supplierId = EvalSpelUtil.get(method, point.getArgs(), annotation.supplierIdEL(), Long.class);
        }
        if (Objects.isNull(supplierId) || CharSequenceUtil.isBlank(subpackageCode)) {
            throw new ServiceException("查询供应商报名信息，参数：subpackageCode supplierId 必填");
        }
        return supplierSignUpApi.query(subpackageCode, supplierId);
    }
}
