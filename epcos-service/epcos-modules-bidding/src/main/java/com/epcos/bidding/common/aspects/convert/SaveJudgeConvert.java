package com.epcos.bidding.common.aspects.convert;

import com.epcos.bidding.common.annotaion.GetCommon;
import com.epcos.bidding.common.annotaion.GetItem;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.common.aspects.sms.SmsContent;
import com.epcos.bidding.common.aspects.vo.GetItemVo;
import com.epcos.bidding.purchase.extract.domain.dto.JudgeExtractDto;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.common.core.utils.DateUtils;
import com.epcos.common.core.utils.StringUtils;
import com.epcos.common.sms.ISmsApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/23 15:33
 */
@Slf4j
@Component
public class SaveJudgeConvert implements GetParamConvert<JudgeExtractDto, SmsContent> {

    @Autowired
    private ISmsApi smsApi;
    @Autowired
    private ISubPackageApi subPackageApi;

    @Override
    @GetItem(common = @GetCommon(subpackageCodeEL = "#judgeExtractDto.subpackageCode"))
    public SmsContent doConvert(JudgeExtractDto judgeExtractDto) {

        GetItemVo itemVo = GetUtil.getItemVo();
        Map<String, SubpackageDao> subMap = itemVo.getSubpackageDaoList().stream()
                .collect(Collectors.toMap(SubpackageDao::getSubpackageCode, Function.identity()));
        SubpackageDao subpackageDao = subPackageApi.findBySub(judgeExtractDto.getSubpackageCode());
        judgeExtractDto.getVoList().forEach(e -> {
            Date meetingTime = subMap.get(subpackageDao.getSubpackageCode()).getMeetingTime();
            StringBuilder builder = new StringBuilder();
            builder.append(itemVo.getBuyItemVo().getBuyItemName())
                    .append(" 项目, ")
                    .append(subMap.get(judgeExtractDto.getSubpackageCode()).getSubpackageName())
                    .append("将于")
                    .append(Objects.isNull(meetingTime) ? "" : DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", meetingTime))
                    .append("在")
                    .append(StringUtils.hasText(subpackageDao.getBidOpenAddress()) ? subpackageDao.getBidOpenAddress() : "线上")
                    .append("进行评审")
                    .append(" 。 已选择您为评审专家，请登录电子化招标采购平台查看详情。")
            ;
            log.error("发送短信给评审专家,专家手机号:{}，短信内容:{}", e.getExpertCellphone(), builder.toString());
            if (StringUtils.hasText(e.getExpertCellphone())) {
                smsApi.sendSms(e.getExpertCellphone(), builder.toString());
            }
            log.error("发送短信给评审专家成功,专家手机号:{}，短信内容:{}", e.getExpertCellphone(), builder.toString());
        });
        return new SmsContent();
    }
}
