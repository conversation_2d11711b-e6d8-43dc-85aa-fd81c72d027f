package com.epcos.bidding.purchase.process.mapping;

import com.epcos.bidding.purchase.process.domain.dao.DoubtTimeDao;
import com.epcos.bidding.purchase.process.domain.dto.DoubtTimeDto;
import com.epcos.bidding.purchase.process.domain.vo.DoubtTimeVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/6 11:53
 */
@Mapper
public interface DoubtTimeConvert {

    DoubtTimeConvert INSTANCE = Mappers.getMapper(DoubtTimeConvert.class);


    DoubtTimeDao convert(DoubtTimeDto doubtTimeDto);

    DoubtTimeVo convert(DoubtTimeDao doubtTimeDao);
}
