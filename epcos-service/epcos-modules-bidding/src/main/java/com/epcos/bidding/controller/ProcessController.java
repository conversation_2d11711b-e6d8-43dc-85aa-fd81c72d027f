package com.epcos.bidding.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.common.SubpackageCodeAndSupplierIdEntity;
import com.epcos.bidding.common.annotaion.GetCommon;
import com.epcos.bidding.common.annotaion.GetSimpleItem;
import com.epcos.bidding.common.annotaion.Sms;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.common.aspects.convert.SupplierAuditConvert;
import com.epcos.bidding.common.aspects.vo.GetSimpleItemVo;
import com.epcos.bidding.purchase.api.params.DoubtVo;
import com.epcos.bidding.purchase.api.params.dto.AskDto;
import com.epcos.bidding.purchase.api.params.dto.DoubtDto;
import com.epcos.bidding.purchase.api.params.dto.QueryDoubtDto;
import com.epcos.bidding.purchase.api.params.dto.ReplyDto;
import com.epcos.bidding.purchase.api.params.vo.answer.AnswerVo;
import com.epcos.bidding.purchase.api.params.vo.buyitem.ItemSubpackageVo;
import com.epcos.bidding.purchase.api.params.vo.buyitem.SupplierSignContrastVo;
import com.epcos.bidding.purchase.bulletin.business.api.IBulletinApi;
import com.epcos.bidding.purchase.bulletin.domain.dao.BulletinDao;
import com.epcos.bidding.purchase.bulletin.domain.dto.BulletinSupplierDto;
import com.epcos.bidding.purchase.excel.business.ExcelService;
import com.epcos.bidding.purchase.opening.business.api.IPurchaseBidOpeningApi;
import com.epcos.bidding.purchase.opening.domain.dao.PurchaseBidOpeningDao;
import com.epcos.bidding.purchase.process.business.api.IAskAnswerApi;
import com.epcos.bidding.purchase.process.business.api.IDoubtApi;
import com.epcos.bidding.purchase.process.business.api.IDoubtTimeApi;
import com.epcos.bidding.purchase.process.business.api.IProcessApi;
import com.epcos.bidding.purchase.process.domain.dto.DoubtTimeDto;
import com.epcos.bidding.purchase.process.domain.dto.MeetDto;
import com.epcos.bidding.purchase.process.domain.dto.SignUpStatusDto;
import com.epcos.bidding.purchase.process.domain.vo.BuyItemFilePageVo;
import com.epcos.bidding.purchase.process.domain.vo.BuyItemFileVo;
import com.epcos.bidding.purchase.process.domain.vo.BuyItemMeetVo;
import com.epcos.bidding.purchase.process.domain.vo.DoubtTimeVo;
import com.epcos.bidding.purchase.process.domain.vo.FileInfoVo;
import com.epcos.bidding.purchase.process.domain.vo.SubpackageFileVo;
import com.epcos.bidding.purchase.process.domain.vo.SupplierRespInfoVo;
import com.epcos.bidding.purchase.process.mapping.DtoVoConvert;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.purchase.project.xyzy.domain.dto.XYZYBuyItemQueryDto;
import com.epcos.bidding.purchase.remote.RemoteToOtherServiceApi;
import com.epcos.bidding.supplier.answer.business.api.IAnswerFileQuoteFormApi;
import com.epcos.bidding.supplier.api.params.AnswerFileQuoteFormVo;
import com.epcos.bidding.supplier.api.params.SupplierSignUpStatusDto;
import com.epcos.bidding.supplier.api.params.SupplierSignUpVo;
import com.epcos.bidding.supplier.common.SupplierInfoEntityShared;
import com.epcos.bidding.supplier.sign.business.api.ISupplierBidderApi;
import com.epcos.bidding.supplier.sign.business.api.ISupplierBidderRelatedApi;
import com.epcos.bidding.supplier.sign.business.api.ISupplierBidderShareholderApi;
import com.epcos.bidding.supplier.sign.business.api.ISupplierSignApi;
import com.epcos.bidding.supplier.sign.business.api.ISupplierSignUpApi;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierBidderRelatedDao;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierSignUpDao;
import com.epcos.bidding.supplier.sign.domain.dto.SupplierSignQueryDto;
import com.epcos.common.core.constant.UserConstants;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.enums.ClientEnum;
import com.epcos.common.core.enums.FileTypeNameEnum;
import com.epcos.common.core.enums.SmsEnum;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.utils.bean.BeanUtils;
import com.epcos.common.core.web.domain.AjaxResult;
import com.epcos.common.core.web.page.TableDataVo;
import com.epcos.common.log.annotation.Log;
import com.epcos.common.log.enums.BusinessType;
import com.epcos.common.security.annotation.Logical;
import com.epcos.common.security.annotation.RequiresPermissions;
import com.epcos.epcfile.api.domain.vo.ProjectDocumentVo;
import com.epcos.system.api.RemoteSystemService;
import com.epcos.system.api.RemoteUserService;
import com.epcos.system.api.domain.common.SupplierBidderRelatedVo;
import com.epcos.system.api.domain.common.SupplierBidderShareholderVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.epcos.bidding.common.enums.BulletinTypeEnum.BIDDING_ANNOUNCEMENT;
import static com.epcos.common.core.constant.FileTypeNameConstants.ANSWER_FILE;
import static com.epcos.common.core.constant.FileTypeNameConstants.TENDER_DOC_ENCLOSURE;
import static com.epcos.common.core.constant.FileTypeNameConstants.TENDER_DOC_PDF_ENCLOSURE;
import static com.epcos.common.core.constant.FileTypeNameConstants.TENDER_DOC_PDF_QUOTE_FORM;
import static com.epcos.common.core.constant.PurchaseConstants.BulletinAudit.PASS;
import static com.epcos.common.core.constant.PurchaseConstants.Currency.TWO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/26 11:33
 */
@Slf4j
@Api(tags = "采购流程中[包含答疑、质疑等]")
@RestController
@RequestMapping("/purchase/process")
@RequiredArgsConstructor
public class ProcessController {

    private final ISubPackageApi subpackageApi;
    private final IProcessApi processApi;
    private final IBulletinApi bulletinApi;
    private final ISupplierSignApi supplierSignApi;
    private final ISupplierSignUpApi supplierSignUpApi;
    private final IAskAnswerApi askAnswerApi;
    private final IDoubtTimeApi doubtTimeApi;
    private final IDoubtApi doubtApi;
    private final ExcelService ExcelService;
    private final IAnswerFileQuoteFormApi answerFileQuoteFormApi;
    private final RemoteToOtherServiceApi remoteToOtherServiceApi;
    private final ISupplierBidderShareholderApi supplierBidderShareholderApi;
    private final ISupplierBidderRelatedApi supplierBidderRelatedApi;
    private final ISupplierBidderApi supplierBidderApi;
    private final IPurchaseBidOpeningApi purchaseBidOpeningApi;
    private final RemoteSystemService remoteSystemService;

    /*=================================================== 报名列表 ===================================================*/

    @RequiresPermissions("project:process:query")
    @ApiOperation(value = "报名列表")
    @GetMapping(value = "/supplierSignList")
    @ApiImplicitParam(name = "buyItemCode", value = "项目code", paramType = "query", dataTypeClass = String.class)
    public R<List<ItemSubpackageVo>> supplierSignList(@NotBlank String buyItemCode) {
        // 标段信息
        List<SubpackageDao> subpackageDaoList = subpackageApi.findByBuyItemCode(buyItemCode);

        List<ItemSubpackageVo> voList = subpackageDaoList.stream().map(s -> {
            ItemSubpackageVo vo = new ItemSubpackageVo(s.getSubpackageCode(), s.getSubpackageName());
            List<SupplierSignUpVo> upVoList = supplierSignApi.query(new SupplierSignQueryDto(null, s.getSubpackageCode()), null);
            vo.setData(upVoList);
            return vo;
        }).collect(Collectors.toList());
        return R.ok(voList);
    }


    @Log(title = "审核供应商报名信息[接口：auditSignInfo]", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "审核供应商报名信息")
    @Sms(clients = ClientEnum.XY, smsEnum = SmsEnum.SMS_SUPPLIER_PASS, convert = SupplierAuditConvert.class)
    @PostMapping(value = "/auditSignInfo")
    public R<Boolean> auditSignInfo(@RequestBody SignUpStatusDto dto) {
        SupplierSignUpStatusDto convert = DtoVoConvert.INSTANCE.convert(dto);
        if (dto.getReviewStatus() == 1) {
            Optional.ofNullable(remoteToOtherServiceApi.getConfigKeyData(UserConstants.BID_FILE_QUALIFIED, Boolean.class))
                    .ifPresent(convert::setQualified);
        }
        supplierSignUpApi.updateSignUpStatus(dto.getSubpackageCode(), convert);
        //发布短信订阅时间

        return R.ok();
    }

    @ApiOperation("报名单位股东、关联企业对比")
    @GetMapping("/supplierSignContrast")
    public R<List<SupplierSignContrastVo>> supplierSignContrast(@NotBlank String subpackageCode) {
        Map<Long, List<SupplierBidderRelatedDao>> listMap = supplierBidderRelatedApi.list(subpackageCode, null).stream()
                .collect(Collectors.groupingBy(SubpackageCodeAndSupplierIdEntity::getSupplierId));
        Map<Long, String> supplierNameMap = supplierBidderApi.findBy(subpackageCode, listMap.keySet()).stream()
                .collect(Collectors.toMap(SubpackageCodeAndSupplierIdEntity::getSupplierId, SupplierInfoEntityShared::getBidderName));
        // 记录所有报名供应商的关联公司及其中的股东或法人
        List<SupplierSignContrastVo> result = listMap.keySet().stream()
                .map(i -> {
                    SupplierSignContrastVo vo = new SupplierSignContrastVo();
                    vo.setSupplierId(i);
                    vo.setBidderName(supplierNameMap.get(i));
                    // 当前关联公司
                    List<SupplierBidderRelatedVo> relatedVoList = listMap.get(i).stream().map(j -> {
                        SupplierBidderRelatedVo relatedVo = new SupplierBidderRelatedVo();
                        relatedVo.setRelatedId(j.getId());
                        relatedVo.setBidderName(j.getBidderName());
                        relatedVo.setLicNumber(j.getLicNumber());
                        // 当前关联公司的股东或法人
                        List<SupplierBidderShareholderVo> shareholderVos = supplierBidderShareholderApi.list(j.getId())
                                .stream()
                                .map(k -> {
                                    SupplierBidderShareholderVo shareholderVo = new SupplierBidderShareholderVo();
                                    shareholderVo.setId(k.getId());
                                    shareholderVo.setName(k.getName());
                                    shareholderVo.setNumber(k.getNumber());
                                    return shareholderVo;
                                }).collect(Collectors.toList());
                        relatedVo.setShareholderList(shareholderVos);
                        return relatedVo;
                    }).collect(Collectors.toList());
                    vo.setRelatedList(relatedVoList);
                    return vo;
                }).collect(Collectors.toList());
        analyzeCrossLabeling(result);
        return R.ok(result);
    }

    /**
     * 分析当前供应商与其他并联企业、股东对比
     */
    private void analyzeCrossLabeling(List<SupplierSignContrastVo> voList) {
        if (CollUtil.isEmpty(voList) || voList.size() == 1) {
            return;
        }
        voList.forEach(i -> {
            // 当前公司关联公司信息id
            List<SupplierBidderRelatedVo> currentRelatedList = i.getRelatedList();
            // 其余供应商
            List<SupplierSignContrastVo> otherList = voList.stream()
                    .filter(j -> !Objects.equals(j.getSupplierId(), i.getSupplierId())).collect(Collectors.toList());
            // 求 licNumber 相同的
            List<Long> relatedCompanyList = getRelatedCompany(currentRelatedList, otherList);
            i.setRelatedCompanyList(relatedCompanyList);

            List<Long> relatedShareholderList = getRelatedShareholder(currentRelatedList, otherList);
            i.setRelatedShareholderList(relatedShareholderList);
        });
    }

    /**
     * 查询当前关联公司的股东  与 其他报名供应商关联公司的股东  证件号码相同的
     */
    private List<Long> getRelatedShareholder(List<SupplierBidderRelatedVo> currentRelatedList, List<SupplierSignContrastVo> otherList) {
        Set<String> otherNumerSet = otherList.stream().flatMap(i -> i.getRelatedList().stream())
                .flatMap(i -> i.getShareholderList().stream())
                .map(SupplierBidderShareholderVo::getNumber).collect(Collectors.toSet());
        return currentRelatedList.stream()
                .flatMap(i -> i.getShareholderList().stream())
                .filter(i -> otherNumerSet.contains(i.getNumber()))
                .map(SupplierBidderShareholderVo::getId).collect(Collectors.toList());
    }

    /**
     * 查询当前关联公司  与 其他报名供应商关联公司  信用代码相同的
     */
    private List<Long> getRelatedCompany(List<SupplierBidderRelatedVo> currentRelatedList,
                                         List<SupplierSignContrastVo> otherList) {
        Set<String> otherLicNumberSet = otherList.stream()
                .flatMap(i -> i.getRelatedList().stream())
                .map(SupplierBidderRelatedVo::getLicNumber)
                .collect(Collectors.toSet());
        return currentRelatedList.stream()
                .filter(i -> otherLicNumberSet.contains(i.getLicNumber()))
                .map(SupplierBidderRelatedVo::getRelatedId).collect(Collectors.toList());
    }

    @ApiOperation(value = "查看响应附加条件")
    @GetMapping(value = "/supplierCondition")
    @GetSimpleItem(common = @GetCommon(subpackageCodeEL = "#subpackageCode"))
    @ApiImplicitParam(name = "buyItemCode", value = "项目code", paramType = "query", dataTypeClass = String.class)
    public R<SupplierRespInfoVo> supplierCondition(@NotBlank String subpackageCode, @NotNull Long supplierId) {
        GetSimpleItemVo simpleItemVo = GetUtil.getSimpleItemVo();
        BulletinDao bulletinDao = bulletinApi.findBySubpackageCodeAndBulletinTypeInAndAuditStatusIn(simpleItemVo.getBuyItemCode(),
                null, Collections.singletonList(BIDDING_ANNOUNCEMENT.getKey()), Collections.singletonList(PASS)).get(0);
        SupplierSignUpDao query = supplierSignUpApi.query(subpackageCode, supplierId);
        SupplierRespInfoVo respInfoVo = new SupplierRespInfoVo();
        BeanUtils.copyProperties(query, respInfoVo);
        List<BulletinSupplierDto> voList = JSONArray.parseArray(bulletinDao.getSupplierAddition(), BulletinSupplierDto.class);
        BulletinSupplierDto supplierDto = voList.stream().filter(v -> v.getSubpackageCode().equals(subpackageCode)).collect(Collectors.toList()).get(0);
        respInfoVo.setSupplierAdditionList(supplierDto.getAttributeVoList());
        return R.ok(respInfoVo);
    }

    @ApiOperation(value = "查询第0轮报价内容")
    @GetMapping(value = "/quoteFormInfo")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "subpackageCode", value = "包code", paramType = "query", dataTypeClass = String.class),
            @ApiImplicitParam(name = "supplierId", value = "供应商id", paramType = "query", dataTypeClass = Long.class)
    })
    public R<AnswerFileQuoteFormVo> quoteForm(@NotBlank String subpackageCode, @NotNull Long supplierId) {
        return R.ok(answerFileQuoteFormApi.quoteForm(subpackageCode, supplierId, 0));
    }

    /**
     * 以标段导出报名投标人信息excel
     */
    @ApiOperation("以标段导出报名投标人信息excel")
    @GetMapping("/exportExcelOfRegisteredBidders")
    public ResponseEntity<byte[]> exportExcelOfRegisteredBidders(@NotBlank String subpackageCode) {
        return ExcelService.generateSignExcel(subpackageCode);
    }

    @ApiOperation(value = "是否允许响应")
    @Log(title = "是否允许响应[接口：allow]", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/allow")
    public R<Boolean> allow(@RequestBody @Validated SignUpStatusDto dto) {
        SupplierSignUpStatusDto convert = DtoVoConvert.INSTANCE.convert(dto);
        supplierSignUpApi.updateSignUpStatus(dto.getSubpackageCode(), convert);
        return R.ok();
    }

    @ApiOperation(value = "是否合格供应商")
    @Log(title = "是否合格供应商[接口：qualify]", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/qualify")
    public R<Boolean> qualify(@RequestBody @Validated SignUpStatusDto dto) {
        SupplierSignUpStatusDto convert = DtoVoConvert.INSTANCE.convert(dto);
        supplierSignUpApi.updateSignUpStatus(dto.getSubpackageCode(), convert);
        return R.ok();
    }

    /*=================================================== 质疑时间 ===================================================*/

    /**
     * 保存质疑时间
     * <p>
     * 《质疑的四个阶段及时间》
     * 1、开标之前提出质疑截止时间和质疑回复截止时间
     * 2、开标过程中提出质疑截止时间和质疑回复截止时间
     * 3、评标过程中提出质疑截止时间和质疑回复截止时间
     * 4、中标公示期提出质疑截止时间和质疑回复截止时间
     * <p>
     * 每个时间都单独的存一条记录，且一旦确定不可修改
     *
     * @return
     */
    @ApiOperation(value = "保存质疑时间------[质疑时间]")
    @Log(title = "保存质疑时间", businessType = BusinessType.INSERT)
    @PostMapping(value = "/saveTime")
    public R<Boolean> saveTime(@RequestBody @Validated DoubtTimeDto dto) {
        return R.ok(doubtTimeApi.saveTime(dto));
    }

    @ApiOperation(value = "查询质疑时间------[质疑时间]")
    @ApiImplicitParam(name = "subpackageCode", value = "包编号", paramType = "query", dataTypeClass = String.class, required = true)
    @GetMapping(value = "/queryDoubtTime")
    public R<List<DoubtTimeVo>> queryDoubtTime(@NotNull(message = "参数必填") String subpackageCode) {
        List<DoubtTimeVo> voList = doubtTimeApi.findBySubpackageCode(subpackageCode);
        if (CollectionUtils.isEmpty(voList)) {
            return R.ok(Collections.EMPTY_LIST);
        }
        return R.ok(voList);
    }

    /*=================================================== 质疑 ===================================================*/

    /**
     * 提出/回复质疑通用接口
     * 发起质疑时，需要判断招标办是否针对发起的标段设置各阶段的质疑时间
     * 若没有设置时间则或发起质疑时间已过则不可质疑
     * 回复质疑时，只有被质疑时才可回复且同样需要设置时间
     * 不可超过回复质疑时间，超过则不可回复
     * <p>
     * 《质疑的四个阶段及时间》
     * 1、开标之前提出质疑截止时间和质疑回复截止时间
     * 2、开标过程中提出质疑截止时间和质疑回复截止时间
     * 3、评标过程中提出质疑截止时间和质疑回复截止时间
     * 4、中标公示期提出质疑截止时间和质疑回复截止时间
     * <p>
     * 提出质疑
     * 专家向供应商发起质疑
     * <p>
     * 专家提出质疑时，必须需要选择某个标段下的供应商进行质疑，且默认遵守
     * 《质疑的四个阶段及时间中的》 3 中设置的时间
     * <p>
     * 供应商向采购办发起质疑
     * 供应商发起质疑，需要从某个标段中质疑，且必须在《质疑的四个阶段及时间》中选择除了 3 以外的阶段
     * 并遵守其中设置的发起质疑时间
     * <p>
     * 回复质疑【回复质疑根据purchase_question_file表中主键id进行回复】
     * 回复质疑则需要遵守《质疑的四个阶段及时间》中设置的回复质疑截止时间，过时或未设置则不可回复
     * 回复质疑只做更新不做新增
     * <p>
     * 供应商回复专家发起的质疑
     * 采购办回复供应商发起的质疑
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "提出/回复质疑------[质疑]")
    @Log(title = "提出/回复质疑------[质疑][接口：askDoubt]", businessType = BusinessType.INSERT)
    @PostMapping(value = "/askDoubt")
    public R<Boolean> askDoubt(@RequestBody DoubtDto dto) {
        doubtApi.askReplyDoubt(dto);
        return R.ok();
    }

    /**
     * 供应商质疑列表
     * 采购人质疑列表
     * 专家质疑列表
     * <p>
     * 采购人质疑列表根据项目查询且根据标段分组
     * 专家及采购人则按照标段查询
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "质疑列表------[质疑]")
    @PostMapping(value = "/doubtList")
    public R<List<DoubtVo>> doubtList(@RequestBody @Validated QueryDoubtDto dto) {
        return R.ok(doubtApi.doubtList(dto));
    }

    /*=================================================== 答疑 ===================================================*/

    @ApiOperation(value = "提出疑问------[答疑]")
    @Log(title = "提出疑问------[答疑][接口：submitAsk]", businessType = BusinessType.INSERT)
    @PostMapping(value = "/submitAsk")
    public R<Boolean> submitAsk(@RequestBody @Validated AskDto dto) {
        return R.ok(askAnswerApi.submitAsk(dto));
    }

    @ApiOperation(value = "回复疑问------[答疑]")
    @Log(title = "回复疑问------[答疑][接口：replyAsk]", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/replyAsk")
    public R<Boolean> replyAsk(@RequestBody @Validated ReplyDto dto) {
        return R.ok(askAnswerApi.replyAsk(dto));
    }

    @ApiOperation(value = "答疑列表------[答疑]")
    @GetMapping(value = "/answerList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "subpackageCode", value = "包code", paramType = "query", dataTypeClass = String.class),
            @ApiImplicitParam(name = "askUserType", value = "提问人类型", paramType = "query", dataTypeClass = String.class)
    })
    public R<List<AnswerVo>> answerList(@NotBlank String subpackageCode, @NotBlank String askUserType) {
        return R.ok(askAnswerApi.answerList(subpackageCode, SecurityUtils.getUserId(), askUserType));
    }


    /*=================================================== 项目文件 ===================================================*/


    @ApiOperation("查询项目归档列表")
    @RequiresPermissions(value = {"project:archive:query", "project:archive:queryDept", "project:archive:queryAll"}, logical = Logical.OR)
    @PostMapping("/buyItemFilePage")
    public TableDataVo<BuyItemFilePageVo> buyItemFilePage(@RequestBody PageSortEntity<XYZYBuyItemQueryDto> dto) {
        IPage<BuyItemFilePageVo> page = processApi.buyItemFilePage(dto);
        List<BuyItemFilePageVo> voList = page.getRecords();
        return new TableDataVo(voList, page.getTotal());
    }


    /**
     * 当前只会获取到 项目类型为
     * public static final String BUY_ITEM_OTHER_FILE = "buy_item_other_file";
     * 类型的 文件
     * <p>
     * 如果后期需要更多 则可以去掉此过滤条件
     *
     * @param buyItemCode
     * @return
     */
    @ApiOperation("获取项目及包的文件信息")
    @RequiresPermissions(value = {"project:archive:query", "project:process:query"}, logical = Logical.OR)
    @GetMapping(value = "/itemFileInfo")
    @GetSimpleItem(common = @GetCommon(buyItemCodeEL = "#buyItemCode", async = true))
    public R<BuyItemFileVo> itemFileInfo(@RequestParam(value = "buyItemCode") String buyItemCode) {
        GetSimpleItemVo simpleItemVo = GetUtil.getSimpleItemVo();

        List<ProjectDocumentVo> itemFileList = remoteToOtherServiceApi.getItemFileInfo(simpleItemVo);

        BuyItemFileVo vo = new BuyItemFileVo();
        BeanUtils.copyProperties(simpleItemVo, vo);
        vo.setFileInfoVoList(fillFileInfo(itemFileList, null));
        List<SubpackageFileVo> subpackageFileVoList = simpleItemVo.getSuperPackageVoList()
                .stream()
                .map(s -> {
                    List<ProjectDocumentVo> subFileList = remoteToOtherServiceApi.getSubFileInfo(
                            simpleItemVo.getYearMonthSplit(),
                            simpleItemVo.getBuyItemCode(),
                            s.getSubpackageCode()
                    );
                    int status;
                    // 查询开标状态
                    PurchaseBidOpeningDao one = purchaseBidOpeningApi.findOne(s.getSubpackageCode());
                    status = Objects.isNull(one) ? 0 : one.getStatus();
                    SubpackageFileVo subpackageFileVo = new SubpackageFileVo();
                    BeanUtils.copyProperties(s, subpackageFileVo);
                    subpackageFileVo.setFileInfoVoList(fillFileInfo(subFileList, status));
                    return subpackageFileVo;
                }).collect(Collectors.toList());
        vo.setSubpackageFileVoList(subpackageFileVoList);
        return R.ok(vo);
    }

    private List<FileInfoVo> fillFileInfo(List<ProjectDocumentVo> itemFileList, Integer status) {
        List<FileInfoVo> fileInfoVoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(itemFileList)) {
            return fileInfoVoList;
        }
//        return itemFileList.stream()
//                .filter(i -> Objects.isNull(status) || status != TWO || !isExcludedFileType(i.getFileTypeName()))
//                .map(i -> {
//                    FileInfoVo infoVo = new FileInfoVo();
//                    infoVo.setFileKey(i.getFileCode());
//                    infoVo.setFileName(i.getFileStoragePath());
//                    return infoVo;
//                })
//                .collect(Collectors.toList());

        String value = remoteToOtherServiceApi.getConfigKeyData(UserConstants.WHETHER_DISPLAY_SUPPLIER_FILE, String.class);
        for (ProjectDocumentVo i : itemFileList) {
            if (Objects.nonNull(status) && TWO != status && "0".equals(value)) {
                if (ANSWER_FILE.equals(i.getFileTypeName())
                        || TENDER_DOC_ENCLOSURE.equals(i.getFileTypeName())
                        || TENDER_DOC_PDF_ENCLOSURE.equals(i.getFileTypeName())
                        || TENDER_DOC_PDF_QUOTE_FORM.equals(i.getFileTypeName())
                        || FileTypeNameEnum.SUPPLIER_ANSWER_UP_ATT.getCode().equals(i.getFileTypeName())
                ) {
                    continue;
                }
            }
            FileInfoVo infoVo = new FileInfoVo();
            infoVo.setFileKey(i.getFileCode());
            infoVo.setFileName(i.getFileStoragePath());
            fileInfoVoList.add(infoVo);
        }
        return fileInfoVoList;
    }



    /*=================================================== 上会安排 ===================================================*/

    @ApiOperation(value = "上会安排页面")
    @PostMapping(value = "/meetingPage")
    public TableDataVo<BuyItemMeetVo> meetingPage(@RequestBody PageSortEntity<MeetDto> dto) {
        IPage<BuyItemMeetVo> page = processApi.meetingPage(dto);
        List<BuyItemMeetVo> voList = page.getRecords();
        return new TableDataVo(voList, page.getTotal());
    }
}
