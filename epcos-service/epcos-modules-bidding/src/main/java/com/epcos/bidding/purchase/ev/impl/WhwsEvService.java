package com.epcos.bidding.purchase.ev.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.utils.BiddingBaseUtil;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.purchase.project.mapping.whws.WHWSBuyItemConvert;
import com.epcos.bidding.purchase.project.whws.business.api.buyitem.WHWSBuyItemApi;
import com.epcos.bidding.purchase.project.whws.domain.dao.BuyItemWHWSDao;
import com.epcos.bidding.workbench.vo.SpecialFieldVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.epcos.common.core.enums.ClientEnum.WS;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/21 15:49
 */
@Slf4j
@Service("ws")
public class WhwsEvService extends AbEvService {

    private final WHWSBuyItemApi whwsBuyItemApi;

    public WhwsEvService(IBuyItemApi buyItemApi, ISubPackageApi subPackageApi, WHWSBuyItemApi whwsBuyItemApi) {
        super(buyItemApi, subPackageApi);
        this.whwsBuyItemApi = whwsBuyItemApi;
    }

    @Override
    public String ev() {
        return WS.getCode();
    }

    @Override
    public boolean isDing() {
        return false;
    }

    @Override
    public String getTableName() {
        return "purchase_whws_buy_item";
    }

    @Override
    public Map<String, String> queryBuyItemMap(Map<String, String> voMap, BuyItemDao buyItemInfo) {
        voMap = BiddingBaseUtil.convert(whwsBuyItemApi.findOneByBuyItemCode(buyItemInfo.getBuyItemCode()));
        return voMap;
    }

    @Override
    public List<SpecialFieldVo> queryDiffInfo(List<String> buyItemCodes, Map<String, List<SubpackageDao>> subMap, List<SpecialFieldVo> voList) {
        List<BuyItemWHWSDao> whwsDaoList = whwsBuyItemApi.findOneByBuyItemCodeList(buyItemCodes);
        whwsDaoList.forEach(dao -> {
            List<SubpackageDao> subpackages = subMap.get(dao.getBuyItemCode());
            subpackages.forEach(
                    sub -> voList.add(WHWSBuyItemConvert.INSTANCE.convert(dao, sub.getSubpackageCode()))
            );
        });
        return voList;
    }

    @Override
    public Boolean delBuyItem(String buyItemCode, List<String> subpackageCodeList) {
        return whwsBuyItemApi.delWHWSBuyItemInfo(buyItemCode);
    }


    @Override
    public List<String> queryAllBuyItemCode() {
        List<BuyItemWHWSDao> bjxkItemList = whwsBuyItemApi.list(Wrappers.lambdaQuery(BuyItemWHWSDao.class)
                .select(BuyItemWHWSDao::getBuyItemCode));
        if (CollectionUtils.isEmpty(bjxkItemList)) {
            return Collections.emptyList();
        }
        List<String> codeList = bjxkItemList.stream().map(BuyItemWHWSDao::getBuyItemCode).collect(Collectors.toList());
        return codeList;
    }
}
