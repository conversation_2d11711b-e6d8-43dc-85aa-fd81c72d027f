package com.epcos.bidding.purchase.extract.domain.vo;

import com.epcos.system.api.domain.vo.ExtractJudgeVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/18 11:07
 */
@Data
public class ExtractLogVo extends ExtractJudgeVo {

    private static final long serialVersionUID = -6409958736665036835L;

    @ApiModelProperty(value = "专家抽取记录主键id")
    private Long extractLogId;

    @ApiModelProperty(value = "抽取时间")
    private Date extractTime;

    @ApiModelProperty(value = "票数")
    private Integer numberVotes;

    @ApiModelProperty(value = "投票人")
    private List<String> judgeNames;

    @ApiModelProperty(value = "是否投票")
    private Integer voting;

    @ApiModelProperty(value = "是否是组长 [0-不是 1-是]")
    private String whetherGroup;

    @ApiModelProperty(value = "是否是科室代表 [0-不是 1-是]")
    private String deptRepresent;

    @ApiModelProperty(value = "该专家是否能到场[0-不能到场,1-正常]")
    private Integer whetherRefuse;

    @ApiModelProperty(value = "不能到场理由")
    private String refuseReason;
}
