package com.epcos.bidding.purchase.pdf.business.generate.impl;

import com.epcos.bidding.purchase.api.params.AttributeVo;
import com.epcos.bidding.purchase.pdf.business.generate.AbGeneratePdf;
import com.epcos.bidding.purchase.project.wzlg.domain.dto.buyitem.WZLGCreateBuyItemDto;
import com.epcos.common.core.domain.pdf.Pdf;
import com.epcos.common.core.utils.DateUtils;
import com.epcos.common.core.utils.StringUtils;
import com.epcos.common.file.pdf.Itext7PdfUtil;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Table;
import lombok.extern.slf4j.Slf4j;

import java.util.LinkedHashMap;
import java.util.List;

import static com.epcos.bidding.purchase.pdf.constans.PdfValues.LG_BUY_ITEM_CONTENT;
import static com.epcos.bidding.purchase.pdf.constans.PdfValues.LG_BUY_ITEM_CONTENT_FILE_NAME;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/31 10:40
 */
@Slf4j
public class BuyItemContentPdfImpl extends AbGeneratePdf {


    @Override
    protected String getFileName() {
        return LG_BUY_ITEM_CONTENT_FILE_NAME + super.getFileName();
    }

    @Override
    protected void generateTitle(Document document) {
        document.add(Itext7PdfUtil.paragraphTitle(LG_BUY_ITEM_CONTENT));
    }

    @Override
    protected void generateTableContent(Document document, Pdf vo) {

        WZLGCreateBuyItemDto buyItemVo = (WZLGCreateBuyItemDto) vo;

        document.add(Itext7PdfUtil.paragraphHeadLeft("申请单位(部室)：" + buyItemVo.getApplyDept()));
        document.add(Itext7PdfUtil.paragraphHeadLeft("申请时间: " + DateUtils.date(buyItemVo.getApplyTime())));

        List<AttributeVo> buyHeaders = buyItemVo.getBuyHeader();

        Table table;
        // 总列数
        int maxColumnWidth = buyHeaders.size() + 1;
        //行跨度，即合并几行
        int rowSpan = 1;
        //列跨度，即合并几列
        int columnSpan = 7;
        //常量用的默认列跨度
        int constColumnSpan = maxColumnWidth - columnSpan;
        table = Itext7PdfUtil.initTable(maxColumnWidth);

        List<LinkedHashMap<String, String>> buyBodyList = buyItemVo.getBuyBody();

        table.addCell(Itext7PdfUtil.cellCenter(rowSpan, constColumnSpan, Itext7PdfUtil.paragraphHeadCenter("项目名称")));
        table.addCell(Itext7PdfUtil.cellCenter(rowSpan, columnSpan, Itext7PdfUtil.paragraphHeadCenter(buyItemVo.getBuyItemName())));

        addHeaderRows(table, rowSpan, constColumnSpan, buyHeaders);
        addBodyRows(table, rowSpan, constColumnSpan, buyHeaders, buyBodyList);
        addConstRow(table, rowSpan, columnSpan, constColumnSpan, buyItemVo.getBuyPurposeTime());
        document.add(table);
    }

    /**
     * 添加常量行
     *
     * @param table
     * @param rowSpan         行合并
     * @param columnSpan      列合并
     * @param constColumnSpan 多列列合并
     * @param buyPurposeTime
     */
    private void addConstRow(Table table, int rowSpan, int columnSpan, int constColumnSpan, String buyPurposeTime) {
        table.addCell(Itext7PdfUtil.cellCenter(2, constColumnSpan, 50, Itext7PdfUtil.paragraphHeadCenter("采购用途及需求时间")));
        table.addCell(Itext7PdfUtil.cellCenterAndNoBorderBottom(rowSpan, columnSpan, 50, Itext7PdfUtil.paragraphLeft("\n" + buyPurposeTime)));
        table.addCell(Itext7PdfUtil.cellCenterAndNoBorder(rowSpan, columnSpan, 50, Itext7PdfUtil.paragraphRight("经办人：\t\t\t申请单位负责人：\t\t\t")));
        table.addCell(Itext7PdfUtil.cellCenter(rowSpan, constColumnSpan, 100, Itext7PdfUtil.paragraphHeadCenter("分管领导意见")));
        table.addCell(Itext7PdfUtil.cellCenter(rowSpan, columnSpan, 100, Itext7PdfUtil.paragraphHeadCenter("")));
        table.addCell(Itext7PdfUtil.cellCenter(rowSpan, constColumnSpan, 100, Itext7PdfUtil.paragraphHeadCenter("总经理意见")));
        table.addCell(Itext7PdfUtil.cellCenter(rowSpan, columnSpan, 100, Itext7PdfUtil.paragraphHeadCenter("")));
        table.addCell(Itext7PdfUtil.cellCenter(rowSpan, constColumnSpan, 100, Itext7PdfUtil.paragraphHeadCenter("董事长意见")));
        table.addCell(Itext7PdfUtil.cellCenter(rowSpan, columnSpan, 100, Itext7PdfUtil.paragraphHeadCenter("")));
    }


    private void addHeaderRows(Table table, int rowSpan, int constColumnSpan, List<AttributeVo> buyHeaders) {
        for (int i = 0; i < buyHeaders.size() + 1; i++) {
            if (i == 0) {
                table.addCell(Itext7PdfUtil.cellCenter(rowSpan, constColumnSpan, Itext7PdfUtil.paragraphHeadCenter("序号")));
                continue;
            }
            AttributeVo attributeVo = buyHeaders.get(i - 1);
            table.addCell(Itext7PdfUtil.cellCenter(rowSpan, constColumnSpan, Itext7PdfUtil.paragraphHeadCenter(attributeVo.getKeyName())));
        }
    }


    private void addBodyRows(Table table, int rowSpan, int constColumnSpan, List<AttributeVo> buyHeaders, List<LinkedHashMap<String, String>> buyBodyList) {
        for (int i = 0; i < buyBodyList.size(); i++) {
            LinkedHashMap<String, String> map = buyBodyList.get(i);
            for (int j = 0; j < buyHeaders.size() + 1; j++) {
                if (j == 0) {
                    table.addCell(Itext7PdfUtil.cellCenter(rowSpan, constColumnSpan, Itext7PdfUtil.paragraphHeadCenter(String.valueOf(i + 1))));
                    continue;
                }
                AttributeVo attributeVo = buyHeaders.get(j - 1);
                String value = map.get(attributeVo.getKeyVal());
                table.addCell(Itext7PdfUtil.cellCenter(rowSpan, constColumnSpan, Itext7PdfUtil.paragraphHeadCenter(StringUtils.hasText(value) ? value : "")));
            }
        }
    }
}
