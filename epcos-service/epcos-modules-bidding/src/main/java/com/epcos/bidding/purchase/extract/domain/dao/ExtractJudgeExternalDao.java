package com.epcos.bidding.purchase.extract.domain.dao;

import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.bidding.common.BaseDao;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/25 14:19
 */
@ApiModel(description = "院外--评委抽取表")
@Data
@NoArgsConstructor
@TableName("purchase_extract_judge_external")
public class ExtractJudgeExternalDao extends BaseDao implements Serializable {

    private static final long serialVersionUID = 5339029692276653474L;

    public ExtractJudgeExternalDao(String extractLongName) {
        this.extractLongName = extractLongName;
    }

    @ApiModelProperty(value = "抽取记录名称")
    private String extractLongName;

    @ApiModelProperty(value = "评委id")
    private Long judgeId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "该专家是否能到场[0-不能到场,1-正常]")
    private Integer whetherRefuse;

    @ApiModelProperty(value = "不能到场理由")
    private String refuseReason;


}
