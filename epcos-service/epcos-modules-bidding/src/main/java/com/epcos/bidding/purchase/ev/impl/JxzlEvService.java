package com.epcos.bidding.purchase.ev.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.utils.BiddingBaseUtil;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.purchase.project.jxzl.business.api.buyitem.JXZLBuyItemApi;
import com.epcos.bidding.purchase.project.jxzl.domain.dao.BuyItemJXZLDao;
import com.epcos.bidding.purchase.project.mapping.SubpackageConvert;
import com.epcos.bidding.workbench.vo.SpecialFieldVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.epcos.common.core.enums.ClientEnum.ZL;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/21 15:47
 */
@Slf4j
@Service("zl")
public class JxzlEvService extends AbEvService {

    private final JXZLBuyItemApi jxzlBuyItemApi;

    public JxzlEvService(IBuyItemApi buyItemApi, ISubPackageApi subPackageApi, JXZLBuyItemApi jxzlBuyItemApi) {
        super(buyItemApi, subPackageApi);
        this.jxzlBuyItemApi = jxzlBuyItemApi;
    }

    @Override
    public String ev() {
        return ZL.getCode();
    }

    @Override
    public boolean isDing() {
        return false;
    }

    @Override
    public String getTableName() {
        return "purchase_jxzl_buy_item";
    }

    @Override
    public Map<String, String> queryBuyItemMap(Map<String, String> voMap, BuyItemDao buyItemInfo) {
        voMap = BiddingBaseUtil.convert(jxzlBuyItemApi.findOneByBuyItemCode(buyItemInfo.getBuyItemCode()));
        return voMap;
    }

    @Override
    public List<SpecialFieldVo> queryDiffInfo(List<String> buyItemCodes, Map<String, List<SubpackageDao>> subMap, List<SpecialFieldVo> voList) {
        List<BuyItemJXZLDao> jxzlDaoList = jxzlBuyItemApi.findOneByBuyItemCodeList(buyItemCodes.stream().collect(Collectors.toList()));
        jxzlDaoList.forEach(dao -> subMap.get(dao.getBuyItemCode())
                .forEach(sub -> voList.add(SubpackageConvert.INSTANCE.convert_3(sub))));
        return voList;
    }


    @Override
    public Boolean delBuyItem(String buyItemCode, List<String> subpackageCodeList) {
        return jxzlBuyItemApi.delJXZLBuyItemInfo(buyItemCode);
    }

    @Override
    public List<String> queryAllBuyItemCode() {
        List<BuyItemJXZLDao> bjxkItemList = jxzlBuyItemApi.list(Wrappers.lambdaQuery(BuyItemJXZLDao.class)
                .select(BuyItemJXZLDao::getBuyItemCode));
        if (CollectionUtils.isEmpty(bjxkItemList)) {
            return Collections.emptyList();
        }
        List<String> codeList = bjxkItemList.stream().map(BuyItemJXZLDao::getBuyItemCode).collect(Collectors.toList());
        return codeList;
    }
}
