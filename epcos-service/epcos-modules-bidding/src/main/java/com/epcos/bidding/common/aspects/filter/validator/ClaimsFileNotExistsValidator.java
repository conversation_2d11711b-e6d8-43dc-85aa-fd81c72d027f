package com.epcos.bidding.common.aspects.filter.validator;

import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileDao;
import com.epcos.common.core.exception.ServiceException;

import java.util.Optional;

public class ClaimsFileNotExistsValidator implements ResultPostHandlerFilterChain<ClaimsFileDao> {
    @Override
    public void postHandler(AspectContext context, ClaimsFileDao result) {
        Optional.ofNullable(result)
                .orElseThrow(() -> new ServiceException("采购文件为空"));
    }
}
