package com.epcos.bidding.purchase.project.fjnx.domain.vo;

import com.epcos.bidding.purchase.api.params.SubPackageInfoVo;
import com.epcos.bidding.purchase.project.base.domain.vo.BuyItemBaseVo;
import com.epcos.common.core.domain.AttachmentDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 福建省农信版本
 * @date 2024/4/23 15:05
 */
@Data
@ApiModel(description = "福建省农信版本")
public class FJNXBuyItemInfoVo extends BuyItemBaseVo {

    private static final long serialVersionUID = 8000748135096267522L;

    @ApiModelProperty(value = "项目外资料分配之后的主键id")
    private Long allocateId;

    @ApiModelProperty(value = "申报科室")
    private String applyDept;

    @ApiModelProperty(value = "申请人")
    private String applyPerson;

    @ApiModelProperty(value = "申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;

    @ApiModelProperty(value = "需购数量")
    private Integer buyCnt;

    @ApiModelProperty(value = "单价限价")
    private BigDecimal buyFixePrice;

    @ApiModelProperty(value = "总价限价")
    private BigDecimal buyTotalPrice;

    @ApiModelProperty(value = "项目类型")
    private String buyClass;

    @ApiModelProperty(value = "是否可采购进口产品[0-否，1-是]")
    private Integer whetherImport;

    @ApiModelProperty(value = "是否有专机专用或耗材或试剂[0-否, 1-是]")
    private Integer whetherConsume;

    @ApiModelProperty(value = "项目附件")
    private List<AttachmentDto> paramsAttList;

    /**
     * 这里 不能随意更换 subpackageDtoList 名字 页面对应回显 需要 这个名字
     */
    @ApiModelProperty(value = "标段信息")
    private List<SubPackageInfoVo> subpackageDtoList;
}
