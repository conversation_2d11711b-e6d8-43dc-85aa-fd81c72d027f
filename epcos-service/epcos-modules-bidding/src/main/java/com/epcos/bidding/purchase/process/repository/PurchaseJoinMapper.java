package com.epcos.bidding.purchase.process.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.epcos.bidding.purchase.api.params.dto.BaseQueryDto;
import com.epcos.bidding.purchase.process.domain.dto.MeetDto;
import com.epcos.bidding.purchase.process.domain.vo.BuyItemFilePageVo;
import com.epcos.bidding.purchase.process.domain.vo.BuyItemMeetVo;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.xyzy.domain.dto.XYZYBuyItemQueryDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/28 9:59
 */
public interface PurchaseJoinMapper {

    List<BuyItemDao> selectItemByTable(@Param(value = "tableName") String tableName, @Param(value = "entity") BaseQueryDto entity);

    IPage<BuyItemMeetVo> meetingPage(Page of, @Param(value = "tableName") String tableName, @Param(value = "entity") MeetDto entity);

    IPage<BuyItemFilePageVo> buyItemFilePage(Page of, @Param(value = "tableName") String tableName,
                                             @Param(value = "entity") XYZYBuyItemQueryDto entity);
}
