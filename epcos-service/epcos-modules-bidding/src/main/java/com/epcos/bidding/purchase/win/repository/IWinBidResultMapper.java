package com.epcos.bidding.purchase.win.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.epcos.bidding.purchase.win.domain.dao.WinBidResultDao;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/9 14:57
 */
public interface IWinBidResultMapper extends BaseMapper<WinBidResultDao> {

    @MapKey(value = "supplier_id")
    List<Map<Long, Long>> selectBySupplierIds(@Param(value = "supplierIds") List<Long> supplierIds);
}
