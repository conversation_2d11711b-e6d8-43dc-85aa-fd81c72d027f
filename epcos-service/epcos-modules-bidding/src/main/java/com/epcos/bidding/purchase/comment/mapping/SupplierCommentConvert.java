package com.epcos.bidding.purchase.comment.mapping;

import com.epcos.bidding.purchase.api.domian.cliams.SupplierCommentVo;
import com.epcos.bidding.purchase.comment.domain.dao.SupplierCommentDao;
import com.epcos.bidding.purchase.comment.domain.dto.SupplierCommentDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/11 13:51
 */
@Mapper
public interface SupplierCommentConvert {

    SupplierCommentConvert INSTANCE = Mappers.getMapper(SupplierCommentConvert.class);

    SupplierCommentDao convert(SupplierCommentDto supplierCommentDto);

    @Mapping(source = "supplierScore", target = "score")
    SupplierCommentVo convert(SupplierCommentDao supplierCommentDao, String supplierScore);
}
