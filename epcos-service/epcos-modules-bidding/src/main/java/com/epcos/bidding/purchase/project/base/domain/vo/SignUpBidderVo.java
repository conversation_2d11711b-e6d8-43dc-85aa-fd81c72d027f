package com.epcos.bidding.purchase.project.base.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 报名投标供应商
 */
@Data
public class SignUpBidderVo implements Serializable {
    private static final long serialVersionUID = 8357143929962696945L;

    private Long supplierId;

    // 单位名称
    private String bidderName;

    // 社会统一信用代码
    private String licNumber;

    // 法定代表人
    private String certificateName;

    // 法定代表人证件号码
    private String certificateCode;

    // 项目负责人
    private String infoReporterName;

    // 项目负责人电话
    private String infoReporterContactNumber;

    // 是否中标人
    private Boolean isWinningBidder;

    // 成交结果通知时间
    private Date sendTime;

    // 报价信息
    private List<BidQuoteVo> bidQuoteVoList;


}
