package com.epcos.bidding.purchase.home.domain.vo;

import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import com.epcos.common.core.annotation.desensitization.BoolToStrSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/23 15:35
 */
@Data
@NoArgsConstructor
public class WhetherSignVo extends SuperPackageVo {

    @ApiModelProperty(value = "没有报名则返回false,已报名-true")
    @JsonSerialize(using = BoolToStrSerializer.class)
    private Boolean whetherSign;

    public WhetherSignVo(String subpackageCode, String subpackageName, Boolean whetherSign) {
        super(subpackageCode, subpackageName);
        this.whetherSign = whetherSign;
    }
}
