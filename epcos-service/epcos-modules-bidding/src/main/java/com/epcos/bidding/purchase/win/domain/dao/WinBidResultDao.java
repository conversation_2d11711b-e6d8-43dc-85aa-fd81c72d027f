package com.epcos.bidding.purchase.win.domain.dao;

import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.bidding.common.SubpackageCodeEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/9 14:52
 */
@ApiModel(description = " 中标结果表")
@Data
@TableName("purchase_win_bid_result")
public class WinBidResultDao extends SubpackageCodeEntity implements Serializable {

    @ApiModelProperty(value = "标段名称")
    private String subpackageName;

    @ApiModelProperty(value = "投标人id")
    private Long supplierId;

    @ApiModelProperty(value = "投标单位")
    private String supplierCompanyName;

    @ApiModelProperty(value = "评委推荐候选人[1-第一候选人，2-第二...，3-第三...]")
    private String recommendCandidate;

    @ApiModelProperty(value = "设定中标人【0-未中标，1-中标】")
    private String winBid;

    @ApiModelProperty(value = "中标价格")
    private BigDecimal winBidPrice;

    @ApiModelProperty(value = "说明")
    private String remark;

    @ApiModelProperty(value = "中标通知书类型[0-未中标，1-中标")
    private String isWin;

    @ApiModelProperty(value = "通知书发送时间")
    private Date sendTime;

    @ApiModelProperty(value = "中标结果通知书pdf_key---供应商")
    private String resultNotice;

    @ApiModelProperty(value = "结果通知书的状态【0-未发送, 1-第一次已发送, 2-已重新发送】")
    private String againSend;

}
