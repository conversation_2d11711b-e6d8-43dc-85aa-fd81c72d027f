package com.epcos.bidding.purchase.project.fjnx.domain.dto;

import com.epcos.bidding.purchase.api.params.dto.BaseQueryDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 福建省农信版本
 * @date 2024/4/23 14:40
 */
@Data
@ApiModel(description = "福建省农信版本")
public class FJNXBuyItemQueryDto extends BaseQueryDto {

    private static final long serialVersionUID = 1770521218768516531L;

    @ApiModelProperty(value = "项目类型")
    private String buyClass;

    @ApiModelProperty(value = "采购编号")
    @Length(max = 60, message = "采购编号【60位字符】")
    private String innerCode;
}
