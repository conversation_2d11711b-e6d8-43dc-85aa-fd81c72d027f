package com.epcos.bidding.purchase.opening.business.service;

import cn.hutool.core.date.DateUtil;
import com.epcos.bidding.common.aspects.vo.GetItemVo;
import com.epcos.bidding.purchase.api.params.vo.buyitem.BuyItemVo;
import com.epcos.bidding.purchase.opening.business.api.IGenerateBidOpeningRecordFile;
import com.epcos.bidding.supplier.api.params.MultiSupplierQuoteFormVo;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierBidderDao;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.sign.AESUtil;
import com.epcos.common.file.html2pdf.HtmlUtil;
import com.epcos.common.file.pdf.Itext7PdfUtil;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.nio.file.Path;
import java.util.Date;
import java.util.Map;
import java.util.Optional;

@Slf4j
public abstract class AbstractGenerateBidOpeningRecordFile implements IGenerateBidOpeningRecordFile {

    /**
     * 生成临时文件地址
     */
    protected Path getPdfPath() {
        return HtmlUtil.getTmpPdfFilePath("开标记录表_" + AESUtil.getAESKey(6) + ".pdf");
    }

    @Override
    public Path generate(String tenderer, GetItemVo itemVo, Date meetingTime, MultiSupplierQuoteFormVo quoteFormVo, Map<Long, SupplierBidderDao> supplierBidderDaoMap) {
        Path pdfPath = getPdfPath();
        try (PdfWriter pdfWriter = new PdfWriter(pdfPath.toFile());
             PdfDocument pdfDocument = new PdfDocument(pdfWriter);
             Document document = new Document(pdfDocument, pdfPageSize().rotate())) {
            document.setFont(Itext7PdfUtil.pdfFont());
            document.add(Itext7PdfUtil.paragraphTitle("开标记录表"));
            Itext7PdfUtil.blankLinesInTheDocument(document, 2);
            document.add(Itext7PdfUtil.paragraphHeadLeft("招标人：" + tenderer));
            document.add(Itext7PdfUtil.paragraphHeadLeft("院内编号：" + itemVo.getInnerCode()));
            document.add(Itext7PdfUtil.paragraphHeadLeft("项目名称：" + Optional.ofNullable(itemVo).map(GetItemVo::getBuyItemVo)
                    .map(BuyItemVo::getBuyItemName).orElse("")));
            document.add(Itext7PdfUtil.paragraphHeadLeft("开标时间：" + Optional.ofNullable(meetingTime).map(DateUtil::formatDateTime)
                    .orElse("")));
            Itext7PdfUtil.blankLinesInTheDocument(document, 1);
            generateTable(document, quoteFormVo, supplierBidderDaoMap);
        } catch (IOException e) {
            log.error("生成开标记录表失败", e);
            throw new ServiceException(e.getMessage());
        }
        return pdfPath;
    }

    protected PageSize pdfPageSize() {
        return PageSize.A4;
    }

    protected abstract void generateTable(Document document, MultiSupplierQuoteFormVo quoteFormVo,
                                          Map<Long, SupplierBidderDao> supplierBidderDaoMap);

    protected String keyword(String keyword) {
        return keyword + "（签）";
    }

}
