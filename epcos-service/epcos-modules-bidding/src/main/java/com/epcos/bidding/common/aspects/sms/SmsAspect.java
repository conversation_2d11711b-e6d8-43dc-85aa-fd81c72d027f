package com.epcos.bidding.common.aspects.sms;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.epcos.bidding.common.annotaion.Sms;
import com.epcos.bidding.common.aspects.convert.GetParamConvert;
import com.epcos.common.core.enums.SmsEnum;
import com.epcos.common.core.utils.EvUtils;
import com.epcos.common.sms.ISmsApi;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class SmsAspect {

    private final ISmsApi smsApi;

    @AfterReturning(value = "@annotation(com.epcos.bidding.common.annotaion.Sms)")
    public void send(JoinPoint point) {
        Sms smsAn = ((MethodSignature) point.getSignature()).getMethod().getAnnotation(Sms.class);
        if (Arrays.stream(smsAn.clients()).noneMatch(a -> Objects.equals(a.getCode(), EvUtils.ev()))) {
            return;
        }
        SmsContent smsContent = (SmsContent) GetParamConvert.getBeanOrNewInstance(smsAn.convert()).doConvert(point.getArgs()[0]);
        setSmsContent(smsAn.smsEnum(), smsContent);
        if (CollUtil.isNotEmpty(smsContent.getMobiles()) && StrUtil.isNotBlank(smsContent.getContent())) {
            smsApi.sendSms(smsContent.getMobiles(), smsContent.getContent());
        }
    }

    /**
     * 根据不同短信枚举填充短信内容
     */
    private void setSmsContent(SmsEnum smsEnum, SmsContent smsContent) {
        if (SmsEnum.SMS_OPEN_DECRYPT == smsEnum) {
            smsContent.setContent(smsEnum.getInfo(smsContent.getBuyItemName(), smsContent.getSubpackageName()));
        } else if (SmsEnum.SMS_SUPPLIER_PASS == smsEnum) {
            smsContent.setContent(smsEnum.getInfo(smsContent.getBuyItemName(), smsContent.getSubpackageName(), smsContent.getDate_1()));
        }
    }

}
