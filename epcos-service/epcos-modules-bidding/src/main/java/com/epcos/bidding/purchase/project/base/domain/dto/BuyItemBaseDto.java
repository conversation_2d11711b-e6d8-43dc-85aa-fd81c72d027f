package com.epcos.bidding.purchase.project.base.domain.dto;

import com.epcos.bidding.purchase.api.params.dto.QuoteFormCreatorDto;
import com.epcos.bidding.purchase.api.params.dto.SubPackageBaseDto;
import com.epcos.common.core.annotation.valid.Add;
import com.epcos.common.core.annotation.valid.Up;
import com.epcos.common.core.domain.pdf.Pdf;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/23 12:11
 */
@Data
@ApiModel(value = "采购项目基础信息", description = "采购项目基础信息")
public class BuyItemBaseDto extends Pdf implements Serializable {

    private static final long serialVersionUID = -6986162313629863786L;

    @ApiModelProperty(value = "采购项目code")
    @NotBlank(groups = {Up.class}, message = "采购项目code 不能为空")
    @Length(max = 64, message = "采购项目code 超出长度限制")
    private String buyItemCode;

    @ApiModelProperty(value = "采购项目名称")
    @NotBlank(groups = {Add.class, Up.class}, message = "采购项目名称 不能为空")
    @Length(max = 128, message = "采购项目名称 超出长度限制")
    private String buyItemName;

    @ApiModelProperty(value = "组织Code")
    @Length(max = 64, message = "组织Code 超出长度限制")
    @NotBlank(groups = {Add.class, Up.class}, message = "组织不能为空")
    private String orgCode;

    @ApiModelProperty(value = "采购方式表Code")
    @Length(max = 64, message = "采购方式表Code 超出长度限制")
    private String purchaseMethodCode;

    @ApiModelProperty(value = "采购方式表Type")
    @Length(max = 64, message = "采购方式表Type 超出长度限制")
    private String purchaseMethodType;


    /**
     * 设置报价表信息
     *
     * @param bodyMap
     */
    public void setQuote(Map<String, List<LinkedHashMap<String, String>>> bodyMap, List<SubPackageBaseDto> subpackageDtoList) {
        subpackageDtoList.forEach(sub -> {
            List<LinkedHashMap<String, String>> bodyMaps = bodyMap.get(sub.getSubpackageName());
            QuoteFormCreatorDto claimsFileQuoteFormCreatorDto = sub.getClaimsFileQuoteFormCreatorDto();
            if (Objects.nonNull(claimsFileQuoteFormCreatorDto)) {
                claimsFileQuoteFormCreatorDto.setBodyMaps(bodyMaps);
            }
        });
    }

}
