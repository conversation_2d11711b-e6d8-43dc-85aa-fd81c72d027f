package com.epcos.bidding.purchase.project.base.domain.vo;

import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/21 9:32
 */
@Data
public class LabelJson extends SuperPackageVo {

    private static final long serialVersionUID = 7958307024492137395L;

    @ApiModelProperty(value = "标签key，区别每个标签")
    private String jsonKey;

    @ApiModelProperty(value = "标签值")
    @Length(max = 25, message = "超出长度限制")
    private String jsonLabel;
}
