package com.epcos.bidding.common;

import com.alibaba.fastjson.JSON;
import com.epcos.system.api.domain.assmble.vo.FunctionKV;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

public class JsonToListFunctionKVHandler extends BaseTypeHandler<List<FunctionKV>> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<FunctionKV> parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, JSON.toJSONString(parameter));
    }

    @Override
    public List<FunctionKV> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return Optional.ofNullable(rs.getString(columnName))
                .map(i -> JSON.parseArray(i, FunctionKV.class))
                .orElse(Collections.emptyList());
    }

    @Override
    public List<FunctionKV> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return JSON.parseArray(rs.getString(columnIndex), FunctionKV.class);
    }

    @Override
    public List<FunctionKV> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return JSON.parseArray(cs.getString(columnIndex), FunctionKV.class);
    }
}
