package com.epcos.bidding.purchase.monitor.business.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.api.params.dto.monitor.CreateMonitorDto;
import com.epcos.bidding.purchase.ev.factory.EvFactory;
import com.epcos.bidding.purchase.monitor.business.api.IMonitorBidApi;
import com.epcos.bidding.purchase.monitor.domain.dao.MonitorBidDao;
import com.epcos.bidding.purchase.monitor.domain.dto.RetrieveMonitorDto;
import com.epcos.bidding.purchase.monitor.domain.dto.UpdateMonitorDto;
import com.epcos.bidding.purchase.monitor.domain.vo.MonitorInfoVo;
import com.epcos.bidding.purchase.monitor.domain.vo.MonitorVo;
import com.epcos.bidding.purchase.monitor.mapping.MonitorBidConvert;
import com.epcos.bidding.purchase.monitor.repository.IMonitorBidMapper;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.common.core.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/5 13:45
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MonitorBidImpl extends ServiceImpl<IMonitorBidMapper, MonitorBidDao> implements IMonitorBidApi {

    private final IMonitorBidMapper monitorBidMapper;
    private final ISubPackageApi subPackageApi;

    /**
     * 新增监控人
     * 新增到标段表中
     *
     * @param dto
     * @return
     */
    @Override
    public Boolean addMonitor(CreateMonitorDto dto) {
        return subPackageApi.update(Wrappers.lambdaUpdate(SubpackageDao.class)
                .set(SubpackageDao::getMonitorBidPerson, dto.getMonitorBidPerson())
                .set(SubpackageDao::getMonitorBidPersonId, dto.getMonitorBidPersonId())
                .eq(SubpackageDao::getBuyItemCode, dto.getBuyItemCode())
                .eq(SubpackageDao::getSubpackageCode, dto.getSubpackageCode()));
    }


    @Override
    public Boolean addMonitorInfo(CreateMonitorDto dto) {
        MonitorBidDao monitorBidDao = MonitorBidConvert.INSTANCE.convert(dto);
        return save(monitorBidDao);
    }

    @Override
    public Boolean editMonitor(UpdateMonitorDto dto) {
        MonitorBidDao monitorBidDao = MonitorBidConvert.INSTANCE.convert(dto);
        return updateById(monitorBidDao);
    }

    @Override
    public List<MonitorInfoVo> monitorInfo(String buyItemCode) {

        List<SubpackageDao> subpackageDaoList = subPackageApi.findByBuyItemCode(buyItemCode);

        List<MonitorInfoVo> voList = subpackageDaoList.stream()
                .map(subpackageDao -> {
                    MonitorInfoVo vo = new MonitorInfoVo();
                    BeanUtils.copyProperties(subpackageDao, vo);
                    return vo;
                }).collect(Collectors.toList());
        return voList;
    }

    @Override
    public IPage<MonitorVo> monitorPage(PageSortEntity<RetrieveMonitorDto> dto) {
        RetrieveMonitorDto entity = dto.getEntity();
        entity.setUserId(SecurityUtils.getUserId());
        String tableName = EvFactory.getInstance().getTableName();
        IPage<MonitorVo> page = monitorBidMapper.selectMonitorPage(
                Page.of(dto.getPageNum(), dto.getPageSize()),
                tableName,
                entity
        );
        return page;
    }
}
