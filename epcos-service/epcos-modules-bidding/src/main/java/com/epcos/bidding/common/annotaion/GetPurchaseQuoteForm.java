package com.epcos.bidding.common.annotaion;

import org.springframework.core.annotation.Order;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 获取采购人报价表
 * return PurchaseQuoteFormVo
 *
 * <AUTHOR>
 */
@Order(260)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface GetPurchaseQuoteForm {

    GetCommon common() default @GetCommon;
}
