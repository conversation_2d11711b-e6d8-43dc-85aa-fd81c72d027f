package com.epcos.bidding.common;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IBaseService<T> extends IService<T> {

    Logger log = LoggerFactory.getLogger(IBaseService.class);

    default List<T> list(T dao) {
        return list(queryWrapper(dao));
    }

    default Page<T> page(PageSortEntity<T> pageSortEntity) {
        return page(new Page<>(pageSortEntity.getCalcPageNum(), pageSortEntity.getCalcPageSize()),
                queryWrapper(pageSortEntity.getEntity()));
    }

    /**
     * 查询条件
     */
    LambdaQueryWrapper<T> queryWrapper(T dao);


}
