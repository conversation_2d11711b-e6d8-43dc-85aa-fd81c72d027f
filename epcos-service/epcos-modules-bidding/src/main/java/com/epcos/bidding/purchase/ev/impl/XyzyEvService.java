package com.epcos.bidding.purchase.ev.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.utils.BiddingBaseUtil;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.purchase.project.xyzy.business.api.buyitem.XYZYBuyItemApi;
import com.epcos.bidding.purchase.project.xyzy.domain.dao.BuyItemXYZYDao;
import com.epcos.bidding.workbench.vo.SpecialFieldVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.epcos.common.core.enums.ClientEnum.XY;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/21 15:49
 */
@Slf4j
@Service("xy")
public class XyzyEvService extends AbEvService {

    private final XYZYBuyItemApi xyzyBuyItemApi;

    public XyzyEvService(IBuyItemApi buyItemApi, ISubPackageApi subPackageApi, XYZYBuyItemApi xyzyBuyItemApi) {
        super(buyItemApi, subPackageApi);
        this.xyzyBuyItemApi = xyzyBuyItemApi;
    }

    @Override
    public String ev() {
        return XY.getCode();
    }

    @Override
    public boolean isDing() {
        return false;
    }

    @Override
    public String getTableName() {
        return "purchase_xyzy_buy_item";
    }

    @Override
    public Map<String, String> queryBuyItemMap(Map<String, String> voMap, BuyItemDao buyItemInfo) {
        voMap = BiddingBaseUtil.convert(xyzyBuyItemApi.findOneByBuyItemCode(buyItemInfo.getBuyItemCode()));
        return voMap;
    }

    @Override
    public List<SpecialFieldVo> queryDiffInfo(List<String> buyItemCodes, Map<String, List<SubpackageDao>> subMap, List<SpecialFieldVo> voList) {
        List<BuyItemXYZYDao> xyzyDaoList = xyzyBuyItemApi.findOneByBuyItemCodeList(buyItemCodes);
        xyzyDaoList.forEach(dao -> subMap.get(dao.getBuyItemCode())
                .forEach(sub -> voList.add(new SpecialFieldVo(dao.getBuyItemCode(), sub.getSubpackageCode(), null, dao.getBuyClass(), null))));
        return voList;
    }

    @Override
    public Boolean delBuyItem(String buyItemCode, List<String> subpackageCodeList) {
        return xyzyBuyItemApi.delXYZYBuyItemInfo(buyItemCode);
    }


    @Override
    public List<String> queryAllBuyItemCode() {
        List<BuyItemXYZYDao> bjxkItemList = xyzyBuyItemApi.list(Wrappers.lambdaQuery(BuyItemXYZYDao.class)
                .select(BuyItemXYZYDao::getBuyItemCode));
        if (CollectionUtils.isEmpty(bjxkItemList)) {
            return Collections.emptyList();
        }
        List<String> codeList = bjxkItemList.stream().map(BuyItemXYZYDao::getBuyItemCode).collect(Collectors.toList());
        return codeList;
    }
}
