package com.epcos.bidding.common.aspects.convert;

import com.epcos.bidding.common.annotaion.GetCommon;
import com.epcos.bidding.common.annotaion.GetItem;
import com.epcos.bidding.common.annotaion.GetTime;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.common.aspects.sms.SmsContent;
import com.epcos.bidding.common.aspects.vo.GetItemVo;
import com.epcos.bidding.purchase.api.params.vo.bulletin.BulletinAndItemTimeVo;
import com.epcos.bidding.purchase.api.params.vo.buyitem.BuyItemVo;
import com.epcos.bidding.purchase.process.domain.dto.SignUpStatusDto;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.supplier.api.params.SupplierSignUpVo;
import com.epcos.bidding.supplier.sign.business.api.ISupplierSignApi;
import com.epcos.bidding.supplier.sign.domain.dto.SupplierSignQueryDto;
import com.epcos.system.api.domain.common.SupplierInfoShare;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/23 14:22
 */
@Slf4j
@Component
public class SupplierAuditConvert implements GetParamConvert<SignUpStatusDto, SmsContent> {

    @Autowired
    private ISupplierSignApi supplierSignApi;

    @Override
    @GetItem(common = @GetCommon(subpackageCodeEL = "#signUpStatusDto.subpackageCode"))
    @GetTime(common = @GetCommon(subpackageCodeEL = "#signUpStatusDto.subpackageCode"))
    public SmsContent doConvert(SignUpStatusDto signUpStatusDto) {
        SmsContent vo = new SmsContent();
        if (signUpStatusDto.getReviewStatus() == 1) {
            List<SupplierSignUpVo> upVoList = supplierSignApi.query(new SupplierSignQueryDto(), signUpStatusDto.getSupplierId());

            SupplierInfoShare supplierSignUpInfo = upVoList.get(0).getSupplierSignUpInfo();
            BulletinAndItemTimeVo time = GetUtil.getTime();
            GetItemVo itemVo = GetUtil.getItemVo();
            BuyItemVo buyItemVo = itemVo.getBuyItemVo();
            Map<String, String> subMap = itemVo.getSubpackageDaoList().stream()
                    .collect(Collectors.toMap(SubpackageDao::getSubpackageCode, SubpackageDao::getSubpackageName));
            vo = SmsContent.builder()
                    .mobiles(Collections.singleton(supplierSignUpInfo.getInfoReporterContactNumber()))
                    .buyItemName(buyItemVo.getBuyItemName())
                    .subpackageName(subMap.get(signUpStatusDto.getSubpackageCode()))
                    .date_1(time.getResponseFileEndTime())
                    .build();
        }
        log.error("审核供应商报名，发送短信信息构建，SupplierAuditConvert:{}，signUpStatusDto:{}", vo, signUpStatusDto);
        return vo;
    }
}
