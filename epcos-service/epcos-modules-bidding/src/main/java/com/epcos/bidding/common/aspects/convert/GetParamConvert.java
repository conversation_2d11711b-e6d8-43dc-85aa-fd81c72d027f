package com.epcos.bidding.common.aspects.convert;

import cn.hutool.extra.spring.SpringUtil;
import com.epcos.common.core.exception.ServiceException;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;

import java.lang.reflect.InvocationTargetException;
import java.util.Objects;

/**
 * 参数转化
 * PARAM 必须是非基本类型与 string
 *
 * <AUTHOR>
 */
public interface GetParamConvert<PARAM, RES> {


    static boolean isImpl(Class<? extends GetParamConvert> convertClass) {
        return convertClass != null
                && !convertClass.isInterface()
                && GetParamConvert.class.isAssignableFrom(convertClass);
    }

    static GetParamConvert getBeanOrNewInstance(Class<? extends GetParamConvert> convertClass, Object... args) {
        GetParamConvert bean = null;
        try {
            bean = SpringUtil.getBean(convertClass);
        } catch (NoSuchBeanDefinitionException e) {

        }
        if (Objects.nonNull(bean)) {
            return bean;
        }
        try {
            if (args.length == 0) {
                return convertClass.newInstance();
            }
            return convertClass.getDeclaredConstructor(args.getClass()).newInstance(args);
        } catch (NoSuchMethodException | InstantiationException | IllegalAccessException |
                 InvocationTargetException e) {
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * PARAM 必须不是基本类型包括包装类型与string
     */
    default void nonPrimitiveGenericAndString(PARAM param) {
        if (param.getClass().isPrimitive() ||
                param instanceof Number ||
                param instanceof Character ||
                param instanceof Boolean ||
                param instanceof String) {
            throw new IllegalArgumentException("PARAM 必须是非基本类型与 string");
        }
    }

    default RES convert(PARAM param) {
        nonPrimitiveGenericAndString(param);
        return doConvert(param);
    }

    RES doConvert(PARAM param);


}
