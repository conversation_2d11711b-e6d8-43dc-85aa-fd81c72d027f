package com.epcos.bidding.purchase.goods.business.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.api.params.AttributeVo;
import com.epcos.bidding.purchase.api.params.dto.AttributeValVo;
import com.epcos.bidding.purchase.goods.business.api.IGoodsCartApi;
import com.epcos.bidding.purchase.goods.domain.dao.GoodsCartDao;
import com.epcos.bidding.purchase.goods.domain.dto.AddCartDetail;
import com.epcos.bidding.purchase.goods.domain.dto.AddCartDto;
import com.epcos.bidding.purchase.goods.domain.vo.GoodsCartVo;
import com.epcos.bidding.purchase.goods.domain.vo.GoodsParamsVo;
import com.epcos.bidding.purchase.goods.repository.IGoodsCartMapper;
import com.epcos.bidding.purchase.remote.RemoteSupplierApi;
import com.epcos.bidding.supplier.goods.domain.vo.GoodsVo;
import com.epcos.common.core.utils.bean.BeanUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/12 17:36
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GoodsCartService extends ServiceImpl<IGoodsCartMapper, GoodsCartDao> implements IGoodsCartApi {

    private final IGoodsCartMapper goodsCartMapper;
    private final RemoteSupplierApi remoteSupplierApi;


    @Override
    public Boolean addCart(AddCartDto dto) {
//        dto.getAddCartDetailList().forEach(c -> {
        GoodsCartDao goodsCartDao = new GoodsCartDao();
        BeanUtils.copyProperties(dto, goodsCartDao);
        goodsCartDao.setBuyerId(dto.getBuyerId());
        save(goodsCartDao);
//        });
        return Boolean.TRUE;
    }

    @Override
    public IPage<GoodsCartVo> cartList(PageSortEntity<GoodsCartDao> dto) {
        IPage<GoodsCartVo> page = goodsCartMapper.selectPageBy(Page.of(dto.getPageNum(), dto.getPageSize()), dto.getEntity());
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return page;
        }
        Set<Long> gooddsIdSet = page.getRecords().stream().map(GoodsCartVo::getGoodsId).collect(Collectors.toSet());
        List<GoodsVo> goodsVos = remoteSupplierApi.goodsInfos(gooddsIdSet);
        List<GoodsCartVo> records = page.getRecords();
        records.forEach(r -> goodsVos.forEach(g -> {
            if (r.getGoodsId().equals(g.getGoodsId())) {
                BeanUtils.copyProperties(g, r);
                List<AttributeValVo> goodsDetail = g.getGoodsDetail();
                LinkedHashMap<String, String> bodyMap = new LinkedHashMap<>();
                List<AttributeVo> attributeVoList = goodsDetail.stream().map(a -> {
                    AttributeVo attributeVo = new AttributeVo();
                    BeanUtils.copyProperties(a, attributeVo);
                    bodyMap.put(a.getKeyVal(), a.getValue().toString());
                    return attributeVo;
                }).collect(Collectors.toList());
                r.setHeadeList(attributeVoList);
                r.setBodyMaps(Collections.singletonList(bodyMap));
            }
        }));
        return page;
    }

    @Override
    public Boolean updateCart(List<AddCartDetail> dto) {
        dto.forEach(c -> {
            GoodsCartDao goodsCartDao = new GoodsCartDao();
            BeanUtils.copyProperties(c, goodsCartDao);
            update(goodsCartDao, Wrappers.lambdaUpdate(GoodsCartDao.class).eq(GoodsCartDao::getId, c.getCartId()));
        });
        return Boolean.TRUE;
    }

    @Override
    public Boolean delCart(List<Long> cartIdList) {
        if (CollUtil.isNotEmpty(cartIdList)) {
            return remove(Wrappers.lambdaUpdate(GoodsCartDao.class)
                    .in(GoodsCartDao::getId, cartIdList));
        }
        return Boolean.FALSE;
    }

    @Override
    public List<GoodsParamsVo> cartInfo(List<Long> cartIdList) {
        List<GoodsCartDao> list = list(Wrappers.lambdaQuery(GoodsCartDao.class).in(!CollectionUtils.isEmpty(cartIdList), GoodsCartDao::getId, cartIdList));
        Set<Long> goodsIdSet = list.stream().map(GoodsCartDao::getGoodsId).collect(Collectors.toSet());
        List<GoodsVo> goodsVos = remoteSupplierApi.goodsInfos(goodsIdSet);
        List<GoodsParamsVo> voList = new ArrayList<>();
        list.forEach(r -> goodsVos.forEach(g -> {
            if (r.getGoodsId().equals(g.getGoodsId())) {
                GoodsParamsVo vo = new GoodsParamsVo();
                List<AttributeValVo> goodsDetail = g.getGoodsDetail();
                LinkedHashMap<String, String> bodyMap = new LinkedHashMap<>();
                List<AttributeVo> attributeVoList = goodsDetail.stream().map(a -> {
                    AttributeVo attributeVo = new AttributeVo();
                    BeanUtils.copyProperties(a, attributeVo);
                    bodyMap.put(a.getKeyVal(), a.getValue().toString());
                    return attributeVo;
                }).collect(Collectors.toList());
                vo.setGoodsCategory(g.getGoodsCategory());
                vo.setHeadeList(attributeVoList);
                vo.setBodyMaps(Collections.singletonList(bodyMap));
                voList.add(vo);
            }
        }));
        return voList;
    }
}
