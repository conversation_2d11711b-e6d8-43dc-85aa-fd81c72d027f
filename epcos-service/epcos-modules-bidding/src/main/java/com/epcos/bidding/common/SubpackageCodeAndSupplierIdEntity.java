package com.epcos.bidding.common;

import com.epcos.common.core.web.validator.CreateGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
public abstract class SubpackageCodeAndSupplierIdEntity extends SubpackageCodeEntity {

    @ApiModelProperty(value = "供应商id")
    @NotNull(message = "供应商id，必填", groups = CreateGroup.class)
    protected Long supplierId;

}
