package com.epcos.bidding.purchase.pdf.business;

import com.epcos.bidding.audit.api.vo.AuditVo;
import com.epcos.bidding.audit.business.api.IAuditApi;
import com.epcos.bidding.purchase.pdf.business.generate.impl.BuyItemContentPdfImpl;
import com.epcos.bidding.purchase.pdf.domian.vo.audit.AuditPdfVo;
import com.epcos.bidding.purchase.project.wzlg.domain.dto.buyitem.WZLGCreateBuyItemDto;
import com.epcos.common.core.factory.pdf.IGeneratePdf;
import com.epcos.common.core.factory.pdf.PdfFactory;
import com.epcos.common.core.utils.bean.BeanUtils;
import com.epcos.system.api.RemoteSystemService;
import com.epcos.system.api.domain.SysDictData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.nio.file.Path;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/29 11:44
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PdfService {

    private final IAuditApi auditApi;
    private final RemoteSystemService remoteSystemService;

    public AuditPdfVo queryAuditPdfVo(String auditCode) {

        AuditPdfVo vo = new AuditPdfVo();
        AuditVo auditVo = auditApi.query(auditCode);
        List<SysDictData> approvalTypeList = remoteSystemService.getDictValue("approval_type").getData();
        BeanUtils.copyProperties(auditVo, vo);
        vo.setDictList(approvalTypeList);
        return vo;
    }

    public WZLGCreateBuyItemDto generateBuyItemPdf(WZLGCreateBuyItemDto dto) {
        IGeneratePdf generatePdf = PdfFactory.GeneratePdfFactory(BuyItemContentPdfImpl.class);
        Path path = generatePdf.generatePdf(dto);
        dto.setFile(path.toFile());
        return dto;
    }
}
