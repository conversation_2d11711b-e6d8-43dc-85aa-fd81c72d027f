package com.epcos.bidding.purchase.remote;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.epcos.bidding.common.annotaion.GetEsign;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.common.aspects.vo.GetSimpleItemVo;
import com.epcos.bidding.purchase.remote.dto.NotifyJudgeDto;
import com.epcos.common.core.constant.SecurityConstants;
import com.epcos.common.core.constant.SupplierConstants;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.domain.review.ReviewSummaryVo;
import com.epcos.common.core.enums.ClientEnum;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.EvUtils;
import com.epcos.common.core.web.domain.user.SysUser;
import com.epcos.common.core.web.page.TableDataVo;
import com.epcos.dingtalk.api.RemoteDingtalk;
import com.epcos.epcfile.api.RemoteProjectFileService;
import com.epcos.epcfile.api.domain.vo.ProjectDocumentVo;
import com.epcos.review.api.RemoteReviewService;
import com.epcos.review.api.domain.vo.VotingResultsVo;
import com.epcos.system.api.RemoteSupplierCompanyService;
import com.epcos.system.api.RemoteSystemService;
import com.epcos.system.api.RemoteUserService;
import com.epcos.system.api.domain.SysDictData;
import com.epcos.system.api.domain.SysOrganize;
import com.epcos.system.api.domain.assmble.vo.PurchaseMethodInfoVo;
import com.epcos.system.api.domain.assmble.vo.PurchaseMethodVo;
import com.epcos.system.api.domain.dto.AgainExtractDto;
import com.epcos.system.api.domain.dto.JudgeConditionsDto;
import com.epcos.system.api.model.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @desc 远程调用其他微服务接口
 * @date 2023/8/15 9:19
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RemoteToOtherServiceApi {

    private final RemoteSystemService remoteSystemService;
    private final RemoteUserService remoteUserService;
    private final RemoteSupplierCompanyService remoteSupplierCompanyService;
    private final RemoteReviewService remoteReviewService;
    private final RemoteProjectFileService remoteProjectFileService;
    private final RemoteDingtalk remoteDingtalk;

    /**
     * 查询采购方式功能点
     *
     * @param purchaseMethodCode 采购方式code
     * @return
     */
    public PurchaseMethodInfoVo getPurchaseMethodInfoToJson(String purchaseMethodCode) {
        R<PurchaseMethodInfoVo> r = remoteSystemService.getPurchaseInformation(SecurityConstants.INNER, purchaseMethodCode);
        if (r.hasFail()) {
            log.error("查询采购方式功能点失败, purchaseMethodCode:{},r:{}", purchaseMethodCode, r);
            throw new ServiceException("采购方式不存在");
        }
        return r.getData();
    }

    public List<PurchaseMethodVo> getPurchaseMethodNoticeInfo(String purchaseMethodCode) {
        R<List<PurchaseMethodVo>> r = remoteSystemService.getPurchaseNoticeInfo(SecurityConstants.INNER, purchaseMethodCode);
        if (r.hasFail()) {
            log.error("查询采购方式功能点失败, purchaseMethodCode:{},r:{}", purchaseMethodCode, r);
            throw new ServiceException("采购方式不存在");
        }
        return r.getData();
    }


    public String getNoticeTemplate(String tmpType, String tmpKey) {
        R<String> r = remoteSystemService.getTemplate(SecurityConstants.INNER, tmpType, tmpKey);
        if (r.hasFail()) {
            log.error("查询公告模板 失败, tmpType:{},tmpKey:{},r:{}", tmpType, tmpKey, r);
            throw new ServiceException("查询公告模板 失败");
        }
        return r.getData();
    }

    public TableDataVo<SupplierCompanyAndSingleOrgVO> findByCompanyAndOrg(String orgCode, String goodsCategory, SupplierCompanyPageDto dto) {
        SupplierCompanyAndOrgPageDto pageDto = new SupplierCompanyAndOrgPageDto(orgCode, SupplierConstants.PlatformStatus.OK, dto);
        pageDto.setGoodsCategory(goodsCategory);
        return remoteSupplierCompanyService.findByCompanyAndOrg(SecurityConstants.INNER, pageDto);
    }

    public List<SupplierCompanyVO> getSupplierInfo(List<Long> userIdList) {
        R<List<SupplierCompanyVO>> r = remoteSupplierCompanyService.queryByUserIds(SecurityConstants.INNER, new HashSet<>(userIdList));
        if (r.hasFail()) {
            log.error("根据用户id查询供应商企业信息 失败, userIdList:{}, r:{}", userIdList, r);
            throw new ServiceException("根据用户id查询供应商企业信息 失败");
        }
        return r.getData();
    }

    @GetEsign(orgCodeEL = "#orgCode")
    public EsignVO getSeal(String orgCode) {
        return GetUtil.getEsign();
    }

    @GetEsign(userIdEL = "#userId")
    public EsignVO getSeal(Long userId) {
        return GetUtil.getEsign();
    }

    public List<SysOrganize> getOrgInfo(List<String> orgCodeList) {
        R<List<SysOrganize>> r = remoteUserService.getOrgInfo(SecurityConstants.INNER, orgCodeList);
        if (r.hasFail()) {
            log.error("查询组织信息 失败, orgCodeList:{},r:{}", orgCodeList, r);
            throw new ServiceException("查询组织信息 失败");
        }
        return r.getData();
    }

    public List<SysOrganize> getOrgInfo(Long userId) {
        R<List<SysOrganize>> r = remoteUserService.getOrgInfo(SecurityConstants.INNER, userId);
        if (r.hasFail()) {
            log.error("查询组织信息 失败, orgCodeList:{},,r:{}", userId, r);
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }

    public List<SysUser> getJudgesInfo(JudgeConditionsDto dto) {
        R<List<SysUser>> r = remoteUserService.randomExtract(SecurityConstants.INNER, dto);
        if (r.hasFail()) {
            log.error("查询评委信息 失败, dto:{},,r:{}", dto, r);
            throw new ServiceException("抽取专家 失败");
        }
        return r.getData();
    }

    public List<SysUser> getOrgPerson(String orgCode) {
        R<List<SysUser>> r = remoteUserService.getOrgAndUserInfo(SecurityConstants.INNER, orgCode);
        if (r.hasFail()) {
            log.error("查询组织下的人员信息 失败, orgCode:{},,r:{}", orgCode, r);
            throw new ServiceException("查询组织下的人员信息 失败");
        }
        return r.getData();
    }

    public SysUser getSysUserInfo(Long userId) {
        R<SysUser> r = remoteUserService.getInfoById(userId, SecurityConstants.INNER);
        if (r.hasFail()) {
            log.error("根据用户id查询用户信息 失败, userId:{},,r:{}", userId, r);
            throw new ServiceException("根据用户id查询用户信息 失败");
        }
        return r.getData();
    }

    public List<SysUser> getSysUserInfo(List<Long> userIdList) {
        R<List<SysUser>> r = remoteUserService.getUserList(SecurityConstants.INNER, userIdList);
        if (r.hasFail()) {
            log.error("根据用户id查询用户信息 失败, userIdList:{},,r:{}", userIdList, r);
            throw new ServiceException("根据用户id查询用户信息 失败");
        }
        return r.getData();
    }

    public List<SysUser> againJudge(AgainExtractDto dto) {
        R<List<SysUser>> r = remoteUserService.againExtract(dto);
        if (r.hasFail()) {
            log.error("再次抽取评委 失败, dto:{},,r:{}", dto, r);
            throw new ServiceException("再次抽取评委 失败");
        }
        return r.getData();
    }

    public Boolean isTender(Long userId) {
        R<Boolean> r = remoteUserService.isPurchaser(userId);
        if (r.hasFail()) {
            log.error("查询 用户是否是采购人失败, userId:{},,r:{}", userId, r);
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }

    public Boolean isSupp(Long userId) {
        R<Boolean> r = remoteUserService.isSupplier(userId);
        if (r.hasFail()) {
            log.error("查询 用户是否是供应商失败, userId:{},,r:{}", userId, r);
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }

    public Long queryTenderId() {
        R<Long> r = remoteSystemService.getPurchaserOrAdminUserId();
        if (r.hasFail()) {
            log.error("查询 用户采购人id 失败, r:{}", r);
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }


    public void notifyJudgeToIsBargain(NotifyJudgeDto dto, Long deptJudgeId) {
        log.error("采购人点击确认会签准备调用评委接口, remoteReviewService.reviewData().参入参数,dto:{},deptJudgeId:{}", dto, deptJudgeId);
        // 采购人确认评委会签
        R<Boolean> r = remoteReviewService.reviewData(dto.getSubpackageCode(), Integer.parseInt(dto.getIsBargaining()), deptJudgeId);
        log.error("采购人点击确认会签调用评委接口结束, remoteReviewService.reviewData().参入参数,dto:{},deptJudgeId:{},r:{}", dto, deptJudgeId, r);
        if (Objects.isNull(r) || r.hasFail() || !r.getData()) {
            log.error("通知 评委会签 失败, subpackageCode:{}, r:{}", dto.getSubpackageCode(), r);
            throw new ServiceException(r.getMsg());
        }
    }

    public void notifyJudgeToIsReview(NotifyJudgeDto dto) {
        log.error("采购人点击 确认评审 准备调用评委接口, remoteReviewService.isReview().参入参数,dto:{}", dto);
        R<Boolean> r = remoteReviewService.isReview(dto.getSubpackageCode(), dto.getIsReview());
        log.error("采购人点击 确认评审 调用评委接口结束, remoteReviewService.isReview().参入参数,dto:{},r:{}", dto, r);
        if (Objects.isNull(r) || r.hasFail()) {
            log.error("通知 评委评审[确认评审] 失败, subpackageCode:{}, r:{}", dto.getSubpackageCode(), r);
            throw new ServiceException(r.getMsg());
        }
    }

    public void notifyJudgeToGroup(NotifyJudgeDto dto) {
        log.error("采购人点击 确认评审 准备调用评委接口, remoteReviewService.updateLeader().参入参数,subpackageCode:{},judgeId:{}",
                dto.getSubpackageCode(), dto.getGroupId());
        R<Boolean> r = remoteReviewService.updateLeader(dto.getSubpackageCode(), dto.getGroupId());
        log.error("采购人点击 确认评审 调用评委接口结束, remoteReviewService.reviewData().参入参数,subpackageCode:{},judgeId:{},r:{}",
                dto.getSubpackageCode(), dto.getGroupId(), r);
        if (Objects.isNull(r) || r.hasFail()) {
            log.error("通知 评委评审[确认评审] 失败, subpackageCode:{}, r:{}", dto.getSubpackageCode(), r);
            throw new ServiceException(r.getMsg());
        }
    }

    public void notifyJudgeChoice(NotifyJudgeDto dto) {
        log.error("采购人点击 重新投标票 准备调用评委接口, remoteReviewService.refreshChoiceLeader().参入参数,subpackageCode:{}",
                dto.getSubpackageCode());
        R<Boolean> r = remoteReviewService.refreshChoiceLeader(dto.getSubpackageCode());
        log.error("采购人点击 重新投标票 调用评委接口结束, remoteReviewService.refreshChoiceLeader().参入参数,subpackageCode:{},r:{}",
                dto.getSubpackageCode(), r);
        if (Objects.isNull(r) || r.hasFail()) {
            log.error("通知 评委评审重新投标 失败, subpackageCode:{}, r:{}", dto.getSubpackageCode(), r);
            throw new ServiceException(r.getMsg());
        }
    }

    public void notifyJudgeAgain(NotifyJudgeDto dto) {
        log.error("采购人点击 重新评审 准备调用评委接口, remoteReviewService.delete().参入参数,subpackageCode:{}",
                dto.getSubpackageCode());
        R<Boolean> r = remoteReviewService.delete(dto.getSubpackageCode());
        log.error("采购人点击 重新评审 调用评委接口结束, remoteReviewService.delete().参入参数,subpackageCode:{},r:{}",
                dto.getSubpackageCode(), r);
        if (Objects.isNull(r) || r.hasFail()) {
            log.error("通知 评委重新评审 失败, subpackageCode:{}, r:{}", dto.getSubpackageCode(), r);
            throw new ServiceException(r.getMsg());
        }
    }


    public ReviewSummaryVo getJudgesReviewInfo(NotifyJudgeDto dto) {
        R<ReviewSummaryVo> r;
        log.error("采购人查询 评审结果 准备调用评委接口, remoteReviewService.reviewSummary().参入参数,subpackageCode:{}", dto.getSubpackageCode());
        if (ClientEnum.LG.getCode().equals(EvUtils.ev())) {
            r = remoteReviewService.getSupListToEnd(dto.getSubpackageCode());
        } else {
            r = remoteReviewService.reviewSummary(dto.getSubpackageCode());
        }
        log.error("采购人查询 评审结果 调用评委接口结束, remoteReviewService.reviewSummary().参入参数,subpackageCode:{},r:{}",
                dto.getSubpackageCode(), r);
        if (Objects.isNull(r) || r.hasFail()) {
            log.error("查询 评审结果 失败, subpackageCode:{}, r:{}", dto.getSubpackageCode(), r);
        }
        return r.getData();
//                 65432156565                                    log.error("采购人查询 评审结果 准备调用评委接口, remoteReviewService.reviewSummary().参入参数,subpackageCode:{}", dto.getSubpackageCode());
//        R<ReviewSummaryVo> r = remoteReviewService.reviewSummary(dto.getSubpackageCode());
//        log.error("采购人查询 评审结果 调用评委接口结束, remoteReviewService.reviewSummary().参入参数,subpackageCode:{},r:{}",
//                dto.getSubpackageCode(), r);
//        if (Objects.isNull(r) || r.hasFail()) {
//            log.error("查询 评审结果 失败, subpackageCode:{}, r:{}", dto.getSubpackageCode(), r);
//        }
//        return r.getData();
    }


    public void notifyJudgeConfirmed(NotifyJudgeDto dto) {
        log.error("采购人同意 评委评审结果 准备调用评委接口, remoteReviewService.operatorConfirm().参入参数,subpackageCode:{}",
                dto.getSubpackageCode());
        R<Boolean> r = remoteReviewService.operatorConfirm(dto.getSubpackageCode());
        log.error("采购人同意 评委评审结果 调用评委接口结束, remoteReviewService.operatorConfirm().参入参数,subpackageCode:{},r:{}",
                dto.getSubpackageCode(), r);
        if (Objects.isNull(r)) {
            log.error("同意 评委评审结果 失败, subpackageCode:{}, r:{}", dto.getSubpackageCode(), r);
            throw new ServiceException("同意 评委评审结果 失败");
        }
        if (r.hasFail()) {
            log.error("同意 评委评审结果 失败, subpackageCode:{}, r:{}", dto.getSubpackageCode(), r);
            throw new ServiceException(r.getMsg());
        }
    }


    public Map<String, List<VotingResultsVo>> getJudgeVote(List<String> subpackageCodeList) {
        Map<String, List<VotingResultsVo>> voMap = new HashMap<>();
        subpackageCodeList.forEach(s -> {
            log.error("采购人点击 获取投票信息 准备调用评委接口, remoteReviewService.choiceLeaderList().参入参数,subpackageCode:{}", s);
            R<List<VotingResultsVo>> r = remoteReviewService.choiceLeaderList(s);
            log.error("采购人点击 获取投票信息 调用评委接口结束, remoteReviewService.choiceLeaderList().参入参数,subpackageCode:{}", s);
            if (Objects.isNull(r) || r.hasFail()) {
                log.error("通知 评委评审重新投标 失败, subpackageCode:{}, r:{}", s, r);
//                throw new ServiceException(r.getMsg());
            }
            voMap.put(s, r.getData());
        });
        return voMap;
    }

    public List<SysDictData> getDict(String dictTye) {
        R<List<SysDictData>> r = remoteSystemService.getDictValue(dictTye);
        if (r.hasFail()) {
            log.error("查询 字典信息事变 失败, dictTye:{}, r:{}", dictTye, r);
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }

    public SysDictData getDict(String dictTye, String dictLabel) {
        R<SysDictData> r = remoteSystemService.getDictValueOne(dictTye, dictLabel);
        if (r.hasFail()) {
            log.error("查询 字典信息 失败, dictTye:{}, dictLabel:{}, r:{}", dictTye, dictLabel, r);
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }

    public List<ProjectDocumentVo> getItemFileInfo(GetSimpleItemVo simpleItemVo) {
        //查询项目文件信息
        log.error("准备调用 文件接口查询 项目文件信息, 参数:simpleItemVo:{}", simpleItemVo);
        R<List<ProjectDocumentVo>> r = remoteProjectFileService.fileArchivingListByProject(simpleItemVo.getBuyItemCode());
        log.error("调用 文件接口查询 项目文件信息完成, 参数:simpleItemVo:{}", simpleItemVo);
        if (r.hasFail()) {
            log.error("查询 文件接口查询 项目文件信息 失败, 返回结果：r:{}", r);
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }

    public List<ProjectDocumentVo> getSubFileInfo(String yearMonthSplit, String buyItemCode, String subpackageCode) {
        //查询包文件信息
        log.error("准备调用 文件接口查询 包文件信息, 参数:yearMonthSplit:{},buyItemCode:{},subpackageCode:{}", yearMonthSplit, buyItemCode, subpackageCode);
        R<List<ProjectDocumentVo>> r = remoteProjectFileService.fileArchivingListBySection(yearMonthSplit, buyItemCode, subpackageCode);
        log.error("调用 文件接口查询 包文件信息完成, 返回:r:{}", r);
        if (r.hasFail()) {
            log.error("查询 文件接口查询 包文件信息 失败, 返回结果：r:{}", r);
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }

    public Boolean delFile(String subpackageCode) {
        //删除文件信息
        log.error("准备调用 删除文件信息, 参数:subpackageCode:{}", subpackageCode);
        R<Boolean> r = remoteProjectFileService.deleteFileData(subpackageCode);
        log.error("调用删除文件信息完成, 返回:r:{}", r);
        if (r.hasFail()) {
            log.error("查询 删除文件信息 失败, 返回结果：r:{}", r);
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }

    public Boolean delFileByBuyItemCode(String buyItemCode) {
        //删除文件信息
        log.error("准备调用 删除文件信息, 参数:buyItemCode:{}", buyItemCode);
        R<Boolean> r = remoteProjectFileService.deleteProjectFileAll(buyItemCode);
        log.error("调用删除文件信息完成, 返回:r:{}", r);
        if (r.hasFail()) {
            log.error("查询 删除文件信息 失败, 返回结果：r:{}", r);
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }

    public <T> T getConfigKeyData(String configKey, Class<T> tClass) {
        R r = remoteSystemService.getConfigKey(configKey).toR();
        if (r.hasFail()) {
            log.error("查询参数配置异常，configKey={}, r={}", configKey, r);
            return null;
        }
        Object data = r.getData();
        if (Objects.nonNull(data)) {
            String str = data.toString();
            if (StrUtil.isNotBlank(str))
                return Convert.convert(tClass, str);
        } else {
            log.error("查询参数配置值为空，configKey={}, data={}", configKey, data);
        }
        return null;
    }

    public void updateSupplierScore(Long supplierId, String score) {
        R<Boolean> r = remoteUserService.upScore(SecurityConstants.INNER, supplierId, score);
        if (r.hasFail()) {
            log.error("更新供应商评分失败，supplierId={}, score={}, r={}", supplierId, score, r);
            throw new ServiceException(r.getMsg());
        }
    }

    public String findSupplierScore(Long supplierId) {
        R<String> r = remoteUserService.findScore(SecurityConstants.INNER, supplierId);
        if (r.hasFail()) {
            log.error("更新供应商评分失败，supplierId={}, r={}", supplierId, r);
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }

    public Boolean revokeProcessInstance(String auditCode, String remark) {
        return remoteDingtalk.revokeProcessInstance(auditCode, remark);
    }
}
