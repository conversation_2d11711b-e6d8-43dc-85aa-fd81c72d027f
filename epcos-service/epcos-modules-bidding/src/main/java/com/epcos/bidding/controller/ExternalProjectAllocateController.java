package com.epcos.bidding.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.webservice.external.project.domain.dao.ExternalProjectAllocateDao;
import com.epcos.bidding.purchase.webservice.external.project.domain.dao.ExternalProjectDataDao;
import com.epcos.bidding.purchase.webservice.external.project.domain.dto.inner.AllocateDto;
import com.epcos.bidding.purchase.webservice.external.project.domain.dto.inner.ExternalProjectAllocateDto;
import com.epcos.bidding.purchase.webservice.external.project.domain.dto.inner.ExternalProjectAllocateVo;
import com.epcos.bidding.purchase.webservice.external.project.domain.dto.inner.ExternalProjectDataDetailVo;
import com.epcos.bidding.purchase.webservice.external.project.service.IExternalProjectAllocateApi;
import com.epcos.bidding.purchase.webservice.external.project.service.IExternalProjectDataService;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.utils.bean.BeanUtils;
import com.epcos.common.core.web.page.TableDataVo;
import com.epcos.common.security.annotation.RequiresPermissions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/17 15:54
 */
@RequestMapping("/project.before.allocate")
@RestController
@Slf4j
@Api(tags = "项目立项分配--通用")
@RequiredArgsConstructor
public class ExternalProjectAllocateController {

    private final IExternalProjectAllocateApi externalProjectAllocateApi;
    private final IExternalProjectDataService externalProjectDataService;

    @ApiOperation("查询项目分配列表")
//    @RequiresPermissions(value = "")
    @PostMapping("/page")
    public TableDataVo<ExternalProjectDataDetailVo> page(@RequestBody PageSortEntity<ExternalProjectAllocateDto> dto) {
        dto.getEntity().setAllocateId(SecurityUtils.getUserId());
        IPage<ExternalProjectDataDetailVo> page = externalProjectAllocateApi.allocatePage(dto);
        return new TableDataVo(page.getRecords(), page.getTotal());
    }


    @ApiOperation("查询当前项目资料分配的用户")
//    @RequiresPermissions(value = "")
    @GetMapping("/pageByUser")
    public R<List<ExternalProjectAllocateVo>> pageByUser(@RequestParam Long externalProjectDataId) {
        return R.ok(externalProjectAllocateApi.pageByUser(externalProjectDataId));
    }


    @ApiOperation("项目立项文件分派")
//    @RequiresPermissions(value = "")
    @PostMapping("/allocate")
    public R<Boolean> allocate(@RequestBody @Validated AllocateDto dto) {
        return R.ok(externalProjectAllocateApi.allocate(dto));
    }

    @ApiOperation("删除分派信息")
//    @RequiresPermissions(value = "")
    @GetMapping("/del")
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> del(@NotNull @NotEmpty Long[] ids) {
        for (Long id : ids) {
            ExternalProjectAllocateDao allocateDao = externalProjectAllocateApi.getById(id);
            if (allocateDao.getAllocateWhetherCreate() == 1) {
                return R.fail("该分派信息已创建立项，无法删除");
            }
            externalProjectAllocateApi.removeById(id);
        }
        return R.ok();
    }

    @ApiOperation("查询详细信息")
    @GetMapping("/info")
    public R<ExternalProjectDataDetailVo> info(@NotNull @NotEmpty Long id) {
        ExternalProjectAllocateDao allocateDao = externalProjectAllocateApi.getById(id);
        ExternalProjectDataDao projectDataDao = externalProjectDataService.getById(allocateDao.getExternalProjectDataId());
        ExternalProjectDataDetailVo vo = new ExternalProjectDataDetailVo();
        BeanUtils.copyProperties(projectDataDao, vo);
        BeanUtils.copyProperties(allocateDao, vo);
        return R.ok(vo);
    }

    @ApiOperation("查询未分配的项目条数")
    @RequiresPermissions(value = "project:assign:list")
    @PostMapping("/cnt")
    public R<Long> cnt() {
        long count = externalProjectAllocateApi.count(Wrappers.lambdaQuery(ExternalProjectAllocateDao.class)
                .eq(ExternalProjectAllocateDao::getAllocateWhetherCreate, "0")
                .eq(ExternalProjectAllocateDao::getAllocateId, SecurityUtils.getUserId())
                .groupBy(ExternalProjectAllocateDao::getAllocateId));
        return R.ok(count);
    }
}
