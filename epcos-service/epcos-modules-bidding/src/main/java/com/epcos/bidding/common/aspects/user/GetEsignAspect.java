package com.epcos.bidding.common.aspects.user;

import cn.hutool.core.text.CharSequenceUtil;
import com.epcos.bidding.common.annotaion.GetEsign;
import com.epcos.bidding.common.aspects.AbstractMethod;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.EvalSpelUtil;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.system.api.RemoteUserService;
import com.epcos.system.api.model.EsignVO;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@Aspect
public class GetEsignAspect extends AbstractMethod<GetEsign, EsignVO> {

    public GetEsignAspect() {
        super(GetUtil.GET_ESIGN, "获取用户印章");
    }

    @Autowired
    private RemoteUserService remoteUserService;

    @Override
    @Around("@annotation(getEsign)")
    public Object around(ProceedingJoinPoint point, GetEsign getEsign) {
        return super.around(point, getEsign);
    }

    @Override
    public EsignVO businessMethods(JoinPoint point, GetEsign getEsign) {
        Long userId = null;
        R<EsignVO> esignR;
        String orgCodeEl = getEsign.orgCodeEL();
        if (CharSequenceUtil.isEmpty(orgCodeEl)) {
            String el = getEsign.userIdEL();
            if (CharSequenceUtil.isEmpty(el)) {
                userId = SecurityUtils.getUserId();
            } else {
                userId = EvalSpelUtil.get(((MethodSignature) point.getSignature()).getMethod(), point.getArgs(), el, Long.class);
            }
            esignR = remoteUserService.getSealAndAccountByUserId(userId);
        } else {
            orgCodeEl = EvalSpelUtil.get(((MethodSignature) point.getSignature()).getMethod(), point.getArgs(), orgCodeEl, String.class);
            esignR = remoteUserService.getSealAndAccountByOrgCode(orgCodeEl);
        }
        if (esignR.hasFail()) {
            log.error("远程查询用户印章信息异常，userId={},esignR={}", userId, esignR);
            throw new ServiceException(esignR.getMsg());
        }
        if (esignR.getData() == null) {
            log.error("远程查询用户印章信息返回为空，userId={},esignR={}", userId, esignR);
            throw new ServiceException("查询用户印章信息返回为空");
        }
        return esignR.getData();
    }
}