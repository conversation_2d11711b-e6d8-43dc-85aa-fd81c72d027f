package com.epcos.bidding.common.utils.excel;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellUtil;
import org.apache.poi.xssf.usermodel.*;

import java.util.Optional;

/**
 * <AUTHOR>
 */
public class PoiExcelUtil {

    public static final float DEFAULT_ROW_HEIGHT = 30F;

    /**
     * excel中sheet名不能包含这些字符
     */
    private static String regex = "(\\*|/|:|\\\\|\\[|\\]|\\?)";
    /**
     * excel sheet 名的最大长度
     */
    private static Integer maxSheetName = 32;

    /**
     * 将excel中sheet名不能包含这些字符 统一替换成 _
     *
     * @param sheetName 传进来的sheet表名
     * @return
     */
    public static String replaceSheetName(String sheetName) {
        if (sheetName.length() > maxSheetName) {
            sheetName = sheetName.substring(0, maxSheetName);
        }
        return sheetName.replaceAll(regex, "_");
    }

    // 合并列
    public static void mergedColumn(XSSFSheet sheet, int row, int firstCol, int lastCol) {
        sheet.addMergedRegion(new CellRangeAddress(row, row, firstCol, lastCol));
    }

    // 合并行
    public static void mergedRow(XSSFSheet sheet, int firstRow, int lastRow, int col) {
        sheet.addMergedRegion(new CellRangeAddress(firstRow, lastRow, col, col));
    }

    public static void initSheetStyle(XSSFSheet sheet) {
        sheet.setDefaultRowHeightInPoints(DEFAULT_ROW_HEIGHT);
        sheet.setZoom(100);

        sheet.setHorizontallyCenter(true);
        sheet.setVerticallyCenter(true);
        sheet.setDisplayGridlines(true);

        sheet.setPrintGridlines(false);
        sheet.setFitToPage(true);
    }

    public static Font font(XSSFWorkbook workbook, String fontName, boolean bold, short fontHeightInPoints) {
        XSSFFont font = workbook.createFont();
        font.setFontName(fontName);
        font.setBold(bold);
        font.setFontHeightInPoints(fontHeightInPoints);
        return font;
    }

    public static Font font(XSSFWorkbook workbook, boolean bold, short fontHeightInPoints) {
        return font(workbook, "宋体", bold, fontHeightInPoints);
    }

    // 字体
    public static Font fontTitle(XSSFWorkbook workbook) {
        return font(workbook, true, (short) 18);
    }

    public static Font fontSubtitle(XSSFWorkbook workbook) {
        return font(workbook, false, (short) 12);
    }

    // 字体
    public static Font fontHead(XSSFWorkbook workbook) {
        return font(workbook, true, (short) 12);
    }

    // 字体
    public static Font fontContent(XSSFWorkbook workbook) {
        return font(workbook, false, (short) 10);
    }

    public static Font fontFooter(XSSFWorkbook workbook) {
        return font(workbook, false, (short) 9);
    }

    public static CellStyle cellStyle(XSSFWorkbook workbook,
                                      Font font,
                                      boolean hasBorder,
                                      HorizontalAlignment horizontalAlignment,
                                      VerticalAlignment verticalAlignment) {
        XSSFCellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setWrapText(true);

        if (hasBorder) {
            cellStyle.setBorderTop(BorderStyle.THIN);
            cellStyle.setBorderBottom(BorderStyle.THIN);
            cellStyle.setBorderLeft(BorderStyle.THIN);
            cellStyle.setBorderRight(BorderStyle.THIN);
        }

        cellStyle.setFont(font);
        cellStyle.setAlignment(horizontalAlignment);
        cellStyle.setVerticalAlignment(verticalAlignment);
        return cellStyle;
    }

    // 标题样式
    public static CellStyle cellStyleTitle(XSSFWorkbook workbook) {
        return cellStyle(workbook, fontTitle(workbook), false, HorizontalAlignment.CENTER, VerticalAlignment.CENTER);
    }

    // 表格副标题
    public static CellStyle cellStyleSubtitle(XSSFWorkbook workbook) {
        return cellStyle(workbook, fontSubtitle(workbook), false, HorizontalAlignment.CENTER, VerticalAlignment.CENTER);
    }

    // 表格头样式
    public static CellStyle cellStyleTableHead(XSSFWorkbook workbook) {
        CellStyle cellStyle = cellStyle(workbook, fontHead(workbook), true, HorizontalAlignment.CENTER, VerticalAlignment.CENTER);
        cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return cellStyle;
    }

    // 表格内容样式
    public static CellStyle cellStyleContent(XSSFWorkbook workbook) {
        return cellStyle(workbook, fontContent(workbook), true, HorizontalAlignment.CENTER, VerticalAlignment.CENTER);
    }

    // 表格尾部样式
    public static CellStyle cellStyleTail(XSSFWorkbook workbook) {
        return cellStyle(workbook, fontContent(workbook), true, HorizontalAlignment.LEFT, VerticalAlignment.TOP);
    }

    // 设置页脚样式
    public static CellStyle cellStyleFooter(XSSFWorkbook workbook) {
        return cellStyle(workbook, fontFooter(workbook), false, HorizontalAlignment.RIGHT, VerticalAlignment.CENTER);
    }

    // 添加一行，并在指定位置添加一个单元格数据
    public static void createRowOneCell(XSSFSheet sheet, int cellnum, Float rowHeight, String cellValue, CellStyle cellStyle) {
        XSSFRow rowSecond = sheet.createRow(sheet.getPhysicalNumberOfRows());
        rowSecond.setHeightInPoints(Optional.ofNullable(rowHeight).orElse(DEFAULT_ROW_HEIGHT));
        createCell(sheet.getRow(sheet.getLastRowNum()), cellnum, cellStyle, cellValue);
    }

    // 添加一行，并合并一行中所有列，并按指定样式显示
    public static void createRowOneCellAndMergeAllColumn(XSSFSheet sheet, int cellnum, Float rowHeight, String cellValue, CellStyle cellStyle, int columnSize) {
        PoiExcelUtil.createRowOneCell(sheet, cellnum, rowHeight, cellValue, cellStyle);
        setCellStyle(sheet, cellnum, columnSize, cellStyle);
        PoiExcelUtil.mergedColumn(sheet, sheet.getLastRowNum(), 0, columnSize);
    }

    // 主动设置单元格样式，避免合并单元格后样式失效
    public static void setCellStyle(XSSFSheet sheet, int cellnum, int columnSize, CellStyle cellStyle) {
        while (cellnum <= columnSize) {
            Cell cell = CellUtil.getCell(sheet.getRow(sheet.getLastRowNum()), cellnum++);
            cell.setCellStyle(cellStyle);
        }
    }

    // 在指定位置添加单元格
    public static void createCell(XSSFRow row, int cellnum, CellStyle cellStyle, String cellValue) {
        CellUtil.createCell(
                CellUtil.getRow(row.getRowNum(), row.getSheet()), cellnum, cellValue, cellStyle);
    }


}
