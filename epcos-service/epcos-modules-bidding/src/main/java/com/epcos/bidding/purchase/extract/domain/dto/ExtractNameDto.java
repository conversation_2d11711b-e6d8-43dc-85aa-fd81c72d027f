package com.epcos.bidding.purchase.extract.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/26 10:42
 */
@Data
public class ExtractNameDto extends JudgeExtractDto {

    private static final long serialVersionUID = 4108475509953782698L;

    /**
     * 此字段改为两种方式，一种为手动输入，即用户自己随意输入
     * 另一种为模糊匹配，匹配规则和之前一样保持不变
     */
    @ApiModelProperty(value = "抽取记录名称")
    @NotBlank
    @Length(max = 256, message = "抽取记录名称超出长度限制")
    private String extractLongName;

    /**
     * 补抽保存时，此值为true
     * 其他情况均为false
     */
    @ApiModelProperty(value = "是否补抽，true-是，false-否")
    private Boolean repair = false;

    @ApiModelProperty(value = "备注")
    @Length(max = 256, message = "备注超出长度限制")
    private String remark;
}
