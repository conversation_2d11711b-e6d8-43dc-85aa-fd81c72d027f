package com.epcos.bidding.purchase.extract.mapping;

import com.epcos.bidding.purchase.api.params.JudgesVo;
import com.epcos.bidding.purchase.extract.domain.dao.ExtractJudgeInnerDao;
import com.epcos.bidding.purchase.extract.domain.dto.JudgeExtractDto;
import com.epcos.bidding.purchase.extract.domain.vo.ExtractLogVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/18 14:25
 */
@Mapper
public interface ExtractJudgeInnerConvert {

    ExtractJudgeInnerConvert INSTANCE = Mappers.getMapper(ExtractJudgeInnerConvert.class);

    @Mappings(value = {
            @Mapping(target = "password", source = "password")
    })
    ExtractJudgeInnerDao convert(JudgeExtractDto judgeExtractDto, ExtractLogVo vo, String password);

    ExtractJudgeInnerDao convert(JudgesVo judgesVo);

    JudgesVo convert(ExtractJudgeInnerDao extractJudgeInnerDao);
}
