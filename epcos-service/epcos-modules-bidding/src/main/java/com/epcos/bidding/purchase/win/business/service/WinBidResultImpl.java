package com.epcos.bidding.purchase.win.business.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.audit.api.AuditProcessDto;
import com.epcos.bidding.common.annotaion.GetCommon;
import com.epcos.bidding.common.annotaion.GetItem;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.common.aspects.vo.GetSimpleItemVo;
import com.epcos.bidding.common.utils.BiddingBaseUtil;
import com.epcos.bidding.purchase.api.params.AttributeVo;
import com.epcos.bidding.purchase.api.params.vo.buyitem.BuyItemVo;
import com.epcos.bidding.purchase.api.params.vo.buyitem.ItemSubpackageVo;
import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import com.epcos.bidding.purchase.bargain.business.api.IPurchaseBargainApi;
import com.epcos.bidding.purchase.bargain.domain.dao.PurchaseBargainDao;
import com.epcos.bidding.purchase.bulletin.business.api.IBulletinApi;
import com.epcos.bidding.purchase.bulletin.domain.dao.BulletinDao;
import com.epcos.bidding.purchase.extract.business.api.IExtractJudgeInnerApi;
import com.epcos.bidding.purchase.extract.domain.dao.ExtractJudgeInnerDao;
import com.epcos.bidding.purchase.extract.domain.vo.ExtractLogVo;
import com.epcos.bidding.purchase.monitor.business.api.IMonitorBidApi;
import com.epcos.bidding.purchase.process.business.api.IReviewBeforeApi;
import com.epcos.bidding.purchase.process.domain.dao.ReviewBeforeDao;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.purchase.project.mapping.DefaultVoAndDtoConvert;
import com.epcos.bidding.purchase.remote.RemoteSupplierApi;
import com.epcos.bidding.purchase.remote.RemoteToOtherServiceApi;
import com.epcos.bidding.purchase.remote.dto.NotifyJudgeDto;
import com.epcos.bidding.purchase.win.business.api.IWinBidResultApi;
import com.epcos.bidding.purchase.win.domain.dao.WinBidResultDao;
import com.epcos.bidding.purchase.win.domain.dto.*;
import com.epcos.bidding.purchase.win.domain.vo.ResultBulletinVo;
import com.epcos.bidding.purchase.win.domain.vo.ReviewPageVo;
import com.epcos.bidding.purchase.win.domain.vo.WinBidVo;
import com.epcos.bidding.purchase.win.domain.vo.WinSubPackageVo;
import com.epcos.bidding.purchase.win.mapping.WinBidVoStruct;
import com.epcos.bidding.purchase.win.repository.IWinBidResultMapper;
import com.epcos.bidding.supplier.answer.business.api.IAnswerFileQuoteFormApi;
import com.epcos.bidding.supplier.api.params.MultiSupplierQuoteFormVo;
import com.epcos.bidding.supplier.api.params.RoundQuoteFormVo;
import com.epcos.bidding.supplier.api.params.SupplierQuoteFormVo;
import com.epcos.bidding.supplier.api.params.SupplierSignUpVo;
import com.epcos.bidding.supplier.sign.business.api.ISupplierSignApi;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierSignUpDao;
import com.epcos.bidding.supplier.sign.domain.dto.SupplierSignQueryDto;
import com.epcos.bidding.supplier.sign.repository.SupplierSignUpMapper;
import com.epcos.common.core.constant.FileTypeNameConstants;
import com.epcos.common.core.constant.SecurityConstants;
import com.epcos.common.core.constant.UserConstants;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.domain.review.ReviewSummaryVo;
import com.epcos.common.core.domain.review.SupplierDetailsVo;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.DateUtils;
import com.epcos.common.core.utils.EvUtils;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.utils.StringUtils;
import com.epcos.common.core.web.domain.user.SysUser;
import com.epcos.common.file.html2pdf.HtmlUtil;
import com.epcos.common.redis.service.RedisService;
import com.epcos.review.api.domain.vo.VotingResultsVo;
import com.epcos.system.api.RemoteUserService;
import com.epcos.system.api.domain.assmble.vo.FunctionKV;
import com.epcos.system.api.model.FUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring5.SpringTemplateEngine;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.epcos.bidding.common.AuditTypeEnum.BULLETIN;
import static com.epcos.bidding.common.enums.BulletinTypeEnum.WIN_CANDIDATE_PUBLICITY;
import static com.epcos.bidding.common.enums.BulletinTypeEnum.WIN_RESULT_PUBLICITY;
import static com.epcos.bidding.common.enums.FunctionEnum.*;
import static com.epcos.common.core.constant.CacheConstants.COMMENT_SUPPLIER;
import static com.epcos.common.core.constant.PurchaseConstants.Bargain.Judge;
import static com.epcos.common.core.constant.PurchaseConstants.Bulletin.KEYWORDS;
import static com.epcos.common.core.constant.PurchaseConstants.BulletinAudit.PASS;
import static com.epcos.common.core.constant.PurchaseConstants.BulletinAudit.WAIT_VERIFY;
import static com.epcos.common.core.constant.PurchaseConstants.Currency.ONE;
import static com.epcos.common.core.constant.PurchaseConstants.Currency.ZERO;
import static com.epcos.common.core.constant.UserConstants.PURCHASER_COMMENT_SUPPLIER_TIME;
import static com.epcos.common.core.enums.ClientEnum.LG;
import static com.epcos.common.core.enums.ClientEnum.XY;
import static java.lang.Boolean.TRUE;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/9 14:56
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WinBidResultImpl extends ServiceImpl<IWinBidResultMapper, WinBidResultDao> implements IWinBidResultApi {


    private final IExtractJudgeInnerApi extractJudgeInnerApi;
    private final IBuyItemApi buyItemApi;
    private final ISubPackageApi subPackageApi;
    private final IReviewBeforeApi reviewBeforeApi;
    private final RemoteToOtherServiceApi remoteToOtherServiceApi;
    private final ISupplierSignApi supplierSignApi;
    private final IPurchaseBargainApi bargainApi;
    private final IMonitorBidApi monitorBidApi;
    private final IWinBidResultMapper winBidResultMapper;
    private final WinBidVoStruct winBidVoStruct;
    private final RemoteSupplierApi remoteSupplierApi;
    private final DefaultVoAndDtoConvert defaultVoAndDtoConvert;
    private IBulletinApi bulletinApi;
    private final RemoteUserService remoteUserService;
    private final SpringTemplateEngine templateEngine;
    private final IPurchaseBargainApi purchaseBargainApi;
    private final IAnswerFileQuoteFormApi iAnswerFileQuoteFormApi;
    private final SupplierSignUpMapper supplierSignUpMapper;
    private final RedisService redisService;


    @Autowired
    public void setBulletinApi(IBulletinApi bulletinApi) {
        this.bulletinApi = bulletinApi;
    }

    @Override
    public List<ItemSubpackageVo<ReviewPageVo>> confirmReviewPage(String buyItemCode) {

        List<ItemSubpackageVo<ReviewPageVo>> voList = new ArrayList<>();
        List<ItemSubpackageVo> judgeVoList = extractJudgeInnerApi.queryJudgeList(buyItemCode);
        log.error("judgeVoList:{}", judgeVoList);
        if (CollectionUtils.isEmpty(judgeVoList)) {
            return voList;
        }
        List<String> subCodeList = judgeVoList.stream().map(ItemSubpackageVo::getSubpackageCode).collect(Collectors.toList());
        List<ReviewBeforeDao> beforeDaoList = reviewBeforeApi.find(subCodeList);
        Map<String, List<VotingResultsVo>> judgeVoteMap = null;
        if (!CollectionUtils.isEmpty(beforeDaoList) && String.valueOf(ONE).equals(beforeDaoList.get(0).getConfirmCounterSign())) {
            judgeVoteMap = remoteToOtherServiceApi.getJudgeVote(subCodeList);
        }
        Map<String, List<ReviewBeforeDao>> beforeMap =
                beforeDaoList.stream().collect(Collectors.groupingBy(ReviewBeforeDao::getSubpackageCode));
        //填充评委信息
        fillJudge(judgeVoList, judgeVoteMap, beforeMap, voList, buyItemCode);
        return voList;
    }

    private void fillJudge(List<ItemSubpackageVo> judgeVoList, Map<String, List<VotingResultsVo>> judgeVoteMap,
                           Map<String, List<ReviewBeforeDao>> beforeMap, List<ItemSubpackageVo<ReviewPageVo>> voList, String buyItemCode) {
        for (ItemSubpackageVo j : judgeVoList) {
            ItemSubpackageVo<ReviewPageVo> vo = new ItemSubpackageVo(j.getSubpackageCode(), j.getSubpackageName());
            ReviewPageVo pageVo = new ReviewPageVo();
            //没有审核通过则不展示
            if (showInfo(j.getSubpackageCode(), buyItemCode)) {
                List<VotingResultsVo> judgeVoteList = MapUtils.isEmpty(judgeVoteMap) ? Collections.EMPTY_LIST : judgeVoteMap.get(j.getSubpackageCode());
                List<ReviewBeforeDao> reviewBeforeDaoList = beforeMap.get(j.getSubpackageCode());
                if (!CollectionUtils.isEmpty(reviewBeforeDaoList)) {
                    ReviewBeforeDao beforeDao = reviewBeforeDaoList.get(0);
                    pageVo.setConfirmCounterSign(beforeDao.getConfirmCounterSign());
                    pageVo.setConfirmReview(StringUtils.hasText(beforeDao.getConfirmReview()) ? beforeDao.getConfirmReview() : String.valueOf(ZERO));
                    pageVo.setIsBargaining(beforeDao.getIsBargaining());
                    pageVo.setWhetherShowJudge(StringUtils.hasText(beforeDao.getWhetherShowJudge()) ? beforeDao.getWhetherShowJudge() : String.valueOf(ZERO));
                }
                List<ExtractLogVo> judgeList = (List<ExtractLogVo>) j.getData();
                if (!CollectionUtils.isEmpty(judgeVoteList)) {
                    judgeList.forEach(judge -> judgeVoteList.forEach(vote -> {
                        if (judge.getJudgeId().equals(vote.getJudgeId())) {
                            BeanUtils.copyProperties(vote, judge);
                        }
                    }));
                }
                pageVo.setJudgeVoList(judgeList);
            }
            vo.setData(pageVo);
            voList.add(vo);
        }
    }

    private boolean showInfo(String subpackageCode, String buyItemCode) {
        BuyItemDao buyItemInfo = buyItemApi.findBuyItemInfo(buyItemCode);
        List<String> funList = buyItemInfo.parsePurchaseFunctionJson().stream().map(FunctionKV::getPurchaseFunctionKey).collect(Collectors.toList());
        boolean success = false;

        if (!funList.contains(PURCHASER_REVIEW_EXPERT.getKey())) {
            success = true;
        }
        List<ExtractJudgeInnerDao> bySubCode = extractJudgeInnerApi.findBySubCode(subpackageCode);
        if (CollectionUtils.isEmpty(bySubCode)) {
            return success;
        }
        Long id = bySubCode.get(0).getId();
        if (funList.contains(PURCHASER_EXPERT_AUDIT.getKey())) {
            AuditProcessDto auditProcessDto = extractJudgeInnerApi.queryJudgeAudit(buyItemCode, subpackageCode, id);
            if (Objects.isNull(auditProcessDto)) {
                return success;
            }
            if (PASS.equals(String.valueOf(auditProcessDto.getStatus()))) {
                success = true;
            }
        } else {
            //龙港以外的均不用审核
            success = true;
        }
        return success;
    }

    @Override
    public Boolean againVote(String subpackageCode) {
        remoteToOtherServiceApi.notifyJudgeChoice(new NotifyJudgeDto(subpackageCode));
        return TRUE;
    }

    @Override
    @GetItem(common = @GetCommon(buyItemCodeEL = "#buyItemCode", async = true))
    public List<ItemSubpackageVo<WinSubPackageVo>> winPage(String buyItemCode) {

        List<SubpackageDao> subpackageDaoList = GetUtil.getItemVo().getSubpackageDaoList();
        List<ItemSubpackageVo<WinSubPackageVo>> voList = subpackageDaoList.stream()
                .map(s -> {
                    ItemSubpackageVo<WinSubPackageVo> vo = new ItemSubpackageVo(s.getSubpackageCode(), s.getSubpackageName());
                    WinSubPackageVo winSubPackageVo = new WinSubPackageVo();
                    List<SupplierSignUpVo> supplierSignUpVoList = getFilteredSupplierSignUpVoList(s);
                    if (CollectionUtils.isEmpty(supplierSignUpVoList)) {
                        vo.setData(winSubPackageVo);
                        return vo;
                    }
                    vo = defaultVoAndDtoConvert.dtoAndVoConvert(vo, winSubPackageVo, supplierSignUpVoList);
                    //查询公告key
                    winSubPackageVo = queryBulletinKey(s.getSubpackageCode(), winSubPackageVo);
                    //查询中标信息
                    winSubPackageVo = queryWin(s.getSubpackageCode(), winSubPackageVo);
                    //查询评审结果
                    ReviewSummaryVo reviewInfo = remoteToOtherServiceApi.getJudgesReviewInfo(new NotifyJudgeDto(s.getSubpackageCode()));
                    if (Objects.nonNull(reviewInfo)) {
                        winSubPackageVo.setReviewMethod(reviewInfo.getReviewMethod());
                    }
                    //计算价格分
                    calculatePriceScore(s.getSubpackageCode(), reviewInfo);
                    winSubPackageVo.setSummaryVo(reviewInfo);
                    vo.setData(winSubPackageVo);
                    return vo;
                }).collect(Collectors.toList());
        return voList;
    }

    /**
     * 计算价格分
     *
     * @param subpackageCode 标段code
     * @param reviewInfo
     */
    @Override
    public ReviewSummaryVo calculatePriceScore(String subpackageCode, ReviewSummaryVo reviewInfo) {

        SubpackageDao subpackageDao = subPackageApi.findBySub(subpackageCode);
        if (Objects.isNull(reviewInfo)) {
            return null;
        }
        List<SupplierDetailsVo> supplierDetailsVos = reviewInfo.getSupplierDetailsVoList();
        if (CollectionUtils.isEmpty(supplierDetailsVos)) {
            return null;
        }
        int round;
        List<PurchaseBargainDao> maxBargains = bargainApi.findMaxBargain(subpackageCode);
        // 报价轮数
        round = CollectionUtils.isEmpty(maxBargains) ? 0 : maxBargains.get(0).getRound();

        MultiSupplierQuoteFormVo bargainInfo = remoteSupplierApi.getBargainInfo(subpackageCode, null, round);
        List<SupplierQuoteFormVo> supplierQuoteFormList = bargainInfo.getSupplierQuoteFormList();
        //获取最小值
        BigDecimal min = supplierQuoteFormList.stream()
                .flatMap(supplierQuoteForm -> supplierQuoteForm.getRoundQuoteFormList().stream())
                .map(RoundQuoteFormVo::getAllRowQuotationTotalPrice)
                .min(BigDecimal::compareTo)
                .orElse(null);
        Map<Long, List<RoundQuoteFormVo>> supplierFormMap = supplierQuoteFormList.stream()
                .collect(Collectors.toMap(SupplierQuoteFormVo::getSupplierId, SupplierQuoteFormVo::getRoundQuoteFormList));
        supplierDetailsVos.forEach(supplierDetailsVo -> {
            List<RoundQuoteFormVo> formVoList = supplierFormMap.get(supplierDetailsVo.getSupplierId());
            BigDecimal totalPrice;
            if (CollectionUtils.isEmpty(formVoList)) {
                totalPrice = BigDecimal.ZERO;
            } else {
                totalPrice = formVoList.get(0).getAllRowQuotationTotalPrice();
            }
            //投标报价
            BigDecimal priceSource;
            if (totalPrice.equals(BigDecimal.ZERO)) {
                priceSource = BigDecimal.ZERO;
            } else {
                priceSource = min.divide(totalPrice, 10, RoundingMode.HALF_UP)
                        .multiply(
                                Objects.isNull(subpackageDao.getPriceTotalSource())
                                        ? BigDecimal.ZERO
                                        : subpackageDao.getPriceTotalSource()
                        )
                        .setScale(2, RoundingMode.HALF_UP);
            }
            supplierDetailsVo.setPriceSource(priceSource);
            if (Objects.nonNull(priceSource)) {
                supplierDetailsVo.setTotalSource(priceSource.add(BigDecimal.valueOf
                                (
                                        Objects.nonNull(supplierDetailsVo.getScoreResult())
                                                ? supplierDetailsVo.getScoreResult()
                                                : 0
                                )
                        )
                );
            }
        });
        return reviewInfo;
    }

    /**
     * 根据超级包装物信息获取并过滤供应商信息
     *
     * @param s 超级包装物对象，包含子包代码
     * @return 过滤后的供应商报名信息列表。如果原始列表为空，返回空列表；如果需要进一步过滤，根据评审前信息进行过滤。
     */
    private List<SupplierSignUpVo> getFilteredSupplierSignUpVoList(SubpackageDao s) {
        BuyItemDao buyItemDao = buyItemApi.findBySubpackageCode(s.getSubpackageCode());
        SupplierSignQueryDto queryDto = defaultVoAndDtoConvert.dtoAndVoConvert(s.getSubpackageCode(), buyItemDao);
        List<SupplierSignUpVo> supplierSignUpVoList = supplierSignApi.query(queryDto, null);
        if (CollectionUtils.isEmpty(supplierSignUpVoList)) {
            return Collections.emptyList();
        }

        List<ReviewBeforeDao> beforeDaoList = reviewBeforeApi.find(s.getSubpackageCode(), null);
        if (!CollectionUtils.isEmpty(beforeDaoList)
                && String.valueOf(ONE).equals(beforeDaoList.get(0).getConfirmReview())) {

            List<Long> supplierIdList = beforeDaoList.stream()
                    .map(ReviewBeforeDao::getSupplierId)
                    .collect(Collectors.toList());
            supplierSignUpVoList = supplierSignUpVoList.stream()
                    .filter(signUpVo -> supplierIdList.contains(signUpVo.getSupplierId()))
                    .collect(Collectors.toList());
        }
        return supplierSignUpVoList;
    }

    private WinSubPackageVo queryWin(String subpackageCode, WinSubPackageVo vo) {
        List<WinBidResultDao> daoList = find(subpackageCode);
        if (CollectionUtils.isEmpty(daoList)) {
            vo.setSelected(0);
            return vo;
        }
        Map<Long, WinBidResultDao> winMap =
                daoList.stream().collect(Collectors.toMap(WinBidResultDao::getSupplierId, Function.identity()));
        vo.setSelected(1);
        if (CollectionUtils.isEmpty(vo.getWinBidVoList())) {
            return vo;
        }
        vo.getWinBidVoList().forEach(w -> BeanUtils.copyProperties(winMap.get(w.getSupplierId()), w));
        return vo;
    }

    private WinSubPackageVo queryBulletinKey(String subpackageCode, WinSubPackageVo vo) {
        List<String> bulletinTypeList = Arrays.asList(WIN_CANDIDATE_PUBLICITY.getKey(), WIN_RESULT_PUBLICITY.getKey());
        List<String> bulletinStateList = Arrays.asList(PASS, WAIT_VERIFY);
        List<BulletinDao> daoList = bulletinApi.findBySubpackageCodeAndBulletinTypeInAndAuditStatusIn(null,
                subpackageCode, bulletinTypeList, bulletinStateList);
        if (!CollectionUtils.isEmpty(daoList)) {
            daoList.forEach(b -> {
                if (WIN_CANDIDATE_PUBLICITY.getKey().equals(b.getBulletinType())) {
                    vo.setCandidateInfo(fillResultNotice(b));
                } else {
                    vo.setWinInfo(fillResultNotice(b));
                }
            });
        }
        vo.setAuditType(BULLETIN.getType());
        return vo;
    }

    private ResultBulletinVo fillResultNotice(BulletinDao b) {
        ResultBulletinVo resultBulletinVo = new ResultBulletinVo();
        resultBulletinVo.setBulletinId(b.getId());
        resultBulletinVo.setAuditStatus(b.getAuditStatus());
        resultBulletinVo.setBulletinKey(b.getBulletinContentKey());
        resultBulletinVo.setAuditCode(b.getBulletinAuditCode());
        return resultBulletinVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmCounterSign(ConfirmCounterDto dto) {
        dto.setIsBargaining(Judge);
        //通知评委初始化数据
        remoteToOtherServiceApi.notifyJudgeToIsBargain(new NotifyJudgeDto(dto.getSubpackageCode(), dto.getIsBargaining()), dto.getDeptJudgeId());
        if (Objects.nonNull(dto.getDeptJudgeId())) {
            //选科室代表
            extractJudgeInnerApi.deptRepresent(dto.getDeptJudgeId(), String.valueOf(ONE), dto.getSubpackageCode());
        }
        //修改会签状态
        reviewBeforeApi.updateBy(String.valueOf(ONE), dto.getSubpackageCode(), dto.getIsBargaining());
        return TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmReview(ConfirmReviewDto dto) {
        //通知评委
        remoteToOtherServiceApi.notifyJudgeToIsReview(new NotifyJudgeDto(dto.getSubpackageCode(), dto.getIsReview()));
        //将选取的组长id传给评委模块
        remoteToOtherServiceApi.notifyJudgeToGroup(new NotifyJudgeDto(dto.getSubpackageCode(), dto.getGroupId()));
        //保存组长标记
        extractJudgeInnerApi.update(new ExtractJudgeInnerDao() {{
            setWhetherGroup(String.valueOf(ONE));
        }}, extractJudgeInnerApi.updateWrapper(dto.getSubpackageCode(), dto.getGroupId()));
        //修改专家状态
        extractJudgeInnerApi.updateBy(String.valueOf(ONE), dto.getSubpackageCode());
        //修改进入评审的状态
        reviewBeforeApi.updateBy(String.valueOf(ONE), dto.getSubpackageCode());
        return TRUE;
    }


//    /**
//     * 判断是否有 发送成交候选人公示 功能
//     *
//     * @return
//     */
//    @HasFunction(functionEnum = FunctionEnum.PURCHASER_CANDIDATE_PUBLICITY, belongRole = "1",
//            common = @GetCommon(buyItemCodeEL = "#buyItemCode"))
//    private Boolean candidateFunction(String buyItemCode) {
//        return GetUtil.getHasFunction();
//    }
//
//    /**
//     * 判断是否有 发送成交候选人公示 功能
//     *
//     * @return
//     */
//    @HasFunction(functionEnum = FunctionEnum.PURCHASER_RESULT_PUBLICITY, belongRole = "1",
//            common = @GetCommon(buyItemCodeEL = "#buyItemCode"))
//    private Boolean resultFunction(String buyItemCode) {
//        return GetUtil.getHasFunction();
//    }


    @Override
    @Transactional
    public Boolean sendNotice(ResultNoticeDto dto) {
//            QueryWrapper<BulletinDao> qw = new QueryWrapper<>();
//            qw.lambda().eq(BulletinDao::getSubpackageCode, dto.getSubpackageCode())
//                    .eq(BulletinDao::getAuditStatus, 1).eq(BulletinDao::getDeleted, 0)
//                    .eq(BulletinDao::getBulletinType, WIN_CANDIDATE_PUBLICITY.getKey())
//                    .last("limit 1");
//            BulletinDao one = bulletinApi.getOne(qw);
//            if (one == null) {
//                throw new ServiceException("请先发送采购候选人公示");
//            }
        // 查询项目信息
        BuyItemDao buyItemDao = buyItemApi.findBuyItemInfo(dto.getBuyItemCode());
        // html 转 pdf
        File pdf = HtmlUtil.toPdf(dto.getNoticeTemplate());
        // 文件上传
        String url = BiddingBaseUtil.generateFileAndReturnUrl(pdf, buyItemDao, buyItemDao.getBuyItemName() + "成交结果通知书",
                FileTypeNameConstants.WIN_LETTER, dto.getSubpackageCode(), SecurityUtils.getUserId());
//        FUtil.orgSealByKeyword(url, KEYWORDS, buyItemDao.getOrgCode());
        // 盖章
        FUtil.startSeal(url, KEYWORDS, buyItemDao.getOrgCode(), null, UserConstants.FIRM_KEY_SEAL_PARAMETER);

        WinBidResultDao winBidResultDao = new WinBidResultDao();
        winBidResultDao.setIsWin(dto.getIsWin());
        winBidResultDao.setResultNotice(url);
        winBidResultDao.setSendTime(new Date());
        update(winBidResultDao, updateWrapper(dto.getSubpackageCode(), dto.getSupplierId()));
        //项目完成
        completed(buyItemDao.getBuyItemCode());
        return TRUE;
    }

    /**
     * 项目完成标识
     *
     * @param buyItemCode
     */

    public void completed(String buyItemCode) {
        GetSimpleItemVo vo = GetUtil.getSimpleItemVo();
        List<SuperPackageVo> superPackageVoList = vo.getSuperPackageVoList();

        // 遍历所有标段，只要发现一个未完成的非废标标段就终止循环
        for (SuperPackageVo superPackageVo : superPackageVoList) {
            // 跳过废标标段
            if (String.valueOf(ONE).equals(superPackageVo.getAbandon())) {
                continue;
            }
            // 检查非废标标段是否完成
            if (!isPackageCompleted(superPackageVo.getSubpackageCode())) {
                throw new ServiceException(superPackageVo.getSubpackageName() + "标段未完成，请耐心等待");
            }
        }
        // 所有非废标标段均完成，更新状态
        updateBuyItemStatus(buyItemCode);
        //项目完成缓存住项目code，在一定时间后短信通知创建项目的采购人
        if (LG.getCode().equals(EvUtils.ev())) {
            lgRedisCache(buyItemCode);
        }
    }

    private void lgRedisCache(String buyItemCode) {
        //缓存key
        Integer month = remoteToOtherServiceApi.getConfigKeyData(PURCHASER_COMMENT_SUPPLIER_TIME, Integer.class);
        if (Objects.isNull(month) || month == 0) {
            redisService.setCacheObject(COMMENT_SUPPLIER + ":" + buyItemCode, "1", 3L, TimeUnit.MINUTES);
        } else {
            //获取当前时间，并计算三个月后的时间，并计算出两者相差多少天
            Long day = dateDiffDay(month);
            redisService.setCacheObject(COMMENT_SUPPLIER + ":" + buyItemCode, "1", day, TimeUnit.DAYS);
        }
    }


    private Long dateDiffDay(Integer month) {
        LocalDate currentDate = LocalDate.now();
        LocalDate futureDate = currentDate.plusMonths(month);
        return ChronoUnit.DAYS.between(currentDate, futureDate);
    }

    /**
     * 判断标段是否完成（有中标结果且至少一个结果通知书已发送）
     */
    private boolean isPackageCompleted(String subpackageCode) {

        List<WinBidResultDao> resultDaoList = find(subpackageCode);
        if (CollectionUtils.isEmpty(resultDaoList)) {
            return false;
        }
        for (WinBidResultDao result : resultDaoList) {
            if (StringUtils.hasText(result.getResultNotice())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 更新采购项为已完成状态
     */
    private void updateBuyItemStatus(String buyItemCode) {
        BuyItemDao updateDao = new BuyItemDao();
        updateDao.setEnd(1);
        updateDao.setEndTime(DateUtils.getNowDate());
        buyItemApi.update(updateDao, buyItemApi.updateWrapper(buyItemCode));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmReviewResult(String subpackageCode) {

        remoteToOtherServiceApi.notifyJudgeConfirmed(new NotifyJudgeDto(subpackageCode));
        extractJudgeInnerApi.updateBy("2", subpackageCode);
        reviewBeforeApi.update(
                new ReviewBeforeDao() {{
                    setReviewEndTime(new Date());
                }},
                reviewBeforeApi.updateWrapper(subpackageCode, null)
        );
        return TRUE;
    }

    @Override
    public Boolean selectWin(SelectWinDto dto) {
        BuyItemVo buyItemVo = GetUtil.getItemVo().getBuyItemVo();
        if (buyItemVo.getEnd() != 0) {
            throw new ServiceException("该项目已完成，不可操作");
        }
        if (buyItemVo.whetherHasFunction(PURCHASER_START_REVIEW.getKey())) {

            List<ExtractJudgeInnerDao> innerDaoList = extractJudgeInnerApi.findBySubpackageCode(dto.getSubpackageCode());
            if (CollectionUtils.isEmpty(innerDaoList) || !"2".equals(innerDaoList.get(0).getStatus())) {
                throw new ServiceException("评审还未结束，请耐心等待");
            }
        }
        List<WinBidResultDao> winDaoList = find(dto.getSubpackageCode());
        List<WinBidResultDao> daoList = dto.getWinSupplierList().stream().map(w -> {
            WinBidResultDao winBidResultDao = new WinBidResultDao();
            BeanUtils.copyProperties(dto, winBidResultDao);
            BeanUtils.copyProperties(w, winBidResultDao);
            return winBidResultDao;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(winDaoList)) {
            return saveBatch(daoList);
        } else {
            dto.getWinSupplierList()
                    .forEach(w -> {
                        WinBidResultDao upDao = new WinBidResultDao();
                        upDao.setWinBid(w.getWinBid());
                        update(upDao, updateWrapper(dto.getSubpackageCode(), w.getSupplierId()));
                    });
            return TRUE;
        }
    }

    @Override
    public WinBidVo findBy(String subpackageCode, Long supplierId) {
        // 查询 purchase_win_bid_result 接口
        WinBidResultDao resultDao = findOne(subpackageCode, supplierId);
        WinBidVo vo = winBidVoStruct.entityConvertTObj(resultDao);
        return vo;
    }

    @Override
    public List<WinBidResultDao> findBy(String subpackageCode, Long supplierId, String winBid) {
        return list(queryWrapper(subpackageCode, supplierId, winBid));
    }


    @Override
    public List<WinBidVo> findBy(String subpackageCode) {
        List<WinBidResultDao> winBidResultDaoList = find(subpackageCode);
        List<WinBidVo> voList =
                winBidResultDaoList.stream().map(w -> winBidVoStruct.entityConvertTObj(w)).collect(Collectors.toList());
        return voList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean againConfirmReview(String subpackageCode) {
        String ev = EvUtils.ev();
        if (!XY.getCode().equals(ev)) {
            List<WinBidVo> winBidList = findBy(subpackageCode);
            if (!CollectionUtils.isEmpty(winBidList)) {
                throw new ServiceException("已确认评审结果 不可重新评审");
            }
        }
        //调用评委 TODO 因 去掉了分布式事务Seata  此处远程调用存在 分布式事务问题
        remoteToOtherServiceApi.notifyJudgeAgain(new NotifyJudgeDto(subpackageCode));
        //专家状态退回到未评审状态.将评委的组长以及科室代表恢复到默认状态
        extractJudgeInnerApi.rollbackData(subpackageCode);
        //退回到未评审的状态
        reviewBeforeApi.rollbackData(subpackageCode);
        //清除议价 数据
        bargainApi.delBargainInfo(subpackageCode);
        //清除监标信息
        monitorBidApi.remove(subpackageCode, "2");
        return TRUE;
    }

    public Map<Long, Long> calculateWinCnt(List<Long> supplierIds) {
        Map<Long, Long> voMap = new HashMap<>();
        List<Map<Long, Long>> supplierWinCntList = winBidResultMapper.selectBySupplierIds(supplierIds);
        supplierWinCntList.forEach(win -> voMap.put(win.get("supplier_id"), win.get("cnt")));
        return voMap;
    }

    /**
     * 生成评审报告
     *
     * @param reviewReportDto
     * @return
     */
    @Transactional
    @Override
    public R<Boolean> confirmReviewReport(ReviewReportDto reviewReportDto) {
        LambdaQueryWrapper<SubpackageDao> qw = new LambdaQueryWrapper<>();
        qw.eq(SubpackageDao::getSubpackageCode, reviewReportDto.getSubpackageCode());
        SubpackageDao subpackageDao = subPackageApi.getOne(qw);
        if (subpackageDao == null) {
            log.error("标段信息查询为空,subpackageCode={}", reviewReportDto.getSubpackageCode());
            throw new ServiceException("标段信息查询为空");
        }
        Integer maxRound = purchaseBargainApi.findMaxRound(reviewReportDto.getSubpackageCode());
        if (maxRound == null) {
            log.error("查询议价最大轮数失败,subpackageCode={}", reviewReportDto.getSubpackageCode());
            getReviewReport(subpackageDao, reviewReportDto.getReviewReportHtml(), reviewReportDto.getReviewReportHtml());
            return R.ok(true, "生成评审报告成功");
        }
        MultiSupplierQuoteFormVo vo = iAnswerFileQuoteFormApi.findVo(reviewReportDto.getSubpackageCode(), Collections.EMPTY_SET, maxRound);
        if (vo == null) {
            log.error("以包编码查询报价内容失败,subpackageCode={}", reviewReportDto.getSubpackageCode());
            throw new ServiceException("以包编码查询报价内容失败");
        }

        List<QuotationSheetTable> quotationSheetTableList = new ArrayList<>();
        // 表头列
        List<AttributeVo> heads = vo.getHeads();
        // 供应商报价列
        List<SupplierQuoteFormVo> datas = vo.getSupplierQuoteFormList();
        // 表格数量
        int tableNum = (heads.size() / 5) + 1;
        for (int i = 0; i < tableNum; i++) {
            QuotationSheetTable quotationSheetTable = new QuotationSheetTable();
            quotationSheetTable.setRoundNum(maxRound + 1);
            // 插入表头
            List<String> headerList = new ArrayList<>();
            List<String> headerCodeList = new ArrayList<>();
            headerList.add("供应商");
            for (int j = i * 5; j < (Math.min((i + 1) * 5, heads.size())); j++) {
                headerList.add(heads.get(j).getKeyName());
                headerCodeList.add(heads.get(j).getKeyVal());
            }
            quotationSheetTable.setHeaderList(headerList);

            // 插入数据
            List<List<List<String>>> supplierDataList = new ArrayList<>();

            List<Long> userIds = new ArrayList<>();
            List<String> userNames = new ArrayList<>();
            datas.forEach(data -> userIds.add(data.getSupplierId()));
            R<List<SysUser>> supplierListR = remoteUserService.getInfoByIds(userIds, SecurityConstants.INNER);
            if (supplierListR.hasFail() || supplierListR.getData() == null || supplierListR.getData().isEmpty()) {
                userNames.add("供应商xxx");
            }
            // 供应商信息
            List<SysUser> supplierList = supplierListR.getData();
            // 每次循环都是一个新供应商
            for (int k = 0; k < datas.size(); k++) {
                List<List<String>> dataListList = new ArrayList<>();

                for (LinkedHashMap<String, String> bodyMap : datas.get(k).getRoundQuoteFormList().get(0).getBodyMaps()) {
                    List<String> dataList = new ArrayList<>();
                    dataList.add(supplierList.get(k).getNickName());
                    for (String s : headerCodeList) {
                        dataList.add(bodyMap.get(s));
                    }
                    dataListList.add(dataList);
                }
                supplierDataList.add(dataListList);
            }
            quotationSheetTable.setSupplierDataList(supplierDataList);
            quotationSheetTableList.add(quotationSheetTable);
        }
        // 创建Thymeleaf上下文并设置变量
        Context context = new Context();
        context.setVariable("quotationSheetTableList", quotationSheetTableList);
        // 处理模板并生成HTML字符串
        String tableHtml = templateEngine.process("quotationSheet", context);
        getReviewReport(subpackageDao, reviewReportDto.getReviewReportHtml() + tableHtml, reviewReportDto.getReviewReportHtml());
        return R.ok(true, "生成评审报告成功");
    }

    /**
     * 查询项目评审报告集合
     *
     * @param buyItemCode
     * @return
     */
    @Override
    public R<List<SubpackageDao>> getReviewReport(String buyItemCode) {
        LambdaQueryWrapper<SubpackageDao> qw = new LambdaQueryWrapper<>();
        qw.eq(SubpackageDao::getBuyItemCode, buyItemCode);
        return R.ok(subPackageApi.list(qw));
    }

    /**
     * 计算邀请了该供应商但未报名的次数
     *
     * @param supplierIds
     * @return
     */
    @Override
    public Map<Long, Long> calculateInviteNum(List<Long> supplierIds) {
        Map<Long, Long> map = new HashMap<>();
        for (Long supplierId : supplierIds) {
            Long num = 0L;
            QueryWrapper<SubpackageDao> qw = new QueryWrapper<>();
            qw.lambda().eq(SubpackageDao::getDeleted, 0).like(SubpackageDao::getInvitedSupplierJson, supplierId);
            List<SubpackageDao> list = subPackageApi.list(qw);
            for (SubpackageDao subpackageDao : list) {
                QueryWrapper<SupplierSignUpDao> qw1 = new QueryWrapper<>();
                qw1.lambda().eq(SupplierSignUpDao::getSubpackageCode, subpackageDao.getSubpackageCode())
                        .eq(SupplierSignUpDao::getSupplierId, supplierId);
                SupplierSignUpDao supplierSignUpDao = supplierSignUpMapper.selectOne(qw1);
                if (supplierSignUpDao == null) {
                    num++;
                }
            }
            map.put(supplierId, num);
        }
        return map;
    }

    /**
     * html 转pdf并保存到文件服务
     *
     * @param html             加了报价表的生成pdf的 html
     * @param reviewReportHtml 存数据库的 html
     * @return
     */
    private void getReviewReport(SubpackageDao subpackageDao, String html, String reviewReportHtml) {
        File file = HtmlUtil.toPdfByReport(html, "【评审报告】");
        // 删除旧的评审报告
        FUtil.delProjectFileList(subpackageDao.getSubpackageCode(), FileTypeNameConstants.BUY_REPORT);
        // 项目归档文件上传
        String fileCode = FUtil.upFile(subpackageDao.getBuyItemCode(), getYearMonthSplit(), file, FileTypeNameConstants.BUY_REPORT, subpackageDao.getSubpackageCode(), SecurityUtils.getUserId());
        subpackageDao.setReportFileKey(fileCode);
        subpackageDao.setReviewReportHtml(reviewReportHtml);
        QueryWrapper<SubpackageDao> qw = new QueryWrapper<>();
        qw.lambda().eq(SubpackageDao::getSubpackageCode, subpackageDao.getSubpackageCode());
        if (!subPackageApi.update(subpackageDao, qw)) {
            log.error("修改标段信息表失败,subpackageCode={}", subpackageDao.getSubpackageCode());
            throw new ServiceException("文件生成失败,请重新生成");
        }
    }


    private String getYearMonthSplit() {
        int year = DateUtil.year(DateTime.now());
        String month = String.format("%02d", DateUtil.month(DateTime.now()) + 1);
        return year + month;
    }


    /**
     * file 转换 fileItem
     */
    private FileItem createFileItem(File file) {
        if (Objects.isNull(file) || !file.exists()) {
            log.error("传入 file 不能为 null, 且文件必须存在");
            throw new ServiceException("传入 file 不能为 null, 且文件必须存在");
        }
        final DiskFileItemFactory diskFileItemFactory = new DiskFileItemFactory(50 * 1024 * 1024, new File(System.getProperty("java.io.tmpdir")));
        final FileItem item = diskFileItemFactory.createItem("file", MediaType.MULTIPART_FORM_DATA_VALUE, false, file.getName());
        final byte[] buff = new byte[1024 * 10];
        try (
                final FileInputStream fileIn = new FileInputStream(file);
                final OutputStream out = item.getOutputStream()
        ) {
            int n;
            while ((n = fileIn.read(buff)) != -1) {
                out.write(buff, 0, n);
            }
        } catch (IOException e) {
            log.error("file 转换 fileItem IO流异常，e:{}, file:{}", e, file);
            throw new ServiceException("file 转换 fileItem IO流异常");
        }
        return item;
    }

}
