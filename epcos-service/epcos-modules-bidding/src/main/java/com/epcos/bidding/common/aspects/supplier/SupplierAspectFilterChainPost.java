package com.epcos.bidding.common.aspects.supplier;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.epcos.bidding.common.annotaion.GetSupplier;
import com.epcos.bidding.common.aspects.AbstractMethod;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.common.core.constant.SecurityConstants;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.EvalSpelUtil;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.system.api.RemoteSupplierCompanyService;
import com.epcos.system.api.model.SupplierCompanyVO;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@Aspect
public class SupplierAspectFilterChainPost extends AbstractMethod<GetSupplier, Object> {

    public SupplierAspectFilterChainPost() {
        super(GetUtil.GET_SUPPLIER, "获取供应商企业信息");
    }

    @Autowired
    private RemoteSupplierCompanyService remoteSupplierCompanyService;

    @Override
    @Around("@annotation(getSupplier)")
    public Object around(ProceedingJoinPoint point, GetSupplier getSupplier) {
        return super.around(point, getSupplier);
    }

    private Set<Long> getUserIds(JoinPoint point, GetSupplier getSupplier) {
        String el = getSupplier.supplierIdEL();
        if (CharSequenceUtil.isEmpty(el)) {
            return Collections.singleton(SecurityUtils.getUserId());
        }
        if (getSupplier.batch()) {
            Collection<Long> collection = EvalSpelUtil.get(((MethodSignature) point.getSignature()).getMethod(), point.getArgs(), el, Collection.class);
            return Optional.ofNullable(collection).map(HashSet::new).orElseGet(HashSet::new);
        } else {
            return Collections.singleton(EvalSpelUtil.get(((MethodSignature) point.getSignature()).getMethod(), point.getArgs(), el, Long.class));
        }
    }

    @Override
    public Object businessMethods(JoinPoint point, GetSupplier getSupplier) {
        Set<Long> userIds = getUserIds(point, getSupplier);
        if (CollUtil.isEmpty(userIds)) {
            log.error("参数值异常，供应商id为空，@GetSupplier={}, userIds={}", getSupplier, userIds);
            throw new ServiceException("参数值异常，供应商id为空");
        }
        R<List<SupplierCompanyVO>> supplierCompanyVoR = remoteSupplierCompanyService.queryByUserIds(SecurityConstants.INNER, userIds);
        if (supplierCompanyVoR.hasFail()) {
            log.error("远程调用供应商信息异常，userIds={},supplierCompanyVoR={}", userIds, supplierCompanyVoR);
            throw new ServiceException(supplierCompanyVoR.getMsg());
        }
        if (CollUtil.isEmpty(supplierCompanyVoR.getData())) {
            log.error("远程查询供应商信息返回为空，userIds={},supplierCompanyVoR={}", userIds, supplierCompanyVoR);
            throw new ServiceException("查询供应商信息返回为空");
        }
        if (userIds.size() == 1) {
            String orgCode = EvalSpelUtil.get(((MethodSignature) point.getSignature()).getMethod(),
                    point.getArgs(), getSupplier.orgCodeEl(), String.class);
            SupplierCompanyVO res = supplierCompanyVoR.getData().get(0);
            res.setOrgCode(orgCode);

            return res;
        }
        return supplierCompanyVoR.getData();
    }


}
