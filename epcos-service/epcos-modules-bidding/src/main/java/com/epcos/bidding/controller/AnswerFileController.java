package com.epcos.bidding.controller;

import cn.hutool.core.io.file.PathUtil;
import com.epcos.bidding.common.annotaion.*;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.common.aspects.filter.validator.*;
import com.epcos.bidding.common.enums.FunctionEnum;
import com.epcos.bidding.common.utils.*;
import com.epcos.bidding.purchase.api.params.EpcFileContentVo;
import com.epcos.bidding.purchase.api.params.EvaluationMethodVo;
import com.epcos.bidding.purchase.api.params.PurchaseQuoteFormVo;
import com.epcos.bidding.purchase.api.params.ReviewItemVo;
import com.epcos.bidding.purchase.api.params.dto.BuyItemAndSubPackageDto;
import com.epcos.bidding.purchase.claims.business.api.IClaimsFileEvaluationMethodApi;
import com.epcos.bidding.purchase.claims.business.service.ClaimsService;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileDao;
import com.epcos.bidding.purchase.ev.factory.EvFactory;
import com.epcos.bidding.supplier.answer.business.api.IAnswerFileAttApi;
import com.epcos.bidding.supplier.answer.business.api.IAnswerFileEvaluationMethodApi;
import com.epcos.bidding.supplier.answer.business.api.IAnswerFileQuoteFormApi;
import com.epcos.bidding.supplier.answer.business.service.AnswerService;
import com.epcos.bidding.supplier.answer.domain.dto.*;
import com.epcos.bidding.supplier.api.params.*;
import com.epcos.bidding.supplier.api.params.dto.answer.AnswerFileQuoteFormDto;
import com.epcos.bidding.supplier.api.params.dto.answer.EvaluationMethodWrapper;
import com.epcos.bidding.supplier.sign.business.api.ISupplierBiddingApi;
import com.epcos.bidding.supplier.sign.business.api.ISupplierSignApi;
import com.epcos.bidding.supplier.sign.business.api.ISupplierSignUpApi;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierSignUpDao;
import com.epcos.common.core.constant.RoleConstants;
import com.epcos.common.core.constant.UserConstants;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.enums.FileTypeNameEnum;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.log.annotation.LogBySupplier;
import com.epcos.common.log.enums.BusinessType;
import com.epcos.common.redis.annotation.RedisLock;
import com.epcos.common.redis.chunk.ChunkCompleteContext;
import com.epcos.common.redis.chunk.ChunkFileHelper;
import com.epcos.common.redis.chunk.ChunkUploadResponse;
import com.epcos.common.redis.publish.BusinessTypeEnum;
import com.epcos.common.security.annotation.InnerAuth;
import com.epcos.common.security.annotation.RequiresPermissions;
import com.epcos.epcfile.api.RemoteProjectFileService;
import com.epcos.epcfile.api.domain.dto.FileUploadDto;
import com.epcos.epcfile.api.domain.vo.SysFileVo;
import com.epcos.system.api.model.FUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.File;
import java.nio.file.Path;
import java.util.*;
import java.util.stream.Collectors;

import static com.epcos.common.core.constant.PurchaseConstants.UserType.SUPPLIER;

@Slf4j
@Api(tags = "供应商响应文件")
@RestController
@RequiredArgsConstructor
@RequestMapping("/supplier")
public class AnswerFileController {

    private final ClaimsService claimsService;
    private final AnswerService answerService;
    private final IAnswerFileEvaluationMethodApi iAnswerFileEvaluationMethodApi;
    private final IAnswerFileQuoteFormApi iAnswerFileQuoteFormApi;
    private final ISupplierBiddingApi iSupplierBiddingApi;
    private final IClaimsFileEvaluationMethodApi iClaimsFileEvaluationMethodApi;
    private final ISupplierSignApi iSupplierSignApi;
    private final ISupplierSignUpApi iSupplierSignUpApi;
    private final ChunkFileHelper chunkFileHelper;
    private final IAnswerFileAttApi answerFileAttApi;
    private final ThreadPoolTaskExecutor taskExecutor;
    private final RemoteProjectFileService remoteProjectFileService;

    @LogBySupplier(title = "客户端投标文件切片上传", businessType = BusinessType.IMPORT)
    @NotNullUserId
    @RequiresPermissions("supplier:process:operate")
    @ApiOperation("客户端投标文件切片上传")
    @PostMapping("/answer/up")
    public R<ChunkUploadResponse> up(@RequestPart("file") MultipartFile file,
                                     @RequestParam(value = "filename") String filename,
                                     @RequestParam("buyItemCode") String buyItemCode,
                                     @RequestParam("subpackageCode") String subpackageCode,
                                     @RequestParam(value = "chunkNumber") Integer chunkNumber,
                                     @RequestParam(value = "totalChunks") Integer totalChunks) {
        String sessionId = chunkFileHelper.getSessionId(subpackageCode, SecurityUtils.getUserId(), null);
        try {
            chunkFileHelper.upload(file, sessionId, filename, chunkNumber, totalChunks);
            ChunkUploadResponse response = new ChunkUploadResponse();
            response.setSessionId(sessionId);
            response.setChunkNumber(chunkNumber);
            response.setTotalChunks(totalChunks);
            response.setCompleted(chunkFileHelper.hasAllUpload(sessionId, totalChunks));
            response.setCompletedChunks(chunkFileHelper.getUploadCount(sessionId));
            response.calculateProgress();
            return R.ok(response);
        } catch (Exception e) {
            log.error("客户端投标文件切片上传异常：chunkNumber={}, totalChunks={}, buyItemCode={}, subpackageCode={}, filename={}",
                    chunkNumber, totalChunks, buyItemCode, subpackageCode, filename, e);
            return R.fail(e.getMessage());
        }
    }

    @LogBySupplier(title = "投标人上传附件", businessType = BusinessType.INSERT)
    @ApiOperation("投标人上传附件")
    @NotNullUserId
    @GetTime(common = @GetCommon(buyItemCodeEL = "#buyItemCode", subpackageCodeEL = "#subpackageCode", afters = ResponseFileEndTimeHasPassedValidator.class))
    @GetSupplierBidding(common = @GetCommon(buyItemCodeEL = "#buyItemCode", subpackageCodeEL = "#subpackageCode", afters = SupplierBiddingHasPublishedValidator.class))
    @RequiresPermissions("supplier:process:operate")
    @PostMapping("/answer/up/att")
    public R<Boolean> upAtt(@RequestPart("file") MultipartFile file,
                            @RequestParam("resumableIdentifier") String resumableIdentifier,
                            @RequestParam("filename") String filename,
                            @RequestParam("buyItemCode") String buyItemCode,
                            @RequestParam("subpackageCode") String subpackageCode,
                            @RequestParam("chunkNumber") Integer chunkNumber,
                            @RequestParam("totalChunks") Integer totalChunks) {
        if (!BidFileUtil.isWhwsSupportedAttachment(filename)) {
            return R.fail("不支持的文件类型：" + filename);
        }
        Long userId = SecurityUtils.getUserId();
        String sessionId = chunkFileHelper.getSessionId(subpackageCode, userId, resumableIdentifier);
        FileUploadDto dto = new FileUploadDto();
        dto.setFile(file);
        dto.setFileDatabase(1);
        dto.setFileTypeName(FileTypeNameEnum.SUPPLIER_ANSWER_UP_ATT.getCode());
        dto.setBuyItemCode(buyItemCode);
        dto.setSubpackageCode(subpackageCode);
        dto.setSupplierId(userId);
        dto.setSessionId(sessionId);
        dto.setFileName(filename);
        dto.setChunkNumber(chunkNumber);
        dto.setTotalChunks(totalChunks);
        R<SysFileVo> sysFileVoR = remoteProjectFileService.bigFileUpload(dto);
        if (sysFileVoR.hasFail()) {
            return R.fail(sysFileVoR.getMsg());
        }
        SysFileVo data = sysFileVoR.getData();
        if (data != null && StringUtils.hasText(data.getUrl())) {
            answerFileAttApi.save(subpackageCode, userId,
                    StringUtils.hasText(data.getName()) ? data.getName() : filename,
                    data.getUrl());
        }
        return R.ok();
    }

    @NotNullUserId
    @RequiresPermissions("supplier:process:query")
    @ApiOperation("查询投标附件列表")
    @GetMapping("/answer/att")
    public R<List<AnswerAttVo>> getAtt(@RequestParam("subpackageCode") String subpackageCode) {
        return R.ok(answerFileAttApi.list(subpackageCode, SecurityUtils.getUserId()));
    }

    @NotNullUserId
    @LogBySupplier(title = "投标人删除附件", businessType = BusinessType.DELETE)
    @RedisLock(second = 60 * 10)
    @GetTime(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode", afters = ResponseFileEndTimeHasPassedValidator.class))
    @GetSupplierBidding(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode", afters = SupplierBiddingHasPublishedValidator.class))
    @RequiresPermissions("supplier:process:operate")
    @ApiOperation("投标人删除附件")
    @PostMapping("/answer/att/del")
    public R<Boolean> attDel(@RequestBody @Valid AnswerAttDelWrapper dto) {
        answerFileAttApi.del(dto);
        return R.ok(Boolean.TRUE);
    }

    @InnerAuth
    @GetMapping("/remote/answer/att")
    public R<List<AnswerAttVo>> getAtt(@RequestParam("subpackageCode") String subpackageCode,
                                       @RequestParam("supplierId") Long supplierId) {
        return R.ok(answerFileAttApi.list(subpackageCode, supplierId));
    }

    @LogBySupplier(title = "客户端投标文件合并解析", businessType = BusinessType.INSERT)
    @NotNullUserId
    @RequiresPermissions("supplier:process:operate")
    @RedisLock(second = 60 * 10)
    @GetTime(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode", afters = ResponseFileEndTimeHasPassedValidator.class))
    @GetSupplierBidding(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode", afters = SupplierBiddingHasPublishedValidator.class))
    @GetPurchaseQuoteForm(common = @GetCommon(async = true, buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode"))
    @HasFunction(common = @GetCommon(async = true, subpackageCodeEL = "#dto.subpackageCode"), functionEnum = FunctionEnum.SUPPLIER_CHECK_ZEPCKEY, belongRole = "2")
    @GetClaimsFile(common = @GetCommon(async = true, subpackageCodeEL = "#dto.subpackageCode"))
    @GetSimpleItem(common = @GetCommon(async = true, buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode"))
    @ApiOperation("客户端投标文件合并解析")
    @PostMapping("/answer/complete")
    public R<Boolean> complete(@RequestBody @Valid BuyItemAndSubPackageDto dto) {
        String sessionId = chunkFileHelper.getSessionId(dto.getSubpackageCode(), SecurityUtils.getUserId(), null);
        try {
            chunkFileHelper.complete(sessionId, context -> {
                handleBidFile(context, dto.getBuyItemCode(), dto.getSubpackageCode());
                return null;
            });
            return R.ok();
        } catch (Exception e) {
            log.error("投标文件合并解析异常：dto={}", dto, e);
            return R.fail(e.getMessage());
        }
    }

    @LogBySupplier(title = "投标文件PDF上传", businessType = BusinessType.IMPORT)
    @RedisLock(second = 60 * 10)
    @GetTime(common = @GetCommon(buyItemCodeEL = "#buyItemCode", subpackageCodeEL = "#subpackageCode", afters = ResponseFileEndTimeHasPassedValidator.class))
    @GetSupplierBidding(common = @GetCommon(buyItemCodeEL = "#buyItemCode", subpackageCodeEL = "#subpackageCode", afters = SupplierBiddingHasPublishedValidator.class))
    @NotNullUserId
    @RequiresPermissions("supplier:process:operate")
    @ApiOperation("投标文件PDF上传")
    @PostMapping("/answer/up/pdf")
    public R<Boolean> upPdf(@RequestPart("file") MultipartFile file,
                            @RequestParam("buyItemCode") String buyItemCode,
                            @RequestParam("subpackageCode") String subpackageCode) {
        if (!file.getOriginalFilename().toLowerCase().endsWith(".pdf")) {
            return R.fail("请上传PDF文件");
        }
        if (file.getSize() > Integer.MAX_VALUE) {
            return R.fail("文件过大，超过2GB");
        }
        answerService.upPdf(file, buyItemCode, subpackageCode, SecurityUtils.getUserId());
        return R.ok();
    }

    @LogBySupplier(title = "投标人设置评审对照表", businessType = BusinessType.UPDATE)
    @RedisLock
    @NotNullUserId
    @RequiresPermissions("supplier:process:operate")
    @GetTime(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode", afters = ResponseFileEndTimeHasPassedValidator.class))
    @GetSupplierBidding(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode", afters = SupplierBiddingHasPublishedValidator.class))
    @PostMapping("/answer/evaluationMethod")
    public R<Boolean> evaluationMethod(@RequestBody @Valid EvaluationMethodWrapper dto) {
        List<ReviewItem> reviewItemList = dto.getEvaluationMethodList().stream()
                .map(i -> {
                    ReviewItem ri = new ReviewItem();
                    ri.setUuid(i.getUuid());
                    ri.setScoreChapter(i.getScoreChapter());
                    return ri;
                }).collect(Collectors.toList());
        EvaluationMethod em = new EvaluationMethod();
        em.setConformityReview(reviewItemList);
        em.setScoreReview(Collections.emptyList());
        iAnswerFileEvaluationMethodApi.delAndCreate(dto.getSubpackageCode(), SecurityUtils.getUserId(), em);
        return R.ok();
    }

    @LogBySupplier(title = "供应商保存报价表", businessType = BusinessType.INSERT)
    @RedisLock(second = 2 * 60)
    @NotNullUserId
    @RequiresPermissions("supplier:process:operate")
    @GetTime(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode", afters = ResponseFileEndTimeHasPassedValidator.class))
    @GetSupplierBidding(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode", afters = SupplierBiddingHasPublishedValidator.class))
    @GetPurchaseQuoteForm(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode"))
    @PostMapping("/answer/quoteForm")
    public R<Boolean> quoteForm(@RequestBody @Valid AnswerFileQuoteFormDto dto) {
        AttributeUtil.validRequiredAndRegex(true, true,
                GetUtil.getPurchaserQuoteForm().getHeads(), dto.getBodyMaps(), null);
        iAnswerFileQuoteFormApi.delAndCreate(null, GetUtil.getPurchaserQuoteForm(),
                dto.getBuyItemCode(), dto.getSubpackageCode(), null,
                SecurityUtils.getUserId(), 0, dto.getBodyMaps());
        return R.ok();
    }

    /**
     * 解析投标文件
     */
    private void handleBidFile(ChunkCompleteContext context, String buyItemCode, String subpackageCode) {
        if (context.getTotalSize() > Integer.MAX_VALUE - 500 * 1024 * 1024) {
            throw new ServiceException("文件过大，超过1.5GB");
        }
        String originalFilename = context.getOriginalFilename().trim();
        BidFileUtil.isBidFile(originalFilename);
        Long userId = SecurityUtils.getUserId();
        Path tmpDirPath = BidFileUtil.createTmpDirPath(subpackageCode, userId.toString());
        try {
            File outputZipFile = tmpDirPath.resolve(originalFilename).toFile();
            List<String> filenames = new ArrayList<>();
            BidFileJson bidFileJson = BidFileUtil.rewriteDataJsonInZipDirectly(
                    context.getStreamedFile(), null, null, outputZipFile, filenames);
            if (bidFileJson == null) {
                log.error("投标文件中没有 data.json 文件。 tmpDirPath={}", tmpDirPath);
                throw new ServiceException("投标文件不满足要求");
            }
            String yearMonthSplit = GetUtil.getSimpleItemVo().getYearMonthSplit();
            if (!StringUtils.hasText(yearMonthSplit)) {
                log.error("查询项目创建时间年月为空，异常。buyItemCode={}, subpackageCode={}", buyItemCode, subpackageCode);
                throw new ServiceException("查询项目创建时间年月为空");
            }
            // 校验投标文件
            BidFileUtil.checkFileType(null, bidFileJson.getVersion(), filenames);
            checkVersion(GetUtil.getClaimsFile(), bidFileJson.getVersion());
            // 校验投标文件zepcKey
            if (Boolean.TRUE.equals(GetUtil.getHasFunction())) {
                GetUtil.getClaimsFile().verifyNotEqualZepcKey(bidFileJson.getZepcKey());
            }
            // 校验 合格制评审 打分制评审
            checkEvaluationMethod(subpackageCode, bidFileJson.getEvaluationMethod());
            // 抽取压缩包中的文件，排除采购文件与投标文件压缩包
            BidFileUtil.extractZipFiles(outputZipFile, tmpDirPath);
            // 检查PDF加密和文件类型
            BidFileUtil.checkPdfEncrypted(tmpDirPath);
            // 验证报价表单
            PurchaseQuoteFormVo quoteForm = GetUtil.getPurchaserQuoteForm();
            if (Objects.nonNull(quoteForm)) {
                if (quoteForm.getHeads().size() != bidFileJson.getQuoteSheetHeaders().size()) {
                    throw new ServiceException("报价表头不匹配");
                }
                AttributeUtil.validRequiredAndRegex(true, false, quoteForm.getHeads(), bidFileJson.getQuoteSheet(), tmpDirPath);
            } else if (!CollectionUtils.isEmpty(bidFileJson.getQuoteSheet())) {
                throw new ServiceException("采购人未设置报价表头");
            }
            // 生成合并pdf
            File mergePdf = BidFileUtil.mergeParallel(false, tmpDirPath, bidFileJson, taskExecutor).toFile();
            answerService.up(outputZipFile, mergePdf, userId, buyItemCode, subpackageCode, yearMonthSplit, quoteForm, bidFileJson);
        } catch (Exception e) {
            log.error("投标文件解析异常，context={}, buyItemCode={}, subpackageCode={}", context, buyItemCode, subpackageCode, e);
            throw new ServiceException(e.getMessage());
        } finally {
            Optional.of(tmpDirPath).ifPresent(PathUtil::del);
        }
    }

    /**
     * 校验 合格制评审 打分制评审
     */
    private void checkEvaluationMethod(String subpackageCode, EvaluationMethod evaluationMethod) {
        EvaluationMethodVo purchaseEvaluationMethod = iClaimsFileEvaluationMethodApi.findBySubpackageCodeVo(subpackageCode);
        boolean isNullEvaluationMethod = Objects.isNull(evaluationMethod) && Objects.nonNull(purchaseEvaluationMethod);
        boolean isNullPurchaseEvaluationMethod = Objects.nonNull(evaluationMethod) && Objects.isNull(purchaseEvaluationMethod);
        if (isNullEvaluationMethod || isNullPurchaseEvaluationMethod) {
            throw new ServiceException("响应文件评审数据异常");
        }
        List<ReviewItemVo> purchaseConformityReview = purchaseEvaluationMethod.getConformityReview();
        List<ReviewItemVo> purchaseScoreReview = purchaseEvaluationMethod.getScoreReview();
        if (purchaseConformityReview.size() != evaluationMethod.getConformityReview().size() ||
                purchaseScoreReview.size() != evaluationMethod.getScoreReview().size()) {
            throw new ServiceException("响应文件评审数据数量 != 采购文件评审数据数量");
        }
        boolean checkEqualReview = purchaseConformityReview.stream().allMatch(
                p -> evaluationMethod.getConformityReview()
                        .stream()
                        .anyMatch(s -> Objects.equals(p.getUuid(), s.getUuid()))) &&
                purchaseScoreReview.stream().allMatch(p -> evaluationMethod.getScoreReview()
                        .stream()
                        .anyMatch(s -> Objects.equals(p.getUuid(), s.getUuid())));
        if (!checkEqualReview) {
            throw new ServiceException("响应文件评审数据内容无法与采购文件相匹配");
        }
    }

    /**
     * 校验版本是否正确
     */
    private void checkVersion(ClaimsFileDao claimsFileDao, String version) {
        if (Objects.isNull(claimsFileDao)) {
            throw new ServiceException("采购文件未上传，请联系招标办");
        }
        String appVersion = claimsFileDao.getAppVersion();
        if (!Objects.equals(version, appVersion)) {
            throw new ServiceException(String.format("客户端版本不一致,采购人客户端版本:【%s】,当前版本:【%s】", appVersion, version));
        }
    }


    @NotNullUserId
    @RequiresPermissions("supplier:process:query")
    @ApiOperation("响应文件所有内容查询与状态")
    @GetMapping("/answer")
    public R<EpcFileContentVo> queryAll(@RequestParam("subpackageCode") String subpackageCode) {
        return queryAll(subpackageCode, SecurityUtils.getUserId());
    }

    @InnerAuth
    @ApiOperation("响应文件所有内容查询与状态")
    @GetMapping("/queryAllBySupplierId")
    public R<EpcFileContentVo> queryAllBySupplierId(@RequestParam("subpackageCode") @NotBlank String subpackageCode,
                                                    @RequestParam("supplierId") @NotNull Long supplierId) {
        EpcFileContentVo res = Optional.ofNullable(answerService.getEpcFileContentVo(subpackageCode, supplierId))
                .map(i -> {
                    Optional.ofNullable(iSupplierSignUpApi.query(subpackageCode, supplierId))
                            .ifPresent(f -> i.setPermit(f.getPermit()));
                    return i;
                }).orElseGet(() -> {
                    EpcFileContentVo vo = new EpcFileContentVo();
                    vo.setVideoUrl(iSupplierBiddingApi.findDemoVideoUrl(subpackageCode, supplierId));
                    vo.setPermit(iSupplierSignApi.getRemoteSystemConfigPermit());
                    return vo;
                });
        return R.ok(res);
    }

    @NotNullUserId
    @RequiresPermissions("supplier:process:query")
    @ApiOperation("查询采购文件内容，是否允许响应")
    @GetMapping("/claims")
    public R<EpcFileContentVo> queryClaimsFile(@RequestParam("subpackageCode") String subpackageCode) {
        EpcFileContentVo vo = new EpcFileContentVo();
        Optional.ofNullable(claimsService.queryAll(subpackageCode))
                .ifPresent(f -> BeanUtils.copyProperties(f, vo));
        Optional.ofNullable(iSupplierSignUpApi.query(subpackageCode, SecurityUtils.getUserId()))
                .ifPresent(f -> {
                    vo.setPermit(f.getPermit());
                    vo.setPaymentVoucher(f.getPaymentVoucher());
                });
        Set<String> subpackageCodes = new HashSet<>(Collections.singleton(subpackageCode));
        Optional.ofNullable(EvFactory.getInstance().queryDiffInfo(null, subpackageCodes).get(0))
                .ifPresent(f -> BeanUtils.copyProperties(f, vo));
        return R.ok(vo);
    }

    @NotNullUserId
    @LogBySupplier(title = "供应商下载采购文件", businessType = BusinessType.OTHER)
    @RequiresPermissions("supplier:process:operate")
    @Jump(subpackageCodeEL = "#subpackageCode", processRole = SUPPLIER)
    @GetSignUp(common = @GetCommon(subpackageCodeEL = "#subpackageCode",
            afters = {SignUpNotExistsValidator.class, SignUpReviewStatusPassedValidator.class}))
    @GetClaimsFile(common = @GetCommon(subpackageCodeEL = "#subpackageCode",
            afters = {ClaimsFileNotExistsValidator.class, ClaimsFileNotPublishedValidator.class}))
    @ApiOperation("下载采购文件")
    @GetMapping("/claims/load")
    public ResponseEntity<Resource> claimsLoad(@RequestParam("subpackageCode") String subpackageCode) {
        return FUtil.downloadProjectFile(GetUtil.getClaimsFile().getEpcFile());
    }

    @InnerAuth
    @ApiOperation("响应文件所有内容查询")
    @GetMapping("/answer/remote/queryAll")
    public R<EpcFileContentVo> queryAll(@RequestParam("subpackageCode") String subpackageCode,
                                        @RequestParam("supplierId") Long supplierId) {
        return R.ok(Optional.ofNullable(iSupplierSignUpApi.query(subpackageCode, supplierId))
                .map(SupplierSignUpDao::getPermit)
                .map(permit -> {
                    EpcFileContentVo vo = Optional.ofNullable(answerService.getEpcFileContentVo(subpackageCode, supplierId))
                            .orElseGet(EpcFileContentVo::new);
                    vo.setPermit(permit);
                    return vo;
                }).orElse(null));
    }

    @InnerAuth
    @ApiOperation("响应文件评审办法")
    @GetMapping("/answer/remote/evaluationMethod")
    public R<EvaluationMethodVo> evaluationMethod(@RequestParam("subpackageCode") String subpackageCode, @RequestParam("supplierId") Long supplierId) {
        return R.ok(iAnswerFileEvaluationMethodApi.findVo(subpackageCode, supplierId));
    }

    @InnerAuth
    @ApiOperation("响应文件评审办法")
    @GetMapping("/answer/remote/evaluationMethods")
    public R<List<EvaluationMethodVo>> evaluationMethods(@RequestBody @Valid Set<SubpackageCodeAndUserIdDto> dto) {
        return R.ok(iAnswerFileEvaluationMethodApi.findVos(dto));
    }

    @InnerAuth
    @ApiOperation("响应文件中报价内容")
    @GetMapping("/answer/remote/quoteForm")
    public R<AnswerFileQuoteFormVo> quoteForm(@RequestParam("subpackageCode") String subpackageCode, @RequestParam("supplierId") Long supplierId) {
        return R.ok(iAnswerFileQuoteFormApi.quoteForm(subpackageCode, supplierId, 0));
    }

    /**
     * 以包编码查询报价内容
     *
     * @param subpackageCode 包code，必填
     * @param supplierId     供应商id，可为空，为空时，查询所有此包下供应商的报价
     * @param round          轮数,为 null 查询所有轮，不为 null 则指定轮数查询
     */
    @InnerAuth
    @ApiOperation("以包编码查询报价内容")
    @GetMapping("/answer/remote/queryQuoteContent")
    public R<MultiSupplierQuoteFormVo> queryQuoteContent(@RequestParam("subpackageCode") String subpackageCode, @RequestParam(value = "supplierId", required = false) Long supplierId, @RequestParam(value = "round", required = false) Integer round) {
        return R.ok(iAnswerFileQuoteFormApi.findVo(subpackageCode, supplierId, round));
    }

    /**
     * 以多包编码 多供应商id 轮数 查询报价内容
     */
    @InnerAuth
    @ApiOperation("以包编码查询报价内容")
    @PostMapping("/answer/remote/queryQuoteContents")
    public R<List<MultiSupplierQuoteFormVo>> queryQuoteContents(@RequestBody @Valid Set<SubpackageCodeAndUserIdAndRoundDto> dto) {
        return R.ok(iAnswerFileQuoteFormApi.findVos(dto));
    }

    @NotNullUserId
    @RequiresPermissions("supplier:process:query")
    @ApiOperation("供应商查询报价内容，轮数不传查所有轮[0-]")
    @GetMapping("/answer/quoteForm")
    public R<MultiSupplierQuoteFormVo> quoteForm(@RequestParam("subpackageCode") String subpackageCode,
                                                 @RequestParam(value = "round", required = false) Integer round) {
        return R.ok(Optional.ofNullable(
                        iAnswerFileQuoteFormApi.findVo(subpackageCode, SecurityUtils.getUserId(), round))
                .orElse(new MultiSupplierQuoteFormVo()));
    }


    @NotNullUserId
    @RequiresPermissions("supplier:process:operate")
    @GetTime(common = @GetCommon(subpackageCodeEL = "#dto.subpackageCode", afters = ResponseFileEndTimeHasPassedValidator.class))
    @GetSupplierBidding(common = @GetCommon(subpackageCodeEL = "#dto.subpackageCode",
            afters = {SupplierBiddingIsNullValidator.class, SupplierBiddingNotPublishedValidator.class}))
    @LogBySupplier(title = "响应文件撤回", businessType = BusinessType.UPDATE)
    @RedisLock(second = 30)
    @ApiOperation("响应文件撤回")
    @PostMapping("/answer/revocation")
    public R<Boolean> revocation(@RequestBody @Valid PacketUnitDto dto) {
        answerService.revocation(dto.getSubpackageCode(), SecurityUtils.getUserId());
        return R.ok();
    }

    @LogBySupplier(title = "响应文件确认发布并盖章", businessType = BusinessType.UPDATE)
    @NotNullUserId
    @RequiresPermissions("supplier:process:operate")
    @GetItem(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode"))
    @GetTime(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode", afters = ResponseFileEndTimeHasPassedValidator.class))
    @GetSupplierBidding(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode", afters = SupplierBiddingIsNullValidator.class))
    @Msg(
            fromRole = RoleConstants.SUPPLIER,
            cacheMsg = true, cacheMsgHours = 24 * 90,
            businessType = BusinessTypeEnum.PROCUREMENT_RESPONSE_FILE,
            msg = "响应文件已递交",
            buyItemCodeEL = "#dto.buyItemCode",
            subpackageCodeEL = "#dto.subpackageCode"
    )
    @Jump(subpackageCodeEL = "#dto.subpackageCode", processRole = SUPPLIER)
    @HasFunction(functionEnum = FunctionEnum.SUPPLIER_RESPONSE_FILE_ENCRYPTION, belongRole = "2",
            common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode", afters = SupplierResponseFileEncryptionHasFunctionValidator.class))
    @RedisLock(second = 30)
    @ApiOperation("响应文件确认发布并盖章")
    @PostMapping("/answer/releaseAndStamp")
    public R<Boolean> releaseAndStamp(@RequestBody @Valid AnswerFileReleaseAndStampDto dto) {
        iSupplierBiddingApi.findOne(dto.getSubpackageCode(), SecurityUtils.getUserId())
                .verifyPublished();
        // 个人根据坐标盖章
        FUtil.startSeal(dto.getPdfFile(), null, String.valueOf(SecurityUtils.getUserId()), dto.getExtrasContents(), UserConstants.PERSON_COORDINATE_SEAL_PARAMETER, dto.getFlowId(), dto.getAuthCode());
//        FUtil.psnSealByCoordinate(dto.getPdfFile(), String.valueOf(SecurityUtils.getUserId()), dto.getExtrasContents());
        // 企业根据坐标盖章
//        FUtil.orgSealByCoordinate(dto.getPdfFile(), String.valueOf(SecurityUtils.getUserId()), dto.getExtrasContents());
        FUtil.startSeal(dto.getPdfFile(), null, String.valueOf(SecurityUtils.getUserId()), dto.getExtrasContents(), UserConstants.FIRM_COORDINATE_SEAL_PARAMETER);

        iSupplierBiddingApi.releaseAndStamp(dto, SecurityUtils.getUserId());
        return R.ok();
    }

    @LogBySupplier(title = "发布演示视频", businessType = BusinessType.UPDATE)
    @NotNullUserId
    @RequiresPermissions("supplier:process:operate")
    @RedisLock(second = 30)
    @GetTime(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode", afters = ResponseFileEndTimeHasPassedValidator.class))
    @GetSupplierBidding(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode", afters = SupplierBiddingIsNullValidator.class))
    @GetSupplierDemoVideo(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode", afters = SupplierDemoVideoExistsValidator.class))
    @ApiOperation("发布演示视频")
    @PostMapping("/answer/postDemoVideo")
    public R<Boolean> postDemoVideo(@RequestBody @Valid AnswerFilePostDemoVideoDto dto) {
        iSupplierBiddingApi.postDemoVideo(dto, SecurityUtils.getUserId());
        return R.ok();
    }

    @LogBySupplier(title = "撤回演示视频", businessType = BusinessType.UPDATE)
    @NotNullUserId
    @RequiresPermissions("supplier:process:operate")
    @RedisLock(second = 30)
    @GetTime(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode", afters = ResponseFileEndTimeHasPassedValidator.class))
    @GetSupplierDemoVideo(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode", afters = SupplierDemoVideoIsNullValidator.class))
    @ApiOperation("撤回演示视频")
    @PostMapping("/answer/recallDemoVideo")
    public R<Boolean> recallDemoVideo(@RequestBody @Valid AnswerFilePostDemoVideoDto dto) {
        iSupplierBiddingApi.recallDemoVideo(dto, SecurityUtils.getUserId());
        return R.ok();
    }

    @LogBySupplier(title = "开标签到", businessType = BusinessType.UPDATE)
    @NotNullUserId
    @RequiresPermissions("supplier:process:operate")
    @RedisLock(second = 30)
    @GetBidOpening(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode",
            afters = {BidOpeningIsNullValidator.class, BidOpeningNotWaitValidator.class}))
    @GetSignUp(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode",
            afters = {SignUpUnqualifiedValidator.class, SignUpSignInValidator.class}))
    @GetSupplierBidding(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode",
            afters = {SupplierBiddingIsNullValidator.class, SupplierBiddingNotPublishedValidator.class, SupplierBiddingDecryptedValidator.class}))
    @Msg(
            cacheMsg = true,
            cacheMsgHours = 24 * 90,
            fromRole = RoleConstants.SUPPLIER,
            businessType = BusinessTypeEnum.BID_OPENING,
            msg = "开标已签到",
            buyItemCodeEL = "#dto.buyItemCode",
            subpackageCodeEL = "#dto.subpackageCode"
    )
    @ApiOperation("开标签到")
    @Jump(subpackageCodeEL = "#dto.subpackageCode", processRole = SUPPLIER)
    @PostMapping("/answer/bidOpeningSign")
    public R<Boolean> bidOpeningSign(@RequestBody @Valid AnswerFileBidOpeningSignDto dto) {
        iSupplierSignUpApi.updateSignUpStatus(
                dto.getSubpackageCode(),
                new SupplierSignUpStatusDto(SecurityUtils.getUserId()).bidOpening()
        );
        return R.ok();
    }

    @LogBySupplier(title = "开标解密", businessType = BusinessType.UPDATE)
    @NotNullUserId
    @RequiresPermissions("supplier:process:operate")
    @RedisLock(second = 30)
    @GetSignUp(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode",
            afters = {SignUpUnqualifiedValidator.class, SignUpNotSignInValidator.class}))
    @GetBidOpening(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode",
            afters = {BidOpeningIsNullValidator.class, BidOpeningNotStartValidator.class}))
    @GetSupplierBidding(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode",
            afters = {SupplierBiddingIsNullValidator.class, SupplierBiddingNotPublishedValidator.class,
                    SupplierBiddingDecryptedValidator.class, SupplierBiddingSignedValidator.class}))
    @Msg(
            cacheMsg = true,
            cacheMsgHours = 24 * 90,
            fromRole = RoleConstants.SUPPLIER,
            businessType = BusinessTypeEnum.BID_OPENING,
            msg = "响应文件已解密",
            buyItemCodeEL = "#dto.buyItemCode",
            subpackageCodeEL = "#dto.subpackageCode"
    )
    @Jump(subpackageCodeEL = "#dto.subpackageCode", processRole = SUPPLIER)
    @ApiOperation("开标解密")
    @PostMapping("/answer/bidOpeningDecrypt")
    public R<Boolean> bidOpeningDecrypt(@RequestBody @Valid AnswerFileBidOpeningDecryptDto dto) {
        iSupplierBiddingApi.bidOpeningDecrypt(dto, SecurityUtils.getUserId());
        return R.ok();
    }

    @NotNullUserId
    @RequiresPermissions("supplier:process:operate")
    @RedisLock(second = 30)
    @Msg(
            fromRole = RoleConstants.SUPPLIER,
            businessType = BusinessTypeEnum.BID_OPENING,
            msg = "开标记录文件已签字",
            buyItemCodeEL = "#dto.buyItemCode",
            subpackageCodeEL = "#dto.subpackageCode"
    )
    @GetBidOpening(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode",
            afters = {BidOpeningIsNullValidator.class, BidOpeningNotSingingValidator.class}))
    @GetSupplierBidding(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode",
            afters = {SupplierBiddingIsNullValidator.class, SupplierBiddingNotPublishedValidator.class,
                    SupplierBiddingNotDecryptedValidator.class, SupplierBiddingSignedValidator.class}))
    @Jump(subpackageCodeEL = "#dto.subpackageCode", processRole = SUPPLIER)
    @LogBySupplier(title = "开标记录文件签字", businessType = BusinessType.UPDATE)
    @ApiOperation("开标记录文件签字")
    @PostMapping("/answer/bidOpeningSignature")
    public R<Boolean> bidOpeningSignature(@RequestBody @Valid AnswerFileBidOpeningSignatureDto dto) {
        iSupplierBiddingApi.bidOpeningSignature(dto, SecurityUtils.getUserId());
        return R.ok();
    }

}
