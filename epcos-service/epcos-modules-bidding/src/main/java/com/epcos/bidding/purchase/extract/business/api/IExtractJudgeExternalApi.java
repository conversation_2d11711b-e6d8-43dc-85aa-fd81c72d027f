package com.epcos.bidding.purchase.extract.business.api;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.IBaseService;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.extract.domain.dao.ExtractJudgeExternalDao;
import com.epcos.bidding.purchase.extract.domain.dto.ExtractNameDto;
import com.epcos.bidding.purchase.extract.domain.dto.ExtractQueryDto;
import com.epcos.bidding.purchase.extract.domain.dto.MarkJudgeLogDto;
import com.epcos.bidding.purchase.extract.domain.vo.ExtractLogVo;
import com.epcos.common.core.web.page.TableDataVo;
import com.epcos.system.api.domain.dto.AgainExtractDto;
import com.epcos.system.api.domain.dto.ExtractJudgeInfoDto;
import com.epcos.system.api.domain.dto.JudgeConditionsDto;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/25 14:25
 */
public interface IExtractJudgeExternalApi extends IBaseService<ExtractJudgeExternalDao> {

    @Override
    default LambdaQueryWrapper<ExtractJudgeExternalDao> queryWrapper(ExtractJudgeExternalDao dao) {
        LambdaQueryWrapper<ExtractJudgeExternalDao> queryWrapper = Wrappers.lambdaQuery(ExtractJudgeExternalDao.class);
        if (Objects.nonNull(dao.getId())) {
            return queryWrapper.eq(ExtractJudgeExternalDao::getId, dao.getId());
        }
        return queryWrapper.eq(StringUtils.hasText(dao.getExtractLongName()), ExtractJudgeExternalDao::getExtractLongName, dao.getExtractLongName())
                .eq(Objects.nonNull(dao.getJudgeId()), ExtractJudgeExternalDao::getJudgeId, dao.getJudgeId());
    }

    default LambdaQueryWrapper<ExtractJudgeExternalDao> queryWrapper(String extractLongName) {
        return queryWrapper(new ExtractJudgeExternalDao(extractLongName));
    }

    default LambdaQueryWrapper<ExtractJudgeExternalDao> queryWrapper(String extractLongName, Collection<Long> judgeIds) {
        return queryWrapper(extractLongName)
                .in(CollUtil.isNotEmpty(judgeIds),
                        ExtractJudgeExternalDao::getJudgeId,
                        judgeIds);
    }

    default List<ExtractJudgeExternalDao> findByExtractLongName(String name) {
        return list(queryWrapper(name));
    }

    default List<ExtractJudgeExternalDao> findByExtractLongNameAndJudgeIdIn(String extractLongName, Collection<Long> judgeIds) {
        return list(queryWrapper(extractLongName, judgeIds));
    }

    List<String> queryProjectName(String buyItemName);

    List<ExtractLogVo> extract(JudgeConditionsDto dto);

    List<ExtractLogVo> againExtract(AgainExtractDto dto);

    List<ExtractLogVo> queryJudgeInfo(ExtractJudgeInfoDto dto);

    Boolean saveJudge(ExtractNameDto dto);

    TableDataVo extractJudgeLog(PageSortEntity<ExtractQueryDto> dto);

    Boolean markJudge(MarkJudgeLogDto dto);

    Boolean delJudgeLog(MarkJudgeLogDto dto);
}
