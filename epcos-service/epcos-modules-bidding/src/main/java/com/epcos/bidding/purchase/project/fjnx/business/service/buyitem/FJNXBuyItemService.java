package com.epcos.bidding.purchase.project.fjnx.business.service.buyitem;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.project.fjnx.business.api.buyitem.FJNXBuyItemApi;
import com.epcos.bidding.purchase.project.fjnx.domain.dao.BuyItemFJNXDao;
import com.epcos.bidding.purchase.project.fjnx.domain.dto.FJNXBuyItemQueryDto;
import com.epcos.bidding.purchase.project.fjnx.domain.dto.FJNXCreateBuyItemDto;
import com.epcos.bidding.purchase.project.fjnx.domain.vo.FJNXBuyItemPageVo;
import com.epcos.bidding.purchase.project.fjnx.repository.FJNXBuyItemMapper;
import com.epcos.common.core.domain.AttachmentDto;
import com.epcos.common.core.utils.DateUtils;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.utils.bean.BeanUtils;
import com.epcos.dingtalk.domain.dto.AttachFileDto;
import com.epcos.system.api.model.FUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.epcos.common.core.constant.FileTypeNameConstants.CREATE_BUY_ITEM;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 福建省农信版本
 * @date 2024/4/23 14:26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FJNXBuyItemService extends ServiceImpl<FJNXBuyItemMapper, BuyItemFJNXDao> implements FJNXBuyItemApi {

    private final FJNXBuyItemMapper fjnxBuyItemMapper;

    @Override
    public void insertFJNXBuyItem(FJNXCreateBuyItemDto dto, String buyItemCode) {
        BuyItemFJNXDao fjnxDao = new BuyItemFJNXDao();
        BeanUtils.copyProperties(dto, fjnxDao);
        fjnxDao.setBuyItemCode(buyItemCode);
        fjnxDao.setAnnexFile(dto.toJsonAnnexFile());
        fjnxDao.setAnnexFile(attUp(dto.getParamsAttList(), buyItemCode));
        fjnxDao.setApplyTime(DateUtils.parseDate(dto.getApplyTime()));
        fjnxBuyItemMapper.insert(fjnxDao);
    }


    @Override
    public IPage<FJNXBuyItemPageVo> selectJoinBuyItemPage(PageSortEntity<FJNXBuyItemQueryDto> dto) {
        FJNXBuyItemQueryDto entity = dto.getEntity();
        return fjnxBuyItemMapper.selectJoinBuyItemPage(
                Page.of(dto.getPageNum(), dto.getPageSize()),
                entity, entity.getOrgCode()
        );
    }

    @Override
    public BuyItemFJNXDao findOneByBuyItemCode(String buyItemCode) {
        return getOne(queryWrapper(new BuyItemFJNXDao(buyItemCode)));
    }

    @Override
    public List<BuyItemFJNXDao> findOneByBuyItemCodeList(List<String> buyItemCodeList) {
        return list(Wrappers.lambdaQuery(BuyItemFJNXDao.class)
                .in(BuyItemFJNXDao::getBuyItemCode, buyItemCodeList));
    }

    @Override
    public void updateFJNXBuyItemInfo(FJNXCreateBuyItemDto dto) {
        BuyItemFJNXDao fjnxDao = new BuyItemFJNXDao();
        BeanUtils.copyProperties(dto, fjnxDao);
        fjnxDao.setAnnexFile(attUp(dto.getParamsAttList(), dto.getBuyItemCode()));
        fjnxBuyItemMapper.update(fjnxDao, updateWrapper(dto.getBuyItemCode()));
    }

    private String attUp(List<AttachFileDto> paramAttList, String buyItemCode) {
        if (CollectionUtils.isNotEmpty(paramAttList)) {
            List<AttachmentDto> paramAttVoList = paramAttList.stream()
                    .map(a -> {
                        AttachmentDto attachmentDto = new AttachmentDto(a.getName(), a.getUrl());
                        if (Objects.nonNull(a.getFile())) {
                            String url = FUtil.upFile(buyItemCode, DateUtils.getDateToM(), a.getFile(),
                                    CREATE_BUY_ITEM, null, SecurityUtils.getUserId());
                            attachmentDto.setUrl(url);
                        }
                        return attachmentDto;
                    }).collect(Collectors.toList());
            return JSONArray.toJSONString(paramAttVoList);
        }
        return null;
    }

    @Override
    public Boolean delFJNXBuyItemInfo(String buyItemCode) {
        return remove(updateWrapper(buyItemCode));
    }
}
