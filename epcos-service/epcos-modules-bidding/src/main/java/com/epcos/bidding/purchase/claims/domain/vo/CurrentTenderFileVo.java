package com.epcos.bidding.purchase.claims.domain.vo;

import com.epcos.bidding.purchase.api.params.vo.claims.TenderFileRequirementInfoVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/23 9:43
 */
@Data
public class CurrentTenderFileVo extends TenderFileRequirementInfoVo {

    private static final long serialVersionUID = 1870981383622347525L;

    @ApiModelProperty(value = "供应商当前上传的响应文件key")
    private String currentBidFileKey;

    @ApiModelProperty("驳回状态[0-待上传、1-已上传、2-被驳回]")
    private Integer status;
}
