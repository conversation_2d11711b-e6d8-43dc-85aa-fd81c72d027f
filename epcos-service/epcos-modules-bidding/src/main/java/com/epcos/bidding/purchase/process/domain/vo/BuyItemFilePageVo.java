package com.epcos.bidding.purchase.process.domain.vo;

import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperBuyItemVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 项目归档也页面分页vo
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/28 14:34
 */
@Data
public class BuyItemFilePageVo extends SuperBuyItemVo {

    private static final long serialVersionUID = -26786866158557897L;

    @ApiModelProperty(value = "采购方式code[存储 purchase_method表中的 purchase_method_code字段]")
    private String purchaseMethodCode;

    @ApiModelProperty(value = "采购编号(院方使用)")
    private String innerCode;

    @ApiModelProperty(value = "使用科室")
    private String useDept;

    @ApiModelProperty(value = "标的类型")
    private String buyClass;
}
