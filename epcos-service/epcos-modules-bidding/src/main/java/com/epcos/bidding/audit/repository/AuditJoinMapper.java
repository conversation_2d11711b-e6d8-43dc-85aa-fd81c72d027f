package com.epcos.bidding.audit.repository;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.epcos.bidding.audit.api.vo.AuditInfoVo;
import com.epcos.bidding.audit.api.vo.AuditPersonVo;
import com.epcos.bidding.audit.domain.dao.AuditInfoDao;
import com.epcos.bidding.audit.domain.dto.AuditPageDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AuditJoinMapper extends BaseMapper<AuditInfoDao> {

    IPage<AuditInfoDao> selectJoinPerson(Page page,
                                         @Param("dto") AuditPageDto dto,
                                         @Param("userId") Long userId);

    IPage<AuditInfoDao> selectJoinRecipient(Page page,
                                            @Param("dto") AuditPageDto dto,
                                            @Param("copyId") Long copyId);

    // 查询审批及抄送人
    AuditInfoVo selectJoinRecipientById(@Param("id") Long auditInfoId);

    AuditInfoVo selectJoinRecipientByAuditCode(@Param("auditCode") String auditCode);

    List<AuditInfoVo> selectJoinRecipientByAuditCodeIn(@Param("auditCodeList") List<String> auditCodeList);

    List<AuditPersonVo> selectJoinRecordAndCommentById(@Param("id") Long auditInfoId);

    List<Long> selectJoinRecordById(@Param("id") Long auditInfoId);

    List<Long> selectAllUserId(@Param("id") Long auditInfoId);

    AuditPersonVo selectCurrentAuditName(@Param("id") Long auditInfoId);

    String selectAtts(@Param("idList") List<Long> idList);

    int selectAuditCount(@Param("userId") Long userId);

    int countProjectAsApprover(@Param("userId") Long userId);

    IPage<AuditInfoDao> selectWaitAuditJoinPerson(Page page, @Param("userId") Long userId);

    IPage<AuditInfoDao> selectCompletedAuditJoinPerson(Page page, @Param("userId") Long userId);

}
