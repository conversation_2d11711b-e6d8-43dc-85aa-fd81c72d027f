package com.epcos.bidding.purchase.claims.domain.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@ApiModel(description = "引入其它包的采购文件")
@NoArgsConstructor
@AllArgsConstructor
public class ClaimsFileImportDto implements Serializable {

    private static final long serialVersionUID = 630862100068216071L;


    @ApiModelProperty("导入的包code")
    @NotBlank(message = "导入的包code,必填")
    private String importSubpackageCode;

    @ApiModelProperty("包code")
    @NotBlank(message = "包code,必填")
    private String subpackageCode;

}
