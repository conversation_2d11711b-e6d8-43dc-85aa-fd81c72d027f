package com.epcos.bidding.purchase.extract.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/18 14:51
 */
@Data
public class ExtractQueryDto implements Serializable {

    private static final long serialVersionUID = -4836744036095845166L;


    /**
     * 此字段改为两种方式，一种为手动输入，即用户自己随意输入
     * 另一种为模糊匹配，匹配规则和之前一样保持不变
     */
    @ApiModelProperty(value = "抽取记录名称")
    @Length(max = 256, message = "抽取记录名称超出长度限制")
    private String extractLongName;
}
