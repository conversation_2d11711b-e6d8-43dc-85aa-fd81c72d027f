package com.epcos.bidding.purchase.report.business.api;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.IBaseService;
import com.epcos.bidding.purchase.api.domian.reprot.ReportInfoVo;
import com.epcos.bidding.purchase.report.domain.dao.ResearchReportDao;
import com.epcos.bidding.purchase.report.domain.dto.InitReportDto;
import com.epcos.common.core.utils.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/29 13:59
 */
public interface IResearchReportApi extends IBaseService<ResearchReportDao> {


    /**
     * 根据包code进行删除
     *
     * @param subpackageCode 包code
     */
    default void remove(String subpackageCode) {
        this.remove(Wrappers.lambdaUpdate(ResearchReportDao.class)
                .eq(StringUtils.hasText(subpackageCode), ResearchReportDao::getSubpackageCode, subpackageCode));
    }

    /**
     * 查询带有文件的那一条数据
     *
     * @param subpackageCode
     * @return
     */
    default ResearchReportDao selectReport(String subpackageCode) {
        return getOne(Wrappers.lambdaQuery(ResearchReportDao.class)
                .eq(StringUtils.hasText(subpackageCode), ResearchReportDao::getSubpackageCode, subpackageCode)
                .isNotNull(ResearchReportDao::getResearchReportText));
    }

    /**
     * 查询不带文件的其他数据
     *
     * @param subpackageCode
     * @return
     */
    default List<ResearchReportDao> selectReportList(String subpackageCode) {
        return list(Wrappers.lambdaQuery(ResearchReportDao.class)
                .eq(StringUtils.hasText(subpackageCode), ResearchReportDao::getSubpackageCode, subpackageCode)
                .isNull(ResearchReportDao::getResearchReportText));
    }

    /**
     * 发起调研报告
     * 记录发起人------存一条数据
     * 记录抽取的评委，每个评委一条数据
     * 总数据为评委数+1，即评委数+发起人数
     *
     * @param dto
     */
    void initReport(InitReportDto dto);

    /**
     * 根据项目查询报告信息
     *
     * @param buyItemCode 项目code
     * @return
     */
    List<ReportInfoVo> reportInfoByItem(String buyItemCode);

    /**
     * 根据标段查询报告信息
     *
     * @param subpackageCode 标段code
     * @return
     */
    ReportInfoVo reportInfoBySub(String subpackageCode);


    /**
     * 编辑报告
     * 先删除。再进行新增
     *
     * @param dto
     */
    void editReport(InitReportDto dto);

    /**
     * 修改签字状态
     *
     * @param subpackageCode 包code
     * @param userId         用户id
     * @param signStatus     签字状态（0-未签字, 1-已签字）
     */
    Boolean updateSignStatus(String subpackageCode, Long userId, String signStatus);

}
