package com.epcos.bidding.common;

import com.alibaba.fastjson.JSON;
import com.epcos.bidding.audit.api.AuditAttribute;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

public class JsonToListAuditAttributeHandler extends BaseTypeHandler<List<AuditAttribute>> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<AuditAttribute> parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, JSON.toJSONString(parameter));
    }

    @Override
    public List<AuditAttribute> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return Optional.ofNullable(rs.getString(columnName))
                .map(i -> JSON.parseArray(i, AuditAttribute.class))
                .orElse(Collections.emptyList());
    }

    @Override
    public List<AuditAttribute> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return JSON.parseArray(rs.getString(columnIndex), AuditAttribute.class);
    }

    @Override
    public List<AuditAttribute> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return JSON.parseArray(cs.getString(columnIndex), AuditAttribute.class);
    }
}
