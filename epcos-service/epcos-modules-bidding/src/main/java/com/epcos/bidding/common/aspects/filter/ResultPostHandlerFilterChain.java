package com.epcos.bidding.common.aspects.filter;

import cn.hutool.extra.spring.SpringUtil;
import com.epcos.bidding.common.aspects.AspectContext;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

public interface ResultPostHandlerFilterChain<R> {

    // 缓存构造器
    Map<Class<? extends ResultPostHandlerFilterChain>, Constructor<? extends ResultPostHandlerFilterChain>> CONSTRUCTOR_CACHE = new ConcurrentHashMap<>();

    static ResultPostHandlerFilterChain createHandler(Class<? extends ResultPostHandlerFilterChain> clazz) {
        ResultPostHandlerFilterChain bean = null;
        try {
            bean = SpringUtil.getBean(clazz);
        } catch (NoSuchBeanDefinitionException e) {

        }
        if (Objects.nonNull(bean)) {
            return bean;
        }
        Constructor<? extends ResultPostHandlerFilterChain> constructor = CONSTRUCTOR_CACHE.computeIfAbsent(
                clazz, k -> {
                    try {
                        return k.getDeclaredConstructor();
                    } catch (NoSuchMethodException e) {
                        throw new RuntimeException(e);
                    }
                });
        try {
            return constructor.newInstance();
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException e) {
            throw new RuntimeException(e);
        }
    }

    void postHandler(AspectContext context, R result);

}
