package com.epcos.bidding.purchase.entrust.domain.dao;

import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.bidding.purchase.entrust.domain.EntrustBulletinEnum;
import com.epcos.common.core.annotation.Gzip;
import com.epcos.common.core.exception.ServiceException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import java.io.Serializable;
import java.util.Date;


@Setter
@Getter
@ToString
@TableName("entrust_bulletin")
public class EntrustBulletinDao implements Serializable {
    private static final long serialVersionUID = -6571983448334797898L;

    @ApiModelProperty("委托id")
    private Long id;

    @ApiModelProperty(value = "创建人id", hidden = true)
    private Long createUserId;

    @ApiModelProperty(value = "创建人名称", hidden = true)
    @Length(max = 100, message = "名称最长100")
    private String createUserName;

    @ApiModelProperty("项目名称，最大长度500")
    @Length(max = 500, message = "项目名称最长500")
    private String projectName;

    @ApiModelProperty("公告名称，最大长度500")
    @Length(max = 500, message = "公告名称最长500")
    private String bulletinName;

    @ApiModelProperty("公告类型")
    @Length(max = 100, message = "公告类型最长100")
    private String bulletinType;

    @Gzip
    @ApiModelProperty("委托公告文本")
    @Length(max = 65000, message = "公告文本最长65000")
    private String bulletinText;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("审核状态，0-待审核，1-审核成功，2-审核失败")
    @Range(min = 0, max = 2, message = "审核状态0-2")
    private Integer auditStatus;

    @ApiModelProperty("展示状态，1-展示，2-隐藏")
    @Range(min = 1, max = 2, message = "审核状态1-2")
    private Integer showStatus;

    @ApiModelProperty("审核失败时说明，最大长度500")
    @Length(max = 500, message = "审核失败时说明最长500")
    private String failureDescription;

    @ApiModelProperty(value = "审核人id", hidden = true)
    private Long auditUserId;

    @ApiModelProperty(value = "审核人名称", hidden = true)
    @Length(max = 100, message = "名称最长100")
    private String auditUserName;

    @ApiModelProperty("审核时间")
    private Date auditTime;

    // 初始审核状态与公告展示状态
    public EntrustBulletinDao init() {
        setAuditStatus(EntrustBulletinEnum.AUDIT_CREATED.getCode());
        setShowStatus(EntrustBulletinEnum.BULLETIN_HIDE.getCode());
        return this;
    }

    public EntrustBulletinDao validIdNotNull() {
        if (id == null) {
            throw new ServiceException("委托公告id不能为空");
        }
        return this;
    }


    public EntrustBulletinDao validAuditStatusIsPassOrFail() {
        if (auditStatus != EntrustBulletinEnum.AUDIT_PASS.getCode() && auditStatus != EntrustBulletinEnum.AUDIT_FAIL.getCode()) {
            throw new ServiceException("审核状态不正确");
        }
        return this;
    }

    public EntrustBulletinDao validShowStatusNotNull() {
        if (showStatus == null) {
            throw new ServiceException("公告展示状态不能为空");
        }
        return this;
    }
}
