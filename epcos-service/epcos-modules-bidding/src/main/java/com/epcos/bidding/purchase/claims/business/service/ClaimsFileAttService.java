package com.epcos.bidding.purchase.claims.business.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.audit.api.BizAuditRelationDto;
import com.epcos.bidding.audit.api.vo.AuditInfoVo;
import com.epcos.bidding.audit.business.api.IAuditInfoApi;
import com.epcos.bidding.audit.domain.dao.AuditInfoDao;
import com.epcos.bidding.audit.event.AuditResultEvent;
import com.epcos.bidding.biz.business.api.IBizAuditRelationApi;
import com.epcos.bidding.common.annotaion.Jump;
import com.epcos.bidding.common.annotaion.Msg;
import com.epcos.bidding.common.utils.BidFileJson;
import com.epcos.bidding.common.utils.BidFileUtil;
import com.epcos.bidding.purchase.api.domian.cliams.ClaimsFileReleaseAndStampDto;
import com.epcos.bidding.purchase.api.params.EpcFileContentVo;
import com.epcos.bidding.purchase.claims.business.api.IClaimsFileApi;
import com.epcos.bidding.purchase.claims.business.api.IClaimsFileAttApi;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileAttDao;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileDao;
import com.epcos.bidding.purchase.claims.repository.ClaimsFileAttMapper;
import com.epcos.bidding.purchase.claims.repository.ClaimsFileMapper;
import com.epcos.common.core.constant.RoleConstants;
import com.epcos.common.core.constant.UserConstants;
import com.epcos.common.core.domain.AuditStatusDto;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.redis.publish.BusinessTypeEnum;
import com.epcos.system.api.model.FUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

import static com.epcos.common.core.constant.PurchaseConstants.UserType.PURCHASER;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ClaimsFileAttService extends ServiceImpl<ClaimsFileAttMapper, ClaimsFileAttDao> implements IClaimsFileAttApi {

}
