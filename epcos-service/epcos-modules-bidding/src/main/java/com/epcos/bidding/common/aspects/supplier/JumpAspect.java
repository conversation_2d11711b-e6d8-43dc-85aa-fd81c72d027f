package com.epcos.bidding.common.aspects.supplier;

import com.epcos.bidding.common.annotaion.Jump;
import com.epcos.bidding.common.aspects.convert.GetParamConvert;
import com.epcos.bidding.common.aspects.params.GetSimpleItemParam;
import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.supplier.sign.business.api.ISupplierProcessNodeApi;
import com.epcos.bidding.supplier.sign.domain.vo.SupplierProcessNodeVo;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.EvalSpelUtil;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.utils.ServletUtils;
import com.epcos.dingtalk.domain.dto.AuditBulletinDto;
import com.epcos.system.api.domain.assmble.vo.FunctionKV;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.epcos.bidding.common.enums.BulletinTypeEnum.BIDDING_ANNOUNCEMENT;
import static com.epcos.common.core.constant.PurchaseConstants.BulletinAudit.PASS;
import static com.epcos.common.core.constant.PurchaseConstants.UserType.PURCHASER;

@Slf4j
@Component
@Aspect
@RequiredArgsConstructor
public class JumpAspect {

    private final ISupplierProcessNodeApi supplierProcessNodeApi;
    private final IBuyItemApi buyItemApi;

    @AfterReturning(value = "@annotation(jump)")
    public void afterReturning(JoinPoint point, Jump jump) {
        HttpServletRequest request = ServletUtils.getRequest();
        String servletPath;
        if (request == null) {
            servletPath = jump.triggerPoint();
        } else {
            servletPath = request.getServletPath();
        }
        if (!StringUtils.hasText(servletPath)) {
            return;
        }
        if ("/audit/audit".equals(servletPath)) {
            if (point.getArgs()[0] instanceof AuditBulletinDto) {
                AuditBulletinDto args = (AuditBulletinDto) point.getArgs()[0];
                if (!BIDDING_ANNOUNCEMENT.getKey().equals(args.getBulletinType())) {
                    return;
                }
                if (!PASS.equals(args.getAuditStatus())) {
                    return;
                }
            }
        }
        Method method = ((MethodSignature) point.getSignature()).getMethod();
        String supplierIdEl = jump.supplierIdEl();
        String processRole = jump.processRole();
        Class<? extends GetParamConvert> supplierIdsConvert = jump.toSupplierIdsConvert();
        Set<Long> userIds;
        if (StringUtils.hasText(supplierIdEl)) {
            userIds = Collections.singleton(EvalSpelUtil.get(method, point.getArgs(), supplierIdEl, Long.class));
        } else if (GetParamConvert.isImpl(supplierIdsConvert)) {
            userIds = (Set<Long>) GetParamConvert.getBeanOrNewInstance(supplierIdsConvert).doConvert(point.getArgs()[0]);
        } else {
            userIds = Collections.singleton(SecurityUtils.getUserId());
        }
        String subpackageCode = EvalSpelUtil.get(method, point.getArgs(), jump.subpackageCodeEL(), String.class);
        if (!StringUtils.hasText(subpackageCode) || CollectionUtils.isEmpty(userIds)) {
            log.error("包code与用户id,参数异常, servletPath={}, subpackageCode={}, userIds={}", servletPath, subpackageCode, userIds);
            throw new ServiceException("包code与用户ids,参数异常");
        }
        List<String> subpackageCodeList = new ArrayList<>();
        if (PURCHASER.equals(processRole)) {
            subpackageCodeList = buyItemApi
                    .getSimpleItemVo(new GetSimpleItemParam(null, subpackageCode))
                    .getSuperPackageVoList()
                    .stream()
                    .map(SuperPackageVo::getSubpackageCode)
                    .collect(Collectors.toList());
            userIds = Collections.emptySet();
        } else {
            subpackageCodeList.add(subpackageCode);
        }
        List<SupplierProcessNodeVo> supplierProcessNodeVoList = supplierProcessNodeApi.query(subpackageCodeList, userIds, Integer.parseInt(processRole));
        List<SupplierProcessNodeVo> waitingUpDataNodes = new ArrayList<>();
        for (SupplierProcessNodeVo nodeVo : supplierProcessNodeVoList) {
            // 当前节点
            String curNode = nodeVo.getPurchaseFunctionKey();
            List<FunctionKV> fvs = nodeVo.getFvs();
            Optional<FunctionKV> curFv = fvs.stream()
                    .filter(f -> Objects.equals(f.getTriggerPoint(), servletPath) && Objects.equals(curNode, f.getPurchaseFunctionKey()))
                    .findAny();
            if (curFv.isPresent()) {
                Integer curRank = curFv.get().getRanking();
                // 求最大的节点
                FunctionKV maxFv = fvs.stream()
                        .filter(f -> Objects.nonNull(f.getRanking()))
                        .max(Comparator.comparingInt(FunctionKV::getRanking))
                        .get();
                // 当前节点 -> 下一个节点
                // 如果下一节点中没有设置触发点，直接显示，一直循环到有设置触发点，并设置有触发点的 purchaseFunctionKey，如果一直循环到最后都没有设置触发点，设置最后的 purchaseFunctionKey
                // 如果下一节点有设置触发点，直接设置显示，并设置 purchaseFunctionKey，后面的节点不做处理
                while (curRank++ < maxFv.getRanking()) {
                    final Integer finalCurRank = curRank;
                    FunctionKV nextFv = fvs.stream()
                            .filter(f -> Objects.equals(f.getRanking(), finalCurRank)).findAny()
                            .get();
                    if (StringUtils.hasText(nextFv.getTriggerPoint())) {
                        nextFv.setIsShow("1");
                        nodeVo.setPurchaseFunctionKey(nextFv.getPurchaseFunctionKey());
                        break;
                    }
                    nextFv.setIsShow("1");
                    nodeVo.setPurchaseFunctionKey(nextFv.getPurchaseFunctionKey());
                }
                waitingUpDataNodes.add(nodeVo);
            }
        }
        if (!CollectionUtils.isEmpty(waitingUpDataNodes)) {
            supplierProcessNodeApi.updateProcessNodes(waitingUpDataNodes);
        }
    }

    private String nextFunctionKey(List<FunctionKV> fvs, int rank) {
        TreeMap<Integer, FunctionKV> treeMap = fvs.stream()
                .filter(f -> f.getRanking() != null)
                .collect(Collectors.toMap(
                        FunctionKV::getRanking, Function.identity(),
                        (a, b) -> a, TreeMap::new
                ));
        return recursionNext(treeMap, rank + 1);
    }

    private String recursionNext(TreeMap<Integer, FunctionKV> fvsMap, int currentRanking) {
        FunctionKV f = fvsMap.get(currentRanking);
        if (f == null) {
            return fvsMap.get(currentRanking - 1).getPurchaseFunctionKey();
        }
        if (f.getTriggerPoint() != null) {
            return f.getPurchaseFunctionKey();
        }
        return recursionNext(fvsMap, currentRanking + 1);
    }

}
