package com.epcos.bidding.purchase.extract.domain.dao;

import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.bidding.common.BaseDao;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/11 16:32
 */
@ApiModel(description = "项目内--评委抽取表")
@Data
@TableName("purchase_extract_judge_inner")
public class ExtractJudgeInnerDao extends BaseDao implements Serializable {

    private static final long serialVersionUID = 3527246744892220746L;

    @ApiModelProperty(value = "采购项目编号")
    private String buyItemCode;

    @ApiModelProperty(value = "标段code")
    private String subpackageCode;

    @ApiModelProperty(value = "评委id")
    private Long judgeId;

    @ApiModelProperty(value = "评委名称")
    private String judgeName;

    @ApiModelProperty(value = "评审密码")
    private String password;

    @ApiModelProperty(value = "是否是科室代表 [0-不是 1-是]")
    private String deptRepresent;

    @ApiModelProperty(value = "是否是组长 [0-不是 1-是]")
    private String whetherGroup;

    @ApiModelProperty(value = "是否签到 [0-未签到 1-签到成功]")
    private String whetherSign;

    @ApiModelProperty(value = "签到时间")
    private Date signTime;

    @ApiModelProperty(value = "评委状态[0-未评标 1-评标 2-评标完成]")
    private String status;

    public static Map<String, Boolean> sort() {
        return Collections.singletonMap("createAt", true);
    }

}
