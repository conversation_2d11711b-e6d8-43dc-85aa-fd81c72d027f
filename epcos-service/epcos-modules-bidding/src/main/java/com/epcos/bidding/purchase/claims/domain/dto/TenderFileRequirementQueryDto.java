package com.epcos.bidding.purchase.claims.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class TenderFileRequirementQueryDto implements Serializable {
    private static final long serialVersionUID = -2761941721986175856L;

    public TenderFileRequirementQueryDto(String subpackageCode) {
        this.subpackageCode = subpackageCode;
    }

    @ApiModelProperty("招标项目编码")
    @Length(max = 64, message = "招标项目编码最长646字符")
    private String buyItemCode;

    @ApiModelProperty("供应商id")
    private Long supplierId;

    @ApiModelProperty("标段编码")
    @Length(max = 64, message = "标段编码最长64字符")
    private String subpackageCode;

    @ApiModelProperty("属性")
    @Length(max = 500, message = "属性最长500字符")
    private String attribute;

    @ApiModelProperty("描述")
    @Length(max = 1000, message = "描述最长1000字符")
    private String description;

}
