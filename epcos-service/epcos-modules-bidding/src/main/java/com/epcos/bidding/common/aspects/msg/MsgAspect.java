package com.epcos.bidding.common.aspects.msg;

import com.epcos.bidding.common.annotaion.Msg;
import com.epcos.bidding.common.aspects.convert.GetParamConvert;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.supplier.api.params.SupplierSignUpVo;
import com.epcos.bidding.supplier.sign.business.api.ISupplierSignApi;
import com.epcos.bidding.supplier.sign.domain.dto.SupplierSignQueryDto;
import com.epcos.common.core.constant.RoleConstants;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.utils.EvalSpelUtil;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.web.domain.AjaxResult;
import com.epcos.common.core.web.domain.user.LoginUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Method;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class MsgAspect {

    private final ApplicationContext ac;
    private final IBuyItemApi iBuyItemApi;
    private final ISupplierSignApi iSupplierSignApi;
    private final ISubPackageApi iSubpackageApi;

    @AfterReturning(value = "@annotation(com.epcos.bidding.common.annotaion.Msg)", returning = "res")
    public void send(JoinPoint point, Object res) {
        if (Objects.nonNull(res)) {
            if ((res instanceof R && ((R<?>) res).hasFail())) {
                log.error("返回结果 R 失败，不发送消息，结果: {}", res);
                return;
            }
            if (res instanceof AjaxResult && !((AjaxResult) res).isOk()) {
                log.error("返回结果 AjaxResult 失败，不发送消息，结果: {}", res);
            }
        }
        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    sendMsg(point);
                }
            });
        } else {
            sendMsg(point);
        }
    }

    private void sendMsg(JoinPoint point) {
        Msg msgAn = ((MethodSignature) point.getSignature()).getMethod().getAnnotation(Msg.class);
        com.epcos.common.redis.publish.Msg msg = msg(point, msgAn, SecurityUtils.getLoginUser());
        ac.publishEvent(msg);
    }

    private com.epcos.common.redis.publish.Msg msg(JoinPoint point,
                                                   Msg annotation,
                                                   LoginUser user) {
        Method method = ((MethodSignature) point.getSignature()).getMethod();
        String buyItemCodeEl = annotation.buyItemCodeEL();
        String subpackageCodeEl = annotation.subpackageCodeEL();
        String buyItemCode = EvalSpelUtil.get(method, point.getArgs(), buyItemCodeEl, String.class);
        String subpackageCode = EvalSpelUtil.get(method, point.getArgs(), subpackageCodeEl, String.class);
        // 采购项目名字 | 标段名称 | ''
        String name = Optional.ofNullable(buyItemCode)
                .map(iBuyItemApi::findBuyItemInfo)
                .map(BuyItemDao::getBuyItemName)
                .orElse(Optional.ofNullable(subpackageCode)
                        .map(iSubpackageApi::findBySubpackageCode)
                        .map(SubpackageDao::getSubpackageName)
                        .orElse("")
                );
        Set<Long> toUserIds = getConvert(annotation.toUserIdsConvert(), point.getArgs()[0]);
        if (CollectionUtils.isEmpty(toUserIds)) {
            toUserIds = getToUserIds(buyItemCode, subpackageCode, annotation.fromRole());
        }
        return com.epcos.common.redis.publish.Msg.builder()
                .businessTypeEnum(annotation.businessType())
                .cacheMsg(annotation.cacheMsg())
                .cacheMsgHours(annotation.cacheMsgHours())
                .msg(name + "：" + annotation.msg())
                .displayEnums(Arrays.stream(annotation.displayEnums()).collect(Collectors.toSet()))
                .fromUserId(Optional.ofNullable(user).map(LoginUser::getUserId).orElse(1L))
                .fromUserName(Optional.ofNullable(user).map(LoginUser::getNickName).orElse("采购人"))
                .fromUserRole(annotation.fromRole())
                .toUserIds(toUserIds)
                .date(new Date())
                .build();
    }

    private Set<Long> getToUserIds(String buyItemCode, String subpackageCode, String fromRole) {
        if (RoleConstants.SUPPLIER.equals(fromRole)) {
            return Optional.ofNullable(userIds(buyItemCode, iBuyItemApi::findBuyItemInfo))
                    .orElseGet(() -> userIds(subpackageCode, iBuyItemApi::findBySubpackageCode));
        }
        if (RoleConstants.PURCHASER.equals(fromRole)) {
            return iSupplierSignApi.query(new SupplierSignQueryDto(buyItemCode, subpackageCode), null)
                    .stream()
                    .map(SupplierSignUpVo::getSupplierId)
                    .collect(Collectors.toSet());
        }
        return Collections.emptySet();
    }

    private Set<Long> getConvert(Class<? extends GetParamConvert> userIdsConvert, Object arg) {
        if (GetParamConvert.isImpl(userIdsConvert)) {
            return (Set<Long>) GetParamConvert.getBeanOrNewInstance(userIdsConvert).doConvert(arg);
        } else {
            return Collections.emptySet();
        }
    }

    private Set<Long> userIds(String code, Function<String, BuyItemDao> function) {
        return Optional.ofNullable(code)
                .map(function)
                .map(i -> Collections.singleton(i.getUserId()))
                .orElse(Collections.emptySet());
    }


}
