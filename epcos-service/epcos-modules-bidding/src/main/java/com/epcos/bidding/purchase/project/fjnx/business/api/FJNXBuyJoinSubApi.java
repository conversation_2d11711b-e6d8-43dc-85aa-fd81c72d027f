package com.epcos.bidding.purchase.project.fjnx.business.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import com.epcos.bidding.purchase.project.fjnx.domain.dto.FJNXBuyItemQueryDto;
import com.epcos.bidding.purchase.project.fjnx.domain.dto.FJNXCreateBuyItemDto;
import com.epcos.bidding.purchase.project.fjnx.domain.vo.FJNXBuyItemInfoVo;
import com.epcos.bidding.purchase.project.fjnx.domain.vo.FJNXBuyItemPageVo;

import java.util.List;

/**
 * 项目 和 标段 共有的业务 api
 *
 * <AUTHOR>
 * @version 1.0
 * @description 福建省农信版本
 * @date 2024/4/23 13:57
 */
public interface FJNXBuyJoinSubApi {

    void createFJNXBuyItem(FJNXCreateBuyItemDto dto);

    IPage<FJNXBuyItemPageVo> fjnxBuyItemPage(PageSortEntity<FJNXBuyItemQueryDto> dto);

    FJNXBuyItemInfoVo queryFJNXBuyItemInfo(String buyItemCode);

    void updateFJNXBuyItemInfo(FJNXCreateBuyItemDto dto);

    List<SuperPackageVo> findByBuyItemCode(String buyItemCode);

    Boolean delFJNXBuyItemInfo(String buyItemCode);

    /**
     * 查询流程编号
     *
     * @param buyItemCode
     * @return
     */
    String getInnerCode(String buyItemCode);
}
