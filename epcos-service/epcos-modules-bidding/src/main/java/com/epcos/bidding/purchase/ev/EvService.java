package com.epcos.bidding.purchase.ev;

import com.epcos.bidding.workbench.vo.SpecialFieldVo;
import com.epcos.system.api.domain.assmble.vo.FunctionKV;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 提供顶层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/21 15:34
 */
public interface EvService {

    /**
     * 获取当前服务对应的环境标识
     *
     * @return 环境标识字符串
     */
    String ev();

    /**
     * 判断是否需要钉钉审批
     *
     * @return 需要钉钉审批返回true
     */
    boolean isDing();

    /**
     * 判断是否需要审核
     *
     * @param functionKVList 功能权限列表
     * @return 需要审核返回true
     */
    boolean isAudit(List<FunctionKV> functionKVList, String bulletinType);

    /**
     * 获取当前环境对应的表名
     *
     * @return 表名
     */
    String getTableName();

    /**
     * 查询差异化采购项信息
     *
     * @param buyItemCode 采购项编码
     * @return 采购项信息映射
     */
    Map<String, String> queryBuyItemMap(String buyItemCode);

    /**
     * 查询差异化子包信息
     *
     * @param buyItemCodes    采购项编码列表
     * @param subpackageCodes 子包编码列表
     * @return 特殊字段信息列表
     */
    List<SpecialFieldVo> queryDiffInfo(Set<String> buyItemCodes, Set<String> subpackageCodes);

    /**
     * 删除所有采购项相关信息
     *
     * @param buyItemCode 采购项编码
     * @return 删除结果
     */
    Boolean delBuyItem(String buyItemCode, List<String> subpackageCodeList);

    /**
     * 查询所有采购项编码
     *
     * @return
     */
    List<String> queryAllBuyItemCode();

}
