package com.epcos.bidding.common.annotaion.excel.aspect;

import com.epcos.bidding.common.annotaion.FieldMap;
import com.epcos.bidding.common.annotaion.FieldMapping;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.Map;

@Aspect
@Component
public class FieldMapProcessor {

    @Autowired
    private CacheManager cacheManager;


    @Around("@annotation(fieldMap)")
    public Object handleFieldMap(ProceedingJoinPoint joinPoint, FieldMap fieldMap) throws Throwable {

        Object result = joinPoint.proceed();

        Class<?> targetClass = fieldMap.value();
        String className = targetClass.getName();
        // 检查缓存中是否已经存在该类的字段映射
        Cache cache = cacheManager.getCache("excelFieldMappings");
        Map<String, String> fieldMappings;

        if (cache != null && cache.get(className) != null) {
            // 如果缓存中已经有了映射，直接从缓存中获取并返回
            fieldMappings = cache.get(className, LinkedHashMap.class);
        } else {
            // 如果缓存中没有数据，则继续处理
            fieldMappings = new LinkedHashMap<>();
            // 处理自定义字段，默认放在最前面
            processCustom(fieldMap, fieldMappings);
            // 处理字段映射
            processFields(targetClass, fieldMappings);
            if (cache != null) {
                cache.put(className, fieldMappings);
            }
        }
        // 将字段映射的结果填充到 result 中
        fillResult(((ArrayList) result).get(0), fieldMappings);
        return result;
    }

    private void processCustom(FieldMap fieldMap, Map<String, String> fieldMappings) {
        if (fieldMap.required()) {
            String[] keys = fieldMap.keys();
            String[] values = fieldMap.values();
            for (int i = 0; i < keys.length; i++) {
                fieldMappings.put(keys[i], values[i]);
            }
        }
    }

    private void processFields(Class<?> clazz, Map<String, String> fieldMappings) {
        // 处理当前类和父类的字段映射
        while (clazz != null) {
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                if (field.isAnnotationPresent(FieldMapping.class)) {
                    FieldMapping fieldMapping = field.getAnnotation(FieldMapping.class);
                    if (!fieldMapping.isIgnore()) {
                        String key = fieldMapping.name();
                        String value = fieldMapping.value().isEmpty() ? field.getName() : fieldMapping.value();
                        // 如果是子类映射，并且没有被忽略，则继续映射
                        if (fieldMapping.isSub() != void.class) {
                            try {
                                processFields(fieldMapping.isSub(), fieldMappings);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                        // 如果字段标记为 isSub，则不放入 fieldMappings
                        if (!fieldMapping.isSub().equals(void.class)) {
                            continue;
                        }
                        // 如果字段标记为 isFill，则不放入 fieldMappings
                        if (!fieldMapping.isFill()) {
                            continue;
                        }
                        // 将字段映射放入 Map
                        fieldMappings.put(key, value);
                    }
                }
            }
            // 获取父类，继续处理父类的字段
            clazz = clazz.getSuperclass();
        }
    }

    private void fillResult(Object result, Map<String, String> fieldMappings) {
        if (result != null) {
            Class<?> clazz = result.getClass(); // 获取当前类
            while (clazz != null && clazz != Object.class) {
                // 遍历当前类的字段
                for (Field field : clazz.getDeclaredFields()) {
                    if (field.isAnnotationPresent(FieldMapping.class)) {
                        FieldMapping fieldMapping = field.getAnnotation(FieldMapping.class);
                        // 判断是否需要填充
                        if (fieldMapping.isFill()) {
                            field.setAccessible(true);
                            try {
                                // 将 fieldMappings 填充到该字段
                                field.set(result, fieldMappings);
                            } catch (IllegalAccessException e) {
                                e.printStackTrace();
                            }
                        }
                    }
                }
                // 获取父类，继续处理父类的字段
                clazz = clazz.getSuperclass();
            }
        }
    }

    @PostConstruct
    public void initCache() {
        // 获取缓存实例
        Cache cache = cacheManager.getCache("excelFieldMappings");
        if (cache != null) {
            // 清除缓存
            cache.clear();
        }
    }
}
