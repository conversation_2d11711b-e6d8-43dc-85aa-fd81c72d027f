package com.epcos.bidding.purchase.claims.business.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.common.utils.AttributeUtil;
import com.epcos.bidding.common.utils.BidFileJson;
import com.epcos.bidding.common.utils.BidFileUtil;
import com.epcos.bidding.common.utils.MenuData;
import com.epcos.bidding.purchase.api.params.MenuDataVo;
import com.epcos.bidding.purchase.claims.business.api.IClaimsFileMenuApi;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileMenuDao;
import com.epcos.bidding.purchase.claims.repository.ClaimsFileMenuMapper;
import com.epcos.common.core.constant.FileTypeNameConstants;
import com.epcos.system.api.model.FUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.nio.file.Path;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ClaimsFileMenuService extends ServiceImpl<ClaimsFileMenuMapper, ClaimsFileMenuDao> implements IClaimsFileMenuApi {

    @Override
    public List<MenuDataVo> findBySubpackageCodeVo(String subpackageCode) {
        return BidFileUtil.convertClaimsFileMenu(findBySubpackageCode(subpackageCode));
    }

    // 查询目录中文件keys
    private Set<String> findAttachKeysBySubpackageCode(List<ClaimsFileMenuDao> daos) {
        return daos.stream().filter(f -> StringUtils.hasText(f.getAttachKey())).map(i ->
                AttributeUtil.asMap(i.getAttachKey())
        ).flatMap(m -> m.keySet().stream()).collect(Collectors.toSet());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delAttachFileAndCreate(Path dirPath, BidFileJson bidFileJson,
                                       String buyItemCode, String subpackageCode, String yearMonthSplit) {
        delete(subpackageCode);
        for (MenuData m : bidFileJson.getMenuData()) {
            ClaimsFileMenuDao by = ClaimsFileMenuDao.by(subpackageCode, m.getOrder(), m.getChapterName());
            save(by);
            Long pid = by.getId();
            List<ClaimsFileMenuDao> childrens = claimsFileMenuDaos(
                    dirPath, pid, subpackageCode, m, buyItemCode, yearMonthSplit);
            saveBatch(childrens);
        }
    }

    // 上传采购文件
    private List<ClaimsFileMenuDao> claimsFileMenuDaos(Path dirPath, Long pid, String subpackageCode,
                                                       MenuData menuData, String buyItemCode, String yearMonthSplit) {
        return menuData.getSectionList().stream()
                .map(s -> {
                    ClaimsFileMenuDao dao = ClaimsFileMenuDao.by(pid, subpackageCode, s);
                    if (CharSequenceUtil.isNotBlank(s.getAttachKey())) {
                        File file = dirPath.resolve(s.getAttachKey()).toFile();
                        if (file.exists()) {
                            LinkedHashMap<String, String> fileAndNames = BidFileUtil.upFileAndReturn(
                                    file,
                                    buyItemCode,
                                    yearMonthSplit,
                                    FileTypeNameConstants.BIDDING_DOC_ENCLOSURE_APPENDIX,
                                    subpackageCode);
                            dao.setAttachKey(AttributeUtil.asJson(fileAndNames));
                        }
                    }
                    return dao;
                }).collect(Collectors.toList());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String subpackageCode) {
        List<ClaimsFileMenuDao> daos = findBySubpackageCode(subpackageCode);
        if (CollUtil.isNotEmpty(daos)) {
            // 2025-4-7 删除采购文件中旧的，保留最后的
            findAttachKeysBySubpackageCode(daos)
                    .forEach(FUtil::delNonArchivedFileByFileKey);
            removeByIds(daos.stream()
                    .map(ClaimsFileMenuDao::getId)
                    .collect(Collectors.toList())
            );
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importOther(String importSubpackageCode, String subpackageCode) {
        List<ClaimsFileMenuDao> menus = findBySubpackageCode(importSubpackageCode);
        if (CollectionUtils.isEmpty(menus)) {
            return;
        }
        delete(subpackageCode);
        menus.stream()
                .filter(f -> Objects.isNull(f.getPid()))
                .forEach(i -> {
                    ClaimsFileMenuDao by = ClaimsFileMenuDao.by(subpackageCode, i.getOrderNum(), i.getChapterName());
                    save(by);
                    Long pid = by.getId();
                    List<ClaimsFileMenuDao> daos = menus.stream()
                            .filter(fi ->
                                    Objects.nonNull(fi.getPid())
                                            && Objects.equals(fi.getPid(), i.getId()))
                            .map(c -> {
                                ClaimsFileMenuDao d = new ClaimsFileMenuDao();
                                d.setPid(pid);
                                d.setOrderNum(c.getOrderNum());
                                d.setChapterName(c.getChapterName());
                                d.setChapterContext(c.getChapterContext());
                                d.setChapterType(c.getChapterType());
                                d.setAttachKey(c.getAttachKey());
                                d.setSubpackageCode(c.getSubpackageCode());
                                return d;
                            }).collect(Collectors.toList());
                    saveBatch(daos);
                });
    }


}

