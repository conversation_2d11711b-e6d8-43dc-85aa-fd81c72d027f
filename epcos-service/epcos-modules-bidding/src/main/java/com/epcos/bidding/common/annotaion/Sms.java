package com.epcos.bidding.common.annotaion;

import com.epcos.bidding.common.aspects.convert.GetParamConvert;
import com.epcos.bidding.common.aspects.sms.SmsContent;
import com.epcos.common.core.enums.ClientEnum;
import com.epcos.common.core.enums.SmsEnum;
import org.springframework.core.annotation.Order;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 发送短信
 */
@Order(800)
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Sms {

    /**
     * 生效的环境
     */
    ClientEnum[] clients();

    /**
     * 短信内容
     */
    SmsEnum smsEnum();

    /**
     * 参数转换
     */
    Class<? extends GetParamConvert<?, SmsContent>> convert();

}
