package com.epcos.bidding.purchase.comment.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.epcos.bidding.purchase.api.params.dto.BaseQueryDto;
import com.epcos.bidding.purchase.comment.domain.dao.SupplierCommentDao;
import com.epcos.bidding.workbench.vo.BidOpenTodayVo;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/19 17:26
 */
public interface ISupplierCommentMapper extends BaseMapper<SupplierCommentDao> {


    IPage<BidOpenTodayVo> supplierCommentPageWait(Page<Object> of, @Param("nowDate") String nowDate,
                                                  @Param("month") Integer month,
                                                  @Param("baseQueryDto") BaseQueryDto baseQueryDto);

    IPage<BidOpenTodayVo> supplierCommentPage(Page<Object> of, @Param("baseQueryDto") BaseQueryDto baseQueryDto);
}
