package com.epcos.bidding.common.annotaion;

import org.springframework.core.annotation.Order;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 查询供应商报名信息
 * <AUTHOR>
 */
@Order(290)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface GetSignUp {

    GetCommon common() default @GetCommon;

    /**
     * 使用el获取
     */
    String supplierIdEL() default "";
}
