package com.epcos.bidding.supplier.answer.business.api;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.IBaseService;
import com.epcos.bidding.purchase.api.params.AttributeVo;
import com.epcos.bidding.purchase.api.params.PurchaseQuoteFormVo;
import com.epcos.bidding.supplier.answer.domain.dao.AnswerFileQuoteFormDao;
import com.epcos.bidding.supplier.api.params.AnswerFileQuoteFormVo;
import com.epcos.bidding.supplier.api.params.MultiSupplierQuoteFormVo;
import com.epcos.bidding.supplier.api.params.SubpackageCodeAndUserIdAndRoundDto;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.nio.file.Path;
import java.util.*;

/**
 * <AUTHOR>
 */
public interface IAnswerFileQuoteFormApi extends IBaseService<AnswerFileQuoteFormDao> {

    @Override
    default LambdaQueryWrapper<AnswerFileQuoteFormDao> queryWrapper(AnswerFileQuoteFormDao dao) {
        LambdaQueryWrapper<AnswerFileQuoteFormDao> query = Wrappers.lambdaQuery(AnswerFileQuoteFormDao.class);
        if (Objects.nonNull(dao.getId())) {
            return query.eq(AnswerFileQuoteFormDao::getId, dao.getId());
        }
        return query
                .eq(StringUtils.hasText(dao.getSubpackageCode()), AnswerFileQuoteFormDao::getSubpackageCode, dao.getSubpackageCode())
                .eq(Objects.nonNull(dao.getSupplierId()), AnswerFileQuoteFormDao::getSupplierId, dao.getSupplierId())
                .eq(Objects.nonNull(dao.getRound()), AnswerFileQuoteFormDao::getRound, dao.getRound())
                .orderByDesc(AnswerFileQuoteFormDao::getId);
    }

    default Wrapper<AnswerFileQuoteFormDao> queryWrapperIn(List<String> subCodeList) {
        return Wrappers.lambdaQuery(AnswerFileQuoteFormDao.class)
                .in(AnswerFileQuoteFormDao::getSubpackageCode, subCodeList);
    }

    default LambdaQueryWrapper<AnswerFileQuoteFormDao> queryWrapper(String subpackageCode,
                                                                    Long supplierId, Integer round) {
        return queryWrapper(new AnswerFileQuoteFormDao(subpackageCode, supplierId, round));
    }

    default LambdaQueryWrapper<AnswerFileQuoteFormDao> queryWrapperIn(String subpackageCode,
                                                                      Collection<Long> supplierIdList,
                                                                      Collection<Integer> roundList) {
        return queryWrapper(new AnswerFileQuoteFormDao(subpackageCode, null, null))
                .in(CollUtil.isNotEmpty(supplierIdList), AnswerFileQuoteFormDao::getSupplierId, supplierIdList)
                .in(CollUtil.isNotEmpty(roundList), AnswerFileQuoteFormDao::getRound, roundList);
    }

    default AnswerFileQuoteFormDao findOne(String subpackageCode,
                                           Long supplierId,
                                           Integer round) {
        return getOne(queryWrapper(subpackageCode, supplierId, round));
    }

    default List<AnswerFileQuoteFormDao> find(String subpackageCode,
                                              Collection<Long> supplierIdList,
                                              Collection<Integer> roundList) {


        return list(queryWrapperIn(subpackageCode, supplierIdList, roundList));
    }


    void delete(PurchaseQuoteFormVo quoteFormVo, String subpackageCode, Collection<Long> supplierIds,
                Collection<Integer> roundList, List<LinkedHashMap<String, String>> quoteSheet);

    void create(Path tmpDirPath, PurchaseQuoteFormVo quoteFormVo, String buyItemCode,
                String subpackageCode, String yearMonthSplit, Long supplierId,
                Integer round, List<LinkedHashMap<String, String>> bodyList);

    /**
     * 删除并新增
     */
    void delAndCreate(Path tmpDirPath, PurchaseQuoteFormVo quoteFormVo,
                      String buyItemCode, String subpackageCode, String yearMonthSplit,
                      Long supplierId, Integer round, List<LinkedHashMap<String, String>> quoteSheet);

    /**
     * 以包编码查询报价内容
     *
     * @param subpackageCode 包code，必填
     * @param supplierId     供应商id，可为空，为空时，查询所有此包下供应商的报价
     * @param round          轮数,为 null 查询所有轮，不为 null 则指定轮数查询
     */
    MultiSupplierQuoteFormVo findVo(String subpackageCode, Long supplierId, Integer round);

    /**
     * 以多包编码 多供应商id 轮数 查询报价内容
     */
    List<MultiSupplierQuoteFormVo> findVos(Set<SubpackageCodeAndUserIdAndRoundDto> dto);

    /**
     * 以单包编码 多供应商id 轮数 查询报价内容
     */
    MultiSupplierQuoteFormVo findVo(String subpackageCode, Set<Long> supplierIds, Integer round);

    /**
     * 查询指定轮的报价内容
     *
     * @param subpackageCode 包code，必填
     * @param supplierId     供应商id，必填
     * @param round          轮数,必填
     */
    List<LinkedHashMap<String, String>> queryByRounds(String subpackageCode, Long supplierId, Integer round);

    /**
     * 查询指定轮的报价内容
     *
     * @param subpackageCode 包code，必填
     * @param supplierId     供应商id，必填
     * @param round          轮数,必填
     */
    AnswerFileQuoteFormVo quoteForm(String subpackageCode, Long supplierId, Integer round);

    // 查询字典中配置的报价表信息
    List<String> getQuoteDictValue();

    BigDecimal calcTotal(List<AttributeVo> heads, List<LinkedHashMap<String, String>> bodyMaps);

}
