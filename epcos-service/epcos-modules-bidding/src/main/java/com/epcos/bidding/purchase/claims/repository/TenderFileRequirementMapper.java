package com.epcos.bidding.purchase.claims.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.epcos.bidding.purchase.claims.domain.dao.TenderFileRequirementDao;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/10 16:39
 */
public interface TenderFileRequirementMapper extends BaseMapper<TenderFileRequirementDao> {

    int insert(TenderFileRequirementDao requirement);

    int updateById(TenderFileRequirementDao requirement);

    TenderFileRequirementDao selectById(Long id);

    List<TenderFileRequirementDao> selectAll();

    List<TenderFileRequirementDao> select(TenderFileRequirementDao requirement);

    List<TenderFileRequirementDao> selectByCode(String bidSectionCode);

    int deleteByIds(List<Long> ids);
}
