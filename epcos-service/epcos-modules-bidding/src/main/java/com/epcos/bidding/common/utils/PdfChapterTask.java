package com.epcos.bidding.common.utils;

import cn.hutool.core.util.IdUtil;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.uuid.IdUtils;
import com.epcos.common.file.html2pdf.HtmlUtil;
import com.epcos.common.file.pdf.Itext7PdfUtil;
import com.itextpdf.html2pdf.resolver.font.DefaultFontProvider;
import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Table;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;

@Data
@Slf4j
public class PdfChapterTask implements Callable<BidFileOutLine> {

    private Path tmpDirPath;

    private Integer order;

    // 是招标文件
    private boolean isTender;

    // 是否封面
    private boolean isCover;

    // 章节名
    private String title;

    // 小节名
    private String nodeName;

    // 章节类型: 1 编辑器文本，2 评审办法前附表(只能设置一个)，3 投标函附录/报价表(只能设置一个)，4 补充内容/附件
    private Integer chapterType;

    // 内容
    private String sectionText;

    // 附件 mysql:text
    private String attachKey;

    // 报价表单，表头
    private List<QuoteSheetHeaders> quoteSheetHeaders;

    // 报价表单，内容
    private List<LinkedHashMap<String, String>> quoteSheet;

    // 评审方法
    private EvaluationMethod evaluationMethod;

    @Override
    public BidFileOutLine call() {
        switch (chapterType) {
            case 1: // 编辑器文本
                return generateTextChapter();
            case 2: // 评审办法前附表(只能设置一个)
                if (isTender) {
                    return generateEvaluationMethod();
                }
                break;
            case 3: // 投标函附录/报价表(只能设置一个)
                if (!isTender && !CollectionUtils.isEmpty(quoteSheetHeaders)) {
                    return generateQuoteTable();
                }
                break;
            case 4: // 补充内容/附件
                return generateAttachmentChapter();
            default:
                String tip = "客户端只支持章节类型: [1-编辑器文本，2-评审办法前附表(只能设置一个)，3-投标函附录/报价表(只能设置一个)，4-补充内容/附件]。";
                log.error("{} chapterType={}", tip, chapterType);
                throw new ServiceException(tip + "当前值：" + chapterType);
        }
        return null;
    }

    // 补充内容/附件
    private BidFileOutLine generateAttachmentChapter() {
        BidFileOutLine outLine = new BidFileOutLine(title, nodeName, null);
        outLine.setOrder(order);
        addAttachments(outLine);
        return outLine;
    }


    // 投标函附录/报价表
    private BidFileOutLine generateQuoteTable() {
        List<File> fileList = generateQuotationTableFiles();
        if (!fileList.isEmpty()) {
            BidFileOutLine outLine = new BidFileOutLine("报价表", "报价表", fileList.get(0));
            outLine.setOrder(order);
            // 添加子项-附件文件
            if (fileList.size() > 1) {
                List<BidFileOutLine> childList = new ArrayList<>();
                for (int i = 1, fileListSize = fileList.size(); i < fileListSize; i++) {
                    File file = fileList.get(i);
                    childList.add(new BidFileOutLine("报价表", "附件：" + file.getName(), file));
                }
                outLine.setChildList(childList);
            }
            return outLine;
        }
        return null;
    }

    // 投标函附录/报价表
    private List<File> generateQuotationTableFiles() {
        List<File> files = new ArrayList<>();
        Path path = tmpDirPath.resolve(IdUtils.fastSimpleUUID() + ".pdf");
        files.add(path.toFile());
        try (Document document = new Document(new PdfDocument(new PdfWriter(path.toFile())), PageSize.A4.rotate())) {
            document.setFont(Itext7PdfUtil.pdfFont());
            document.add(Itext7PdfUtil.paragraphSmallTitle(title));
            Optional.ofNullable(nodeName).ifPresent(na -> document.add(Itext7PdfUtil.paragraphLeft(na)));
            int size = quoteSheetHeaders.size();
            int split = 6;// 不包含序号
            int totalTable = (size + split - 1) / split;
            for (int i = 0; i < totalTable; i++) {
                int min = Math.min((i + 1) * split, size);
                int minQ = min % split;
                int currentCols = minQ == 0 ? split : minQ;
                Table table = Itext7PdfUtil.initTable(currentCols + 1);
                // 表头
                int hs = i * split;
                boolean num = true;
                for (int j = hs; j < min; j++) {
                    if (num) {
                        num = false;
                        Cell cell = Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphHeadCenter("序号"));
                        cell.setBackgroundColor(ColorConstants.LIGHT_GRAY);
                        table.addHeaderCell(cell);
                    }
                    QuoteSheetHeaders headers = quoteSheetHeaders.get(j);
                    Cell cell = Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphHeadCenter(headers.getLabel()));
                    cell.setBackgroundColor(ColorConstants.LIGHT_GRAY);
                    table.addHeaderCell(cell);
                }
                // 内容
                for (LinkedHashMap<String, String> row : quoteSheet) {
                    // 序号值
                    table.addCell(Itext7PdfUtil.paragraphCenter(String.valueOf(quoteSheet.indexOf(row) + 1)));
                    for (int j = hs; j < min; j++) {
                        QuoteSheetHeaders headers = quoteSheetHeaders.get(j);
                        String value = row.get(headers.getKey());
                        value = (Objects.isNull(value) || value.isEmpty()) ? "" : value;
                        if (Objects.equals("file", headers.getKeyType())) {
                            Path attFile = tmpDirPath.resolve(value);
                            if (Files.exists(attFile) && Files.isRegularFile(attFile)) {
                                files.add(attFile.toFile());
                            }
                            value = value.substring(value.lastIndexOf("/") + 1);
                        }
                        table.addCell(Itext7PdfUtil.paragraphCenter(value));
                    }
                }
                Itext7PdfUtil.blankLinesInTheDocument(document, 2);
                document.add(table);
            }
        } catch (IOException e) {
            log.error("生成报价表PDF失败，pdf={}", path, e);
            throw new ServiceException("生成报价表PDF失败: " + e.getMessage());
        }
        return files;
    }

    // 生成评审办法
    private BidFileOutLine generateEvaluationMethod() {
        File methodFile = generateMethod();
        BidFileOutLine outLine = new BidFileOutLine(title, nodeName, methodFile);
        outLine.setOrder(order);
        addAttachments(outLine);
        return outLine;
    }

    private File generateMethod() {
        Path pdf = tmpDirPath.resolve(IdUtil.fastSimpleUUID() + ".pdf");
        try (Document document = new Document(
                new PdfDocument(new PdfWriter(pdf.toFile())), PageSize.A4.rotate())) {
            DefaultFontProvider fontProvider = new DefaultFontProvider(true, false, false, "simhei");
            String fontsPath = HtmlUtil.getFontsPath();
            fontProvider.addDirectory(fontsPath);
            // 黑体
            fontProvider.addFont(fontsPath + "simhei.ttf", PdfEncodings.IDENTITY_H);
            // 宋体
            fontProvider.addFont(fontsPath + "simsun.ttc", PdfEncodings.IDENTITY_H);
            // 楷体
            fontProvider.addFont(fontsPath + "simkai.ttf", PdfEncodings.IDENTITY_H);

            document.setFontProvider(fontProvider);
            document.setFont(Itext7PdfUtil.pdfFont());
            document.add(Itext7PdfUtil.paragraphSmallTitle(title));
            Optional.ofNullable(nodeName).ifPresent(na -> document.add(Itext7PdfUtil.paragraphLeft(na)));
            // 符合性评审
            document.add(Itext7PdfUtil.paragraphLeft("符合性评审"));
            Table reviewFormTable = generateConformityReviewForm(evaluationMethod.getConformityReview());
            if (Objects.nonNull(reviewFormTable)) {
                document.add(reviewFormTable);
            }
            // 评分表
            document.add(Itext7PdfUtil.paragraphLeft("评分表"));
            Table ratingTable = generateRatingTable(evaluationMethod.getScoreReview());
            if (Objects.nonNull(ratingTable)) {
                document.add(ratingTable);
            }
            // 补充说明
            document.add(Itext7PdfUtil.paragraphLeft("补充说明：" + evaluationMethod.getRemark()));
        } catch (IOException e) {
            log.error("生成评审办法PDF失败，pdf={}", pdf, e);
            throw new ServiceException("生成评审办法PDF失败: " + e.getMessage());
        }
        return pdf.toFile();
    }

    /**
     * 生成 评分表 - 技术，商务 pdf 表格
     */
    private Table generateRatingTable(List<ReviewItem> scoreReview) {
        if (!CollectionUtils.isEmpty(scoreReview)) {
            Table table = Itext7PdfUtil.initTable(6);
            // 表头
            table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphHeadCenter("评分模块")));
            table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphHeadCenter("序号")));
            table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphHeadCenter("评分项")));
            table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphHeadCenter("分值")));
            table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphHeadCenter("评分标准")));
            table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphHeadCenter("主观分/客观分")));
            LinkedHashMap<String, List<ReviewItem>> groupReview = scoreReview.stream()
                    .collect(Collectors.groupingBy(
                            ReviewItem::convertReviewType,
                            LinkedHashMap::new,
                            Collectors.toList()));
            groupReview.forEach((key, value) -> {
                if (value.size() == 1) {
                    ReviewItem reviewItem = value.get(0);
                    table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter(key)));
                    table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter("1")));
                    table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter(reviewItem.getReviewItem())));
                    table.addCell(
                            Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter(reviewItem.convertReviewScore())));
                    table.addCell(
                            Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter(reviewItem.getReviewCriteria())));
                    table.addCell(
                            Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter(reviewItem.convertIsSubjective())));
                } else {
                    table.addCell(Itext7PdfUtil.cellCenter(value.size(), 1, Itext7PdfUtil.paragraphCenter(key)));
                    for (int i = 0; i < value.size(); i++) {
                        ReviewItem reviewItem = value.get(i);
                        table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter(String.valueOf(i + 1))));
                        table.addCell(
                                Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter(reviewItem.getReviewItem())));
                        table.addCell(Itext7PdfUtil
                                .cellCenter(Itext7PdfUtil.paragraphCenter(reviewItem.convertReviewScore())));
                        table.addCell(Itext7PdfUtil
                                .cellCenter(Itext7PdfUtil.paragraphCenter(reviewItem.getReviewCriteria())));
                        table.addCell(Itext7PdfUtil
                                .cellCenter(Itext7PdfUtil.paragraphCenter(reviewItem.convertIsSubjective())));
                    }
                }
            });
            return table;
        }
        return null;
    }

    /**
     * 生成 符合性评审 pdf 表格
     */
    private Table generateConformityReviewForm(List<ReviewItem> conformityReview) {
        if (!CollectionUtils.isEmpty(conformityReview)) {
            Table table = Itext7PdfUtil.initTable(4);
            // 表头
            table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphHeadCenter("序号")));
            table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphHeadCenter("审查因素")));
            table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphHeadCenter("审查因素描述")));
            table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphHeadCenter("主观分/客观分")));
            for (int i = 0; i < conformityReview.size(); i++) {
                ReviewItem reviewItem = conformityReview.get(i);
                table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter(String.valueOf(i + 1))));
                table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter(reviewItem.getReviewItem())));
                table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter(reviewItem.getReviewCriteria())));
                table.addCell(
                        Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter(reviewItem.convertIsSubjective())));
            }
            return table;
        }
        return null;
    }


    // 生成章节
    private BidFileOutLine generateTextChapter() {
        String html = isCover ? sectionText
                : BidFileUtil.buildHtmlTag(true, "h2", title) +
                BidFileUtil.buildHtmlTag(false, "h3", nodeName)
                + sectionText;
        File htmlPdf = HtmlUtil.toPdf(html, tmpDirPath);
        BidFileOutLine outLine = new BidFileOutLine(title, nodeName, htmlPdf);
        outLine.setOrder(order);
        addAttachments(outLine);
        return outLine;
    }

    private void addAttachments(BidFileOutLine outLine) {
        if (StringUtils.hasText(attachKey)) {
            List<File> files = BidFileUtil.findFiles(tmpDirPath.resolve(attachKey).toFile());
            if (!CollectionUtils.isEmpty(files)) {
                List<BidFileOutLine> childList = files.stream()
                        .map(i -> new BidFileOutLine(null, "附件：" + i.getName(), i))
                        .collect(Collectors.toList());
                if (outLine.getChildList() == null) {
                    outLine.setChildList(childList);
                } else {
                    outLine.getChildList().addAll(childList);
                }
            }
        }
    }


}
