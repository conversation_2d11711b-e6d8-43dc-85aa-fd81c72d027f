package com.epcos.bidding.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.purchase.api.domian.cliams.SupplierCommentVo;
import com.epcos.bidding.purchase.comment.business.api.ISupplierCommentApi;
import com.epcos.bidding.purchase.comment.domain.dao.SupplierCommentDao;
import com.epcos.bidding.purchase.comment.domain.dto.SupplierCommentDto;
import com.epcos.bidding.purchase.comment.mapping.SupplierCommentConvert;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.remote.RemoteToOtherServiceApi;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.utils.DateUtils;
import com.epcos.common.core.utils.EvUtils;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.redis.annotation.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.epcos.common.core.constant.UserConstants.PURCHASER_COMMENT_SUPPLIER_TIME;
import static com.epcos.common.core.enums.ClientEnum.LG;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/19 17:28
 */
@Slf4j
@Api(tags = "采购人对供应商评论")
@RestController
@RequestMapping("/comment")
@RequiredArgsConstructor
public class SupplierCommentController {

    private final ISupplierCommentApi supplierCommentApi;
    private final RemoteToOtherServiceApi remoteToOtherServiceApi;
    private final IBuyItemApi buyItemApi;

    @ApiOperation(value = "查看标段下供应商评论")
    @GetMapping(value = "/supplierCommentInfo")
    public R<List<SupplierCommentVo>> supplierCommentInfo(@RequestParam(value = "subpackageCode") String subpackageCode,
                                                          @RequestParam(value = "supplierId") Long supplierId) {
        List<SupplierCommentDao> list = supplierCommentApi.list(
                Wrappers.lambdaQuery(SupplierCommentDao.class)
                        .eq(SupplierCommentDao::getSubpackageCode, subpackageCode)
                        .eq(SupplierCommentDao::getSupplierId, supplierId)
        );
        String supplierScore = remoteToOtherServiceApi.findSupplierScore(supplierId);
        List<SupplierCommentVo> voList = list.stream()
                .map(l -> SupplierCommentConvert.INSTANCE.convert(l, supplierScore))
                .collect(Collectors.toList());
        return R.ok(voList);
    }


    @RedisLock(second = 30)
    @ApiOperation(value = "评论供应商")
    @PostMapping(value = "/commentSupplier")
    public R<Boolean> commentSupplier(@RequestBody SupplierCommentDto dto) {

        if (LG.getCode().equals(EvUtils.ev())) {
            BuyItemDao buyItemInfo = buyItemApi.findBuyItemInfo(dto.getBuyItemCode());
            Date endTime = buyItemInfo.getEndTime();
            //如果nowDate在endTime后三个月,才允许评论
            if (Objects.nonNull(endTime)) {
                Integer moth = remoteToOtherServiceApi.getConfigKeyData(PURCHASER_COMMENT_SUPPLIER_TIME, Integer.class);
                if (!DateUtils.addMonths(endTime, moth).before(new Date())) {
                    return R.fail("项目完成不足" + moth + "个月，请耐心等待");
                }
            }
            /*
            将此处的修改评分放在判断时间不为空的外层，是为了兼容，之前的项目没有完成时间问题
            即，如果项目没有完成时间，则是本次功能上线之前的功能
            则依然可以更改分数
            */
            remoteToOtherServiceApi.updateSupplierScore(dto.getSupplierId(), dto.getScore());
        }
        dto.setCreateId(SecurityUtils.getUserId());
        dto.setCreateBy(SecurityUtils.getNickName());
        dto.setCommentTime(DateUtils.getNowDate());
        SupplierCommentDao commentDao = SupplierCommentConvert.INSTANCE.convert(dto);
        return R.ok(supplierCommentApi.save(commentDao));
    }

    @RedisLock(second = 30)
    @ApiOperation(value = "删除供应商评论")
    @GetMapping(value = "/deleteSupplierComment")
    public R<Boolean> deleteSupplierComment(@RequestParam(value = "id") Long id) {
        return R.ok(supplierCommentApi.removeById(id));
    }
}
