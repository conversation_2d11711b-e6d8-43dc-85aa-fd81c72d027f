package com.epcos.bidding.purchase.claims.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

@Data
public class RequirementVo implements Serializable {
    private static final long serialVersionUID = -7792001231748920615L;

    @ApiModelProperty("采购文件要求id")
    private Long requirementId;

    @ApiModelProperty("投标人对采购文件具体响应id")
    private Long id;

    @ApiModelProperty("供应商id")
    private Long supplierId;

    @ApiModelProperty("属性")
    @Length(max = 500, message = "属性最长500字符")
    private String attribute;

    @ApiModelProperty("描述")
    @Length(max = 1000, message = "描述最长1000字符")
    private String description;

    @ApiModelProperty("响应文件key")
    private String bidFileKey;

    @ApiModelProperty("0-待上传、1-已上传、2-被驳回")
    private Integer status;

    @ApiModelProperty("驳回理由")
    private String reason;

}
