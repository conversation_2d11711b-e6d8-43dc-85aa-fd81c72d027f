package com.epcos.bidding.common.aspects.filter.validator;

import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.system.api.model.SupplierCompanyVO;

import java.util.Optional;

/**
 * 校验供应商企业信息审核未通过
 */
public class SupplierCompanyInfoAuditFailedValidator implements ResultPostHandlerFilterChain<SupplierCompanyVO> {
    @Override
    public void postHandler(AspectContext context, SupplierCompanyVO result) {
        Optional.ofNullable(result)
                .orElseThrow(() -> new ServiceException("查询供应商企业信息为空"))
                .verifyFailedAudit(result.getOrgCode());
    }

}
