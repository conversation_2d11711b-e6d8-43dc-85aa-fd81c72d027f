package com.epcos.bidding.purchase.project.fjnx.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.epcos.bidding.purchase.project.fjnx.domain.dao.BuyItemFJNXDao;
import com.epcos.bidding.purchase.project.fjnx.domain.dto.FJNXBuyItemQueryDto;
import com.epcos.bidding.purchase.project.fjnx.domain.vo.FJNXBuyItemPageVo;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 福建省农信版本
 * @date 2024/4/23 14:28
 */
public interface FJNXBuyItemMapper extends BaseMapper<BuyItemFJNXDao> {

    IPage<FJNXBuyItemPageVo> selectJoinBuyItemPage(IPage page, @Param("dto") FJNXBuyItemQueryDto dto,
                                                   @Param("orgCode") String orgCode);
}
