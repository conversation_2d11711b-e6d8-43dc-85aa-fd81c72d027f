package com.epcos.bidding.purchase.win.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/17 9:28
 */
@Data
public class WinSupplier implements Serializable {

    private static final long serialVersionUID = 8553784862372705650L;

    @ApiModelProperty(value = "供应商id")
    private Long supplierId;

    @ApiModelProperty(value = "供应商公司名称")
    private String supplierCompanyName;

    @ApiModelProperty(value = "是否中标  1-中标，2-未中标【如果是第一、第二、等候选人形式，则按照1、2、3、4传递，1为第一候选人，以此类推】")
    private String winBid;
}

