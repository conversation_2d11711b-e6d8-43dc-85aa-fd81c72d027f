package com.epcos.bidding.purchase.extract.domain.dto;

import com.epcos.bidding.audit.api.BizAuditRelation;
import com.epcos.bidding.purchase.extract.domain.vo.ExtractLogVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/18 11:04
 */
@Data
public class JudgeExtractDto extends BizAuditRelation implements Serializable {

    private static final long serialVersionUID = -4120963027753962708L;

    @ApiModelProperty(value = "采购项目编号")
    private String buyItemCode;

    @ApiModelProperty(value = "包code")
    private String subpackageCode;

    @ApiModelProperty(value = "专家信息")
    private List<ExtractLogVo> voList;
}
