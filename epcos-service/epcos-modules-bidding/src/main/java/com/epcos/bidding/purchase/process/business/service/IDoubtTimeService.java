package com.epcos.bidding.purchase.process.business.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.purchase.process.business.api.IDoubtTimeApi;
import com.epcos.bidding.purchase.process.domain.dao.DoubtTimeDao;
import com.epcos.bidding.purchase.process.domain.dto.DoubtTimeDto;
import com.epcos.bidding.purchase.process.domain.vo.DoubtTimeVo;
import com.epcos.bidding.purchase.process.mapping.DoubtTimeConvert;
import com.epcos.bidding.purchase.process.repository.IDoubtTimeMapper;
import com.epcos.common.core.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/7 15:33
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IDoubtTimeService extends ServiceImpl<IDoubtTimeMapper, DoubtTimeDao> implements IDoubtTimeApi {


    @Override
    public Boolean saveTime(DoubtTimeDto dto) {
        DoubtTimeDao doubtTimeDao = findOne(dto.getSubpackageCode(), dto.getDoubtType());
        if (Objects.nonNull(doubtTimeDao)) {
            throw new ServiceException("不可重复设置改时间");
        }
        DoubtTimeDao timeDao = DoubtTimeConvert.INSTANCE.convert(dto);
        return save(timeDao);
    }

    @Override
    public List<DoubtTimeVo> findBySubpackageCode(String subpackageCode) {
        return find(subpackageCode)
                .stream()
                .map(DoubtTimeConvert.INSTANCE::convert)
                .collect(Collectors.toList());
    }

    @Override
    public DoubtTimeDao check(String subpackageCode, String doubtType) {
        DoubtTimeDao doubtTimeDao = findOne(subpackageCode, doubtType);
        if (Objects.isNull(doubtTimeDao)) {
            throw new ServiceException("暂未开启质疑");
        }
        Date currentTime = new Date();
        if (currentTime.after(doubtTimeDao.getStartTime()) && currentTime.before(doubtTimeDao.getEndTime())) {
            return doubtTimeDao;
        }
        throw new ServiceException("不在质疑时间之内");
    }
}
