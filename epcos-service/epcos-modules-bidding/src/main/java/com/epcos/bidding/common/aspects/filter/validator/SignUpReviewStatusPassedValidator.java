package com.epcos.bidding.common.aspects.filter.validator;

import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierSignUpDao;

/**
 * 报名状态审核已通过
 */
public class SignUpReviewStatusPassedValidator implements ResultPostHandlerFilterChain<SupplierSignUpDao> {
    @Override
    public void postHandler(AspectContext context, SupplierSignUpDao result) {
        result.verifyReviewStatus();
    }
}
