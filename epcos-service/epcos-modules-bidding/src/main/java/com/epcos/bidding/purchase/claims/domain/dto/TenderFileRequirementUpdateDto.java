package com.epcos.bidding.purchase.claims.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class TenderFileRequirementUpdateDto implements Serializable {
    private static final long serialVersionUID = -2761941721986175856L;

    @ApiModelProperty("采购要求id")
    @NotNull(message = "采购要求id不能为空")
    private Long id;

    @ApiModelProperty("属性")
    @Length(max = 500, message = "属性最长500字符")
    private String attribute;

    @ApiModelProperty("描述")
    @Length(max = 1000, message = "描述最长1000字符")
    private String description;

}
