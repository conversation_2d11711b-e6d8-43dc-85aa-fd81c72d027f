package com.epcos.bidding.common.annotaion;

import com.epcos.bidding.common.aspects.convert.GetParamConvert;
import com.epcos.common.core.constant.CacheConstants;
import com.epcos.common.redis.publish.BusinessTypeEnum;
import com.epcos.common.redis.publish.FrontEndDisplayEnum;
import org.springframework.core.annotation.Order;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 消息通知
 *
 * <AUTHOR>
 */
@Order(1000)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface Msg {
    /**
     * 所属角色
     * {@link com.epcos.common.core.constant.RoleConstants}
     */
    String fromRole();

    /**
     * 当用户不在线时是否需要缓存消息，默认（false）不缓存，如果缓存，可以设置缓存天数，过后失效
     */
    boolean cacheMsg() default false;

    /**
     * 消息缓存，当 cacheMsg=true 时，此值才会生效，默认1h
     */
    int cacheMsgHours() default CacheConstants.expireHours;

    /**
     * 刷新 通知
     */
    FrontEndDisplayEnum[] displayEnums() default {FrontEndDisplayEnum.REFRESH, FrontEndDisplayEnum.NOTIFY};

    /**
     * 业务类型
     */
    BusinessTypeEnum businessType();

    /**
     * msg
     */
    String msg();

    /**
     * 采购项目code
     * el 表达式取值
     */
    String buyItemCodeEL() default "";

    /**
     * 包code
     * el 表达式取值
     */
    String subpackageCodeEL() default "";

    /**
     * 通知的用户id
     */
    Class<? extends GetParamConvert> toUserIdsConvert() default GetParamConvert.class;

}
