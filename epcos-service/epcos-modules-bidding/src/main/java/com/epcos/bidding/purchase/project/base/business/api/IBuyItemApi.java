package com.epcos.bidding.purchase.project.base.business.api;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.IBaseService;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.common.aspects.params.GetItemParam;
import com.epcos.bidding.common.aspects.params.GetSimpleItemParam;
import com.epcos.bidding.common.aspects.vo.GetItemVo;
import com.epcos.bidding.common.aspects.vo.GetSimpleItemVo;
import com.epcos.bidding.purchase.api.params.dto.BaseQueryDto;
import com.epcos.bidding.purchase.api.params.vo.buyitem.BuyItemVo;
import com.epcos.bidding.purchase.extract.domain.vo.ExtractBuyItemVo;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.base.domain.dto.BuyItemBaseDto;
import com.epcos.bidding.workbench.vo.SupplierRegistrationReviewVo;
import com.epcos.system.api.domain.assmble.vo.PurchaseMethodInfoVo;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 公用基础信息
 * @date 2024/4/23 14:06
 */
public interface IBuyItemApi extends IBaseService<BuyItemDao> {

    @Override
    default LambdaQueryWrapper<BuyItemDao> queryWrapper(BuyItemDao dao) {
        LambdaQueryWrapper<BuyItemDao> query = Wrappers.lambdaQuery(BuyItemDao.class);
        if (Objects.nonNull(dao.getId())) {
            return query.eq(BuyItemDao::getId, dao.getId());
        }
        return query.eq(StringUtils.hasText(dao.getItemCode()), BuyItemDao::getItemCode, dao.getItemCode())
                .eq(StringUtils.hasText(dao.getBuyItemCode()), BuyItemDao::getBuyItemCode, dao.getBuyItemCode())
                .like(StringUtils.hasText(dao.getBuyItemName()), BuyItemDao::getBuyItemName, dao.getBuyItemName())
                .eq(StringUtils.hasText(dao.getPurchaseMethodCode()), BuyItemDao::getPurchaseMethodCode, dao.getPurchaseMethodCode())
                .eq(StringUtils.hasText(dao.getPurchaseMethodType()), BuyItemDao::getPurchaseMethodType, dao.getPurchaseMethodType())
                .like(StringUtils.hasText(dao.getPurchaseMethodName()), BuyItemDao::getPurchaseMethodName, dao.getPurchaseMethodName())
                .eq(Objects.nonNull(dao.getUserId()), BuyItemDao::getUserId, dao.getUserId())
                .eq(StringUtils.hasText(dao.getOrgCode()), BuyItemDao::getOrgCode, dao.getOrgCode());
    }

    default LambdaQueryWrapper<BuyItemDao> queryWrapper(String buyItemCode) {
        return queryWrapper(new BuyItemDao(buyItemCode));
    }

    default LambdaUpdateWrapper<BuyItemDao> updateWrapper(String buyItemCode) {
        return Wrappers.lambdaUpdate(BuyItemDao.class)
                .eq(StringUtils.hasText(buyItemCode),
                        BuyItemDao::getBuyItemCode,
                        buyItemCode
                );
    }

    default List<BuyItemDao> findByName(String buyItemName) {
        BuyItemDao buyItemDao = new BuyItemDao();
        buyItemDao.setBuyItemName(buyItemName);
        return list(buyItemDao);
    }

    default List<BuyItemDao> find(String purchaseMethodType) {
        BuyItemDao buyItemDao = new BuyItemDao();
        buyItemDao.setPurchaseMethodType(purchaseMethodType);
        return list(buyItemDao);
    }

    default LambdaQueryWrapper<BuyItemDao> queryWrapper(Collection<String> buyItemCodeList) {
        return Wrappers.lambdaQuery(BuyItemDao.class)
                .in(CollUtil.isNotEmpty(buyItemCodeList),
                        BuyItemDao::getBuyItemCode,
                        buyItemCodeList);
    }

    default List<BuyItemDao> findByBuyItemCodeIn(List<String> buyItemCodeList) {
        return list(queryWrapper(buyItemCodeList));
    }

    default Boolean del(String buyItemCode) {
        return remove(updateWrapper(buyItemCode));
    }

    default BuyItemDao findBuyItemInfo(String buyItemCode) {
        return getOne(queryWrapper(buyItemCode));
    }

    BuyItemDao insertBuyItemInfo(BuyItemBaseDto baseDto, PurchaseMethodInfoVo purchaseMethodInfoToJson);

    BuyItemDao updateBaseInfo(BuyItemBaseDto dto, PurchaseMethodInfoVo purchaseMethodInfoToJson);

    /**
     * 根据标段code查询采购项目信息
     *
     * @param subpackageCode
     * @return
     */
    BuyItemDao findBySubpackageCode(String subpackageCode);

    BuyItemVo getBuyItemVo(String subpackageCode, String belongRole);

    GetItemVo getItemVo(GetItemParam param);

    GetSimpleItemVo getSimpleItemVo(GetSimpleItemParam param);

    IPage<ExtractBuyItemVo> selectExtract(PageSortEntity<BaseQueryDto> dto, String tableName);

    IPage<SupplierRegistrationReviewVo> selectBuyItemWithSupplierSignUp(Integer pageNum, Integer pageSize,
                                                                        BaseQueryDto dto, List<Integer> reviewStatus);

    int countSupplierRegistrationReviews(Long userId, List<Integer> reviewStatus);
}
