package com.epcos.bidding.common.aspects.audit;

import com.epcos.bidding.audit.api.BizAuditRelationDto;
import com.epcos.bidding.common.annotaion.GetAuditRelation;
import com.epcos.bidding.common.aspects.AbstractMethod;
import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.EvalSpelUtil;
import com.epcos.common.core.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * 查询业务与审批之间的关联关系
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class AuditRelationAspectFilterChainPost extends AbstractMethod<GetAuditRelation, BizAuditRelationDto> {

    public AuditRelationAspectFilterChainPost() {
        super(GetUtil.GET_AUDIT_RELATION, "查询业务与审批之间的关联关系");
    }

    @Override
    public BizAuditRelationDto businessMethods(JoinPoint point, GetAuditRelation annotation) {
        AspectContext context = threadLocal.get();
        String buyItemCode = context.getBuyItemCode();
        Long userId = StringUtils.hasText(annotation.userIdEL())
                ? EvalSpelUtil.get(context.getMethod(), point.getArgs(), annotation.userIdEL(), Long.class)
                : SecurityUtils.getUserId();
        if (!StringUtils.hasText(buyItemCode) || Objects.isNull(userId)) {
            log.error("@GetQueryBizAuditRelation 参数错误，buyItemCode={}, userId={}", buyItemCode, userId);
            throw new ServiceException("@GetQueryBizAuditRelation 参数错误");
        }
        return BizAuditRelationDto.builder()
                .auditType(annotation.auditType().getType())
                .buyItemCode(buyItemCode)
                .subpackageCode(context.getSubpackageCode())
                .businessId(EvalSpelUtil.get(context.getMethod(), point.getArgs(), annotation.bizIdEL(), Long.class))
                .businessType(EvalSpelUtil.get(context.getMethod(), point.getArgs(), annotation.bizTypeEL(), String.class))
                .userId(userId)
                .build();
    }

    @Override
    @Around(value = "@annotation(annotation)")
    public Object around(ProceedingJoinPoint point, GetAuditRelation annotation) {
        return super.around(point, annotation);
    }
}
