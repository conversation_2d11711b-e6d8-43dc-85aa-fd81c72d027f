package com.epcos.bidding.common.aspects;

import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import lombok.Data;

import java.lang.reflect.Method;
import java.util.List;

@Data
public class AspectContext {

    // 异步执行
    private boolean async;

    // 采购项目code
    private String buyItemCode;

    // 包code
    private String subpackageCode;

    // 后续处理
    private List<Class<? extends ResultPostHandlerFilterChain>> afters;

    // 索引 =0 入参
    private Object dto;

    // 出参
    private Object res;

    // 方法
    private Method method;

    // 所有入参
    private Object[] args;

}
