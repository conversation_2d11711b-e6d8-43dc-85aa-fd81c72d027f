package com.epcos.bidding.purchase.excel.business;

import com.epcos.bidding.common.annotaion.GetCommon;
import com.epcos.bidding.common.annotaion.GetSimpleItem;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.common.aspects.vo.GetSimpleItemVo;
import com.epcos.bidding.common.utils.BiddingBaseUtil;
import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import com.epcos.bidding.purchase.bargain.business.api.IPurchaseBargainApi;
import com.epcos.bidding.purchase.bargain.domain.vo.PurchaseBargainVo;
import com.epcos.bidding.purchase.excel.domain.dto.ExportExcelDto;
import com.epcos.bidding.purchase.excel.domain.vo.bargain.BargainPackageExcelVo;
import com.epcos.bidding.supplier.api.params.MultiSupplierQuoteFormVo;
import com.epcos.bidding.supplier.api.params.RoundQuoteFormVo;
import com.epcos.bidding.supplier.api.params.SupplierQuoteFormVo;
import com.epcos.bidding.supplier.api.params.SupplierSignUpVo;
import com.epcos.bidding.supplier.sign.business.api.ISupplierSignApi;
import com.epcos.bidding.supplier.sign.domain.dto.SupplierSignQueryDto;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.bean.BeanUtils;
import com.epcos.common.core.utils.sign.AESUtil;
import com.epcos.common.file.html2pdf.HtmlUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.IOUtils;
import org.jxls.transform.Transformer;
import org.jxls.util.JxlsHelper;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/29 11:44
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExcelService {

    private final ISupplierSignApi supplierSignApi;
    private final IPurchaseBargainApi purchaseBargainApi;

    @GetSimpleItem(common = @GetCommon(subpackageCodeEL = "#subpackageCode", async = true))
    public ResponseEntity<byte[]> generateSignExcel(String subpackageCode) {
        //查询报名信息
        List<SupplierSignUpVo> signUpVoList = supplierSignApi.query(new SupplierSignQueryDto(null, subpackageCode), null);
        if (CollectionUtils.isEmpty(signUpVoList)) {
            throw new ServiceException("暂无报名供应商");
        }
        GetSimpleItemVo simpleItemVo = GetUtil.getSimpleItemVo();
        if (Objects.isNull(simpleItemVo)) {
            throw new ServiceException("没有项目信息");
        }
        JxlsHelper jxlsHelper = JxlsHelper.getInstance();
        Path tmpExcelFilePath = HtmlUtil.getTmpPdfFilePath(HtmlUtil.getBusinessFileName(simpleItemVo.getBuyItemName() + "_报名信息表", AESUtil.getAESKey(6), ".xlsx"));
        try (OutputStream out = Files.newOutputStream(tmpExcelFilePath)) {
            Transformer transformer = jxlsHelper.createTransformer(getClass().getClassLoader().getResourceAsStream("templates/jxls/报名信息表.xlsx"), out);
            org.jxls.common.Context context = new org.jxls.common.Context();
            SuperPackageVo superPackageVo = simpleItemVo.getSuperPackageVoList().stream().filter(s -> s.getSubpackageCode().equals(subpackageCode)).collect(Collectors.toList()).get(0);
            context.putVar("buyItemNameAndSubpackageName", simpleItemVo.getBuyItemName() + "  " + superPackageVo.getSubpackageName());
            context.putVar("buyItemCode", simpleItemVo.getBuyItemCode());
            context.putVar("signBidderList", signUpVoList);
            jxlsHelper.processTemplate(context, transformer);
            out.flush();
            out.close();
            byte[] bytes = IOUtils.toByteArray(Files.newInputStream(tmpExcelFilePath.toFile().toPath()));
            return new ResponseEntity<>(bytes, BiddingBaseUtil.setExcelResponseHeaders(tmpExcelFilePath), HttpStatus.OK);
        } catch (IOException e) {
            log.error("导出报名投标人信息excel失败，tmpExcelFilePath:{}, bidCode:{}", tmpExcelFilePath, subpackageCode, e);
            throw new ServiceException("导出报名投标人信息excel失败");
        }
    }

    @GetSimpleItem(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode"))
    public List<BargainPackageExcelVo> getBargainExcelInfo(ExportExcelDto dto) {

        List<PurchaseBargainVo> purchaseBargainVoList = purchaseBargainApi.bargainList(dto.getBuyItemCode());
        GetSimpleItemVo simpleItemVo = GetUtil.getSimpleItemVo();

        List<BargainPackageExcelVo> bargainPackageExcelVoList = new ArrayList<>();
        simpleItemVo.getSuperPackageVoList().forEach(subpackageVo -> purchaseBargainVoList.forEach(purchaseBargainVo -> {
            BargainPackageExcelVo bargainPackageExcelVo = new BargainPackageExcelVo();
            BeanUtils.copyProperties(simpleItemVo, bargainPackageExcelVo);
            BeanUtils.copyProperties(subpackageVo, bargainPackageExcelVo);
            MultiSupplierQuoteFormVo supplierQuoteFormVo = purchaseBargainVo.getSupplierQuoteFormVo();
            if (Objects.nonNull(supplierQuoteFormVo) && subpackageVo.getSubpackageCode().equals(supplierQuoteFormVo.getSubpackageCode())) {
                //获取所有供应商的最新一轮报价信息
                List<SupplierQuoteFormVo> latelySupplierQuoteFormList = latelyRound(supplierQuoteFormVo.getSupplierQuoteFormList());
                bargainPackageExcelVo.setSupplierQuoteFormList(latelySupplierQuoteFormList);
                bargainPackageExcelVo.setHeads(supplierQuoteFormVo.getHeads());
                bargainPackageExcelVoList.add(bargainPackageExcelVo);
            }
        }));
        return bargainPackageExcelVoList;
    }

    private List<SupplierQuoteFormVo> latelyRound(List<SupplierQuoteFormVo> supplierQuoteFormList) {
        for (SupplierQuoteFormVo supplierQuoteFormVo : supplierQuoteFormList) {
            supplierQuoteFormVo.getRoundQuoteFormList().sort((o2, o1) -> o1.getRound().compareTo(o2.getRound()));
            List<RoundQuoteFormVo> quoteFormList = supplierQuoteFormVo.getRoundQuoteFormList();
            if (CollectionUtils.isEmpty(quoteFormList)) {
                continue;
            }
            supplierQuoteFormVo.setRoundQuoteFormList(Collections.singletonList(supplierQuoteFormVo.getRoundQuoteFormList().get(0)));
        }
        return supplierQuoteFormList;
    }
}
