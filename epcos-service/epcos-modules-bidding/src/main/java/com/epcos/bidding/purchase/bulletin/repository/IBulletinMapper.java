package com.epcos.bidding.purchase.bulletin.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.epcos.bidding.purchase.bulletin.domain.dao.BulletinDao;
import com.epcos.bidding.purchase.home.domain.dto.HomePageDto;
import com.epcos.bidding.purchase.home.domain.vo.PageHomeVo;
import com.epcos.bidding.purchase.remote.dto.SupplierBulletinPageDto;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/29 9:44
 */
public interface IBulletinMapper extends BaseMapper<BulletinDao> {

    IPage<BulletinDao> supplierBulletinPage(IPage page, @Param(value = "dto") SupplierBulletinPageDto entity);

    IPage<PageHomeVo> homePage(IPage page, @Param("dto") HomePageDto dto);
}
