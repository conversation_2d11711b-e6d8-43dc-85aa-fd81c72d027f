package com.epcos.bidding.purchase.pdf.domian.vo.audit;

import com.epcos.bidding.audit.api.vo.AuditInfoVo;
import com.epcos.bidding.audit.api.vo.AuditPersonVo;
import com.epcos.common.core.domain.pdf.Pdf;
import com.epcos.system.api.domain.SysDictData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/15 9:01
 */
@Data
public class AuditPdfVo extends Pdf {

    @ApiModelProperty("审批信息")
    private AuditInfoVo auditInfoVo;

    @ApiModelProperty("审批人信息")
    private List<AuditPersonVo> auditPersonList;

    @ApiModelProperty("字典信息")
    private List<SysDictData> dictList;
}
