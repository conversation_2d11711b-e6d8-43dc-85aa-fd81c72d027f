package com.epcos.bidding.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;

public class JsonToListMapTypeHandler extends BaseTypeHandler<List<LinkedHashMap<String, String>>> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i,
                                    List<LinkedHashMap<String, String>> parameter,
                                    JdbcType jdbcType) throws SQLException {
        ps.setString(i, JSON.toJSONString(parameter));
    }

    @Override
    public List<LinkedHashMap<String, String>> getNullableResult(ResultSet rs,
                                                                 String columnName) throws SQLException {
        return Optional.ofNullable(rs.getString(columnName))
                .map(i -> JSON.parseObject(i,
                        new TypeReference<List<LinkedHashMap<String, String>>>() {
                        }
                ))
                .orElse(Collections.emptyList());
    }

    @Override
    public List<LinkedHashMap<String, String>> getNullableResult(ResultSet rs,
                                                                 int columnIndex) throws SQLException {
        return JSON.parseObject(rs.getString(columnIndex),
                new TypeReference<List<LinkedHashMap<String, String>>>() {
                });
    }

    @Override
    public List<LinkedHashMap<String, String>> getNullableResult(CallableStatement cs,
                                                                 int columnIndex) throws SQLException {
        return JSON.parseObject(cs.getString(columnIndex),
                new TypeReference<List<LinkedHashMap<String, String>>>() {
                });
    }
}
