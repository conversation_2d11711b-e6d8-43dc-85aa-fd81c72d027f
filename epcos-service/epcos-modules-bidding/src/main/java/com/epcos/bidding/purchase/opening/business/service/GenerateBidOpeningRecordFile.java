package com.epcos.bidding.purchase.opening.business.service;

import com.epcos.bidding.common.utils.AttributeUtil;
import com.epcos.bidding.purchase.api.params.AttributeVo;
import com.epcos.bidding.supplier.api.params.MultiSupplierQuoteFormVo;
import com.epcos.common.file.pdf.Itext7PdfUtil;
import com.epcos.bidding.supplier.common.SupplierInfoEntityShared;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierBidderDao;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.TextAlignment;

import java.util.*;

/**
 * 生成开标记录文件
 */
public class GenerateBidOpeningRecordFile extends AbstractGenerateBidOpeningRecordFile {

    @Override
    protected void generateTable(Document document, MultiSupplierQuoteFormVo quoteFormVo,
                                 Map<Long, SupplierBidderDao> supplierBidderDaoMap) {
        if (Objects.isNull(quoteFormVo)) {
            return;
        }
        int spilt = 10;
        List<Integer> tableSpilt = spiltTable(spilt, Objects.isNull(quoteFormVo.getHeads()) ? 0 : quoteFormVo.getHeads().size());
        for (Map.Entry<Long, SupplierBidderDao> entry : supplierBidderDaoMap.entrySet()) {
            document.add(Itext7PdfUtil.paragraphHeadLeft(
                            Optional.ofNullable(entry.getValue())
                                    .map(SupplierInfoEntityShared::getBidderName)
                                    .orElse("")
                    )
            );
            quoteFormVo.getSupplierQuoteFormList()
                    .stream()
                    .filter(f -> Objects.equals(entry.getKey(), f.getSupplierId()))
                    .findAny().ifPresent(su -> {
                                List<LinkedHashMap<String, String>> rows = su.queryBodyByRound(0);
                                for (int i = 0; i < tableSpilt.size(); i++) {
                                    // 列数
                                    Integer col = tableSpilt.get(i);
                                    Table table = Itext7PdfUtil.initTable(col + 1);
                                    // 指定跳过的索引
                                    int skip = i * spilt;
                                    Cell cell = Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphHead("序号", TextAlignment.CENTER));
                                    float cellWidth = 30f;
                                    cell.setWidth(cellWidth);
                                    table.addCell(cell);
                                    // 对应报价表头
                                    for (int j = skip, rowSize = quoteFormVo.getHeads().size(); j < rowSize && j < (col + skip); j++) {
                                        AttributeVo head = quoteFormVo.getHeads().get(j);
                                        table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphHead(head.getKeyName(),
                                                TextAlignment.CENTER)));
                                    }
                                    for (int r = 0; r < rows.size(); r++) {
                                        LinkedHashMap<String, String> row = rows.get(r);
                                        // 序号
                                        Cell cell1 = Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphHead(String.valueOf(r + 1),
                                                TextAlignment.CENTER));
                                        cell1.setWidth(cellWidth);
                                        table.addCell(cell1);
                                        // 对应报价表内容
                                        for (int j = skip, rowSize = quoteFormVo.getHeads().size(); j < rowSize && j < (col + skip); j++) {
                                            AttributeVo head = quoteFormVo.getHeads().get(j);
                                            String v = AttributeUtil.replaceFileTypeField(row.get(head.getKeyVal()), head);
                                            table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphHead(Objects.isNull(v) ? "" : v,
                                                    TextAlignment.CENTER)));
                                        }
                                    }
                                    document.add(table);
                                    Itext7PdfUtil.blankLinesInTheDocument(document, 1);
                                }
                            }
                    );
            document.add(Itext7PdfUtil.paragraphHeadRight(
                            Optional.ofNullable(supplierBidderDaoMap.get(entry.getKey()))
                                    .map(sc -> keyword(sc.getCertificateName()))
                                    .orElse("")
                    )
            );
            Itext7PdfUtil.blankLinesInTheDocument(document, 1);
        }
    }


    private List<Integer> spiltTable(int spilt, int column) {
        List<Integer> columns = new ArrayList<>();
        int rowIndex = column;
        while (rowIndex > 0) {
            int min = Math.min(rowIndex, spilt);
            columns.add(min);
            rowIndex = rowIndex - min;
        }
        return columns;
    }

}
