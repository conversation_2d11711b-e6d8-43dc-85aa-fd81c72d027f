package com.epcos.bidding.common.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSONObject;
import com.epcos.bidding.purchase.api.params.vo.buyitem.BuyItemVo;
import com.epcos.bidding.purchase.bulletin.domain.dto.SupBulletinDto;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierProcessNodeDao;
import com.epcos.common.core.domain.AttachmentDto;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.utils.reflect.ReflectUtils;
import com.epcos.common.file.html2pdf.HtmlUtil;
import com.epcos.system.api.domain.assmble.vo.BulletinKV;
import com.epcos.system.api.model.FUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.epcos.common.core.constant.PurchaseConstants.InnerCodePre.*;

/**
 * @Title:
 * @Description:TODO
 * @author:moyu
 * @version:1.0
 * @since 2022-03-29 19:32
 */
@Slf4j
public class BiddingBaseUtil {

    private static final String BULLETIN = "BULLETIN";

    public static String generaBulletinCode() {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        return BULLETIN + uuid;
    }

    public static HttpHeaders setExcelResponseHeaders(Path tmpExcelFilePath) throws UnsupportedEncodingException {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/vnd.ms-excel;charset=UTF-8");
        headers.setContentDispositionFormData("attachment", URLEncoder.encode(tmpExcelFilePath.toFile().getName(), StandardCharsets.UTF_8.name()));
        return headers;
    }

    /**
     * 文件转换 File======>>>>>>FileItem
     *
     * @param file
     * @return
     */
    public static FileItem createFileItem(File file) {
        FileItemFactory factory = new DiskFileItemFactory(16, null);
        FileItem item = factory.createItem("file", MediaType.MULTIPART_FORM_DATA_VALUE, false, file.getName());
        int bytesRead;
        byte[] buffer = new byte[8192];
        try {
            FileInputStream fis = new FileInputStream(file);
            OutputStream os = item.getOutputStream();
            while ((bytesRead = fis.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            fis.close();
        } catch (IOException e) {
            log.error("文件解析失败 e:{}", e);
        }
        return item;
    }

    /**
     * 获取对象的set方法并赋值
     *
     * @param obj
     * @param propertyName
     * @param value
     */
    public static void setProperty(Object obj, String propertyName, String value) {
        Class<?> objClass = obj.getClass();
        // 构造set方法名称，例如：setMyProperty
        String setMethodName = "set" + propertyName.substring(0, 1).toUpperCase() + propertyName.substring(1);
        try {
            Field field = objClass.getDeclaredField(propertyName);
            String simpleName = field.getType().getSimpleName();
            if (Objects.nonNull(value)) {
                if ("Long".equals(simpleName)) {
                    Method method = objClass.getMethod(setMethodName, Long.class);
                    method.invoke(obj, Long.parseLong(value));
                } else if ("String".equals(simpleName)) {
                    Method method = objClass.getMethod(setMethodName, String.class);
                    method.invoke(obj, value);
                } else {
                    Method method = objClass.getMethod(setMethodName, Date.class);
                    Date date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").parse(value);
                    method.invoke(obj, date);
                }
            }
        } catch (Exception e) {
            log.error("属性值赋值失败,e:{}", e);
            throw new ServiceException("查询公告失败");
        }
    }


    /**
     * 根据反射获取某个对象的属性名称
     * 并存放在String类型的集合中返回
     *
     * @param o
     * @return
     */
    public static List<String> getFieldName(Object o) {
        Field[] fields = o.getClass().getDeclaredFields();
        List<String> filedNameList = Arrays.asList(fields).stream().map(Field::getName).collect(Collectors.toList());
        filedNameList.remove(0);
        return filedNameList;
    }

    /**
     * 拼接属性的get方法
     */
    public static String getFieldGetMethodName(String fieldName) {
        return "get" + (char) (fieldName.charAt(0) - 32) + fieldName.substring(1);
    }

    /**
     * 根据对象的属性以及该对象，获取该对象属性的get方法
     * 并执行该属性的get方法
     * 判断该属性是否有值，若没有值则抛出异常
     *
     * @param filedNameList 需要校验对象的属性
     * @param o             需要校验的对象
     * @param cnt           该对象的前N个属性进行非空校验
     */
    public static void isValue(List<String> filedNameList, Object o, int cnt) {
        for (int i = 0; i < cnt; i++) {
            String f = filedNameList.get(i);
            String fieldGetMethodName = getFieldGetMethodName(f);
            try {
                Method method = o.getClass().getMethod(fieldGetMethodName);
                Object fieldValue = method.invoke(o);
                if (Objects.isNull(fieldValue) || StringUtils.isEmpty(fieldValue.toString())) {
                    throw new ServiceException(f + "必填");
                }
            } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                log.error("获取对象属性值 异常，o={}, f={}, e={}", o, f, e);
                throw new ServiceException("获取字段属性[" + f + "]失败");
            }
        }
    }

    /**
     * 根据对象的属性以及该对象，获取该对象属性的get方法
     * 并执行该属性的get方法
     * 判断该属性是否有值，若该类的属性中所有属性都没有值，则返回false
     * 反之返回true
     *
     * @param filedNameList 需要校验对象的属性
     * @param o             需要校验的对象
     */
    public static boolean isValue(List<String> filedNameList, Object o) {
        boolean flag = false;
        for (int i = 0; i < filedNameList.size(); i++) {
            String f = filedNameList.get(i);
            String fieldGetMethodName = getFieldGetMethodName(f);
            try {
                Method method = o.getClass().getMethod(fieldGetMethodName);
                Object fieldValue = method.invoke(o);
                if (Objects.nonNull(fieldValue) && StringUtils.isNotEmpty(fieldValue.toString())) {
                    flag = true;
                }
            } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                log.error("获取对象属性值 异常，o={}, f={}, e={}", o, f, e);
                return flag;
            }
        }
        return flag;
    }

    /**
     * 传入一个包含某个对象属性的list以及需要校验的对象，并传入某个对象中需要校验的长度（默认从第一个属性开始）
     * 给定一个checkMap 这个checkMap中必须包含需要校验的key和value
     * key为对象的属性名，value为需要校验的属性的长度或者可填入的范围值
     *
     * @param filedNameList 包含需要校验对象属性的list
     * @param o             需要校验的对象
     * @param cnt           需要校验的属性个数，默认从第一个开始，即filedNameList.get(0)
     * @param checkMap      具体每个属性的限定范围和值的大小
     */
    public static void valueSize(List<String> filedNameList, Object o, int cnt, Map<String, Object> checkMap) {

        for (int i = 0; i < cnt; i++) {
            String f = filedNameList.get(i);
            String fieldGetMethodName = getFieldGetMethodName(f);
            try {
                Method method = o.getClass().getMethod(fieldGetMethodName);
                Object fieldValue = method.invoke(o);
                if (Objects.nonNull(fieldValue)) {
                    Object check = checkMap.get(f);
                    if ("tenderClass".equals(f) || "tenderCategory".equals(f)) {
                        List<String> stringList = Arrays.stream(check.toString().split(",")).map(s -> s.trim()).collect(Collectors.toList());
                        if (!stringList.contains(fieldValue.toString())) {
                            throw new ServiceException(f + "必须填入对应的值，例如" + check);
                        }
                    } else {
                        if (fieldValue.toString().length() > Integer.parseInt(check.toString())) {
                            throw new ServiceException(f + "超出长度限制， 最长不超过" + check);
                        }
                    }
                }
            } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                log.error("获取对象属性值 异常，o={}, f={}, e={}", o, f, e);
                throw new ServiceException("获取字段属性[" + f + "]失败");
            }
        }
    }

    /**
     * 针对公告从html转换到pdf的抽取方法
     *
     * @param dto 发布公告的信息
     * @return
     */
    public static String htmlGeneratorFile(SupBulletinDto dto, String subpackageCode, BuyItemVo buyItemVo) {
        List<BulletinKV> bulletinKVS = buyItemVo.getBulletinKVList();
        Map<String, String> bulletinMap = bulletinKVS.stream()
                .collect(Collectors.toMap(BulletinKV::getAnType, BulletinKV::getAnName));
        File file = HtmlUtil.toPdf(dto.getBulletinContentHtml());
        BuyItemDao buyItemDao = new BuyItemDao();
        BeanUtils.copyProperties(buyItemVo, buyItemDao);
        String fileName = buyItemDao.getBuyItemName() + bulletinMap.get(dto.getBulletinType());
        return generateFileAndReturnUrl(file, buyItemDao, fileName, dto.getBulletinType(), subpackageCode, SecurityUtils.getUserId());
    }

    public static String generateFileAndReturnUrl(File pdfFile, BuyItemDao buyItemDao, String filename, String fileType, String subpackageCode, Long userId) {
        pdfFile = FileUtil.rename(pdfFile, filename + ".pdf", true);
        FileItem fileItem = createFileItem(pdfFile);
        CommonsMultipartFile commonsMultipartFile = new CommonsMultipartFile(fileItem);
        //调用文件服务返回文件key
        log.error("文件上传时的采购项目code，buyItemCode:{}", buyItemDao.getBuyItemCode());
        String url = FUtil.upFile(buyItemDao.getBuyItemCode(), buyItemDao.getYearMonthSplit(), commonsMultipartFile, fileType, subpackageCode, userId);
        return url;
    }

    public static Date oneDayLater(Date time) {
        if (Objects.nonNull(time)) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(time);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            return calendar.getTime();
        }
        return null;
    }

    public static Map<String, String> convert(Object object) {
        LinkedHashMap<String, String> linkedHashMap = Optional.ofNullable(object)
                .map(obj -> Arrays.stream(obj.getClass().getDeclaredFields())
                        .collect(Collectors.toMap(
                                Field::getName,
                                i -> {
                                    ReflectUtils.makeAccessible(i);
                                    Object o;
                                    try {
                                        o = i.get(obj);
                                    } catch (IllegalAccessException e) {
                                        throw new ServiceException(e.getMessage());
                                    }
                                    if (Objects.nonNull(o)) {
                                        if (o instanceof Date) {
                                            o = DateUtil.formatDateTime(((Date) o));
                                        }
                                        return o.toString();
                                    }
                                    return "";
                                },
                                (o, o2) -> o2,
                                LinkedHashMap::new
                        ))).orElse(new LinkedHashMap<>());
        linkedHashMap.put("currentTime", DateUtil.format(new Date(), "yyyy年MM月dd日"));
        return linkedHashMap;
    }

    /**
     * 获取对象属性值
     */
    public static String getObjValue(Object obj, String filedName) {
        if (StringUtils.isBlank(filedName)) {
            return "/";
        }
        String fieldGetMethodName = getFieldGetMethodName(filedName);
        try {
            Method declaredMethod = obj.getClass().getMethod(fieldGetMethodName);
            Object methodValue = declaredMethod.invoke(obj);
            String res = "/";
            if (Objects.nonNull(methodValue)) {
                if (methodValue instanceof Date) {
                    res = formatTime(((Date) methodValue));
                } else if (methodValue instanceof Boolean) {
                    res = ((Boolean) methodValue) ? "是" : "否";
                } else if (Objects.equals("needConsumables", filedName)) {
                    res = ((Integer) methodValue) == 0 ? "是" : "否";
                } else {
                    res = methodValue.toString();
                }
            }
            return res;
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            log.error("获取对象属性值 异常，obj={}, fileName={}, e={}", obj, filedName, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 格式化时间
     *
     * @param date
     * @return
     */
    private static String formatTime(Date date) {
        if (Objects.isNull(date)) {
            return null;
        }
        return FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss").format(date);
    }

    /**
     * @param organizeDept 组织部门
     * @param organizeType 组织形式
     * @return
     */
    public static String getInnerCodePrefix(String organizeDept, String organizeType) {
        if ("院内集中".equals(organizeType)) {
            return ZBCG;
        }
        if ("医学装备部".equals(organizeDept)) {
            return YXZB;
        } else if ("后勤保障部".equals(organizeDept)) {
            return HQBZ;
        } else if ("基础设施建设管理办公室".equals(organizeDept)) {
            return JCSS;
        } else if ("信息科".equals(organizeDept)) {
            return XXK;
        } else if ("基建办".equals(organizeDept)) {
            return JJB;
        } else {
            return organizeDept;
        }
    }

    /**
     * 钉钉文件下载
     *
     * @param fileKey
     * @return
     */
    public static CommonsMultipartFile downFile(String fileKey) {
        CommonsMultipartFile commonsMultipartFile = null;
        if (StringUtils.isNotBlank(fileKey)) {
            log.error("下载文件 fileKey={}", fileKey);
            ResponseEntity<byte[]> fileRsp = FUtil.dingTalkFileDownload(fileKey);
            log.error("下载文件 fileKey={}, fileRsp={}", fileKey, fileRsp);
            Path tmpAttPath = Paths.get(FileUtil.getTmpDirPath(), fileRsp.getHeaders().getContentDisposition().getFilename());
            try {
                Path file = Files.write(tmpAttPath, fileRsp.getBody());
                FileItem fileItem = createFileItem(file.toFile());
                commonsMultipartFile = new CommonsMultipartFile(fileItem);
            } catch (IOException e) {
                log.error("下载文件失败 fileKey={}, fileRsp={}", fileKey, fileRsp, e);
                throw new ServiceException("下载文件失败: " + e.getMessage());
            }
        }
        return commonsMultipartFile;
    }

    public static String fileToJson(String fileName, String url) {
        AttachmentDto attach = new AttachmentDto();
        attach.setUrl(url);
        attach.setName(fileName);
        String fileJson = JSONObject.toJSONString(attach);
        return fileJson;
    }

    public static AttachmentDto JsonToFile(String fileJson) {
        AttachmentDto dto = new AttachmentDto();
        if (StringUtils.isNotBlank(fileJson)) {
            dto = JSONObject.parseObject(fileJson, AttachmentDto.class);
        }
        return dto;
    }


    public static SupplierProcessNodeDao process(BuyItemVo buyItemVo, String subpackageCode, Long userId, String processRole) {
        SupplierProcessNodeDao nodeDao = new SupplierProcessNodeDao(subpackageCode, userId, Integer.parseInt(processRole));
        nodeDao.setNodes(buyItemVo.querySupplierFKV(processRole));
        String firstSupplierFKV = buyItemVo.queryFirstSupplierFKV(nodeDao.getNodes());
        nodeDao.setPurchaseFunctionKey(firstSupplierFKV);
        nodeDao.setSupplierId(userId);
        nodeDao.setSubpackageCode(subpackageCode);
        nodeDao.setProcessRole(Integer.parseInt(processRole));
        nodeDao.setBuyItemCode(buyItemVo.getBuyItemCode());
        return nodeDao;
    }
}
