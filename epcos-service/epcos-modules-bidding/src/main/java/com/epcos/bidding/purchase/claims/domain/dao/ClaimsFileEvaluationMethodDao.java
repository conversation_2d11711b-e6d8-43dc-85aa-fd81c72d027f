package com.epcos.bidding.purchase.claims.domain.dao;

import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.bidding.common.SubpackageCodeEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "采购文件评审办法")
@TableName("claims_file_evaluation_method")
public class ClaimsFileEvaluationMethodDao extends SubpackageCodeEntity {

    public ClaimsFileEvaluationMethodDao(String subpackageCode) {
        this.subpackageCode = subpackageCode;
    }

    @NotBlank(message = "uuid,必填")
    @Length(max = 50, message = "uuid【最长：50】")
    @ApiModelProperty(value = "uuid")
    private String uuid;

    @NotNull(message = ("评审类型：1符合性评审 or 评分表"))
    @Range(min = 1, max = 4, message = "评审类型：1符合性评审 or 评分表")
    @ApiModelProperty(value = "评审类型：1符合性评审 or 评分表")
    private Integer reviewType;

    @NotBlank(message = "评审模块必填")
    @Length(max = 500, message = "评审模块【最长：500】")
    @ApiModelProperty(value = "评审模块")
    private String reviewItem;

    @Length(max = 65535, message = "评审规则【最长：65535】")
    @ApiModelProperty(value = "评审规则")
    private String reviewCriteria;

    @NotNull(message = "1-主观分,0-客观分，必填")
    @Range(min = 0, max = 1, message = "1-主观分,0-客观分")
    @ApiModelProperty(value = "1-主观分,0-客观分")
    private Integer subjective;

    @Digits(integer = 8, fraction = 2, message = "分值【8位整数，2位小数】")
    @ApiModelProperty(value = "分值")
    private Double reviewScore;


    @Override
    public String toString() {
        return "ClaimsFileEvaluationMethodDao{" +
                "id=" + id +
                ", reviewItem='" + reviewItem + '\'' +
                ", reviewScore=" + reviewScore +
                ", reviewType=" + reviewType +
                ", subjective=" + subjective +
                ", uuid='" + uuid + '\'' +
                ", subpackageCode='" + subpackageCode + '\'' +
                ", createAt=" + createAt +
                ", createBy='" + createBy + '\'' +
                ", deleted=" + deleted +
                ", updateAt=" + updateAt +
                ", updateBy='" + updateBy + '\'' +
                '}';
    }
}
