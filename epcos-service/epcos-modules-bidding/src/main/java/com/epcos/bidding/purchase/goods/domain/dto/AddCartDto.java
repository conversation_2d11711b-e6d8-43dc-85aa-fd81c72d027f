package com.epcos.bidding.purchase.goods.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/12 17:42
 */
@Data
public class AddCartDto implements Serializable {

    private static final long serialVersionUID = -3521401665270313609L;

    @ApiModelProperty(value = "购买者id")
    @NotNull
    private Long buyerId;

    @ApiModelProperty(value = "购物车id")
    private Long cartId;

    @ApiModelProperty(value = "商品id")
    @NotNull
    private Long goodsId;

    @ApiModelProperty(value = "购买数量")
    @NotNull
    private Integer buyCount = 1;

    @ApiModelProperty(value = "卖家id")
    @NotNull
    private Long sellerId;
}
