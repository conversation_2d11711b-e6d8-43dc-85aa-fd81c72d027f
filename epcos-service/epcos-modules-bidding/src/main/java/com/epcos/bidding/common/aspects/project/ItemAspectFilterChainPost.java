package com.epcos.bidding.common.aspects.project;

import cn.hutool.core.text.CharSequenceUtil;
import com.epcos.bidding.common.annotaion.GetItem;
import com.epcos.bidding.common.aspects.AbstractMethod;
import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.common.aspects.params.GetItemParam;
import com.epcos.bidding.common.aspects.vo.GetItemVo;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.EvalSpelUtil;
import com.epcos.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 获取采购项目相关信息
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class ItemAspectFilterChainPost extends AbstractMethod<GetItem, GetItemVo> {

    @Autowired
    private IBuyItemApi buyItemApi;

    public ItemAspectFilterChainPost() {
        super(GetUtil.GET_ITEM, "获取采购项目相关信息");
    }

    @Override
    @Around(value = "@annotation(getItem)")
    public Object around(ProceedingJoinPoint point, GetItem getItem) {
        return super.around(point, getItem);
    }

    @Override
    public GetItemVo businessMethods(JoinPoint point, GetItem getItem) {
        GetItemParam param;
        try {
            param = getItemParam(point, getItem);
        } catch (InstantiationException | IllegalAccessException e) {
            log.error("检查 @GetItem 使用规则: GetItem={}", getItem, e);
            throw new ServiceException("检查 @GetItem 使用规则: " + e.getMessage());
        }
        return buyItemApi.getItemVo(param);
    }

    private GetItemParam getItemParam(JoinPoint point, GetItem anno) throws InstantiationException, IllegalAccessException {
        AspectContext context = threadLocal.get();
        String buyItemCode = context.getBuyItemCode();
        String subpackageCode = context.getSubpackageCode();
        if (!StringUtils.hasText(buyItemCode) && !StringUtils.hasText(subpackageCode)) {
            throw new ServiceException("getItem方法必须指定buyItemCodeEL或者subpackageCodeEL");
        }
        String belongRole = null;
        if (CharSequenceUtil.isNotBlank(anno.belongRole())) {
            belongRole = EvalSpelUtil.get(((MethodSignature) point.getSignature()).getMethod(), point.getArgs(), anno.belongRole(), String.class);
        }
        return new GetItemParam(buyItemCode, subpackageCode, belongRole, anno.querySubpackage());
    }


}
