package com.epcos.bidding.common;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.epcos.common.core.web.validator.CreateGroup;
import com.epcos.common.core.web.validator.UpdateGroup;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.util.Date;

@Getter
@Setter
public abstract class BaseDao<T extends BaseDao<T>> {

    protected Long id;

    @Getter
    @TableField(fill = FieldFill.INSERT)
    protected Date createAt;

    @TableField(fill = FieldFill.INSERT)
    @Length(max = 100, message = "创建者【最长：100】", groups = {CreateGroup.class, UpdateGroup.class})
    protected String createBy;

    @TableField(fill = FieldFill.UPDATE)
    protected Date updateAt;

    @TableField(fill = FieldFill.UPDATE)
    @Length(max = 100, message = "更新者【最长：100】", groups = {CreateGroup.class, UpdateGroup.class})
    protected String updateBy;

    protected Integer deleted;

}
