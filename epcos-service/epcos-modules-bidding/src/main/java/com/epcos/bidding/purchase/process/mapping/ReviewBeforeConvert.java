package com.epcos.bidding.purchase.process.mapping;

import com.epcos.bidding.purchase.process.domain.dao.ReviewBeforeDao;
import com.epcos.bidding.supplier.api.params.SupplierSignUpVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/6 11:27
 */
@Mapper
public interface ReviewBeforeConvert {

    ReviewBeforeConvert INSTANCE = Mappers.getMapper(ReviewBeforeConvert.class);

    ReviewBeforeDao convert(SupplierSignUpVo supplierSignUpVo);
}
