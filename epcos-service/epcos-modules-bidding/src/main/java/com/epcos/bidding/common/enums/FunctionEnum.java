package com.epcos.bidding.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/31 15:50
 */
@Getter
public enum FunctionEnum {

    PURCHASER_REVIEW_EXPERT("purchaser_review_expert", "评审组"),
    PURCHASER_EXPERT_AUDIT("purchaser_expert_audit", "评委审核"),
    PURCHASER_BID_OPEN("purchaser_bid_open", "开标"),
    PURCHASER_MONITOR_BID_PERSON("purchaser_monitor_bid_person", "监标人"),
    PURCHASER_PURCHASE_FILE_NO_CLIENT("purchaser_purchase_file_no_client", "采购文件非客户端"),
    PURCHASER_PURCHASE_FILE("purchaser_purchase_file", "采购文件（客户端）"),
    PURCHASER_REGISTRATION_SUPPLIER("purchaser_registrationSupplier", "报名单位"),
    SUPPLIER_BIDDING_FILE_DOWN("supplier_biddingFileDown", "招标文件下载"),
    SUPPLIER_TENDER_FILE_UPLOAD("supplier_tenderFileUpload", "投标文件上传"),
    EXPERT_PRELIMINARY_REVIEW("expert_preliminaryReview", "初步评审"),
    EXPERT_DETAILED_REVIEW("expert_detailedReview", "详细评审"),
    EXPERT_REVIEW_RESULTS("expert_reviewResults", "评审结果"),
    PURCHASER_START_REVIEW("purchaser_start_review", "评审"),
    PURCHASER_RESULT("purchaser_result", "成交结果"),
    PURCHASER_BARGAINING("purchaser_bargaining", "报价(采购人议价)"),
    PURCHASER_BULLETIN("purchaser_bulletin", "公示公告"),
    PURCHASER_ANSWER_QUESTIONS("purchaser_answerQuestions", "答疑"),
    PURCHASER_QUERY("purchaser_query", "质疑"),
    SUPPLIER_BARGAINING("supplier_bargaining", "议价"),
    SUPPLIER_BID_OPENING("supplier_bid opening", "开标"),
    SUPPLIER_NOTIFICATION_OF_RESULTS("supplier_Notification of Results", "结果通知书"),
    EXPERT_BARGAINING("expert_bargaining", "议价"),
    EXPERT_QUESTION("expert_question", "质疑"),
    EXPERT_DOCUMENT_QA("expert_Document Q&A", "文件答疑"),
    SUPPLIER_DOCUMENT_QA("supplier_Document Q&A", "文件答疑"),
    SUPPLIER_QUESTION("supplier_question", "质疑"),
    SUPPLIER_SIGN_IN("supplier_sign in", "开标签到"),
    SUPPLIER_DECRYPT("supplier_decrypt", "解密"),
    SUPPLIER_SIGN("supplier_sign", "签字"),
    PURCHASER_BULLETIN_REMOVE("purchaser_bulletin_remove", "公告删除"),
    PURCHASER_BULLETIN_WITHDRAW("purchaser_bulletin_withdraw", "公告撤回"),
    PURCHASER_BULLETIN_PUBLISH("purchaser_bulletin_publish", "公告发布"),
    PURCHASER_CANDIDATE_PUBLICITY("purchaser_candidate_publicity", "发送成交候选人公示"),
    PURCHASER_EXPERT_EXTRACTION_EXPORT("purchaser_expertExtraction_export", "导出专家抽取表"),
    PURCHASER_EXPERT_IMPORT("purchaser_expert_import", "导入抽取评委"),
    PURCHASER_ALLOW_BIDDING("purchaser_allow_bidding", "是否允许响应"),
    PURCHASER_SET_SUPPLIER_QUALIFIED("purchaser_set_supplier_qualified", "设置是否合格供应商"),
    PURCHASER_QUERY_STAMP("purchaser_query_stamp", "质疑文件是否需要盖章"),
    SUPPLIER_RESPONSE_FILE_ENCRYPTION("supplier_response_file_encryption", "响应文件是否加密"),
    PURCHASER_BID_SING("purchaser_bid_sing", "是否开启唱标"),

    PURCHASER_START_VALID_NUMBER_OF_CHECK_IN("purchaser_start_valid_numberOfCheckIn", "控制开标开始是否校验投标人签到满足3家"),
    SUPPLIER_CHECK_ZEPCKEY("supplier_check_zepckey", "校验投标文件zepcKey"),

    PURCHASER_BID_OPEN_AUTOMATIC("purchaser_bid_open_automatic", "自动开标"),
    PURCHASER_TENDER_BULLETIN_NOT_APPROVED("purchaser_procurement_bulletin_not_approved", "采购公告不审批"),
    PURCHASER_BULLETIN_NOT_APPROVED("purchaser_bulletin_not_approved", "公示公告不审批"),
    PURCHASER_CANDIDATE_PUBLICITY_NOT_APPROVED("purchaser_candidate_publicity_not_approved", "成交候选人公示不审批"),
    PURCHASER_RESULT_PUBLICITY_NOT_APPROVED("purchaser_result_publicity_not_approved", "成交结果公示不审批"),
    PURCHASER_RESULT_PUBLICITY("purchaser_result_publicity", "发送成交结果公示"),
    ;


    private String key;
    private String desc;


    FunctionEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
