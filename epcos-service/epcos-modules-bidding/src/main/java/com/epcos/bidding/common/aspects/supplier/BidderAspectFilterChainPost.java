package com.epcos.bidding.common.aspects.supplier;

import cn.hutool.core.text.CharSequenceUtil;
import com.epcos.bidding.common.annotaion.GetBidder;
import com.epcos.bidding.common.aspects.AbstractMethod;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.supplier.sign.business.api.ISupplierBidderApi;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierBidderDao;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.EvalSpelUtil;
import com.epcos.common.core.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@Aspect
public class BidderAspectFilterChainPost extends AbstractMethod<GetBidder, SupplierBidderDao> {

    public BidderAspectFilterChainPost() {
        super(GetUtil.GET_BIDDER, "获取投标人信息");
    }

    @Autowired
    private ISupplierBidderApi supplierBidderApi;

    @Override
    @Around("@annotation(getBidder)")
    public Object around(ProceedingJoinPoint point, GetBidder getBidder) {
        return super.around(point, getBidder);
    }

    @Override
    public SupplierBidderDao businessMethods(JoinPoint point, GetBidder getBidder) {
        String subpackageCode = threadLocal.get().getSubpackageCode();
        if (CharSequenceUtil.isEmpty(subpackageCode)) {
            throw new ServiceException("subpackageCode 参数值必填：" + subpackageCode);
        }
        String suEl = getBidder.supplierIdEL();
        Method method = ((MethodSignature) point.getSignature()).getMethod();
        Long supplierId;
        if (CharSequenceUtil.isEmpty(suEl)) {
            supplierId = SecurityUtils.getUserId();
        } else {
            supplierId = EvalSpelUtil.get(method, point.getArgs(), suEl, Long.class);
        }
        SupplierBidderDao bidderDao = supplierBidderApi.findBy(subpackageCode, supplierId);
        if (Objects.isNull(bidderDao)) {
            log.error("查询供应商报名联系信息为空，subpackageCode={},supplierId={},SupplierBidderDao={}", subpackageCode, supplierId, bidderDao);
            throw new ServiceException("查询供应商报名联系信息为空");
        }
        return bidderDao;
    }


}
