package com.epcos.bidding.common.aspects.filter.validator;

import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import com.epcos.bidding.purchase.api.params.PurchaseBidOpeningVo;

/**
 * 校验开标不是未开始
 */
public class BidOpeningNotWaitValidator implements ResultPostHandlerFilterChain<PurchaseBidOpeningVo> {
    @Override
    public void postHandler(AspectContext context, PurchaseBidOpeningVo result) {
        result.verifyNotWait();
    }
}
