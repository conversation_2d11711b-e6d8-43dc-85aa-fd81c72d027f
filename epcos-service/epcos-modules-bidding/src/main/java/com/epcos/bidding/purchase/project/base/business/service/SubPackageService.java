package com.epcos.bidding.purchase.project.base.business.service;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.common.annotaion.GetCommon;
import com.epcos.bidding.common.annotaion.GetSimpleItem;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.common.aspects.vo.GetSimpleItemVo;
import com.epcos.bidding.common.enums.BulletinTypeEnum;
import com.epcos.bidding.common.enums.FunctionEnum;
import com.epcos.bidding.purchase.api.params.dto.BaseQueryDto;
import com.epcos.bidding.purchase.api.params.dto.QuoteFormCreatorDto;
import com.epcos.bidding.purchase.api.params.dto.SubPackageBaseDto;
import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import com.epcos.bidding.purchase.claims.business.service.ClaimsFileQuoteFormService;
import com.epcos.bidding.purchase.claims.domain.dto.ClaimsFilePageDto;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.purchase.project.base.domain.dto.BidOpenTask;
import com.epcos.bidding.purchase.project.base.domain.vo.LabelJson;
import com.epcos.bidding.purchase.project.base.repository.ISubPackageMapper;
import com.epcos.bidding.purchase.technology.business.api.BuyItemParamApi;
import com.epcos.bidding.purchase.win.domain.dto.SaveSourceDto;
import com.epcos.common.core.constant.CacheConstants;
import com.epcos.common.core.constant.PurchaseConstants;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.utils.bean.BeanUtils;
import com.epcos.system.api.domain.assmble.vo.FunctionKV;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static java.lang.Boolean.TRUE;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 公用基础信息
 * @date 2024/4/23 14:12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SubPackageService extends ServiceImpl<ISubPackageMapper, SubpackageDao> implements ISubPackageApi {

    private final ISubPackageMapper subPackageMapper;
    private final ClaimsFileQuoteFormService claimsFileQuoteFormService;
    private final RedisTemplate redisTemplate;
    @Autowired
    @Lazy
    private BuyItemParamApi buyItemParamApi;

    public List<String> addSubInfo(List<SubPackageBaseDto> subPackageDtoList, String buyItemCode) {
        List<String> subpackageCodes = new ArrayList<>();
        for (SubPackageBaseDto dto : subPackageDtoList) {
            // 保存标段
            SubpackageDao subpackageDao = addSubPackageInfo(buyItemCode, dto);
            dto.setSubpackageCode(subpackageDao.getSubpackageCode());
            // 保存报价表
            QuoteFormCreatorDto creatorDTO = dto.getClaimsFileQuoteFormCreatorDto();
            creatorDTO.setSubpackageCode(subpackageDao.getSubpackageCode());
            log.error("保存报价表：{}", creatorDTO);
            claimsFileQuoteFormService.createOrUpdate(creatorDTO);
            buyItemParamApi.insertBase(subpackageDao.getSubpackageCode());
            subpackageCodes.add(subpackageDao.getSubpackageCode());
        }
        return subpackageCodes;
    }

    private SubpackageDao addSubPackageInfo(String buyItemCode, SubPackageBaseDto dto) {
        SubpackageDao subpackageDao = new SubpackageDao();
        BeanUtils.copyProperties(dto, subpackageDao);
        String subPackageCode = "SUBPACKAGE-" + UUID.randomUUID().toString().replaceAll("-", "");
        subpackageDao.setSubpackageCode(subPackageCode);
        subpackageDao.setAbandon("0");
        subpackageDao.setBuyItemCode(buyItemCode);
        subPackageMapper.insert(subpackageDao);
        return subpackageDao;
    }

    @Override
    public List<SubpackageDao> findByBuyItemCode(String buyItemCode) {
        return list(queryWrapper(buyItemCode));
    }

    @Override
    public List<String> delSubpackage(String buyItemCode) {
        List<SubpackageDao> subpackageDaoList = findByBuyItemCode(buyItemCode);
        remove(queryWrapper(buyItemCode));
        List<String> subCodeList = subpackageDaoList.stream().map(SubpackageDao::getSubpackageCode)
                .collect(Collectors.toList());
        return subCodeList;
    }

    @Override
    public boolean updateByAbandon(String subpackageCode, String abandon) {
        SubpackageDao dao = new SubpackageDao();
        dao.setAbandon(abandon);
        return update(dao, updateWrapper(null, subpackageCode));
    }

    @Override
    public boolean updateByAddress(List<String> subpackageCodes, String address) {
        return update(Wrappers.lambdaUpdate(SubpackageDao.class)
                .set(SubpackageDao::getBidOpenAddress, address)
                .in(SubpackageDao::getSubpackageCode, subpackageCodes));
    }

    @Override
    public Page<SubpackageDao> findByBuyItemCodeAndSubpackageCodeNotPage(PageSortEntity<ClaimsFilePageDto> entity) {
        return page(new Page<>(entity.getCalcPageNum(), entity.getCalcPageSize()),
                queryWrapperBuyItemCodeAndNotEqSubpackageCode(
                        entity.getEntity().getBuyItemCode(),
                        entity.getEntity().getSubpackageCode()));
    }

    @Override
    public List<SubpackageDao> findBySubpackageCodes(List<String> subpackageCodeList) {
        List<SubpackageDao> daoList = list(Wrappers.lambdaQuery(SubpackageDao.class)
                .in(SubpackageDao::getSubpackageCode, subpackageCodeList));
        return daoList;
    }

    @Override
    public Boolean addLabel(LabelJson dto) {
        SubpackageDao subpackageDao = findBySubpackageCode(dto.getSubpackageCode());
        if (Objects.isNull(subpackageDao)) {
            throw new ServiceException("没有该项目");
        }
        String labelJson = subpackageDao.getLabelJson();
        Map<Object, Object> labelMap;
        if (StringUtils.isBlank(labelJson)) {
            labelMap = new HashMap<>();
        } else {
            // 将Json转换为map
            labelMap = (Map) JSON.parse(labelJson);
        }
        if (labelMap.values().size() == 3) {
            throw new ServiceException("标签最多添加 3 个");
        }
        labelMap.put(UUID.randomUUID().toString().replaceAll("-", ""), dto.getJsonLabel());
        // map转json
        String json = JSON.toJSON(labelMap).toString();
        SubpackageDao dao = new SubpackageDao();
        dao.setLabelJson(json);
        return update(dao, updateWrapper(dto.getSubpackageCode()));
    }

    @Override
    public Boolean delLabel(LabelJson dto) {
        SubpackageDao subpackageDao = findBySubpackageCode(dto.getSubpackageCode());
        if (Objects.isNull(subpackageDao)) {
            throw new ServiceException("没有该项目");
        }
        String labelJson = subpackageDao.getLabelJson();
        if (!StringUtils.isBlank(labelJson)) {
            Map<Object, Object> labelMap = (Map) JSON.parse(labelJson);
            labelMap.remove(dto.getJsonKey());
            String json = JSON.toJSON(labelMap).toString();
            SubpackageDao dao = new SubpackageDao();
            dao.setLabelJson(json);
            return update(dao, updateWrapper(dto.getSubpackageCode()));
        }
        return TRUE;
    }

    /**
     * 保存报价总分
     *
     * @return
     */
    @Override
    public Boolean saveSource(SaveSourceDto dto) {
        return update(updateWrapper(dto.getSubpackageCode())
                .set(SubpackageDao::getPriceTotalSource, dto.getPriceTotalSource()));
    }

    @Override
    public List<SubpackageDao> findTodayBidOPen(Integer pageNum, Integer pageSize, BaseQueryDto baseQueryDto) {
        return subPackageMapper.findTodayBidOPen(Page.of(pageNum, pageSize), baseQueryDto);
    }

    @Override
    public Long findTodayBidOPenCnt(Boolean isToday, BaseQueryDto baseQueryDto) {
        return subPackageMapper.findTodayBidOPenCnt(isToday, baseQueryDto);
    }

    @Override
    @GetSimpleItem(common = @GetCommon(buyItemCodeEL = "#buyItemDao.buyItemCode"))
    public void updateTime(SubpackageDao subpackageDao, BuyItemDao buyItemDao, String bulletinType) {
        // 修改时间
        update(subpackageDao, updateWrapper(buyItemDao.getBuyItemCode(), subpackageDao.getSubpackageCode()));
        // 依据自动开标功能点
        if (hasAutoBidOpenFunction(buyItemDao) && subpackageDao.getMeetingTime() != null) {
            if (buyItemDao.getBuyItemCode() == null || bulletinType == null) {
                log.error("设置自动开标-项目code或公告类型为空异常 buyItemCode={}, bulletinType={}", buyItemDao.getBuyItemCode(), bulletinType);
                throw new ServiceException("设置自动开标-项目code或公告类型为空异常");
            }
            if (BulletinTypeEnum.BIDDING_ANNOUNCEMENT.getKey().equals(bulletinType)) {
                GetSimpleItemVo simpleItemVo = GetUtil.getSimpleItemVo();
                if (simpleItemVo.getBuyItemCode() == null) {
                    log.error("获取项目信息异常");
                    throw new ServiceException("获取项目信息异常");
                }
                List<SuperPackageVo> superPackageVoList = simpleItemVo.getSuperPackageVoList();
                if (CollectionUtils.isEmpty(superPackageVoList)) {
                    log.error("获取项目中标段信息异常 simpleItemVo={}", simpleItemVo);
                    throw new ServiceException("获取项目中标段信息异常");
                }
                for (SuperPackageVo superPackageVo : superPackageVoList) {
                    createBidOpenTask(subpackageDao.getMeetingTime(), superPackageVo.getSubpackageCode(), buyItemDao.getBuyItemCode());
                }
            }
            if (org.springframework.util.StringUtils.hasText(subpackageDao.getSubpackageCode())) {
                createBidOpenTask(subpackageDao.getMeetingTime(), subpackageDao.getSubpackageCode(), buyItemDao.getBuyItemCode());
            } else {
                log.error("设置自动开标-非招标公告类型时-标段code为空异常 bulletinType={}, subpackageCode={}", bulletinType, subpackageDao.getSubpackageCode());
            }
        }
    }

    private void createBidOpenTask(Date meetingTime, String subpackageCode, String buyItemCode) {
        if (meetingTime.getTime() - 5 <= System.currentTimeMillis()) {
            // 无法设置开标时间小于当前时间
            log.error("当前设置的开标时间小于当前时间 subpackageCode={}, buyItemCode={}", subpackageCode, buyItemCode);
            throw new ServiceException("当前设置的开标时间小于当前时间");
        }
        String key = CacheConstants.AUTO_BID_OPEN + subpackageCode;
        // 设置过期key
        long delayTime = Duration.between(Instant.now(), meetingTime.toInstant()).getSeconds();
        redisTemplate.opsForValue().set(key, subpackageCode, delayTime, TimeUnit.SECONDS);
        // 添加任务
        BidOpenTask task = new BidOpenTask(subpackageCode, buyItemCode, 0, meetingTime, BidOpenTask.Status.PENDING);
        task.setUserId(SecurityUtils.getUserId());
        task.setUsername(SecurityUtils.getUsername());
        task.setNickName(SecurityUtils.getNickName());
        Map<String, Object> map = BeanUtil.beanToMap(task);
        redisTemplate.opsForHash().putAll(CacheConstants.AUTO_BID_OPEN_HASH + subpackageCode, map);
    }

    private boolean hasAutoBidOpenFunction(BuyItemDao buyItemDao) {
        return buyItemDao.parsePurchaseFunctionJsonByRole(PurchaseConstants.UserType.PURCHASER)
                .stream().map(FunctionKV::getPurchaseFunctionKey)
                .collect(Collectors.toList())
                .contains(FunctionEnum.PURCHASER_BID_OPEN_AUTOMATIC.getKey());
    }


    @Override
    public void updateInvite(String subpackageCode, String inviteJson) {
        update(updateWrapper(subpackageCode)
                .set(SubpackageDao::getInvitedSupplierJson, inviteJson)
                .eq(SubpackageDao::getSubpackageCode, subpackageCode));
    }
}
