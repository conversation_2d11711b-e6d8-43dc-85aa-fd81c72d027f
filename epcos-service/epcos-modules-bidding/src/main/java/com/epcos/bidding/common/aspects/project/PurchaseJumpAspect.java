package com.epcos.bidding.common.aspects.project;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSONArray;
import com.epcos.bidding.audit.api.AuditProcessDto;
import com.epcos.bidding.common.annotaion.PurchaseJump;
import com.epcos.bidding.common.aspects.params.GetSimpleItemParam;
import com.epcos.bidding.common.aspects.vo.GetSimpleItemVo;
import com.epcos.bidding.common.enums.FunctionEnum;
import com.epcos.bidding.purchase.api.params.EpcFileContentVo;
import com.epcos.bidding.purchase.api.params.PurchaseBidOpeningVo;
import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import com.epcos.bidding.purchase.claims.business.service.ClaimsService;
import com.epcos.bidding.purchase.extract.business.api.IExtractJudgeInnerApi;
import com.epcos.bidding.purchase.extract.domain.dao.ExtractJudgeInnerDao;
import com.epcos.bidding.purchase.opening.business.api.IPurchaseBidOpeningApi;
import com.epcos.bidding.purchase.opening.domain.dto.PurchaseBidOpeningDto;
import com.epcos.bidding.purchase.process.business.api.IReviewBeforeApi;
import com.epcos.bidding.purchase.process.domain.dao.ReviewBeforeDao;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.remote.RemoteToOtherServiceApi;
import com.epcos.bidding.purchase.remote.dto.NotifyJudgeDto;
import com.epcos.bidding.supplier.api.params.SupplierSignUpVo;
import com.epcos.bidding.supplier.sign.business.api.ISupplierSignApi;
import com.epcos.bidding.supplier.sign.domain.dto.SupplierSignQueryDto;
import com.epcos.common.core.domain.review.ReviewSummaryVo;
import com.epcos.common.core.domain.review.SupplierDetailsVo;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.EvalSpelUtil;
import com.epcos.common.core.utils.StringUtils;
import com.epcos.system.api.domain.assmble.vo.FunctionKV;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.epcos.bidding.common.enums.FunctionEnum.PURCHASER_EXPERT_AUDIT;
import static com.epcos.bidding.common.enums.FunctionEnum.PURCHASER_REVIEW_EXPERT;
import static com.epcos.common.core.constant.PurchaseConstants.BulletinAudit.PASS;
import static com.epcos.common.core.constant.PurchaseConstants.Currency.ZERO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/1 9:14
 */
@Slf4j
@Aspect
@Component
@AllArgsConstructor
public class PurchaseJumpAspect {

    private final IBuyItemApi buyItemApi;
    private final IPurchaseBidOpeningApi purchaseBidOpeningApi;
    private final ClaimsService claimsService;
    private final IReviewBeforeApi reviewBeforeApi;
    private final ISupplierSignApi supplierSignApi;
    private final IExtractJudgeInnerApi extendJudgeInnerApi;
    private final RemoteToOtherServiceApi remoteToOtherServiceApi;

    @Before(value = "@annotation(purchaseJump)")
    public void processCheck(JoinPoint point, PurchaseJump purchaseJump) {
        GetSimpleItemParam param = getParam(point, purchaseJump);
        List<FunctionKV> functionKVList = getBuyItemFun(param);
        List<String> funKeyList = functionKVList.stream().map(FunctionKV::getPurchaseFunctionKey).collect(Collectors.toList());
        String methodName = point.getSignature().getName();
        //会签相关效验
        if ("confirmCounterSign".equals(methodName)) {
            confirmCounterSign(funKeyList, param);
        }
        //评审相关效验
        if ("confirmReview".equals(methodName)) {
            confirmReview(param.getSubpackageCode());
        }
        //评审页面相关
        if ("confirmReviewPage".equals(methodName)) {
            confirmReviewPage(funKeyList, param);
        }
        //确认评审结果校验
        if ("confirmReviewResult".equals(methodName)) {
            confirmReviewResult(param.getSubpackageCode());
        }
    }

    private void confirmReviewResult(String subpackageCode) {

        ReviewSummaryVo reviewInfo = remoteToOtherServiceApi.getJudgesReviewInfo(new NotifyJudgeDto(subpackageCode));
        if (Objects.isNull(reviewInfo)) {
            throw new ServiceException("评审还未结束，请耐心等待");
        }
        List<SupplierDetailsVo> supplierDetailsVoList = reviewInfo.getSupplierDetailsVoList();
        if (!CollectionUtils.isEmpty(supplierDetailsVoList) &&
                supplierDetailsVoList.stream().anyMatch(s -> Objects.isNull(s.getRank()) || s.getRank() == 0)) {
            throw new ServiceException("评委还未设置推荐排名，请耐心等待");
        }
    }

    private void confirmReviewPage(List<String> funKeyList, GetSimpleItemParam param) {
        if (!isContains(funKeyList, FunctionEnum.PURCHASER_BID_OPEN)) {
            GetSimpleItemVo simpleItemVo = buyItemApi.getSimpleItemVo(param);
            List<String> subpackageCodeList = simpleItemVo.getSuperPackageVoList().stream()
                    .map(SuperPackageVo::getSubpackageCode)
                    .collect(Collectors.toList());
            for (String subpackageCode : subpackageCodeList) {
                List<ReviewBeforeDao> beforeDaoList = reviewBeforeApi.find(subpackageCode, null);
                if (CollectionUtils.isEmpty(beforeDaoList) || "0".equals(beforeDaoList.get(0).getConfirmCounterSign())) {
                    dealSupplier(simpleItemVo.getBuyItemCode(), subpackageCode);
                }
            }
        }
    }

    private void dealSupplier(String buyItemCode, String subpackageCode) {
        PurchaseBidOpeningDto purchaseBidOpeningDto = new PurchaseBidOpeningDto();
        purchaseBidOpeningDto.setBuyItemCode(buyItemCode);
        purchaseBidOpeningDto.setSubpackageCode(subpackageCode);
        List<SupplierSignUpVo> signUpVoList = supplierSignApi.query(new SupplierSignQueryDto(null, subpackageCode), null);
        purchaseBidOpeningDto.setSignUpList(signUpVoList);
        reviewBeforeApi.save(purchaseBidOpeningDto, false);
    }

    private void confirmReview(String subpackageCode) {
        List<ReviewBeforeDao> beforeDaoList = reviewBeforeApi.find(subpackageCode, null);
        if (CollectionUtils.isEmpty(beforeDaoList) || String.valueOf(ZERO).equals(beforeDaoList.get(0).getConfirmCounterSign())) {
            throw new ServiceException("请先确认会签");
        }
        beforeDaoList.get(0).verifyConfirmReview();
    }

    private void confirmCounterSign(List<String> funKeyList, GetSimpleItemParam param) {
        if (funKeyList.contains(PURCHASER_REVIEW_EXPERT.getKey())) {
            //没有评委也不让进
            List<ExtractJudgeInnerDao> innerDaoList = extendJudgeInnerApi.findBySubCode(param.getSubpackageCode());
            if (CollectionUtils.isEmpty(innerDaoList)) {
                throw new ServiceException("请先完成评委抽取");
            }
            if (funKeyList.contains(PURCHASER_EXPERT_AUDIT.getKey())) {
                AuditProcessDto auditProcessDto = extendJudgeInnerApi.queryJudgeAudit(param.getBuyItemCode(), param.getSubpackageCode(), innerDaoList.get(0).getId());
                if (Objects.isNull(auditProcessDto) || !PASS.equals(String.valueOf(auditProcessDto.getStatus()))) {
                    throw new ServiceException("请先完成评委审核");
                }
            }
        }
        //有开标功能
        if (isContains(funKeyList, FunctionEnum.PURCHASER_BID_OPEN)) {
            PurchaseBidOpeningVo query = purchaseBidOpeningApi.query(param.getSubpackageCode());
            if (query.getStatus() != 2) {
                throw new ServiceException("请先完成开标");
            }
            return;
        }
        //没有开标，但是有采购文件
        else if (isContains(funKeyList, FunctionEnum.PURCHASER_PURCHASE_FILE)) {
            EpcFileContentVo query = claimsService.query(param.getSubpackageCode());
            if (Objects.isNull(query)) {
                throw new ServiceException("该标段还未上传采购文件");
            }
            if (query.getReleaseStatus() != 1) {
                throw new ServiceException("采购文件尚未审核");
            }
            List<SupplierSignUpVo> signUpVoList = supplierSignApi.query(new SupplierSignQueryDto(null, param.getSubpackageCode()), null);
            if (CollectionUtils.isEmpty(signUpVoList)) {
                throw new ServiceException("暂无报名供应商");
            }
            if (signUpVoList.stream().allMatch(s -> StringUtils.isEmpty(s.getEpcFile()))) {
                throw new ServiceException("报名供应商尚未上传响应文件");
            }
        }

        //重复点击
        List<ReviewBeforeDao> reviewBeforeDaoList = reviewBeforeApi.find(param.getSubpackageCode(), null);
        if (!CollectionUtils.isEmpty(reviewBeforeDaoList)) {
            reviewBeforeDaoList.get(0).verifyCounterSign();
        }
    }

    private boolean isContains(List<String> functionKVList, FunctionEnum functionEnum) {
        return functionKVList.contains(functionEnum.getKey());
    }

    private List<FunctionKV> getBuyItemFun(GetSimpleItemParam param) {
        BuyItemDao buyItemDao;
        if (StringUtils.hasText(param.getBuyItemCode())) {
            buyItemDao = buyItemApi.findBuyItemInfo(param.getBuyItemCode());
        } else {
            buyItemDao = buyItemApi.findBySubpackageCode(param.getSubpackageCode());
        }
        String purchaseFunctionJson = buyItemDao.getPurchaseFunctionJson();
        return JSONArray.parseArray(purchaseFunctionJson, FunctionKV.class);
    }

    private GetSimpleItemParam getParam(JoinPoint point, PurchaseJump purchaseJump) {
        if (CharSequenceUtil.isNotBlank(purchaseJump.common().buyItemCodeEL())
                || CharSequenceUtil.isNotBlank(purchaseJump.common().subpackageCodeEL())) {

            String buyItemCode = EvalSpelUtil.get(
                    ((MethodSignature) point.getSignature()).getMethod(), point.getArgs(),
                    purchaseJump.common().buyItemCodeEL(),
                    String.class);
            String subPackageCode = EvalSpelUtil.get(
                    ((MethodSignature) point.getSignature()).getMethod(),
                    point.getArgs(), purchaseJump.common().subpackageCodeEL(),
                    String.class
            );
            return new GetSimpleItemParam(buyItemCode, subPackageCode);
        }
        throw new ServiceException("purchaseJump参数异常");
    }
}
