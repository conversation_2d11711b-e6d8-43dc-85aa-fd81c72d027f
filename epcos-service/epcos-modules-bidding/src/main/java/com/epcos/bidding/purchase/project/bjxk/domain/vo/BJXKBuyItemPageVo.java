package com.epcos.bidding.purchase.project.bjxk.domain.vo;

import com.epcos.bidding.purchase.project.base.domain.vo.BuyItemBaseVo;
import com.epcos.bidding.purchase.project.base.domain.vo.SubPackageTimeVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/15 11:08
 */
@Data
@NoArgsConstructor
@ApiModel(description = "北京胸科医院版本")
public class BJXKBuyItemPageVo extends BuyItemBaseVo {

    @ApiModelProperty(value = "标段信息")
    private List<SubPackageTimeVo> subpackageTimeVoList;
}
