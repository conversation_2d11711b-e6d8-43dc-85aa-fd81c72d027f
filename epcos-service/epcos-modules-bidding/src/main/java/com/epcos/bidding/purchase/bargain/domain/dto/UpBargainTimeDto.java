package com.epcos.bidding.purchase.bargain.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/7 11:55
 */
@Data
public class UpBargainTimeDto implements Serializable {

    private static final long serialVersionUID = -4968418072183110887L;

    @ApiModelProperty(value = "标段编号")
    private String subpackageCode;

    @ApiModelProperty("倒计时")
    private Integer countdown;

    @ApiModelProperty("服务需求(备注)")
    private String serviceRequire;
}
