package com.epcos.bidding.audit.business.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.audit.domain.dao.AuditPersonDao;
import com.epcos.bidding.audit.business.api.IAuditPersonApi;
import com.epcos.bidding.audit.repository.AuditPersonMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuditPersonService extends ServiceImpl<AuditPersonMapper, AuditPersonDao>
        implements IAuditPersonApi {

}
