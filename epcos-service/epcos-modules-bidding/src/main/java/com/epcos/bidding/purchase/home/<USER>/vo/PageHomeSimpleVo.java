package com.epcos.bidding.purchase.home.domain.vo;

import com.epcos.bidding.purchase.api.params.AttributeVo;
import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/23 15:05
 */
@Data
public class PageHomeSimpleVo extends SuperPackageVo {

    @ApiModelProperty(value = "报名截止时间（公示期）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date registrationEndTime;

    @ApiModelProperty(value = "供应商附加信息")
    private List<AttributeVo> attributeVoList;

}
