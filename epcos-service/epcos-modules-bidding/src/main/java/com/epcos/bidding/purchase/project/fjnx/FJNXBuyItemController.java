package com.epcos.bidding.purchase.project.fjnx;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import com.epcos.bidding.purchase.project.bjxk.BodyMapsUtil;
import com.epcos.bidding.purchase.project.fjnx.business.api.FJNXBuyJoinSubApi;
import com.epcos.bidding.purchase.project.fjnx.domain.dto.FJNXBuyItemQueryDto;
import com.epcos.bidding.purchase.project.fjnx.domain.dto.FJNXCreateBuyItemDto;
import com.epcos.bidding.purchase.project.fjnx.domain.vo.FJNXBuyItemInfoVo;
import com.epcos.bidding.purchase.project.fjnx.domain.vo.FJNXBuyItemPageVo;
import com.epcos.common.core.annotation.Watch;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.web.page.TableDataVo;
import com.epcos.common.log.annotation.Log;
import com.epcos.common.log.enums.BusinessType;
import com.epcos.common.security.annotation.Logical;
import com.epcos.common.security.annotation.RequiresPermissions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/23 13:53
 */
@RequestMapping("/fjnx.buyItem")
@RestController
@Slf4j
@Api(tags = "采购项目与标段--福建省农信版本")
@RequiredArgsConstructor
public class FJNXBuyItemController {

    private final FJNXBuyJoinSubApi fjnxBuyJoinSubApi;

    @ApiOperation("创建采购项目与标段")
    @Log(title = "创建采购项目与标段(FJNX)", businessType = BusinessType.INSERT)
    @PostMapping("/createBuyItem")
    public R<Boolean> createFJNXBuyItem(@ModelAttribute FJNXCreateBuyItemDto dto, HttpServletRequest request) {
        Map<String, List<LinkedHashMap<String, String>>> bodyMap = BodyMapsUtil.parseBodyMaps(request);
        dto.setQuote(bodyMap, dto.getSubpackageDtoList());
        fjnxBuyJoinSubApi.createFJNXBuyItem(dto);

        return R.ok();
    }


    @Watch(name = "buyItemCode")
    @ApiOperation("查询项目列表")
    @RequiresPermissions(value = {"purchaser:project:query", "purchaser:project:queryDept", "purchaser:project:queryAll"}, logical = Logical.OR)
    @PostMapping("/buyItemPage")
    public TableDataVo<FJNXBuyItemPageVo> fjnxBuyItemPage(@RequestBody PageSortEntity<FJNXBuyItemQueryDto> dto) {
        FJNXBuyItemQueryDto entity = dto.getEntity();
//        if (!StringUtils.hasText(entity.getOrgCode())) {
//            return new TableDataVo(Collections.EMPTY_LIST, Collections.EMPTY_LIST.size());
//        }
        entity.defaultValue("purchaser:project:queryAll");
        IPage<FJNXBuyItemPageVo> page = fjnxBuyJoinSubApi.fjnxBuyItemPage(dto);
        List<FJNXBuyItemPageVo> voList = page.getRecords();
        return new TableDataVo(voList, page.getTotal());
    }

    @ApiOperation("查询项目详细信息")
    @GetMapping("/queryBuyItemInfo")
    @ApiImplicitParam(name = "buyItemCode", value = "采购项目code", paramType = "query", dataType = "String", required = true)
    public R<FJNXBuyItemInfoVo> queryFJNXBuyItemInfo(@RequestParam(value = "buyItemCode") @NotBlank String buyItemCode) {
        return R.ok(fjnxBuyJoinSubApi.queryFJNXBuyItemInfo(buyItemCode));
    }

    @ApiOperation("查询流程编号")
    @GetMapping("/getInnerCode")
    @ApiImplicitParam(name = "buyItemCode", value = "采购项目code", paramType = "query", dataType = "String", required = true)
    public R<String> getInnerCode(@RequestParam(value = "buyItemCode") @NotBlank String buyItemCode) {
        return R.ok(fjnxBuyJoinSubApi.getInnerCode(buyItemCode));
    }

    @ApiOperation("根据项目code查询包信息")
    @GetMapping("/subInfo")
    @ApiImplicitParam(name = "buyItemCode", value = "采购项目code", paramType = "query", dataType = "String", required = true)
    public R<List<SuperPackageVo>> subInfo(@RequestParam(value = "buyItemCode") @NotBlank String buyItemCode) {
        return R.ok(fjnxBuyJoinSubApi.findByBuyItemCode(buyItemCode));
    }

    @ApiOperation("修改项目详细信息")
    @Log(title = "修改项目详细信息(FJNX)", businessType = BusinessType.UPDATE)
    @PostMapping("/updateBuyItemInfo")
    public R<Boolean> updateFJNXBuyItemInfo(@ModelAttribute FJNXCreateBuyItemDto dto, HttpServletRequest request) {
        Map<String, List<LinkedHashMap<String, String>>> bodyMap = BodyMapsUtil.parseBodyMaps(request);
        dto.setQuote(bodyMap, dto.getSubpackageDtoList());
        fjnxBuyJoinSubApi.updateFJNXBuyItemInfo(dto);
        return R.ok();
    }

    @ApiOperation(value = "删除项目信息")
    @Log(title = "删除项目信息(FJNX)", businessType = BusinessType.DELETE)
    @RequiresPermissions(value = {"purchaser:project:remove"})
    @GetMapping(value = "/delBuyItemInfo")
    public R<Boolean> delFJNXBuyItemInfo(@RequestParam(value = "buyItemCode") @NotBlank String buyItemCode) {
        return R.ok(fjnxBuyJoinSubApi.delFJNXBuyItemInfo(buyItemCode));
    }
}
