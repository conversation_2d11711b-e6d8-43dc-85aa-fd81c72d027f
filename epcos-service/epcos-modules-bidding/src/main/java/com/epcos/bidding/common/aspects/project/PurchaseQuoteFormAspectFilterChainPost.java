package com.epcos.bidding.common.aspects.project;

import com.epcos.bidding.common.annotaion.GetPurchaseQuoteForm;
import com.epcos.bidding.common.aspects.AbstractMethod;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.purchase.api.params.PurchaseQuoteFormVo;
import com.epcos.bidding.purchase.claims.business.api.IClaimsFileQuoteFormApi;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Aspect
public class PurchaseQuoteFormAspectFilterChainPost extends AbstractMethod<GetPurchaseQuoteForm, PurchaseQuoteFormVo> {

    public PurchaseQuoteFormAspectFilterChainPost() {
        super(GetUtil.GET_PURCHASER_QUOTE_FORM, "获取采购人报价表");
    }

    @Autowired
    private IClaimsFileQuoteFormApi claimsFileQuoteFormApi;


    @Override
    public PurchaseQuoteFormVo businessMethods(JoinPoint point, GetPurchaseQuoteForm annotation) {
        String subpackageCode = threadLocal.get().getSubpackageCode();
        return claimsFileQuoteFormApi.query(subpackageCode);
    }

    @Override
    @Around("@annotation(getPurchaseQuoteForm)")
    public Object around(ProceedingJoinPoint point, GetPurchaseQuoteForm getPurchaseQuoteForm) {
        return super.around(point, getPurchaseQuoteForm);
    }

}
