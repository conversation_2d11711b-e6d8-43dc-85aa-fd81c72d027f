package com.epcos.bidding.common.annotaion;

import org.springframework.core.annotation.Order;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 获取供应商企业信息
 * return SupplierCompanyVO
 *
 * <AUTHOR>
 */
@Order(190)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface GetSupplier {

    GetCommon common() default @GetCommon;

    /**
     * 使用el获取
     */
    String supplierIdEL() default "";

    /**
     * 组织 code
     */
    String orgCodeEl() default "";

    /**
     * 批量获取
     */
    boolean batch() default false;


}
