package com.epcos.bidding.purchase.project.base.domain.vo;

import com.epcos.bidding.purchase.api.params.vo.bulletin.TimeNodeVo;
import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/15 11:14
 */
@Data
public class SubPackageTimeVo extends SuperPackageVo {

    private static final long serialVersionUID = -7802539731746383653L;

    @ApiModelProperty(value = "是否终止采购 [0-正常，1-终止] 默认为0")
    private String abandon;

    @ApiModelProperty(value = "标签信息")
    private List<LabelJson> jsonList;

    @ApiModelProperty(value = "时间节点信息")
    private List<TimeNodeVo> timeNodeList;
}
