package com.epcos.bidding.purchase.process.business.api;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.IBaseService;
import com.epcos.bidding.purchase.api.params.dto.AskDto;
import com.epcos.bidding.purchase.api.params.dto.ReplyDto;
import com.epcos.bidding.purchase.api.params.vo.answer.AnswerVo;
import com.epcos.bidding.purchase.process.domain.dao.AskAnswerDao;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/28 9:57
 */
public interface IAskAnswerApi extends IBaseService<AskAnswerDao> {

    @Override
    default LambdaQueryWrapper<AskAnswerDao> queryWrapper(AskAnswerDao dao) {
        LambdaQueryWrapper<AskAnswerDao> query = Wrappers.lambdaQuery(AskAnswerDao.class);
        if (Objects.nonNull(dao.getId())) {
            return query.eq(AskAnswerDao::getId, dao.getId());
        }
        return query
                .eq(StringUtils.hasText(dao.getSubpackageCode()), AskAnswerDao::getSubpackageCode, dao.getSubpackageCode())
                .eq(StringUtils.hasText(dao.getAskUserType()), AskAnswerDao::getAskUserType, dao.getAskUserType())
                .eq(Objects.nonNull(dao.getAskUserId()), AskAnswerDao::getAskUserId, dao.getAskUserId())
                .like(StringUtils.hasText(dao.getAskUserName()), AskAnswerDao::getAskUserName, dao.getAskUserName())
                .eq(Objects.nonNull(dao.getAnswerUserId()), AskAnswerDao::getAnswerUserId, dao.getAnswerUserId())
                .like(StringUtils.hasText(dao.getAnswerUserName()), AskAnswerDao::getAnswerUserName, dao.getAnswerUserName())
                .eq(StringUtils.hasText(dao.getAnswerUserType()), AskAnswerDao::getAnswerUserType, dao.getAnswerUserType())
                .like(StringUtils.hasText(dao.getAskContent()), AskAnswerDao::getAskContent, dao.getAskContent())
                .like(StringUtils.hasText(dao.getAnswerContent()), AskAnswerDao::getAnswerContent, dao.getAnswerContent())
                .orderByDesc(AskAnswerDao::getId);
    }

    default LambdaQueryWrapper<AskAnswerDao> queryWrapper(String subpackageCode, Long askUserId) {
        AskAnswerDao askAnswerDao = new AskAnswerDao();
        askAnswerDao.setSubpackageCode(subpackageCode);
        askAnswerDao.setAskUserId(askUserId);
        return queryWrapper(askAnswerDao);
    }

    default LambdaQueryWrapper<AskAnswerDao> queryWrapper(String subpackageCode, Long askUserId, Long answerUserId, List<String> askUserTypeList) {
        return Wrappers.lambdaQuery(AskAnswerDao.class)
                .eq(StringUtils.hasText(subpackageCode), AskAnswerDao::getSubpackageCode, subpackageCode)
                .in(CollectionUtils.isEmpty(askUserTypeList), AskAnswerDao::getAskUserType, askUserTypeList)
                .eq(Objects.nonNull(askUserId), AskAnswerDao::getAskUserId, askUserId)
                .eq(Objects.nonNull(answerUserId), AskAnswerDao::getAnswerUserId, answerUserId)
                .orderByDesc(AskAnswerDao::getId);
    }

    default List<AskAnswerDao> find(String subpackageCode, Long askUserId) {
        return list(queryWrapper(subpackageCode, askUserId));
    }

    default List<AskAnswerDao> find(String subpackageCode, Long askUserId, Long answerUserId, List<String> askUserTypeList) {
        return list(queryWrapper(subpackageCode, askUserId, answerUserId, askUserTypeList));
    }

    /**
     * 提交答疑疑问内容
     *
     * @param dto
     * @return
     */
    Boolean submitAsk(AskDto dto);

    /**
     * 提交答疑回复内容
     * 这个是对原有记录进行修改
     * 不做新增操作
     *
     * @param dto
     * @return
     */
    Boolean replyAsk(ReplyDto dto);

    /**
     * 评委查询 答疑信息
     *
     * @param subpackageCode
     * @param userId
     * @return
     */
//    List<AnswerVo> judgeAnswerList(String subpackageCode, Long userId);

    /**
     * 查询答疑记录
     * 供应商，采购人，评委均通过此接口进行查询
     *
     * @param subpackageCode 包编号
     * @param userId         用户id
     * @return1
     */
    List<AnswerVo> answerList(String subpackageCode, Long userId, String askUserType);
}
