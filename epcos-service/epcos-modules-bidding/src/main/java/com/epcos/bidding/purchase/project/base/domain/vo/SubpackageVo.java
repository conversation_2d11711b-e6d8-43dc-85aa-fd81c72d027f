package com.epcos.bidding.purchase.project.base.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 标段包名
 */
@Data
public class SubpackageVo implements Serializable {
    private static final long serialVersionUID = 897290254265603553L;

    // 包名编号
    private String subpackageCode;

    // 标段包名
    private String subpackageName;

    // 招标公告发布时间
    private Date tenderPushTime;

    // 报名截止期
    private Date registrationEndTime;

    // 投标截止期
    private Date responseFileEndTime;

    // 开标时间
    private Date meetingTime;

    // 结果公示时间
    private Date winPushTime;

    // 评委姓名与证件号
    private String judgeNameAndIdNumber;

    // 报名投标供应商
    private List<SignUpBidderVo> signUpBidderVoList;

}
