package com.epcos.bidding.purchase.report.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 发起调研报告dto
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/29 14:03
 */
@Data
public class InitReportDto implements Serializable {

    private static final long serialVersionUID = 4125569049967287971L;

    @ApiModelProperty(value = "标段code")
    private String subpackageCode;

    @ApiModelProperty(value = "调研报告文本html")
    private String researchReportText;

}
