package com.epcos.bidding.purchase.ev.factory;

import com.epcos.bidding.purchase.ev.EvService;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.EvUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class EvFactory {

    private final Map<String, EvService> serviceMap = new HashMap<>();

    private static EvFactory instance;

    public EvFactory(Map<String, EvService> evServiceMap) {

        instance = this;

        for (Map.Entry<String, EvService> entry : evServiceMap.entrySet()) {
            EvService service = entry.getValue();
            Class<?> serviceClass = service.getClass();

            if (serviceClass.getName().contains("$$")) {
                serviceClass = serviceClass.getSuperclass();
            }

            Service annotation = serviceClass.getAnnotation(Service.class);
            if (annotation != null) {
                serviceMap.put(annotation.value(), service);
            } else {
                log.error("EvService实现类 {} 缺少 @Service 注解", serviceClass.getName());
            }
        }
    }

    /**
     * 静态方法：获取对应环境的采购项信息服务
     */
    public static EvService getInstance() {
        if (instance == null) {
            throw new ServiceException("EvFactory未初始化");
        }
        return instance.getService();
    }

    /**
     * 获取对应环境的采购项信息服务
     */
    public EvService getService() {
        String ev = EvUtils.ev();
        EvService service = serviceMap.get(ev);
        if (service == null) {
            throw new ServiceException("不支持的环境: " + ev);
        }
        return service;
    }
}