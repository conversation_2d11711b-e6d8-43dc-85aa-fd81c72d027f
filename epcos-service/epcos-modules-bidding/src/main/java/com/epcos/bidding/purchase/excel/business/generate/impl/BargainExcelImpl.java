package com.epcos.bidding.purchase.excel.business.generate.impl;

import cn.hutool.core.map.MapUtil;
import com.epcos.bidding.common.utils.AttributeUtil;
import com.epcos.bidding.common.utils.BiddingBaseUtil;
import com.epcos.bidding.common.utils.excel.ExcelStyleUtil;
import com.epcos.bidding.common.utils.excel.PoiExcelUtil;
import com.epcos.bidding.purchase.api.params.AttributeVo;
import com.epcos.bidding.purchase.excel.business.generate.AbGenerateExcel;
import com.epcos.bidding.purchase.excel.domain.Excel;
import com.epcos.bidding.purchase.excel.domain.vo.bargain.BargainPackageExcelVo;
import com.epcos.bidding.supplier.api.params.SupplierQuoteFormVo;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.epcos.bidding.purchase.excel.constans.ExcelValues.SUPPLIER_BARGAIN_MAP;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/6 9:17
 */
public class BargainExcelImpl extends AbGenerateExcel {


    @Override
    protected void generateHead(XSSFSheet sheet, CellStyle cellStyle, List<AttributeVo> headList) {
        final List<String> signList = new ArrayList<>(this.getColumnSize(headList).keySet());
        XSSFRow row = sheet.createRow(sheet.getPhysicalNumberOfRows());
        for (int i = 0; i < signList.size(); i++) {
            PoiExcelUtil.createCell(row, i, cellStyle, signList.get(i));
        }
    }

    @Override
    protected void generateBody(XSSFSheet sheet, CellStyle cellStyle, Excel vo) {
        BargainPackageExcelVo excelVo = (BargainPackageExcelVo) vo;
        Map<String, String> headMap = this.getColumnSize(vo.getHeads());
        List<String> valueList = new ArrayList<>(headMap.values());

        List<SupplierQuoteFormVo> supplierQuoteFormList = excelVo.getSupplierQuoteFormList();
        for (int i = 0; i < supplierQuoteFormList.size(); i++) {
            int startRow = sheet.getPhysicalNumberOfRows();
            SupplierQuoteFormVo supplierQuoteFormVo = supplierQuoteFormList.get(i);
            if (Objects.isNull(supplierQuoteFormVo) || CollectionUtils.isEmpty(supplierQuoteFormVo.getRoundQuoteFormList())) {
                continue;
            }
            List<LinkedHashMap<String, String>> bodyMaps = supplierQuoteFormVo.getRoundQuoteFormList().get(0).getBodyMaps();
            for (int j = 0; j < bodyMaps.size(); j++) {
                XSSFRow row = sheet.createRow(sheet.getPhysicalNumberOfRows());
                row.setHeightInPoints(PoiExcelUtil.DEFAULT_ROW_HEIGHT + 13);
                int[] column = {(valueList.size() - (vo.getHeads().size()))};
                fillOtherData(row, column, cellStyle, excelVo, supplierQuoteFormVo, valueList, i);
                LinkedHashMap<String, String> bodyMap = bodyMaps.get(j);
                if (!CollectionUtils.isEmpty(vo.getHeads())) {
                    fillQuoteData(row, column, cellStyle, excelVo, bodyMap);
                }
            }
            mergeCell(3, 3, sheet, startRow);
        }
        mergeCell(0, 2, sheet, 2);
    }

    private void fillOtherData(XSSFRow row, int[] column, CellStyle cellStyle, BargainPackageExcelVo excelVo,
                               SupplierQuoteFormVo supplierQuoteFormVo, List<String> valueList, int i) {
        for (int k = 0; k < column[0]; k++) {
            if (k == 0) {
                PoiExcelUtil.createCell(row, 0, cellStyle, String.valueOf(i + 1));
            } else if (k == 1) {
                String values = valueList.get(k);
                String objValue = BiddingBaseUtil.getObjValue(excelVo, values);
                PoiExcelUtil.createCell(row, k, cellStyle, objValue);
            } else if (k == 2) {
                String values = valueList.get(k);
                String objValue = BiddingBaseUtil.getObjValue(excelVo, values);
                PoiExcelUtil.createCell(row, k, cellStyle, objValue);
            } else if (k == 3) {
                String values = valueList.get(k);
                String objValue = BiddingBaseUtil.getObjValue(supplierQuoteFormVo, values);
                PoiExcelUtil.createCell(row, k, cellStyle, objValue);
            }
        }
    }

    private void fillQuoteData(XSSFRow row, int[] column, CellStyle cellStyle, BargainPackageExcelVo vo, LinkedHashMap<String, String> bodyMap) {
        if (MapUtil.isEmpty(bodyMap)) {
            vo.getHeads().forEach(h -> PoiExcelUtil.createCell(row, column[0]++, cellStyle, "/"));
        } else {
            Map<String, String> map = vo.getHeads().stream().collect(Collectors.toMap(AttributeVo::getKeyVal,
                    AttributeVo::getKeyName, (v1, v2) -> v1, LinkedHashMap::new));
            map.forEach((ke, va) -> map.put(ke, "/"));
            map.forEach((ke, va) -> map.put(ke, bodyMap.get(ke)));
            map.forEach((key, value) -> {
                AttributeVo attributeVO = vo.getHeads().stream().filter(f -> Objects.equals(key, f.getKeyVal())).findAny().orElseGet(AttributeVo::new);
                String nv = AttributeUtil.replaceFileTypeField(value, attributeVO);
                PoiExcelUtil.createCell(row, column[0]++, cellStyle, nv);
            });
        }
    }

    /**
     * 合并单元格
     *
     * @param start      开始合并的列
     * @param end        最后一个合并的列
     * @param sheet      单元格
     * @param currentRow 当前行
     */
    private void mergeCell(int start, int end, XSSFSheet sheet, int currentRow) {
        if (currentRow < sheet.getLastRowNum()) {
            for (int i = start; i <= end; i++) {
                ExcelStyleUtil.mergeCell(sheet, currentRow, sheet.getLastRowNum(), i, i);
            }
        }
    }

    @Override
    protected String getFileName(String fileName) {
        return "";
    }

    @Override
    protected String getSheetName(Excel vo) {
        BargainPackageExcelVo excelVo = (BargainPackageExcelVo) vo;
        return excelVo.getSubpackageName();
    }

    @Override
    protected String getTitleName() {
        return "供应商报价信息表";
    }

    @Override
    protected Map<String, String> getColumnSize(List<AttributeVo> headList) {
        Map<String, String> headMap = new LinkedHashMap<>(SUPPLIER_BARGAIN_MAP);
        if (!CollectionUtils.isEmpty(headList)) {
            headList.forEach(h -> headMap.put(h.getKeyName(), h.getKeyVal()));
        }
        return headMap;
    }
}
