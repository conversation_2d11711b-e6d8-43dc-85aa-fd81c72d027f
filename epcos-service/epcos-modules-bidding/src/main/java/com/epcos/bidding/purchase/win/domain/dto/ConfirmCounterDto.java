package com.epcos.bidding.purchase.win.domain.dto;

import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/6 15:53
 */
@Data
public class ConfirmCounterDto extends SuperPackageVo {

    private static final long serialVersionUID = 7695014579275046704L;

    @ApiModelProperty(value = "科室代表评委id")
    @NotNull
    private Long deptJudgeId;

    /**
     * 没有议价功能则传2，此数据评委需要，采购人无用
     */
    @ApiModelProperty(value = "议价人[0-采购人议价，1-评委议价, 2-没有议价]")
    private String isBargaining;
}
