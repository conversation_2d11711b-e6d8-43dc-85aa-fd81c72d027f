package com.epcos.bidding.common.utils;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * 客户端文件
 */
@Data
public final class BidFileJson implements Serializable {
    private static final long serialVersionUID = 5317987711302241130L;

    @Valid
    @NotEmpty(message = "【目录及子目录】不能为空")
    @ApiModelProperty("目录及子目录")
    private List<MenuData> menuData;

//    @ApiModelProperty("quoteSheetType  0 没有报价表  2 有报价表")
//    private Integer quoteSheetType;

    @NotNull(message = "报价表单，表头")
    @Valid
    @ApiModelProperty("报价表单，表头")
    private List<QuoteSheetHeaders> quoteSheetHeaders;

    @NotNull(message = "报价表单，内容")
    @ApiModelProperty("报价表单，内容")
    private List<LinkedHashMap<String, String>> quoteSheet;

    @Valid
    @NotNull(message = "【评审方法】不能为空")
    @ApiModelProperty("评审方法")
    private EvaluationMethod evaluationMethod;

    @Length(max = 100, message = "app名字最长100个字符")
    @NotBlank(message = "app名字不能为空")
    @ApiModelProperty("app名字")
    private String appName;

    @Length(max = 100, message = "版本最长100个字符")
    @NotBlank(message = "版本不能为空")
    @ApiModelProperty("版本")
    private String version;

    @Length(max = 100, message = "文件验证key,最长100个字符")
    @NotBlank(message = "文件验证key,不能为空")
    @ApiModelProperty("文件验证key")
    private String zepcKey;


}
