package com.epcos.bidding.purchase.process.business.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.api.params.dto.BaseQueryDto;
import com.epcos.bidding.purchase.ev.factory.EvFactory;
import com.epcos.bidding.purchase.process.business.api.IProcessApi;
import com.epcos.bidding.purchase.process.domain.dto.MeetDto;
import com.epcos.bidding.purchase.process.domain.vo.BuyItemFilePageVo;
import com.epcos.bidding.purchase.process.domain.vo.BuyItemMeetVo;
import com.epcos.bidding.purchase.process.domain.vo.SupplierInfoVo;
import com.epcos.bidding.purchase.process.repository.PurchaseJoinMapper;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.xyzy.domain.dto.XYZYBuyItemQueryDto;
import com.epcos.bidding.purchase.remote.RemoteSupplierApi;
import com.epcos.bidding.supplier.api.params.MultiSupplierQuoteFormVo;
import com.epcos.bidding.supplier.api.params.RoundQuoteFormVo;
import com.epcos.bidding.supplier.api.params.SupplierSignUpVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/3 10:11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProcessApi implements IProcessApi {

    private final PurchaseJoinMapper purchaseJoinMapper;
    private RemoteSupplierApi remoteSupplierApi;

    @Autowired
    public void setRemoteSupplierApi(RemoteSupplierApi remoteSupplierApi) {
        this.remoteSupplierApi = remoteSupplierApi;
    }

    public List<BuyItemDao> queryBuyItemDaoList(BaseQueryDto dto) {
        String tableName = EvFactory.getInstance().getTableName();
        return purchaseJoinMapper.selectItemByTable(tableName, dto);
    }

    @Override
    public IPage<BuyItemMeetVo> meetingPage(PageSortEntity<MeetDto> dto) {

        MeetDto entity = dto.getEntity();
        String tableName = EvFactory.getInstance().getTableName();
        IPage<BuyItemMeetVo> page = purchaseJoinMapper.meetingPage(
                Page.of(dto.getPageNum(), dto.getPageSize()),
                tableName,
                entity
        );
        List<BuyItemMeetVo> voList = page.getRecords();

        voList.forEach(v -> {
            List<SupplierSignUpVo> signUpList = remoteSupplierApi.getSignUp(v.getSubpackageCode());
            List<SupplierInfoVo> supplierList = signUpList.stream().map(signUpVo -> {
                SupplierInfoVo vo = new SupplierInfoVo();
                BeanUtils.copyProperties(signUpVo.getSupplierSignUpInfo(), vo);
                vo.setSupplierId(signUpVo.getSupplierId());
                vo.setSupplierCompanyName(signUpVo.getSupplierSignUpInfo().getBidderName());
                return vo;
            }).collect(Collectors.toList());
            v.setSupplierInfoVoList(supplierList);
        });

        Map<String, List<BuyItemMeetVo>> voMap = voList.stream().collect(Collectors.groupingBy(BuyItemMeetVo::getSubpackageCode));
        List<MultiSupplierQuoteFormVo> bargainInfoVoList = remoteSupplierApi.getBargainInfo(voMap);
        bargainInfoVoList.forEach(bargain -> voList.forEach(vo -> {
            List<SupplierInfoVo> supplierInfoVoList = vo.getSupplierInfoVoList();
            if (vo.getSubpackageCode().equals(bargain.getSubpackageCode())) {
                vo.setHeads(bargain.getHeads());
                supplierInfoVoList.forEach(supplierInfo -> bargain.getSupplierQuoteFormList().forEach(quote -> {
                    if (supplierInfo.getSupplierId().equals(quote.getSupplierId()) && !CollectionUtils.isEmpty(quote.getRoundQuoteFormList())) {
                        RoundQuoteFormVo roundQuoteFormVo = quote.getRoundQuoteFormList().get(quote.getRoundQuoteFormList().size() - 1);
                        supplierInfo.setBodyMaps(roundQuoteFormVo.getBodyMaps());
                    }
                }));
            }
        }));
        return page;
    }

    /**
     * 查询采购项目列表
     *
     * @param dto 条件
     * @return
     */
    @Override
    public IPage<BuyItemFilePageVo> buyItemFilePage(PageSortEntity<XYZYBuyItemQueryDto> dto) {

        XYZYBuyItemQueryDto entity = dto.getEntity();
        entity.defaultValue("project:archive:queryAll");
        String tableName = EvFactory.getInstance().getTableName();
        IPage<BuyItemFilePageVo> page = purchaseJoinMapper.buyItemFilePage(
                Page.of(dto.getPageNum(), dto.getPageSize()),
                tableName,
                entity
        );
        page.getRecords().forEach(p -> {
            Map<String, String> buyItemMap = EvFactory.getInstance().queryBuyItemMap(p.getBuyItemCode());
            p.setUseDept(buyItemMap.get("useDept"));
            p.setBuyClass(buyItemMap.get("buyClass"));
            p.setInnerCode(buyItemMap.get("innerCode"));
        });
        return page;
    }
}
