package com.epcos.bidding.common.aspects.filter.validator;

import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierBiddingDao;

import java.util.Optional;

/**
 * 响应文件已发布
 */
public class SupplierBiddingHasPublishedValidator implements ResultPostHandlerFilterChain<SupplierBiddingDao> {

    @Override
    public void postHandler(AspectContext context, SupplierBiddingDao result) {
        Optional.ofNullable(result)
                .ifPresent(SupplierBiddingDao::verifyPublished);
    }

}
