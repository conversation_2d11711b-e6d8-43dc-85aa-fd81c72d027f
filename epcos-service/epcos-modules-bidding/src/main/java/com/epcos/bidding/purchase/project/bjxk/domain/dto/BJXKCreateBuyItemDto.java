package com.epcos.bidding.purchase.project.bjxk.domain.dto;

import com.epcos.bidding.purchase.api.params.dto.SubPackageBaseDto;
import com.epcos.bidding.purchase.project.base.domain.dto.BuyItemBaseDto;
import com.epcos.common.core.annotation.valid.Add;
import com.epcos.common.core.annotation.valid.Up;
import com.epcos.dingtalk.domain.dto.AttachFileDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/23 14:01
 */
@Data
@ApiModel(description = "北京胸科医院版本")
public class BJXKCreateBuyItemDto extends BuyItemBaseDto {

    @ApiModelProperty(value = "钉钉表id")
    private Long dingTalkId;

    @ApiModelProperty(value = "申请人")
    @NotBlank(message = "申请人【不能为空】")
    @Length(max = 32, message = "申请人【32位字符】")
    private String apply;

    @ApiModelProperty(value = "申请科室")
    @NotBlank(message = "申请科室【不能为空】")
    @Length(max = 32, message = "申请科室【32位字符】")
    private String applyDept;

    @ApiModelProperty(value = "预算号/课题号")
    @NotBlank(message = "预算号/课题号【不能为空】")
    @Length(max = 100, message = "预算号/课题号【100位字符】")
    private String budgetNumber;

    @ApiModelProperty(value = "采购目的")
    @NotBlank(message = "采购目的【不能为空】")
    @Length(max = 100, message = "采购目的【100位字符】")
    private String buyPurpose;

    @ApiModelProperty(value = "采购类型: 务货物/服")
    @NotBlank(message = "采购类型 货物/服【不能为空】")
    @Length(max = 100, message = "采购类型：【100位字符】")
    private String buyClass;

    @ApiModelProperty(value = "采购标的")
    @NotBlank(message = "采购标的【不能为空】")
    @Length(max = 32, message = "采购标的【32位字符】")
    private String procurementType;

    @ApiModelProperty(value = "货物采购类型：医用设备采购/医用设备维修配件及维修服务采购/信息类办公设备及维修配件采购/其他")
    @NotBlank(message = "货物采购类型【不能为空】")
    @Length(max = 32, message = "货物采购类型【32位字符】")
    private String goodsType;

    @ApiModelProperty(value = "详细参数附件")
    private List<AttachFileDto> paramsAttList;

    @ApiModelProperty(value = "是否超过5万")
    @NotBlank(message = "是否超过5万【不能为空】")
    @Length(max = 32, message = "是否超过5万【32位字符】")
    private String middleAmount;

    @ApiModelProperty(value = "预算总金额（元）")
    @NotNull(message = "预算总金额（元）【不能为空】")
    @Digits(integer = 18, fraction = 5, message = "预算总金额（元）【18位整数，5位小数】")
    private BigDecimal buyBudget;

    @ApiModelProperty(value = "是否涉及大额资金（20万元以上）")
    @NotBlank(message = "是否涉及大额资金【不能为空】")
    @Length(max = 32, message = "是否涉及大额资金【32位字符】")
    private String largeAmount;

    @ApiModelProperty(value = "资金来源：研究所/医院/工会")
    @NotBlank(message = "资金来源【不能为空】")
    @Length(max = 32, message = "资金来源【32位字符】")
    private String capitalSource;

    @ApiModelProperty(value = "预算类型：财政项目/科研项目/自有资金")
    @NotBlank(message = "预算类型：【不能为空】")
    @Length(max = 32, message = "预算类型：【32位字符】")
    private String budgetType;

    @ApiModelProperty(value = "项目/课题负责人")
    @NotBlank(message = "项目/课题负责人：【不能为空】")
    @Length(max = 32, message = "项目/课题负责人：【32位字符】")
    private String buyPerson;

    @ApiModelProperty(value = "课题号")
    @NotBlank(message = "课题号【不能为空】")
    @Length(max = 32, message = "课题号【32位字符】")
    private String classNum;

    @ApiModelProperty(value = "院长会议纪要文件")
    private List<AttachFileDto> meetingAttList;

    @ApiModelProperty(value = "备注")
    @Length(max = 128, message = "院长办公纪要【128位字符】")
    private String buyRemark;

    @ApiModelProperty(value = "标段信息")
    @NotEmpty(groups = {Add.class, Up.class}, message = "标段信息 不能为空")
    private List<SubPackageBaseDto> subpackageDtoList;
}
