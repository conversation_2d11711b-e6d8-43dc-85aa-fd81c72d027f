package com.epcos.bidding.purchase.goods.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.epcos.bidding.purchase.goods.domain.dao.GoodsCartDao;
import com.epcos.bidding.purchase.goods.domain.vo.GoodsCartVo;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/12 17:28
 */
public interface IGoodsCartMapper extends BaseMapper<GoodsCartDao> {


    Page<GoodsCartVo> selectPageBy(IPage page, @Param("entity") GoodsCartDao entity);
}
