package com.epcos.bidding.purchase.win.domain.dto;

import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/10 14:46
 */
@Data
public class ConfirmReviewDto extends SuperPackageVo {

    private static final long serialVersionUID = -1961564058272933502L;

    @ApiModelProperty(value = "是否线上评审 : 1-是  ， 0-不评审")
    private Integer isReview;

    @ApiModelProperty(value = "组长id")
    private Long groupId;
}
