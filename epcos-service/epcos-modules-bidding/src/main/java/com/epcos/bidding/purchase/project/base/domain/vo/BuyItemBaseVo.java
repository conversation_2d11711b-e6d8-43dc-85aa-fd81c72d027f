package com.epcos.bidding.purchase.project.base.domain.vo;

import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperBuyItemVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 通用返回字段
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/2 10:03
 */
@Data
public class BuyItemBaseVo extends SuperBuyItemVo {

    @ApiModelProperty(value = "院内项目编号")
    private String innerCode;

    @ApiModelProperty(value = "招标项目创建时间的年月")
    private String yearMonthSplit;

    @ApiModelProperty(value = "备注")
    private String buyRemark;

    @ApiModelProperty(value = "采购方式表Code")
    private String purchaseMethodCode;

    @ApiModelProperty(value = "采购方式表Type")
    private String purchaseMethodType;

    @ApiModelProperty(value = "组织code")
    private String orgCode;

    @ApiModelProperty(value = "项目创建时间")
    private Date createAt;

    @ApiModelProperty(value = "完成并归档[0-未完成,1-已完成，未归档,2-已归档]")
    private Integer end;

    @ApiModelProperty(value = "部门id")
    private Long deptId;
}
