package com.epcos.bidding.common;

import java.util.List;

/**
 * 通过转换
 */
public interface GenericMapper<ENTITY, CREATE, UPDATE, QUERY, VO> {

    ENTITY createAsEntity(CREATE create);

    ENTITY updateAsEntity(UPDATE update);

    ENTITY queryAsEntity(QUERY query);

    VO asVo(ENTITY entity);

    List<ENTITY> createAsEntityList(List<CREATE> createList);

    List<VO> asVoList(List<ENTITY> entityList);


}