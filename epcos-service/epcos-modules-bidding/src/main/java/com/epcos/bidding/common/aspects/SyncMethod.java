package com.epcos.bidding.common.aspects;

import com.epcos.common.core.utils.SpringUtils;
import org.aspectj.lang.JoinPoint;
import org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.lang.annotation.Annotation;

/**
 * <AUTHOR>
 */
public interface SyncMethod<T extends Annotation, R> {

    ThreadPoolTaskExecutor EXECUTOR =
            SpringUtils.getBean(TaskExecutionAutoConfiguration.APPLICATION_TASK_EXECUTOR_BEAN_NAME);

    /**
     * 业务方法
     *
     * @param point      JoinPoint对象，表示方法调用的点
     * @param annotation T类型的注解对象，表示方法上的注解
     * @return 返回值类型R
     */
    R businessMethods(JoinPoint point, T annotation);

}
