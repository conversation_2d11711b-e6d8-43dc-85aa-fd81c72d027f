package com.epcos.bidding.purchase.extract.business.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.extract.business.api.IExtractJudgeExternalApi;
import com.epcos.bidding.purchase.extract.domain.dao.ExtractJudgeExternalDao;
import com.epcos.bidding.purchase.extract.domain.dao.ExtractJudgeInnerDao;
import com.epcos.bidding.purchase.extract.domain.dto.ExtractNameDto;
import com.epcos.bidding.purchase.extract.domain.dto.ExtractQueryDto;
import com.epcos.bidding.purchase.extract.domain.dto.MarkJudgeLogDto;
import com.epcos.bidding.purchase.extract.domain.vo.ExtractLogVo;
import com.epcos.bidding.purchase.extract.domain.vo.JudgeListVo;
import com.epcos.bidding.purchase.extract.mapping.ExtractJudgeExternalConvert;
import com.epcos.bidding.purchase.extract.repository.IExtractJudgeExternalMapper;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.purchase.project.mapping.ExtractJudgeConvert;
import com.epcos.bidding.purchase.remote.RemoteToOtherServiceApi;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.DateUtils;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.web.domain.user.SysUser;
import com.epcos.common.core.web.page.TableDataVo;
import com.epcos.system.api.domain.dto.AgainExtractDto;
import com.epcos.system.api.domain.dto.ExtractJudgeInfoDto;
import com.epcos.system.api.domain.dto.JudgeConditionsDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/25 14:26
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ExtractJudgeExternalService extends ServiceImpl<IExtractJudgeExternalMapper, ExtractJudgeExternalDao> implements IExtractJudgeExternalApi {

    private final IBuyItemApi buyItemApi;
    private final ISubPackageApi subpackageApi;
    private final RemoteToOtherServiceApi remoteToOtherServiceApi;
    private final IExtractJudgeExternalMapper extractJudgeExternalMapper;

    @Override
    public List<String> queryProjectName(String buyItemName) {
        List<BuyItemDao> buyItemDaoList = buyItemApi.findByName(buyItemName);
        if (CollectionUtils.isEmpty(buyItemDaoList)) {
            return Collections.EMPTY_LIST;
        }
        Map<String, BuyItemDao> daoMap = buyItemDaoList.stream().collect(Collectors.toMap(BuyItemDao::getBuyItemCode, Function.identity()));
        List<String> buyItemCodeList = buyItemDaoList.stream().map(BuyItemDao::getBuyItemCode).collect(Collectors.toList());
        List<SubpackageDao> subpackageDaoList = subpackageApi.packageListInfo(buyItemCodeList);
        List<String> strVoList = subpackageDaoList.stream().map(sub -> {
            BuyItemDao buyItemDao = daoMap.get(sub.getBuyItemCode());
            String strVo = buyItemDao.getBuyItemName() + "-" + buyItemDao.getBuyItemCode() + "-" + sub.getSubpackageName();
            return strVo;
        }).collect(Collectors.toList());
        return strVoList;
    }

    @Override
    public List<ExtractLogVo> extract(JudgeConditionsDto dto) {
        dto.checkConditions();
        List<SysUser> userList = remoteToOtherServiceApi.getJudgesInfo(dto);
        if (CollectionUtils.isEmpty(userList)) {
            log.error("当前条件没有专家");
            throw new ServiceException("当前条件没有专家");
        }
        List<ExtractLogVo> voList = userList.stream()
                .map(s -> ExtractJudgeConvert.INSTANCE.convert(s)).collect(Collectors.toList());
        return voList;
    }

    @Override
    public List<ExtractLogVo> againExtract(AgainExtractDto dto) {
        List<SysUser> userList = remoteToOtherServiceApi.againJudge(dto);
        if (CollectionUtils.isEmpty(userList)) {
            return Collections.EMPTY_LIST;
        }
        List<ExtractLogVo> voList = userList.stream()
                .map(s -> ExtractJudgeConvert.INSTANCE.convert(s)).collect(Collectors.toList());
        return voList;
    }

    @Override
    public List<ExtractLogVo> queryJudgeInfo(ExtractJudgeInfoDto dto) {
        List<ExtractJudgeExternalDao> extarctList = findByExtractLongNameAndJudgeIdIn(
                dto.getExtractLogName(), dto.getJudgeIdList());
        if (CollectionUtils.isEmpty(extarctList)) {
            return Collections.EMPTY_LIST;
        }
        List<Long> judgeIdList = extarctList.stream().map(ExtractJudgeExternalDao::getJudgeId).collect(Collectors.toList());
        List<SysUser> sysUserInfoList = remoteToOtherServiceApi.getSysUserInfo(judgeIdList);
        Map<Long, SysUser> userMap = sysUserInfoList.stream()
                .collect(Collectors.toMap(SysUser::getUserId, Function.identity()));
        List<ExtractLogVo> voList = extarctList.stream()
                .map(e -> ExtractJudgeConvert.INSTANCE.convert(userMap.get(e.getJudgeId()), e))
                .collect(Collectors.toList());
        return voList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveJudge(ExtractNameDto dto) {
        List<ExtractJudgeExternalDao> judgeDaoList = findByExtractLongName(dto.getExtractLongName());
        List<ExtractLogVo> voList = dto.getVoList();
        if (dto.getRepair()) {
            if (!CollectionUtils.isEmpty(judgeDaoList)) {
                List<Long> judgeIdList = judgeDaoList.stream().map(ExtractJudgeExternalDao::getJudgeId).collect(Collectors.toList());
                voList.removeIf(vo -> judgeIdList.contains(vo.getJudgeId()));
            }
        } else {
            if (!CollectionUtils.isEmpty(judgeDaoList)) {
                log.error("抽取名称不能相同");
                throw new ServiceException("抽取名称不能相同");
            }
        }
        Date createAt = dto.getRepair()
                ? CollectionUtils.isEmpty(judgeDaoList) ? DateUtils.getNowDate() : judgeDaoList.get(0).getCreateAt()
                : DateUtils.getNowDate();
        List<ExtractJudgeExternalDao> extractLogList = voList.stream()
                .map(v -> ExtractJudgeExternalConvert.INSTANCE.convert(dto, v, createAt, SecurityUtils.getNickName()))
                .collect(Collectors.toList());

        saveBatch(extractLogList);
        return Boolean.TRUE;
    }

    @Override
    public TableDataVo extractJudgeLog(PageSortEntity<ExtractQueryDto> dto) {
        if (CollectionUtils.isEmpty(dto.getSorts())) {
            dto.getSorts().putAll(ExtractJudgeInnerDao.sort());
        }
        IPage<JudgeListVo> page = extractJudgeExternalMapper.judgePage
                (
                        Page.of(dto.getCalcPageNum(),
                                dto.getCalcPageSize()),
                        dto.getEntity()
                );
        List<JudgeListVo> records = page.getRecords();
        if (!CollectionUtils.isEmpty(records)) {
            records.forEach(r -> r.setJudgeIdList(
                    Arrays.stream(r.getJudgeIds().split(","))
                            .map(Long::valueOf)
                            .collect(Collectors.toList())));
        }
        return new TableDataVo(page.getRecords(), page.getTotal());
    }


    @Override
    public Boolean markJudge(MarkJudgeLogDto dto) {
        boolean b = update(Wrappers.lambdaUpdate(ExtractJudgeExternalDao.class)
                .set(ExtractJudgeExternalDao::getWhetherRefuse, 0)
                .set(ExtractJudgeExternalDao::getRefuseReason, dto.getRefuseReason())
                .eq(ExtractJudgeExternalDao::getExtractLongName, dto.getExtractLongName())
                .in(ExtractJudgeExternalDao::getJudgeId, dto.getJudgeIdList()));
        return b;
    }

    @Override
    public Boolean delJudgeLog(MarkJudgeLogDto dto) {
        boolean b = remove(Wrappers.lambdaQuery(ExtractJudgeExternalDao.class)
                .eq(ExtractJudgeExternalDao::getExtractLongName, dto.getExtractLongName())
                .in(ExtractJudgeExternalDao::getJudgeId, dto.getJudgeIdList()));
        return b;
    }
}
