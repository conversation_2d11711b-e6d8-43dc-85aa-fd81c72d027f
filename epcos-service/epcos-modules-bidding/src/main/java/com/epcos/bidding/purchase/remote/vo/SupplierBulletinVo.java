package com.epcos.bidding.purchase.remote.vo;

import com.epcos.bidding.purchase.project.base.domain.vo.SignTimeVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/17 9:47
 */
@Data
public class SupplierBulletinVo implements Serializable {

    private static final long serialVersionUID = 4480202306988781820L;

    @ApiModelProperty(value = "项目名称")
    private String buyItemName;

    @ApiModelProperty(value = "采购项目编号")
    private String buyItemCode;

    @ApiModelProperty(value = "公告id")
    private Long bulletinId;

    @ApiModelProperty(value = "公告名称")
    private String bulletinName;

    @ApiModelProperty(value = "公告类型")
    private String bulletinType;

    @ApiModelProperty(value = "公告发布时间，即创建时间")
    private Date createAt;

    @ApiModelProperty(value = "包名称及时间")
    private List<SignTimeVo> signTimeVoList;
}
