package com.epcos.bidding.audit.business.api;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.audit.domain.dao.AuditRecordDao;
import com.epcos.bidding.common.IBaseService;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.Objects;

public interface IAuditRecordApi extends IBaseService<AuditRecordDao> {
    @Override
    default LambdaQueryWrapper<AuditRecordDao> queryWrapper(AuditRecordDao dao) {
        LambdaQueryWrapper<AuditRecordDao> query = Wrappers.lambdaQuery(AuditRecordDao.class);
        if (Objects.nonNull(dao.getId())) {
            return query.eq(AuditRecordDao::getId, dao.getId());
        }
        return query
                .eq(Objects.nonNull(dao.getAuditPersonId()), AuditRecordDao::getAuditPersonId, dao.getAuditPersonId())
                .eq(Objects.nonNull(dao.getStatus()), AuditRecordDao::getStatus, dao.getStatus())
                .like(StringUtils.hasText(dao.getRemark()), AuditRecordDao::getRemark, dao.getRemark())
                .orderByDesc(AuditRecordDao::getId);
    }

    default LambdaQueryWrapper<AuditRecordDao> queryWrapper(Long auditPersonId) {
        AuditRecordDao dao = new AuditRecordDao();
        dao.setAuditPersonId(auditPersonId);
        return queryWrapper(dao);
    }

    default Wrapper<AuditRecordDao> queryWrapperIn(Collection<Long> auditPersonIds) {
        return Wrappers.lambdaQuery(AuditRecordDao.class)
                .in(AuditRecordDao::getAuditPersonId, auditPersonIds);
    }

    default LambdaUpdateWrapper<AuditRecordDao> updateWrapper(Long auditPersonId) {
        return Wrappers.lambdaUpdate(AuditRecordDao.class)
                .eq(AuditRecordDao::getAuditPersonId, auditPersonId);
    }

    default AuditRecordDao findOne(Long auditPersonId) {
        return getOne(queryWrapper(auditPersonId));
    }


}
