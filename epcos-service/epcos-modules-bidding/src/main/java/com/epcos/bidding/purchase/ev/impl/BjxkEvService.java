package com.epcos.bidding.purchase.ev.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.utils.BiddingBaseUtil;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.purchase.project.bjxk.business.api.buyitem.BJXKBuyItemApi;
import com.epcos.bidding.purchase.project.bjxk.domain.dao.BuyItemBJXKDao;
import com.epcos.bidding.workbench.vo.SpecialFieldVo;
import com.epcos.common.core.utils.StringUtils;
import com.epcos.dingtalk.api.RemoteDingtalk;
import com.epcos.dingtalk.domain.dao.DingtalkProjectRequestDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.epcos.common.core.enums.ClientEnum.XK;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/21 15:48
 */
@Slf4j
@Service("xk")
public class BjxkEvService extends AbEvService {

    private final BJXKBuyItemApi bjxkBuyItemApi;
    private final RemoteDingtalk remoteDingtalk;

    public BjxkEvService(IBuyItemApi buyItemApi, ISubPackageApi subPackageApi, BJXKBuyItemApi bjxkBuyItemApi, RemoteDingtalk remoteDingtalk) {
        super(buyItemApi, subPackageApi);
        this.bjxkBuyItemApi = bjxkBuyItemApi;
        this.remoteDingtalk = remoteDingtalk;
    }

    @Override
    public String ev() {
        return XK.getCode();
    }

    @Override
    public boolean isDing() {
        return true;
    }


    @Override
    public String getTableName() {
        return "purchase_bjxk_buy_item";
    }

    @Override
    public Map<String, String> queryBuyItemMap(Map<String, String> voMap, BuyItemDao buyItemInfo) {
        voMap = BiddingBaseUtil.convert(bjxkBuyItemApi.findOneByBuyItemCode(buyItemInfo.getBuyItemCode()));
        return voMap;
    }

    @Override
    public List<SpecialFieldVo> queryDiffInfo(List<String> buyItemCodes, Map<String, List<SubpackageDao>> subMap, List<SpecialFieldVo> voList) {
        List<BuyItemBJXKDao> bjxkDaoList = bjxkBuyItemApi.findOneByBuyItemCodeList(buyItemCodes);
        bjxkDaoList.forEach(dao -> subMap.get(dao.getBuyItemCode())
                .forEach(sub -> voList.add(new SpecialFieldVo(dao.getBuyItemCode(), sub.getSubpackageCode(), null, dao.getBuyClass(), null))));
        return voList;
    }

    @Override
    public Boolean delBuyItem(String buyItemCode, List<String> subpackageCodeList) {
        Map<String, String> buyItemMap = queryBuyItemMap(buyItemCode);
        bjxkBuyItemApi.delBJXKBuyItemInfo(buyItemCode);

        //需要修改钉钉项目的采购状态
        String dingTalkId = buyItemMap.get("dingTalkId");
        if (StringUtils.hasText(dingTalkId)) {
            remoteDingtalk.updateById(new DingtalkProjectRequestDao() {{
                setId(Long.parseLong(dingTalkId));
                setStatus(0);
            }});
        }
        return Boolean.TRUE;
    }

    @Override
    public List<String> queryAllBuyItemCode() {
        List<BuyItemBJXKDao> bjxkItemList = bjxkBuyItemApi.list(Wrappers.lambdaQuery(BuyItemBJXKDao.class)
                .select(BuyItemBJXKDao::getBuyItemCode));
        if (CollectionUtils.isEmpty(bjxkItemList)) {
            return Collections.emptyList();
        }
        List<String> codeList = bjxkItemList.stream().map(BuyItemBJXKDao::getBuyItemCode).collect(Collectors.toList());
        return codeList;
    }
}
