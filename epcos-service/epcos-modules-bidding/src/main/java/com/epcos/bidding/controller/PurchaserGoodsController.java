package com.epcos.bidding.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.goods.business.api.IGoodsCartApi;
import com.epcos.bidding.purchase.goods.domain.dao.GoodsCartDao;
import com.epcos.bidding.purchase.goods.domain.dto.AddCartDetail;
import com.epcos.bidding.purchase.goods.domain.dto.AddCartDto;
import com.epcos.bidding.purchase.goods.domain.vo.GoodsCartVo;
import com.epcos.bidding.purchase.goods.domain.vo.GoodsParamsVo;
import com.epcos.bidding.purchase.remote.RemoteSupplierApi;
import com.epcos.bidding.supplier.goods.domain.dto.GoodsAuditDto;
import com.epcos.bidding.supplier.goods.domain.dto.GoodsPageDto;
import com.epcos.bidding.supplier.goods.domain.vo.GoodsVo;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.web.page.TableDataVo;
import com.epcos.common.security.annotation.RequiresPermissions;
import com.epcos.common.core.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "采购人商城")
@RequiredArgsConstructor
@RestController
@RequestMapping("/purchaser/goods")
public class PurchaserGoodsController {

    private final IGoodsCartApi goodsCartApi;
    private final RemoteSupplierApi remoteSupplierApi;

    @ApiOperation("查看供应商商品分页")
    @RequiresPermissions(value = {"goods:audit:list", "shopping:mall:list"})
    @PostMapping("/page")
    public TableDataVo<GoodsVo> page(@RequestBody @Valid PageSortEntity<GoodsPageDto> dto) {
        IPage<GoodsVo> pageVo = remoteSupplierApi.pageList(dto);
        return new TableDataVo<>(pageVo.getRecords(), pageVo.getTotal());
    }

    @ApiOperation("审核供应商商品")
    @RequiresPermissions(value = {"goods:audit:audit"})
    @PostMapping("/audit")
    public R<Boolean> audit(@RequestBody @Valid GoodsAuditDto dto) {
        remoteSupplierApi.audit(dto);
        return R.ok();
    }


    @ApiOperation("查看供应商商品详情")
    @GetMapping("/goodsInfo")
    public R<GoodsVo> goodsInfo(@RequestParam(value = "goodsId") Long goodsId) {
        return R.ok(remoteSupplierApi.goodsInfo(goodsId));
    }


    @ApiOperation("加入购物车")
    @PostMapping("/addCart")
    public R<Boolean> addCart(@RequestBody @Valid AddCartDto dto) {
        return R.ok(goodsCartApi.addCart(dto));
    }

    @ApiOperation("查询购物车列表")
    @PostMapping("/cartList")
    public TableDataVo<GoodsCartVo> cartList(@RequestBody PageSortEntity<GoodsCartDao> dto) {
        GoodsCartDao goodsCartDao = new GoodsCartDao();
        goodsCartDao.setBuyerId(SecurityUtils.getUserId());
        dto.setEntity(goodsCartDao);
        IPage<GoodsCartVo> page = goodsCartApi.cartList(dto);
        List<GoodsCartVo> voList = page.getRecords();
        return new TableDataVo(voList, page.getTotal());
    }

    @ApiOperation("去采购时，查询寻购物车参数信息")
    @GetMapping("/cartInfo")
    public R<List<GoodsParamsVo>> cartInfo(Long[] cartIds) {
        List<Long> cartIdList = Arrays.stream(cartIds).collect(Collectors.toList());
        return R.ok(goodsCartApi.cartInfo(cartIdList));
    }

    @ApiOperation("修改购物车信息")
    @PostMapping("/updateCart")
    public R<Boolean> updateCart(@RequestBody List<AddCartDetail> dto) {
        return R.ok(goodsCartApi.updateCart(dto));
    }

    @ApiOperation("删除购物车信息")
    @PostMapping("/delCart")
    public R<Boolean> delCart(@RequestBody List<Long> cartIdList) {
        return R.ok(goodsCartApi.delCart(cartIdList));
    }
}
