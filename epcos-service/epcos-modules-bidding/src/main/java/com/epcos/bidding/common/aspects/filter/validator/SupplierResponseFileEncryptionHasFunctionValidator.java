package com.epcos.bidding.common.aspects.filter.validator;

import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import com.epcos.bidding.supplier.answer.domain.dto.AnswerFileReleaseAndStampDto;
import com.epcos.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * supplier_response_file_encryption
 * 响应文件是否加密
 */
@Slf4j
@Component
public class SupplierResponseFileEncryptionHasFunctionValidator implements ResultPostHandlerFilterChain<Boolean> {

    @Override
    public void postHandler(AspectContext context, Boolean result) {
        if (Boolean.TRUE.equals(result)) {
            AnswerFileReleaseAndStampDto arg = (AnswerFileReleaseAndStampDto) context.getDto();
            if (!StringUtils.hasText(arg.getAnswerFileKey())) {
                throw new ServiceException("响应文件密钥，必填");
            }
        } else if (Boolean.FALSE.equals(result)) {
            AnswerFileReleaseAndStampDto arg = (AnswerFileReleaseAndStampDto) context.getDto();
            if (StringUtils.hasText(arg.getAnswerFileKey())) {
                throw new ServiceException("响应文件密钥功能未配置，无法输入");
            }
        }
    }
}
