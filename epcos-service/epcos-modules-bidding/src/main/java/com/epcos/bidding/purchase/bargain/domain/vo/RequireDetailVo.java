package com.epcos.bidding.purchase.bargain.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/18 10:11
 */
@Data
public class RequireDetailVo implements Serializable {

    private static final long serialVersionUID = 6650973128466707275L;

    @ApiModelProperty(value = "当前轮数")
    private Integer round;

    @ApiModelProperty("倒计时")
    private Date countdown;

    @ApiModelProperty("服务需求(备注)")
    private String serviceRequire;


    public RequireDetailVo() {
        this.round = 0;
    }
}
