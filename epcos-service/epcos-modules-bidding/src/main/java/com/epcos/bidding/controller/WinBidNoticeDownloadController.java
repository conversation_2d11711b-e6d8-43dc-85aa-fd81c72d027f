package com.epcos.bidding.controller;

import com.epcos.bidding.common.annotaion.NotNullUserId;
import com.epcos.bidding.supplier.win.business.api.IWinBidNoticeDownloadApi;
import com.epcos.bidding.supplier.win.domain.dto.WinBidNoticeDownloadDto;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.log.annotation.Log;
import com.epcos.common.log.enums.BusinessType;
import com.epcos.common.redis.annotation.RedisLock;
import com.epcos.common.security.handler.GlobalExceptionHandler;
import com.epcos.pay.api.domain.dto.OrderCloseDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.util.Map;

/**
 * 中标通知书下载付费控制器
 */
@Slf4j
@Api(tags = "中标通知书下载付费")
@RestController
@RequestMapping("/supplier")
@RequiredArgsConstructor
public class WinBidNoticeDownloadController {

    private final IWinBidNoticeDownloadApi winBidNoticeDownloadApi;


    @ApiOperation(value = "中标供应商下载中标通知书请求")
    @RedisLock(second = 60)
    @NotNullUserId
    @Log(title = "中标供应商下载中标通知书请求", businessType = BusinessType.INSERT)
    @PostMapping("/win/notice/pay")
    public R<Map<String, String>> requestDownload(@RequestBody @Valid WinBidNoticeDownloadDto dto) {
        return winBidNoticeDownloadApi.requestDownload(dto, SecurityUtils.getUserId());
    }


    @ApiOperation(value = "检查下载权限")
    @GetMapping("/check-permission")
    public R<Boolean> checkDownloadPermission(@RequestParam @NotBlank(message = "标段编码不能为空") String subpackageCode) {
        boolean hasPermission = winBidNoticeDownloadApi.checkDownloadPermission(subpackageCode, SecurityUtils.getUserId());
        return R.ok(hasPermission);
    }

    @ApiOperation(value = "获取下载文件URL")
    @GetMapping("/url")
    public R<String> getDownloadUrl(@RequestParam @NotBlank(message = "标段编码不能为空") String subpackageCode) {
        String downloadUrl = winBidNoticeDownloadApi.getDownloadUrl(subpackageCode, SecurityUtils.getUserId());
        return R.ok(downloadUrl);
    }

    @ApiOperation(value = "修改订单状态")
    @Log(title = "修改订单状态", businessType = BusinessType.UPDATE)
    @PostMapping("/pay-callback")
    public R<Void> handlePaySuccess(@RequestParam Map<String ,String> map) {
        winBidNoticeDownloadApi.handlePaySuccess(map);
        return R.ok();
    }

}
