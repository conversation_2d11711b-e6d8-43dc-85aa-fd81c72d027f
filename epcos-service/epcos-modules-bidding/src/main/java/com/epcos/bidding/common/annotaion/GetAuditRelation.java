package com.epcos.bidding.common.annotaion;

import com.epcos.bidding.common.AuditTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 查询业务与审批之间的关联关系
 *
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface GetAuditRelation {

    GetCommon common() default @GetCommon;

    /**
     * 审批类型
     */
    AuditTypeEnum auditType();

    /**
     * 用户id，如果为空，默认取绑定的用户id
     */
    String userIdEL() default "";

    /**
     * 业务id
     */
    String bizIdEL() default "";

    /**
     * 业务类型
     */
    String bizTypeEL() default "";

}
