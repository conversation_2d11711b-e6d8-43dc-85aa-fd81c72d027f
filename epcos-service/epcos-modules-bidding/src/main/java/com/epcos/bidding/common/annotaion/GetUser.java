package com.epcos.bidding.common.annotaion;


import org.springframework.core.annotation.Order;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Order(450)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface GetUser {

    GetCommon common() default @GetCommon;

    /**
     * 用户id
     */
    String userIdEl() default "";


}
