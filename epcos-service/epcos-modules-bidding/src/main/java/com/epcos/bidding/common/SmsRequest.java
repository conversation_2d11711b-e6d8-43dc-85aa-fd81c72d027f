package com.epcos.bidding.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Set;

@Data
@ApiModel(description = "短信请求")
public class SmsRequest implements Serializable {
    private static final long serialVersionUID = 913364309311436471L;

    @ApiModelProperty("手机号")
    @NotEmpty(message = "手机号不能为空")
    @Size(min = 1, max = 50, message = "手机号最多50个字符")
    private Set<String> mobiles;

    @ApiModelProperty("短信内容")
    @NotBlank(message = "短信内容不能为空")
    @Length(max = 1015, message = "短信内容最多1015个字符")
    private String content;
}
