package com.epcos.bidding.purchase.claims.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

@Data
@NoArgsConstructor
public class ClaimsFileQuoteFormQueryDto {


    @ApiModelProperty("采购人标段报价表id")
    private Long id;

    @ApiModelProperty("包编码【最长：50】")
    @Length(max = 50, message = "包编码【最长：50】")
    private String subpackageCode;

    public ClaimsFileQuoteFormQueryDto(String subpackageCode) {
        this.subpackageCode = subpackageCode;
    }
}
