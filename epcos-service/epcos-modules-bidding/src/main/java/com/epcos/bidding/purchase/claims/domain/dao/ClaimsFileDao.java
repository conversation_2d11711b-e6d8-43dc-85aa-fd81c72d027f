package com.epcos.bidding.purchase.claims.domain.dao;

import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.bidding.common.SubpackageCodeEntity;
import com.epcos.bidding.common.utils.BidFileJson;
import com.epcos.common.core.utils.bean.BeanValidators;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import java.util.Date;
import java.util.Objects;

/**
 * 采购文件
 */
@Data
@ApiModel(description = "采购文件信息")
@NoArgsConstructor
@TableName("claims_file")
public class ClaimsFileDao extends SubpackageCodeEntity {

    public ClaimsFileDao(String subpackageCode) {
        this.subpackageCode = subpackageCode;
    }

    @NotBlank(message = "采购文件包key，必填")
    @Length(max = 100, message = "采购文件包key【最长：100】")
    @ApiModelProperty(value = "采购文件包key")
    private String epcFile;

    @NotBlank(message = "采购文件pdf key，必填")
    @Length(max = 100, message = "采购文件pdf key【最长：100】")
    @ApiModelProperty(value = "采购文件pdf key")
    private String pdfFile;

    @ApiModelProperty("采购文件发布状态,0已上传 1已提交")
    @Range(max = 1, message = "0已上传 1已提交")
    @NotNull(message = "采购文件发布状态,必填")
    private Integer releaseStatus;

    @ApiModelProperty("采购文件提交时间")
    private Date releaseTime;

    @NotBlank(message = "名称，必填")
    @Length(max = 100, message = "名称【最长：100】")
    @ApiModelProperty(value = "名称")
    private String appName;

    @NotBlank(message = "版本，必填")
    @Length(max = 100, message = "版本【最长：100】")
    @ApiModelProperty(value = "版本")
    private String appVersion;

    @NotBlank(message = "文件验证key，必填")
    @Length(max = 100, message = "文件验证key【最长：100】")
    @ApiModelProperty(value = "文件验证key")
    private String zepcKey;

    public ClaimsFileDao(String subpackageCode, String epcFile, String pdfFile, String appName, String appVersion, String zepcKey) {
        this.subpackageCode = subpackageCode;
        this.epcFile = epcFile;
        this.pdfFile = pdfFile;
        this.appName = appName;
        this.appVersion = appVersion;
        this.zepcKey = zepcKey;
    }

    public ClaimsFileDao init() {
        this.setReleaseStatus(0);
        return this;
    }

    public ClaimsFileDao release() {
        this.setReleaseStatus(1);
        return this;
    }

    public BidFileJson convertBidFileJson() {
        BidFileJson bidFileJson = new BidFileJson();
        bidFileJson.setAppName(this.appName);
        bidFileJson.setVersion(this.appVersion);
        bidFileJson.setZepcKey(this.zepcKey);
        return bidFileJson;
    }

    // 采购文件已发布
    public ClaimsFileDao verifyPublished() {
        return BeanValidators.verifyField(this, getReleaseStatus(), i -> i == 1, "采购文件已发布");
    }

    public ClaimsFileDao verifyNotPublished() {
        return BeanValidators.verifyField(this, getReleaseStatus(), i -> i != 1, "采购文件未发布");
    }

    public void verifyNotEqualZepcKey(String zepcKey) {
        BeanValidators.verifyField(this, getZepcKey(), s -> !Objects.equals(zepcKey, s), "请上传使用本项目采购文件制作的响应文件");
    }
}
