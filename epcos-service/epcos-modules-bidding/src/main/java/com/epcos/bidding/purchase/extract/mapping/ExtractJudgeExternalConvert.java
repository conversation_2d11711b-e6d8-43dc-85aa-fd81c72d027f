package com.epcos.bidding.purchase.extract.mapping;

import com.epcos.bidding.purchase.extract.domain.dao.ExtractJudgeExternalDao;
import com.epcos.bidding.purchase.extract.domain.dto.ExtractNameDto;
import com.epcos.bidding.purchase.extract.domain.dto.JudgeBatchSubDto;
import com.epcos.bidding.purchase.extract.domain.dto.JudgeExtractDto;
import com.epcos.bidding.purchase.extract.domain.vo.ExtractLogVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/18 14:25
 */
@Mapper
public interface ExtractJudgeExternalConvert {

    ExtractJudgeExternalConvert INSTANCE = Mappers.getMapper(ExtractJudgeExternalConvert.class);

    @Mappings(value = {
            @Mapping(target = "createAt", source = "createAt"),
            @Mapping(target = "createBy", source = "createBy")
    })
    ExtractJudgeExternalDao convert(ExtractNameDto dto, ExtractLogVo vo, Date createAt, String createBy);


    JudgeExtractDto otherConvert(JudgeBatchSubDto judgeBatchSubDto);
}
