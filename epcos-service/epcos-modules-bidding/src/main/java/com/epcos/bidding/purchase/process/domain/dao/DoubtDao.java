package com.epcos.bidding.purchase.process.domain.dao;

import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.bidding.common.SubpackageCodeEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/7 15:02
 */
@ApiModel(description = "质疑表")
@Data
@TableName("purchase_doubt")
public class DoubtDao extends SubpackageCodeEntity implements Serializable {

    @ApiModelProperty(value = "质疑时间信息表主键")
    private Long doubtTimeId;

    @ApiModelProperty(value = "质疑人类型[1-投标人, 2-采购办, 3-评委]")
    private String doubtUserType;

    @ApiModelProperty(value = "质疑人id")
    private Long doubtUserId;

    @ApiModelProperty(value = "提问名称[投标人公司名称/采购办操作人姓名/评委姓名]")
    private String doubtUserName;

    @ApiModelProperty(value = "质疑文件地址key")
    private String doubtFileKey;

    @ApiModelProperty(value = "质疑回复文件地址key")
    private String replyFileKey;

    @ApiModelProperty(value = "发起质疑附件文件地址key")
    private String doubtAnnexFileKey;

    @ApiModelProperty(value = "回复质疑附件文件地址key")
    private String replyAnnexFileKey;

    @ApiModelProperty(value = "质疑答复人id")
    private Long replyUserId;

}
