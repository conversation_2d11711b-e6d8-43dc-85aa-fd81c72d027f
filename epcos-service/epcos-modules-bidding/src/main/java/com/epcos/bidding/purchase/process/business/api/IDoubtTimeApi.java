package com.epcos.bidding.purchase.process.business.api;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.IBaseService;
import com.epcos.bidding.purchase.process.domain.dao.DoubtTimeDao;
import com.epcos.bidding.purchase.process.domain.dto.DoubtTimeDto;
import com.epcos.bidding.purchase.process.domain.vo.DoubtTimeVo;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/7 15:32
 */
public interface IDoubtTimeApi extends IBaseService<DoubtTimeDao> {

    @Override
    default LambdaQueryWrapper<DoubtTimeDao> queryWrapper(DoubtTimeDao dao) {
        LambdaQueryWrapper<DoubtTimeDao> query = Wrappers.lambdaQuery(DoubtTimeDao.class);
        if (Objects.nonNull(dao.getId())) {
            return query.eq(DoubtTimeDao::getId, dao.getId());
        }
        return query
                .eq(StringUtils.hasText(dao.getBuyItemCode()), DoubtTimeDao::getBuyItemCode, dao.getBuyItemCode())
                .eq(StringUtils.hasText(dao.getSubpackageCode()), DoubtTimeDao::getSubpackageCode, dao.getSubpackageCode())
                .like(StringUtils.hasText(dao.getSubpackageName()), DoubtTimeDao::getSubpackageName, dao.getSubpackageName())
                .eq(StringUtils.hasText(dao.getDoubtType()), DoubtTimeDao::getDoubtType, dao.getDoubtType())
                .orderByDesc(DoubtTimeDao::getId);
    }

    default LambdaQueryWrapper<DoubtTimeDao> queryWrapper(String subpackageCode, String doubtType) {
        DoubtTimeDao doubtTimeDao = new DoubtTimeDao();
        doubtTimeDao.setSubpackageCode(subpackageCode);
        doubtTimeDao.setDoubtType(doubtType);
        return queryWrapper(doubtTimeDao);
    }

    default List<DoubtTimeDao> find(String subpackageCode) {
        return list(queryWrapper(subpackageCode, null));
    }

    default DoubtTimeDao findOne(String subpackageCode, String doubtType) {
        return getOne(queryWrapper(subpackageCode, doubtType));
    }

    default Boolean del(List<String> subpackageCodeList) {
        return remove(Wrappers.lambdaQuery(DoubtTimeDao.class)
                .in(DoubtTimeDao::getSubpackageCode, subpackageCodeList));
    }

    Boolean saveTime(DoubtTimeDto dto);

    List<DoubtTimeVo> findBySubpackageCode(String subpackageCode);

    DoubtTimeDao check(String subpackageCode, String doubtType);
}
