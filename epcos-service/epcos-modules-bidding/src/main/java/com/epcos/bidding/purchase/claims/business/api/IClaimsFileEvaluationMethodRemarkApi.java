package com.epcos.bidding.purchase.claims.business.api;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.IBaseService;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileEvaluationMethodRemarkDao;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public interface IClaimsFileEvaluationMethodRemarkApi extends IBaseService<ClaimsFileEvaluationMethodRemarkDao> {

    @Override
    default LambdaQueryWrapper<ClaimsFileEvaluationMethodRemarkDao> queryWrapper(ClaimsFileEvaluationMethodRemarkDao dao) {
        LambdaQueryWrapper<ClaimsFileEvaluationMethodRemarkDao> query = Wrappers.lambdaQuery(ClaimsFileEvaluationMethodRemarkDao.class);
        if (Objects.nonNull(dao.getId())) {
            return query.eq(ClaimsFileEvaluationMethodRemarkDao::getId, dao.getId());
        }
        return query.eq(StringUtils.hasText(dao.getSubpackageCode()),
                ClaimsFileEvaluationMethodRemarkDao::getSubpackageCode, dao.getSubpackageCode());
    }

    default LambdaQueryWrapper<ClaimsFileEvaluationMethodRemarkDao> queryWrapper(String subpackageCode) {
        return queryWrapper(new ClaimsFileEvaluationMethodRemarkDao(subpackageCode));
    }

    default void remove(String subpackageCode) {
        remove(queryWrapper(subpackageCode));
    }

    default ClaimsFileEvaluationMethodRemarkDao findBySubpackageCode(String subpackageCode) {
        return getOne(queryWrapper(subpackageCode));
    }

}
