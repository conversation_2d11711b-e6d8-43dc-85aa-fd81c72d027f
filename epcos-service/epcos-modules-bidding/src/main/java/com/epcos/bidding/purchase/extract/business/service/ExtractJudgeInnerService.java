package com.epcos.bidding.purchase.extract.business.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.audit.api.AuditProcessDto;
import com.epcos.bidding.audit.api.BizAuditRelationDto;
import com.epcos.bidding.audit.api.vo.AuditVo;
import com.epcos.bidding.biz.business.api.IBizAuditRelationApi;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.common.annotaion.Jump;
import com.epcos.bidding.purchase.api.domian.judge.judgeProjectVo;
import com.epcos.bidding.purchase.api.params.JudgesVo;
import com.epcos.bidding.purchase.api.params.dto.BaseQueryDto;
import com.epcos.bidding.purchase.api.params.vo.buyitem.ItemSubpackageVo;
import com.epcos.bidding.purchase.ev.factory.EvFactory;
import com.epcos.bidding.purchase.extract.business.api.IExtractJudgeExternalApi;
import com.epcos.bidding.purchase.extract.business.api.IExtractJudgeInnerApi;
import com.epcos.bidding.purchase.extract.domain.dao.ExtractJudgeExternalDao;
import com.epcos.bidding.purchase.extract.domain.dao.ExtractJudgeInnerDao;
import com.epcos.bidding.purchase.extract.domain.dto.ImportJudgeDto;
import com.epcos.bidding.purchase.extract.domain.dto.JudgeBatchDto;
import com.epcos.bidding.purchase.extract.domain.dto.JudgeExtractDto;
import com.epcos.bidding.purchase.extract.domain.vo.ExtractBuyItemVo;
import com.epcos.bidding.purchase.extract.domain.vo.ExtractLogVo;
import com.epcos.bidding.purchase.extract.mapping.ExtractJudgeExternalConvert;
import com.epcos.bidding.purchase.extract.mapping.ExtractJudgeInnerConvert;
import com.epcos.bidding.purchase.extract.repository.IExtractJudgeInnerMapper;
import com.epcos.bidding.purchase.process.business.api.IReviewBeforeApi;
import com.epcos.bidding.purchase.process.domain.dao.ReviewBeforeDao;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.purchase.project.mapping.ExtractJudgeConvert;
import com.epcos.bidding.purchase.remote.RemoteToOtherServiceApi;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.bean.BeanUtils;
import com.epcos.common.core.web.domain.user.SysUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.epcos.common.core.constant.PurchaseConstants.Currency.ONE;
import static com.epcos.common.core.constant.PurchaseConstants.Currency.ZERO;
import static com.epcos.common.core.constant.PurchaseConstants.UserType.PURCHASER;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/11 16:48
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExtractJudgeInnerService extends ServiceImpl<IExtractJudgeInnerMapper, ExtractJudgeInnerDao> implements IExtractJudgeInnerApi {

    private final IBuyItemApi buyItemApi;
    private final RemoteToOtherServiceApi remoteToOtherServiceApi;
    private final IExtractJudgeExternalApi judgeExternalApi;
    private final ISubPackageApi subpackageApi;
    private final IReviewBeforeApi reviewBeforeApi;
    private final IBizAuditRelationApi bizAuditRelationApi;

    @Override
    public List<SysUser> queryLogJudge(ImportJudgeDto dto) {
        String name = dto.getBuyItemName() + "-" + dto.getBuyItemCode() + "-" + dto.getSubpackageName();
        List<ExtractJudgeExternalDao> daoList = judgeExternalApi.findByExtractLongName(name);
        if (CollectionUtils.isEmpty(daoList)) {
            return Collections.EMPTY_LIST;
        }
        //只留下能到场的
        daoList = daoList.stream().filter(e -> e.getWhetherRefuse() == ONE).collect(Collectors.toList());
        List<Long> judgeIdList = daoList.stream().map(ExtractJudgeExternalDao::getJudgeId).collect(Collectors.toList());
        List<SysUser> userList = remoteToOtherServiceApi.getSysUserInfo(judgeIdList);
        return userList;
    }

    @Override
    public List<ItemSubpackageVo> queryJudgeList(String buyItemCode) {
        List<SubpackageDao> subpackageDaoList = subpackageApi.findByBuyItemCode(buyItemCode);
        List<ExtractJudgeInnerDao> judgeDaoList = find(buyItemCode);
        log.error("judgeDaoList:{}", judgeDaoList);
        List<SysUser> userList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(judgeDaoList)) {
            List<Long> judgeIdList = judgeDaoList.stream().map(ExtractJudgeInnerDao::getJudgeId).collect(Collectors.toList());
            userList = remoteToOtherServiceApi.getSysUserInfo(judgeIdList);
        }

        //组装返回信息
        Map<String, List<ExtractJudgeInnerDao>> judgeMap = judgeDaoList.stream()
                .collect(Collectors.groupingBy(ExtractJudgeInnerDao::getSubpackageCode));

        Map<Long, SysUser> userMap = userList.stream()
                .collect(Collectors.toMap(SysUser::getUserId, Function.identity()));

        List<ItemSubpackageVo> voList = subpackageDaoList
                .stream()
                .map(s -> {
                    log.error("getSubpackageCode:{}", s.getSubpackageCode());
                    //查询审批结果
                    List<ExtractJudgeInnerDao> judgeInnerDaoList = judgeMap.get(s.getSubpackageCode());
                    ItemSubpackageVo vo = new ItemSubpackageVo(s.getSubpackageCode(), s.getSubpackageName(), Collections.EMPTY_LIST);
                    if (!CollectionUtils.isEmpty(judgeInnerDaoList)) {
                        AuditProcessDto auditProcessDto = queryJudgeAudit(buyItemCode, s.getSubpackageCode(), judgeInnerDaoList.get(0).getId());
                        vo.setAuditProcessDto(auditProcessDto);
                        List<ExtractLogVo> logVoList = judgeInnerDaoList
                                .stream()
                                .map(e -> ExtractJudgeConvert.INSTANCE.convert(userMap.get(e.getJudgeId()), e))
                                .collect(Collectors.toList());
                        vo.setData(logVoList);
                    }
                    return vo;
                }).collect(Collectors.toList());
        return voList;
    }

    public AuditProcessDto queryJudgeAudit(String buyItemCode, String subpackageCode, Long id) {

        AuditVo auditVo = bizAuditRelationApi.queryAuditVo(BizAuditRelationDto.builder()
                .buyItemCode(buyItemCode)
                .subpackageCode(subpackageCode)
                .businessId(id)
                .build()
        );
        if (Objects.isNull(auditVo)) {
            return null;
        }
        AuditProcessDto auditProcessDto = new AuditProcessDto();
        BeanUtils.copyProperties(auditVo.getAuditInfoVo(), auditProcessDto);
        return auditProcessDto;
    }

    @Override
    public List<JudgesVo> queryBySub(String subpackageCode) {
        List<ExtractJudgeInnerDao> innerDaoList = findBySubpackageCode(subpackageCode);
        if (CollectionUtils.isEmpty(innerDaoList)) {
            return null;
        }
        List<JudgesVo> voList = innerDaoList.stream()
                .map(i -> ExtractJudgeInnerConvert.INSTANCE.convert(i)).collect(Collectors.toList());
        return voList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveExtractJudgeInfo(JudgeExtractDto dto) {

        List<ReviewBeforeDao> reviewBeforeDaoList = reviewBeforeApi.find(Collections.singletonList(dto.getSubpackageCode()));
        if (!CollectionUtils.isEmpty(reviewBeforeDaoList) && "1".equals(reviewBeforeDaoList.get(0).getConfirmCounterSign())) {
            throw new RuntimeException("该分包已确认会签，不能修改");
        }
        //删除之前存在的
        remove(queryWrapper(dto.getSubpackageCode()));
        //保存
        List<ExtractJudgeInnerDao> daoList = dto.getVoList()
                .stream()
                .map(v -> ExtractJudgeInnerConvert.INSTANCE.convert(dto, v, "000000"))
                .collect(Collectors.toList());
        saveBatch(daoList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBy(String status, String subpackageCode) {
        ExtractJudgeInnerDao innerDao = new ExtractJudgeInnerDao();
        innerDao.setStatus(status);
        update(innerDao, updateWrapper(subpackageCode, null));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deptRepresent(Long judgeId, String deptRepresent, String subpackageCode) {
        ExtractJudgeInnerDao judgeInnerDao = findBySubpackageCodeAndJudgeId(subpackageCode, judgeId);
        if (Objects.nonNull(judgeInnerDao)) {
            ExtractJudgeInnerDao innerDao = new ExtractJudgeInnerDao();
            innerDao.setDeptRepresent(deptRepresent);
            update(innerDao, updateWrapper(subpackageCode, judgeId));
        }
    }

    @Override
    public void rollbackData(String subpackageCode) {
        ExtractJudgeInnerDao innerDao = new ExtractJudgeInnerDao();
        innerDao.setStatus(String.valueOf(ZERO));
        innerDao.setWhetherGroup(String.valueOf(ZERO));
        innerDao.setWhetherSign(String.valueOf(ZERO));
        innerDao.setDeptRepresent(String.valueOf(ZERO));
        update(innerDao, updateWrapper(subpackageCode, null));
    }

    @Override
    public IPage<ExtractBuyItemVo> queryExtractBuyItemInfo(PageSortEntity<BaseQueryDto> dto) {
        String tableName = EvFactory.getInstance().getTableName();
        IPage<ExtractBuyItemVo> page = buyItemApi.selectExtract(dto, tableName);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return page;
        }
        List<ExtractBuyItemVo> voList = page.getRecords();
        List<String> subpacageCodeList = voList.stream().map(ExtractBuyItemVo::getSubpackageCode).collect(Collectors.toList());
        List<ExtractJudgeInnerDao> innerDaoList = findBySubpackageCodes(subpacageCodeList);
        Map<String, List<ExtractJudgeInnerDao>> innerMap = innerDaoList.stream()
                .collect(Collectors.groupingBy(ExtractJudgeInnerDao::getSubpackageCode));
        voList.forEach(vo -> {
            List<ExtractJudgeInnerDao> judgeInnerDaoList = innerMap.get(vo.getSubpackageCode());
            if (CollectionUtils.isEmpty(judgeInnerDaoList)) {
                vo.setWhetherExtract(false);
            } else {
                vo.setWhetherExtract(true);
            }
        });
        return page;
    }

    @Override
    @Jump(subpackageCodeEL = "#dto.subDtoList.get(0).subpackageCode", processRole = PURCHASER)
    public void batchSaveExtractJudgeInfo(JudgeBatchDto dto) {
        dto.getSubDtoList().forEach(subDto -> {
            subDto.getVoList().forEach(vo -> {
                if (Objects.isNull(vo.getJudgeId())) {
                    throw new ServiceException("该评委已被删除，不可导入");
                }
            });
            JudgeExtractDto extractDto = ExtractJudgeExternalConvert.INSTANCE.otherConvert(subDto);
            saveExtractJudgeInfo(extractDto);
        });
    }

    /**
     * 统计专家参与项目次数
     *
     * @param judgeIdList@return
     */
    @Override
    public R<Map<Long, judgeProjectVo>> getProjectNum(List<Long> judgeIdList) {
        Map<Long, judgeProjectVo> map = new HashMap<>();

        for (Long judgeId : judgeIdList) {
            LambdaQueryWrapper<ExtractJudgeInnerDao> qw = new LambdaQueryWrapper<>();
            qw.eq(ExtractJudgeInnerDao::getJudgeId, judgeId);
            // 专家抽取次数
            long extractNum = count(qw);
            qw.eq(ExtractJudgeInnerDao::getStatus, 0);
            // 未参与次数
            long notParticipateNum = count(qw);
            judgeProjectVo judgeProjectVo = new judgeProjectVo();
            judgeProjectVo.setExtractNum(extractNum);
            judgeProjectVo.setProjectNum(extractNum - notParticipateNum);
            judgeProjectVo.setNotParticipateNum(notParticipateNum);
            map.put(judgeId, judgeProjectVo);
        }
        return R.ok(map);
    }
}
