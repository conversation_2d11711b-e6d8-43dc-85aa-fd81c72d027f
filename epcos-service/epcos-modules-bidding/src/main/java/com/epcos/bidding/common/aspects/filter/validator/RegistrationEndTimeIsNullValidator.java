package com.epcos.bidding.common.aspects.filter.validator;

import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import com.epcos.bidding.purchase.api.params.vo.bulletin.BulletinAndItemTimeVo;
import com.epcos.common.core.exception.ServiceException;

import java.util.Optional;

/**
 * 校验报名截止时间已过
 */
public class RegistrationEndTimeIsNullValidator implements ResultPostHandlerFilterChain<BulletinAndItemTimeVo> {
    @Override
    public void postHandler(AspectContext context, BulletinAndItemTimeVo result) {
        Optional.ofNullable(result)
                .orElseThrow(() -> new ServiceException("公告时间为空"))
                .verifyRegistrationEndTimeIsNull();
    }

}
