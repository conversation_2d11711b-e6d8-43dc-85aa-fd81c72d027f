package com.epcos.bidding.purchase.process.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/7 16:39
 */
@Data
public class DoubtTimeDto implements Serializable {

    private static final long serialVersionUID = -7358400053818877158L;

    @ApiModelProperty(value = "采购项目编号")
    @NotBlank
    private String buyItemCode;

    @ApiModelProperty(value = "标段code")
    @NotBlank
    private String subpackageCode;

    @ApiModelProperty(value = "标段名称")
    @NotBlank
    private String subpackageName;

    @ApiModelProperty(value = "质疑类型  1-开标之前 （供应商向招标人） 2 - 开标过程中（供应商向招标人） 3 -评标过程中（专家向供应商 澄清 ） 4- 中标公示期（供应商向招标人）")
    @NotBlank
    private String doubtType;

    @ApiModelProperty(value = "发起质疑开始时间")
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "质疑回复截止时间")
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
}
