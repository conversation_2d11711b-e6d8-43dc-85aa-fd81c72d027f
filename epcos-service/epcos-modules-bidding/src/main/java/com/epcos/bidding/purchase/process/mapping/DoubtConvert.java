package com.epcos.bidding.purchase.process.mapping;

import com.epcos.bidding.purchase.api.params.DoubtVo;
import com.epcos.bidding.purchase.api.params.dto.DoubtDto;
import com.epcos.bidding.purchase.process.domain.dao.DoubtDao;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/6 11:51
 */
@Mapper
public interface DoubtConvert {

    DoubtConvert INSTANCE = Mappers.getMapper(DoubtConvert.class);

    @Mappings(value = {
            @Mapping(source = "id", target = "doubtTimeId")
    })
    DoubtDao convert(DoubtDto doubtDto, Long id);

    DoubtVo convert(DoubtDao doubtDao);
}
