/**
 * Copyright 2022 bejson.com
 */
package com.epcos.bidding.common.utils;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * 客户端文件评审
 */
@Data
@NoArgsConstructor
public final class EvaluationMethod {

    @ApiModelProperty("1-符合性评审, 打对钩")
    @Valid
    private List<ReviewItem> conformityReview;

    @ApiModelProperty("2-技术,3-商务  打分")
    @Valid
    private List<ReviewItem> scoreReview;

    @ApiModelProperty("备注")
    @Length(max = 20000, message = "【备注】最长长度不得超过20000个字符")
    private String remark;

}
