package com.epcos.bidding.purchase.project.bjxk.business.service;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.common.enums.BulletinTypeEnum;
import com.epcos.bidding.purchase.api.params.PurchaseQuoteFormVo;
import com.epcos.bidding.purchase.api.params.SubPackageInfoVo;
import com.epcos.bidding.purchase.api.params.dto.QuoteFormCreatorDto;
import com.epcos.bidding.purchase.api.params.vo.bulletin.TimeNodeVo;
import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import com.epcos.bidding.purchase.bulletin.business.api.IBulletinApi;
import com.epcos.bidding.purchase.bulletin.domain.dao.BulletinDao;
import com.epcos.bidding.purchase.claims.business.service.ClaimsFileQuoteFormService;
import com.epcos.bidding.purchase.project.base.business.api.IPublicBaseApi;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.purchase.project.base.domain.vo.LabelJson;
import com.epcos.bidding.purchase.project.base.domain.vo.SubPackageTimeVo;
import com.epcos.bidding.purchase.project.bjxk.business.api.BJXKBuyJoinSubApi;
import com.epcos.bidding.purchase.project.bjxk.business.api.buyitem.BJXKBuyItemApi;
import com.epcos.bidding.purchase.project.bjxk.domain.dao.BuyItemBJXKDao;
import com.epcos.bidding.purchase.project.bjxk.domain.dto.BJXKBuyItemQueryDto;
import com.epcos.bidding.purchase.project.bjxk.domain.dto.BJXKCreateBuyItemDto;
import com.epcos.bidding.purchase.project.bjxk.domain.vo.BJXKBuyItemInfoVo;
import com.epcos.bidding.purchase.project.bjxk.domain.vo.BJXKBuyItemPageVo;
import com.epcos.common.core.domain.AttachmentDto;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.StringUtils;
import com.epcos.common.core.utils.bean.BeanUtils;
import com.epcos.dingtalk.api.RemoteDingtalk;
import com.epcos.dingtalk.domain.dao.DingtalkProjectRequestDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.epcos.common.core.constant.PurchaseConstants.BulletinAudit.PASS;
import static java.util.Collections.EMPTY_LIST;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 江西省南昌市肿瘤医院版本
 * @date 2024/4/23 13:59
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BJXKBuyJoinSubService implements BJXKBuyJoinSubApi {

    private final IPublicBaseApi publicBaseApi;
    private final BJXKBuyItemApi bjxkBuyItemApi;
    private final ISubPackageApi subPackageApi;
    private final IBulletinApi bulletinApi;
    private final RemoteDingtalk remoteDingtalk;
    private final ClaimsFileQuoteFormService claimsFileQuoteFormService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createBJXKBuyItem(BJXKCreateBuyItemDto dto) {
        //先保存基本信息
        BuyItemDao baseInfo = publicBaseApi.insertBaseInfo(dto, dto.getSubpackageDtoList());
        // 成功后则再保存扩展信息
        bjxkBuyItemApi.insertBJXKBuyItem(dto, baseInfo.getBuyItemCode());
        //钉钉推送过来的项目进行保存
        if (Objects.nonNull(dto.getDingTalkId())) {
            DingtalkProjectRequestDao dingtalkProjectRequestDao = new DingtalkProjectRequestDao();
            dingtalkProjectRequestDao.setStatus(1);
            dingtalkProjectRequestDao.setId(dto.getDingTalkId());
            remoteDingtalk.updateById(dingtalkProjectRequestDao);
        }
    }

    @Override
    public IPage<BJXKBuyItemPageVo> bjxkBuyItemPage(PageSortEntity<BJXKBuyItemQueryDto> dto) {
        IPage<BJXKBuyItemPageVo> page = bjxkBuyItemApi.selectJoinBuyItemPage(dto);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return page;
        }
        List<String> buyItemCodeList = page.getRecords().stream().map(BJXKBuyItemPageVo::getBuyItemCode).collect(Collectors.toList());
        Map<String, List<TimeNodeVo>> timeNodeMap = bulletinApi.getTimeNodeList(buyItemCodeList);
        //查询标段包信息
        List<SubpackageDao> subpackageDaoList = subPackageApi.packageListInfo(buyItemCodeList);
        //组装标段以及获取项目列表时间节点
        assemble(page.getRecords(), subpackageDaoList, timeNodeMap);
        return page;
    }

    private List<BJXKBuyItemPageVo> assemble(List<BJXKBuyItemPageVo> voList, List<SubpackageDao> subpackageDaoList,
                                             Map<String, List<TimeNodeVo>> timeNodeMap) {
        voList.forEach(v -> {
            List<SubPackageTimeVo> packageVOList = subpackageDaoList.stream().map(sub -> {
                if (sub.getBuyItemCode().equals(v.getBuyItemCode())) {
                    SubPackageTimeVo packageVo = new SubPackageTimeVo();
                    BeanUtils.copyProperties(sub, packageVo);
                    List<LabelJson> jsonList = convertJson(sub.getLabelJson());
                    packageVo.setJsonList(jsonList);
                    packageVo.setTimeNodeList(timeNodeMap.get(sub.getSubpackageCode()));
                    return packageVo;
                }
                return null;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            v.setSubpackageTimeVoList(packageVOList);
        });
        return voList;
    }

    private List<LabelJson> convertJson(String labelJsonStr) {
        if (StringUtils.hasText(labelJsonStr)) {
            Map<String, String> lableMap = (Map<String, String>) JSON.parse(labelJsonStr);
            List<LabelJson> jsonList = lableMap.keySet().stream().map(k -> {
                LabelJson labelJson = new LabelJson();
                labelJson.setJsonKey(k);
                labelJson.setJsonLabel(lableMap.get(k));
                return labelJson;
            }).collect(Collectors.toList());
            return jsonList;
        }
        return EMPTY_LIST;
    }

    @Override
    public BJXKBuyItemInfoVo queryBJXKBuyItemInfo(String buyItemCode) {
        //查询项目基础信息
        BuyItemDao buyItemDao = publicBaseApi.findOneByBuyItemCode(buyItemCode);
        //查询附加版本信息
        BuyItemBJXKDao bjxkDao = bjxkBuyItemApi.findOneByBuyItemCode(buyItemCode);
        //查询标段基本信息
        List<SubpackageDao> subpackageDaoList = subPackageApi.findByBuyItemCode(buyItemCode);
        // 查询报价信息
        Set<String> subpackageCodeList = subpackageDaoList.stream().map(SubpackageDao::getSubpackageCode).collect(Collectors.toSet());
        Map<String, List<PurchaseQuoteFormVo>> quoteMap = claimsFileQuoteFormService.list(subpackageCodeList);
        //组合数据
        return assembleData(buyItemDao, bjxkDao, subpackageDaoList, quoteMap);
    }

    private BJXKBuyItemInfoVo assembleData(BuyItemDao buyItemDao, BuyItemBJXKDao bjxkDao, List<SubpackageDao> subpackageDaoList,
                                           Map<String, List<PurchaseQuoteFormVo>> quoteMap) {
        BJXKBuyItemInfoVo vo = new BJXKBuyItemInfoVo();
        BeanUtils.copyProperties(buyItemDao, vo);
        BeanUtils.copyProperties(bjxkDao, vo);
        if (StringUtils.hasText(bjxkDao.getParamsAtt())) {
            List<AttachmentDto> attachFileDtoList = JSONArray.parseArray(bjxkDao.getParamsAtt(), AttachmentDto.class);
            vo.setParamsAttList(attachFileDtoList);
        }
        if (StringUtils.hasText(bjxkDao.getMeetingContent())) {
            List<AttachmentDto> attachFileDtoList = JSONArray.parseArray(bjxkDao.getMeetingContent(), AttachmentDto.class);
            vo.setMeetingAttList(attachFileDtoList);
        }
        List<SubPackageInfoVo> subpackageQuoteDtoList = new ArrayList<>();
        subpackageDaoList.forEach(s -> {
            SubPackageInfoVo subPackageInfoVo = new SubPackageInfoVo();
            BeanUtils.copyProperties(s, subPackageInfoVo);
            QuoteFormCreatorDto creatorDto = new QuoteFormCreatorDto();
            creatorDto.setSubpackageCode(s.getSubpackageCode());
            creatorDto.setBodyMaps(EMPTY_LIST);
            creatorDto.setHeads(EMPTY_LIST);
            if (MapUtil.isNotEmpty(quoteMap)) {
                List<LinkedHashMap<String, String>> bodysMapList = new ArrayList<>();
                Optional.ofNullable(quoteMap.get(s.getSubpackageCode()))
                        .ifPresent(list -> list.forEach(q -> {
                            creatorDto.setHeads(q.getHeads());
                            bodysMapList.addAll(q.getBodyMaps());
                        }));
                creatorDto.setBodyMaps(bodysMapList);
            }
            subPackageInfoVo.setClaimsFileQuoteFormCreatorDto(creatorDto);
            subpackageQuoteDtoList.add(subPackageInfoVo);
        });
        vo.setSubpackageDtoList(subpackageQuoteDtoList);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBJXKBuyItemInfo(BJXKCreateBuyItemDto dto) {
        List<BulletinDao> bulletinDaoList = bulletinApi.findByBuyItemCode(dto.getBuyItemCode());
        if (!CollectionUtils.isEmpty(bulletinDaoList)) {
            throw new ServiceException("公告已发，不可修改");
        }
        //先修改基本项目信息
        publicBaseApi.updateBaseInfo(dto, dto.getSubpackageDtoList());
        //修改成功后 再修改 扩展信息
        bjxkBuyItemApi.updateBJXKBuyItemInfo(dto);
    }

    @Override
    public List<SuperPackageVo> findByBuyItemCode(String buyItemCode) {
        List<SubpackageDao> subpackageDaoList = subPackageApi.findByBuyItemCode(buyItemCode);
        List<SuperPackageVo> voList = subpackageDaoList.stream().map(s -> {
            SuperPackageVo vo = new SuperPackageVo();
            BeanUtils.copyProperties(s, vo);
            return vo;
        }).collect(Collectors.toList());
        return voList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delBJXKBuyItemInfo(String buyItemCode) {
        List<BulletinDao> bulletinDaoList = bulletinApi.findByBuyItemCodeAndBulletinTypeAndAuditStatusIn(
                buyItemCode,
                BulletinTypeEnum.BIDDING_ANNOUNCEMENT.getKey(),
                Collections.singletonList(PASS)
        );
        if (!CollectionUtils.isEmpty(bulletinDaoList)) {
            throw new ServiceException("公告已发，不可删除");
        }
        publicBaseApi.delBuyItemInfo(buyItemCode);
        bjxkBuyItemApi.delBJXKBuyItemInfo(buyItemCode);
        bulletinApi.delBulletin(buyItemCode, BulletinTypeEnum.BIDDING_ANNOUNCEMENT.getKey());
        return Boolean.TRUE;
    }
}
