package com.epcos.bidding.purchase.extract.domain.vo;

import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperBuyItemVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/19 10:31
 */
@Data
public class ExtractBuyItemVo extends SuperBuyItemVo {

    @ApiModelProperty(value = "标段编号")
    private String subpackageCode;

    @ApiModelProperty(value = "标段名称")
    private String subpackageName;

    @ApiModelProperty(value = "是否已经抽取评委[true-是, false-否]")
    private Boolean whetherExtract;
}
