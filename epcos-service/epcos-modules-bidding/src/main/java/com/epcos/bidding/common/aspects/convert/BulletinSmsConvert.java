package com.epcos.bidding.common.aspects.convert;

import com.epcos.bidding.common.aspects.params.GetItemParam;
import com.epcos.bidding.common.aspects.sms.SmsContent;
import com.epcos.bidding.common.aspects.vo.GetItemVo;
import com.epcos.bidding.purchase.api.params.vo.buyitem.BuyItemVo;
import com.epcos.bidding.purchase.bulletin.business.api.IBulletinApi;
import com.epcos.bidding.purchase.bulletin.domain.dao.BulletinDao;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.supplier.api.params.SupplierSignUpVo;
import com.epcos.bidding.supplier.sign.business.api.ISupplierSignApi;
import com.epcos.bidding.supplier.sign.domain.dto.SupplierSignQueryDto;
import com.epcos.common.core.utils.DateUtils;
import com.epcos.common.core.utils.StringUtils;
import com.epcos.common.sms.ISmsApi;
import com.epcos.dingtalk.domain.dto.AuditBulletinDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.epcos.bidding.common.enums.BulletinTypeEnum.CHANGE_ANNOUNCEMENT;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/23 14:22
 */
@Slf4j
@Component
public class BulletinSmsConvert implements GetParamConvert<List<AuditBulletinDto>, SmsContent> {

    @Autowired
    private ISupplierSignApi supplierSignApi;
    @Autowired
    private IBulletinApi bulletinApi;
    @Autowired
    private IBuyItemApi buyItemApi;
    @Autowired
    private ISubPackageApi subPackageApi;
    @Autowired
    private ISmsApi smsApi;

    @Override
    public SmsContent doConvert(List<AuditBulletinDto> dtoList) {
        log.error("确认会议时间发送短信，dtoList：{}", dtoList);
        AuditBulletinDto auditBulletinDto = dtoList.get(0);
        BulletinDao bulletin = bulletinApi.getById(auditBulletinDto.getBulletinId());
        GetItemVo itemVo = buyItemApi.getItemVo(new GetItemParam(bulletin.getBuyItemCode(), null, null, true));
        List<SupplierSignUpVo> upVoList = supplierSignApi.query(new SupplierSignQueryDto(bulletin.getBuyItemCode(), null), null);
        List<SupplierSignUpVo> passList = upVoList.stream().filter(s -> s.getReviewStatus() == 1).collect(Collectors.toList());

        BuyItemVo buyItemVo = itemVo.getBuyItemVo();
        Map<String, Date> timeMap = itemVo.getSubpackageDaoList()
                .stream()
                .filter(s -> Objects.nonNull(s.getMeetingTime()))
                .collect(Collectors.toMap(SubpackageDao::getSubpackageCode, SubpackageDao::getMeetingTime));
        log.error("其他参数，passList:{},timeMap：{}", passList, timeMap);
        if (CHANGE_ANNOUNCEMENT.getKey().equals(auditBulletinDto.getBulletinType())) {
            Map<String, AuditBulletinDto> subMaP = dtoList.stream().collect(Collectors.toMap(AuditBulletinDto::getSubpackageCode, Function.identity()));
            passList.forEach(s -> {
                if (Objects.nonNull(subMaP.get(s.getSubpackageCode()))){
                    SubpackageDao subpackageDao = subPackageApi.findBySub(s.getSubpackageCode());
                    if (Objects.nonNull(timeMap.get(s.getSubpackageCode()))) {
                        StringBuilder builder = new StringBuilder();
                        builder.append("您报名的 ")
                                .append(buyItemVo.getBuyItemName())
                                .append(" , ")
                                .append(s.getSubpackageName())
                                .append(" ,于 ")
                                .append(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, timeMap.get(s.getSubpackageCode())))
                                .append("在")
                                .append(StringUtils.hasText(subpackageDao.getBidOpenAddress()) ? subpackageDao.getBidOpenAddress() : "线上")
                                .append(" 开始会议,")
                                .append(" 请在会议开始时间前半小时登录电子化招标采购平台进行签到。")
                        ;
                        log.error("确认会议时间发送短信，手机号：{}，内容：{}", s.getSupplierSignUpInfo().getInfoReporterContactNumber(), builder.toString());
                        smsApi.sendSms(s.getSupplierSignUpInfo().getInfoReporterContactNumber(), builder.toString());
                        log.error("确认会议时间发送短信结束，手机号：{}，内容：{}", s.getSupplierSignUpInfo().getInfoReporterContactNumber(), builder.toString());
                    }
                }
            });
        }
        return new SmsContent();
    }


}
