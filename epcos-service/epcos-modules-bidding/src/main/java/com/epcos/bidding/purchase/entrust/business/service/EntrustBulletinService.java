package com.epcos.bidding.purchase.entrust.business.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.purchase.entrust.business.api.IEntrustBulletinApi;
import com.epcos.bidding.purchase.entrust.domain.dao.EntrustBulletinDao;
import com.epcos.bidding.purchase.entrust.repository.EntrustBulletinMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class EntrustBulletinService extends ServiceImpl<EntrustBulletinMapper, EntrustBulletinDao> implements IEntrustBulletinApi {

    private final EntrustBulletinMapper mapper;

}
