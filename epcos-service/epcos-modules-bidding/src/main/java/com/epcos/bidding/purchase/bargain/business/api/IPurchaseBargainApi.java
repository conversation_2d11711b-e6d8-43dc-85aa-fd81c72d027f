package com.epcos.bidding.purchase.bargain.business.api;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.IBaseService;
import com.epcos.bidding.purchase.api.params.dto.bargin.StartOrEndBargainDto;
import com.epcos.bidding.purchase.bargain.domain.dao.PurchaseBargainDao;
import com.epcos.bidding.purchase.bargain.domain.dto.UpBargainTimeDto;
import com.epcos.bidding.purchase.bargain.domain.vo.PurchaseBargainVo;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/18 14:03
 */
public interface IPurchaseBargainApi extends IBaseService<PurchaseBargainDao> {

    @Override
    default LambdaQueryWrapper<PurchaseBargainDao> queryWrapper(PurchaseBargainDao dao) {
        LambdaQueryWrapper<PurchaseBargainDao> query = Wrappers.lambdaQuery(PurchaseBargainDao.class);
        if (Objects.nonNull(dao.getId())) {
            return query.eq(PurchaseBargainDao::getId, dao.getId());
        }
        return query
                .eq(StringUtils.hasText(dao.getSubpackageCode()), PurchaseBargainDao::getSubpackageCode, dao.getSubpackageCode())
                .eq(Objects.nonNull(dao.getSupplierId()), PurchaseBargainDao::getSupplierId, dao.getSupplierId())
                .eq(Objects.nonNull(dao.getRound()), PurchaseBargainDao::getRound, dao.getRound())
                .eq(StringUtils.hasText(dao.getLaunchStatus()), PurchaseBargainDao::getLaunchStatus, dao.getLaunchStatus())
                .like(StringUtils.hasText(dao.getServiceRequire()), PurchaseBargainDao::getServiceRequire, dao.getServiceRequire())
                .orderByDesc(PurchaseBargainDao::getId);
    }

    default LambdaQueryWrapper<PurchaseBargainDao> queryWrapper(String subpackageCode, String launchStatus) {
        PurchaseBargainDao purchaseBargainDao = new PurchaseBargainDao();
        purchaseBargainDao.setSubpackageCode(subpackageCode);
        purchaseBargainDao.setLaunchStatus(launchStatus);
        return queryWrapper(purchaseBargainDao);
    }

    default LambdaQueryWrapper<PurchaseBargainDao> queryWrapper(String subpackageCode, Integer round, Long supplierId) {
        PurchaseBargainDao purchaseBargainDao = new PurchaseBargainDao();
        purchaseBargainDao.setSubpackageCode(subpackageCode);
        purchaseBargainDao.setRound(round);
        purchaseBargainDao.setSupplierId(supplierId);
        return queryWrapper(purchaseBargainDao);
    }

    default LambdaUpdateWrapper<PurchaseBargainDao> updateWrapper(String subpackageCode, Integer round) {
        return Wrappers.lambdaUpdate(PurchaseBargainDao.class)
                .eq(StringUtils.hasText(subpackageCode), PurchaseBargainDao::getSubpackageCode, subpackageCode)
                .eq(Objects.nonNull(round), PurchaseBargainDao::getRound, round);
    }

    default List<PurchaseBargainDao> find(String subpackageCode, Integer round, Long supplierId) {
        return list(queryWrapper(subpackageCode, round, supplierId));
    }

    default List<PurchaseBargainDao> find(String subpackageCode, Integer round) {
        return find(subpackageCode, round, null);
    }

    default List<PurchaseBargainDao> find(String subpackageCode, String launchStatus) {
        return list(queryWrapper(subpackageCode, launchStatus));
    }

    default List<PurchaseBargainDao> find(String subpackageCode) {
        return list(queryWrapper(subpackageCode, null, null));
    }

    Boolean startOrEndBargain(StartOrEndBargainDto dto);

    List<PurchaseBargainVo> bargainList(String buyItemCode);

    PurchaseBargainDao findBy(String subpackageCode);

    /**
     * 重新评审 需要调用此接口 清除议价数据
     *
     * @param subpackageCode
     */
    void delBargainInfo(String subpackageCode);

    Boolean updateBargainTime(List<UpBargainTimeDto> dto);

    /**
     * 查询标段最大议价轮次
     */
    Integer findMaxRound(String subpackageCode);

    List<PurchaseBargainDao> findMaxBargain(String subpackageCode);

}
