package com.epcos.bidding.common.annotaion;

import org.springframework.core.annotation.Order;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/11 10:58
 */
@Order(170)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface GetTime {

    GetCommon common() default @GetCommon;


}
