package com.epcos.bidding.purchase.technology.domain.dao;

import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.bidding.common.BaseDao;
import com.epcos.common.core.annotation.Gzip;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/4 11:40
 */
@Data
@NoArgsConstructor
@TableName("purchase_buy_item_param")
@ApiModel(description = " 采购项目_技术参数表")
public class BuyItemParamDao extends BaseDao implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "包编码")
    private String subpackageCode;

    @ApiModelProperty(value = "被推送人的id")
    private Long pushedUserId;

    @ApiModelProperty(value = "被推送人的名称")
    private String pushedUserName;

    @ApiModelProperty(value = "参数状态[0-未推送, 1-等待被推送人提交, 2-待确认, 3-已确认，4-不同意]")
    private String paramState;

    @ApiModelProperty(value = "驳回理由")
    private String paramReason;

    @ApiModelProperty(value = "参数推送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pushTime;

    @ApiModelProperty(value = "参数响应时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date responseTime;

    @ApiModelProperty(value = "参数确认时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date confirmTime;

    @ApiModelProperty(value = "备注")
    private String paramRemark;

    @Gzip
    @ApiModelProperty(value = "参数描述")
    private String paramDesc;

    @ApiModelProperty(value = "附件")
    private String paramAttachment;

    @ApiModelProperty(value = "创建者id")
    private Long createId;

    @Gzip
    @ApiModelProperty("内容")
    private String contentHtml;

    @ApiModelProperty("key")
    private String contentKey;

    @ApiModelProperty("审批类型")
    private String auditType;

}
