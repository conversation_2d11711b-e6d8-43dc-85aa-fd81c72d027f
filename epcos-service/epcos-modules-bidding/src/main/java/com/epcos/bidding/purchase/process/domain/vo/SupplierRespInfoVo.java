package com.epcos.bidding.purchase.process.domain.vo;

import com.epcos.bidding.purchase.api.params.AttributeVo;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierSignUpDao;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/29 15:53
 */
@Data
public class SupplierRespInfoVo extends SupplierSignUpDao implements Serializable {

    private static final long serialVersionUID = -7358096968552332770L;

    @ApiModelProperty(value = "供应商附加条件头")
    private List<AttributeVo> supplierAdditionList;

}
