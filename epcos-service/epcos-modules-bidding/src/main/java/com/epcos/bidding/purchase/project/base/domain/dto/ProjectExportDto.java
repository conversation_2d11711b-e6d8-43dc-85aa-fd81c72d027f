package com.epcos.bidding.purchase.project.base.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import java.io.Serializable;
import java.util.Date;

@Data
public class ProjectExportDto implements Serializable {
    private static final long serialVersionUID = 9119604786712288588L;

    @ApiModelProperty("部门id")
    private Integer deptId;

    @ApiModelProperty("采购项目名字")
    @Length(max = 128, message = "采购项目名字，最大长度为128")
    private String buyItemName;

    @ApiModelProperty("采购方式类型")
    @Length(max = 128, message = "采购方式类型，最大长度为128")
    private String purchaseMethodType;

    @ApiModelProperty("完成并归档[0-未完成,1-已完成，未归档,2-已归档]")
    @Range(min = 0, max = 2, message = "完成并归档[0-未完成,1-已完成，未归档,2-已归档]")
    private Integer end;

    @ApiModelProperty("招标项目创建时间的年月日")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonDeserialize(using = LocalDateArrayDeserializer.class)
    private Date[] createAt;

}
