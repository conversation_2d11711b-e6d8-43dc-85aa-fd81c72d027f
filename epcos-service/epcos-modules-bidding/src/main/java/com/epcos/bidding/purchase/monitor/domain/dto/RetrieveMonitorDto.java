package com.epcos.bidding.purchase.monitor.domain.dto;

import com.epcos.common.core.domain.PagerParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/7 10:52
 */
@Data
public class RetrieveMonitorDto extends PagerParam {

    @ApiModelProperty(value = "采购项目名称")
    private String buyItemName;

    @ApiModelProperty(value = "当前登录人", hidden = true)
    private Long userId;
}
