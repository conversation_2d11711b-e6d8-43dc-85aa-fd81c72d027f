package com.epcos.bidding.config;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.remote.RemoteToOtherServiceApi;
import com.epcos.common.core.web.domain.user.SysUser;
import com.epcos.common.sms.ISmsApi;
import com.epcos.system.api.domain.SysDictData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

import java.util.HashMap;
import java.util.Map;

import static com.epcos.common.core.constant.CacheConstants.COMMENT_SUPPLIER;
import static com.epcos.common.core.constant.UserConstants.*;

/**
 * redis key 过期事件
 */
@Slf4j
public class RedisKeyExpirationCommentListener extends KeyExpirationEventMessageListener {

    @Autowired
    private RemoteToOtherServiceApi remoteToOtherServiceApi;
    @Autowired
    private ISmsApi smsApi;
    @Autowired
    private IBuyItemApi buyItemApi;

    public RedisKeyExpirationCommentListener(RedisMessageListenerContainer listenerContainer) {
        super(listenerContainer);
    }

    @Override
    public void onMessage(Message message, byte[] pattern) {
        String expireKey = message.toString();
        if (expireKey.startsWith(COMMENT_SUPPLIER)) {
            System.out.println("expireKey = " + expireKey);
            String[] split = expireKey.split(":");
            String buyItemCode = split[split.length - 1];
            send(buyItemCode);
        }
    }

    private void send(String buyItemCode) {
        BuyItemDao buyItemInfo = buyItemApi.findBuyItemInfo(buyItemCode);
        SysUser sysUserInfo = remoteToOtherServiceApi.getSysUserInfo(buyItemInfo.getUserId());
        String lgSms = remoteToOtherServiceApi.getConfigKeyData(LG_SMS_PARAMETER, String.class);
        JSONObject jsonObject = JSONUtil.parseObj(lgSms);
        String url = jsonObject.getStr("url");
        String appId = jsonObject.getStr("appId");
        String privateKey = jsonObject.getStr("privateKey");
        SysDictData data = remoteToOtherServiceApi.getDict(LG_SMS_TEMPLATE_ID, PROJECT_NAME);
        Map<String, String> map = new HashMap<>();
        map.put("url", url);
        map.put("appId", appId);
        map.put("privateKey", privateKey);
        map.put("templateId", data.getDictValue());
        map.put("type", PROJECT_NAME);
        map.put("data", buyItemInfo.getBuyItemName());
        String jsonBody = JSONUtil.toJsonStr(map);
        smsApi.sendSms(sysUserInfo.getPhonenumber(), jsonBody);
    }


}
