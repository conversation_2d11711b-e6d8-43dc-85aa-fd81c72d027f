package com.epcos.bidding.common.aspects.project;

import com.epcos.bidding.common.annotaion.GetTime;
import com.epcos.bidding.common.aspects.AbstractMethod;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.purchase.api.params.vo.bulletin.BulletinAndItemTimeVo;
import com.epcos.bidding.purchase.bulletin.business.api.IBulletinApi;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/11 11:00
 */
@Slf4j
@Aspect
@Component
public class TimeAspectFilterChainPost extends AbstractMethod<GetTime, BulletinAndItemTimeVo> {

    @Autowired
    private IBulletinApi bulletinApi;

    public TimeAspectFilterChainPost() {
        super(GetUtil.GET_TIME, "获取项目时间");
    }

    @Override
    @Around(value = "@annotation(getTime)")
    public Object around(ProceedingJoinPoint point, GetTime getTime) {
        return super.around(point, getTime);
    }

    @Override
    public BulletinAndItemTimeVo businessMethods(JoinPoint point, GetTime getTime) {
        String subPackageCode = threadLocal.get().getSubpackageCode();
        return bulletinApi.bulletinItemTimeInfo(subPackageCode);
    }


}
