package com.epcos.bidding.purchase.home.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

import static com.epcos.common.core.constant.PurchaseConstants.BulletinAudit.PASS;
import static com.epcos.common.core.constant.PurchaseConstants.BulletinShow.ALL_VISIBLE;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/8 14:45
 */
@Data
public class HomePageDto implements Serializable {

    private static final long serialVersionUID = 4901302559928485762L;

    @ApiModelProperty(value = "搜索关键字")
    private String[] words;

    @ApiModelProperty(value = "公告名称，用于模糊匹配")
    private String bulletinName;

    @ApiModelProperty(value = "公告类型")
    private String bulletinType;

    @ApiModelProperty(value = "采购方式类型")
    private String purchaseMethodType;


    /**
     * 后台自动获取
     */
    @ApiModelProperty(value = "组织code")
    private String orgCode;

    /**
     * 后台自动获取
     * 审核状态，0-不同意，1-同意，2-待审批，3-撤回
     */
    @ApiModelProperty(value = "审核状态，0-不同意，1-同意，2-待审批，3-撤回", hidden = true)
    private String auditStatus;


    /**
     * 后台自动获取
     * 是否展示 0-全网展示 1-供应商可见 2-不展示
     */
    @ApiModelProperty(value = "是否展示", hidden = true)
    private String whetherShow;

    /**
     * 后台自动获取
     * 表名
     */
    @ApiModelProperty(value = "表名", hidden = true)
    private String tableName;


    public void defaultValue() {
        this.auditStatus = PASS;
        this.whetherShow = ALL_VISIBLE;
    }
}
