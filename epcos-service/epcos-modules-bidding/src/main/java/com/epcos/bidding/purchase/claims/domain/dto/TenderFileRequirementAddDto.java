package com.epcos.bidding.purchase.claims.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
public class TenderFileRequirementAddDto implements Serializable {
    private static final long serialVersionUID = -2761941721986175856L;


    @ApiModelProperty("标段编码")
    @NotBlank(message = "标段编码不能为空")
    @Length(max = 64, message = "标段编码最长64字符")
    private String subpackageCode;

    @ApiModelProperty("属性描述")
    @NotEmpty(message = "属性描述不能为空")
    private List<TenderFileRequirementDetailsDto> requestsDetailList;

}
