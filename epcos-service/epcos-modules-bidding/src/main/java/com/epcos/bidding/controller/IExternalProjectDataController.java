package com.epcos.bidding.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.webservice.external.project.domain.dao.ExternalProjectDataDao;
import com.epcos.bidding.purchase.webservice.external.project.service.IExternalProjectDataService;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.web.page.TableDataVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/16 17:01
 */
@RequestMapping("/project.before")
@RestController
@Slf4j
@Api(tags = "项目立项--通用")
@RequiredArgsConstructor
public class IExternalProjectDataController {

    private final IExternalProjectDataService externalProjectDataService;

    @ApiOperation("查询项目立项文件列表")
//    @RequiresPermissions(value = "")
    @PostMapping("/page")
    public TableDataVo<ExternalProjectDataDao> page(@RequestBody PageSortEntity<ExternalProjectDataDao> dto) {
        IPage<ExternalProjectDataDao> page = externalProjectDataService.projectBeforePage(dto);
        return new TableDataVo(page.getRecords(), page.getTotal());
    }

    @ApiOperation("查询项目立项文件详细信息")
//    @RequiresPermissions(value = "")
    @GetMapping("/page")
    public R<ExternalProjectDataDao> info(@RequestParam @NotNull Long id) {
        return R.ok(externalProjectDataService.getById(id));
    }

    @ApiOperation("删除项目立项文件信息")
//    @RequiresPermissions(value = "")
    @GetMapping("/del")
    public R<Boolean> del(@NotNull @NotEmpty Long[] ids) {
        externalProjectDataService.del(ids);
        return R.ok();
    }
}
