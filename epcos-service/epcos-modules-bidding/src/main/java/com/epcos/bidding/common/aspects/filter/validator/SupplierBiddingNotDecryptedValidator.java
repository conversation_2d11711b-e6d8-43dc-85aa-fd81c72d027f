package com.epcos.bidding.common.aspects.filter.validator;

import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierBiddingDao;

/**
 * 校验未解密
 */
public class SupplierBiddingNotDecryptedValidator implements ResultPostHandlerFilterChain<SupplierBiddingDao> {
    @Override
    public void postHandler(AspectContext context, SupplierBiddingDao result) {
        result.verifyNotDecrypted();
    }
}
