package com.epcos.bidding.common.annotaion;

import org.springframework.core.annotation.Order;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 查询开标信息
 * <AUTHOR>
 */
@Order(270)
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface GetBidOpening {

    GetCommon common() default @GetCommon;

}
