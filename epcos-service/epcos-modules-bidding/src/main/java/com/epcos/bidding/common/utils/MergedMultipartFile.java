package com.epcos.bidding.common.utils;

import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;

/**
 * 合并后的文件包装器，用于将合并后的File转换为MultipartFile
 * 
 * <AUTHOR>
 */
public class MergedMultipartFile implements MultipartFile {
    
    private final File file;
    private final String originalFilename;
    private final String contentType;
    
    public MergedMultipartFile(File file) {
        this.file = file;
        this.originalFilename = file.getName();
        this.contentType = "application/octet-stream";
    }
    
    public MergedMultipartFile(File file, String originalFilename) {
        this.file = file;
        this.originalFilename = originalFilename;
        this.contentType = "application/octet-stream";
    }
    
    public MergedMultipartFile(File file, String originalFilename, String contentType) {
        this.file = file;
        this.originalFilename = originalFilename;
        this.contentType = contentType;
    }
    
    @Override
    public String getName() {
        return "file";
    }
    
    @Override
    public String getOriginalFilename() {
        return originalFilename;
    }
    
    @Override
    public String getContentType() {
        return contentType;
    }
    
    @Override
    public boolean isEmpty() {
        return file.length() == 0;
    }
    
    @Override
    public long getSize() {
        return file.length();
    }
    
    @Override
    public byte[] getBytes() throws IOException {
        return Files.readAllBytes(file.toPath());
    }
    
    @Override
    public InputStream getInputStream() throws IOException {
        return new FileInputStream(file);
    }
    
    @Override
    public void transferTo(File dest) throws IOException, IllegalStateException {
        Files.copy(file.toPath(), dest.toPath());
    }
}
