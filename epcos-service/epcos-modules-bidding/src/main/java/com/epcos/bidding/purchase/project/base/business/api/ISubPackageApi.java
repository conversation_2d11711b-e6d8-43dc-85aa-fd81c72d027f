package com.epcos.bidding.purchase.project.base.business.api;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.epcos.bidding.common.IBaseService;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.api.params.dto.BaseQueryDto;
import com.epcos.bidding.purchase.api.params.dto.SubPackageBaseDto;
import com.epcos.bidding.purchase.claims.domain.dto.ClaimsFilePageDto;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.purchase.project.base.domain.vo.LabelJson;
import com.epcos.bidding.purchase.win.domain.dto.SaveSourceDto;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 公用基础信息
 * @date 2024/4/23 14:06
 */
public interface ISubPackageApi extends IBaseService<SubpackageDao> {

    @Override
    default LambdaQueryWrapper<SubpackageDao> queryWrapper(SubpackageDao dao) {
        LambdaQueryWrapper<SubpackageDao> query = Wrappers.lambdaQuery(SubpackageDao.class);
        if (Objects.nonNull(dao.getId())) {
            return query.eq(SubpackageDao::getId, dao.getId());
        }
        return query.eq(StringUtils.hasText(dao.getSubpackageCode()), SubpackageDao::getSubpackageCode, dao.getSubpackageCode())
                .eq(StringUtils.hasText(dao.getBuyItemCode()), SubpackageDao::getBuyItemCode, dao.getBuyItemCode())
                .like(StringUtils.hasText(dao.getSubpackageName()), SubpackageDao::getSubpackageName, dao.getSubpackageName())
                .eq(Objects.nonNull(dao.getInnerBuyItemPackageCode()), SubpackageDao::getInnerBuyItemPackageCode, dao.getInnerBuyItemPackageCode())
                .eq(StringUtils.hasText(dao.getAbandon()), SubpackageDao::getAbandon, dao.getAbandon());
    }

    default LambdaQueryWrapper<SubpackageDao> queryWrapper(String buyItemCode) {
        return queryWrapper(new SubpackageDao(buyItemCode, null));
    }

    default SubpackageDao findBySubpackageCode(String subpackageCode) {
        return getOne(queryWrapperBySubpackageCode(subpackageCode));
    }

    default SubpackageDao findBySub(String subpackageCode) {
        return getOne(queryWrapperBySubpackageCode(subpackageCode));
    }

    default LambdaQueryWrapper<SubpackageDao> queryWrapperBySubpackageCode(String subpackageCode) {
        return queryWrapper(new SubpackageDao(null, subpackageCode));
    }

    default List<SubpackageDao> packageListInfo(Collection<String> buyItemCodeList) {
        return list(queryWrapperBuyItemCodeIn(buyItemCodeList));
    }

    default LambdaUpdateWrapper<SubpackageDao> updateWrapper(String buyItemCode, String subpackageCode) {
        return Wrappers.lambdaUpdate(SubpackageDao.class)
                .eq(StringUtils.hasText(buyItemCode), SubpackageDao::getBuyItemCode, buyItemCode)
                .eq(StringUtils.hasText(subpackageCode), SubpackageDao::getSubpackageCode, subpackageCode);
    }

    default LambdaUpdateWrapper<SubpackageDao> updateWrapper(String subpackageCode) {
        return Wrappers.lambdaUpdate(SubpackageDao.class)
                .eq(StringUtils.hasText(subpackageCode), SubpackageDao::getSubpackageCode, subpackageCode);
    }


    default LambdaQueryWrapper<SubpackageDao> queryWrapperBuyItemCodeIn(Collection<String> buyItemCodeList) {
        return Wrappers.lambdaQuery(SubpackageDao.class)
                .in(CollUtil.isNotEmpty(buyItemCodeList), SubpackageDao::getBuyItemCode, buyItemCodeList);
    }

    default LambdaQueryWrapper<SubpackageDao> queryWrapperBySubpackageCodeIn(Collection<String> subpackageCodeList) {
        return Wrappers.lambdaQuery(SubpackageDao.class)
                .in(CollUtil.isNotEmpty(subpackageCodeList), SubpackageDao::getSubpackageCode, subpackageCodeList);
    }

    default LambdaQueryWrapper<SubpackageDao> queryWrapperBuyItemCodeAndNotEqSubpackageCode(String buyItemCode, String subpackageCode) {
        return Wrappers.lambdaQuery(SubpackageDao.class)
                .eq(StringUtils.hasText(buyItemCode), SubpackageDao::getBuyItemCode, buyItemCode)
                .ne(StringUtils.hasText(subpackageCode), SubpackageDao::getSubpackageCode, subpackageCode);
    }

    default List<SubpackageDao> findBySubpackageCodeIn(List<String> subpackageCodeList) {
        return list(queryWrapperBySubpackageCodeIn(subpackageCodeList));
    }

    List<String> addSubInfo(List<SubPackageBaseDto> subpackageDtoList, String buyItemCode);

    List<SubpackageDao> findByBuyItemCode(String buyItemCode);

    List<String> delSubpackage(String buyItemCode);

    Page<SubpackageDao> findByBuyItemCodeAndSubpackageCodeNotPage(PageSortEntity<ClaimsFilePageDto> dto);

    boolean updateByAbandon(String subpackageCode, String abandon);

    boolean updateByAddress(List<String> subpackageCodes, String address);

    List<SubpackageDao> findBySubpackageCodes(List<String> subpackageCodeList);

    Boolean addLabel(LabelJson dto);

    Boolean delLabel(LabelJson dto);

    Boolean saveSource(SaveSourceDto dto);

    List<SubpackageDao> findTodayBidOPen(Integer pageNum, Integer pageSize, BaseQueryDto baseQueryDto);

    /**
     * @param isToday      是否只查询今天
     * @param baseQueryDto
     * @return
     */
    Long findTodayBidOPenCnt(Boolean isToday, BaseQueryDto baseQueryDto);

    /**
     * 更新时间
     *
     * @param subpackageDao
     */
    void updateTime(SubpackageDao subpackageDao, BuyItemDao buyItemDao, String bulletinType);

    void updateInvite(String subpackageCode, String inviteJson);
}
