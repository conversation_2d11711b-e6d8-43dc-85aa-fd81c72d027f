package com.epcos.bidding.config;

import com.epcos.bidding.purchase.opening.business.service.AutoBidOpenService;
import com.epcos.common.core.constant.CacheConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.util.StringUtils;

import java.util.Optional;

/**
 * redis key 过期事件
 */
@Slf4j
public class RedisKeyExpirationListener extends KeyExpirationEventMessageListener {

    private final AutoBidOpenService autoBidOpenService;

    public RedisKeyExpirationListener(RedisMessageListenerContainer listenerContainer,
                                      AutoBidOpenService autoBidOpenService) {
        super(listenerContainer);
        this.autoBidOpenService = autoBidOpenService;
    }

    @Override
    public void onMessage(Message message, byte[] pattern) {
        String expireKey = message.toString();
        if (expireKey.startsWith(CacheConstants.AUTO_BID_OPEN)) {
            String[] split = expireKey.split(":");
            String subpackageCode = split[split.length - 1];
            String buyItemCode = autoBidOpenService.getRedisHashValue(
                    CacheConstants.AUTO_BID_OPEN_HASH + subpackageCode, "buyItemCode", String.class);
            Integer failCount = Optional.ofNullable(autoBidOpenService.getRedisHashValue(
                            CacheConstants.AUTO_BID_OPEN_HASH + subpackageCode, "failCount", Integer.class))
                    .orElse(0);
            if (StringUtils.hasText(subpackageCode) && StringUtils.hasText(buyItemCode)) {
                autoBidOpenService.autoBidOpen(subpackageCode, buyItemCode, ++failCount);
            }
        }
    }


}
