package com.epcos.bidding.purchase.claims.business.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.common.SubpackageCodeEntity;
import com.epcos.bidding.common.utils.AttributeUtil;
import com.epcos.bidding.purchase.api.params.PurchaseQuoteFormVo;
import com.epcos.bidding.purchase.claims.business.api.IClaimsFileQuoteFormApi;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileQuoteFormDao;
import com.epcos.bidding.purchase.claims.domain.dto.ClaimsFileQuoteFormQueryDto;
import com.epcos.bidding.purchase.api.params.dto.QuoteFormCreatorDto;
import com.epcos.bidding.purchase.claims.mapping.ClaimsFileQuoteFormMap;
import com.epcos.bidding.purchase.claims.repository.ClaimsFileQuoteFormMapper;
import com.epcos.common.core.web.page.TableDataVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ClaimsFileQuoteFormService extends ServiceImpl<ClaimsFileQuoteFormMapper, ClaimsFileQuoteFormDao> implements IClaimsFileQuoteFormApi {

    private final ClaimsFileQuoteFormMap claimsFileQuoteFormMap;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOrUpdate(QuoteFormCreatorDto creator) {
        AttributeUtil.validRequiredAndRegex(false, false, creator.getHeads(), creator.getBodyMaps(), null);
        del(creator.getSubpackageCode());
        List<ClaimsFileQuoteFormDao> asClaimsFileQuoteFormDaos = claimsFileQuoteFormMap.creatorAsEntity(creator);
        saveBatch(asClaimsFileQuoteFormDaos);
    }

    public void delQuote(List<String> subpackageCodeList) {
        subpackageCodeList.forEach(s -> del(s));
    }


    @Override
    public PurchaseQuoteFormVo query(Long id, String subpackageCode) {
        ClaimsFileQuoteFormDao dao = new ClaimsFileQuoteFormDao(subpackageCode);
        dao.setId(id);
        List<ClaimsFileQuoteFormDao> claimsFileQuoteFormDaoList = list(dao);
        return claimsFileQuoteFormMap.asVos(claimsFileQuoteFormDaoList);
    }

    @Override
    public PurchaseQuoteFormVo query(String subpackageCode) {
        return query(null, subpackageCode);
    }


    @Override
    public Map<String, List<PurchaseQuoteFormVo>> list(Set<String> subpackageCodes) {
        if (CollectionUtils.isEmpty(subpackageCodes)) {
            return Collections.emptyMap();
        }
        return list(queryWrapper(subpackageCodes)).stream()
                .collect(Collectors.groupingBy(
                        SubpackageCodeEntity::getSubpackageCode,
                        Collectors.mapping(
                                claimsFileQuoteFormMap::asVo,
                                Collectors.toList())
                ));
    }

    @Override
    public TableDataVo<PurchaseQuoteFormVo> queryPage(PageSortEntity<ClaimsFileQuoteFormQueryDto> pageSort) {
        PageSortEntity<ClaimsFileQuoteFormDao> page = pageSort.convert(claimsFileQuoteFormMap.queryAsEntity(pageSort.getEntity()));
        Page<ClaimsFileQuoteFormDao> pageResult = page(page);
        List<PurchaseQuoteFormVo> vos = pageResult.getRecords()
                .stream()
                .map(claimsFileQuoteFormMap::asVo)
                .collect(Collectors.toList());
        return new TableDataVo<>(vos, pageResult.getTotal());
    }

}
