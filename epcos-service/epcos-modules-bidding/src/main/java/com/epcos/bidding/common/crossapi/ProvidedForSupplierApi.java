package com.epcos.bidding.common.crossapi;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.api.params.AttributeVo;
import com.epcos.bidding.purchase.api.params.PurchaseQuoteFormVo;
import com.epcos.bidding.purchase.api.params.dto.AskDto;
import com.epcos.bidding.purchase.api.params.dto.ReplyDto;
import com.epcos.bidding.purchase.api.params.vo.bulletin.BulletinAndItemTimeVo;
import com.epcos.bidding.purchase.api.params.vo.bulletin.TimeNodeVo;
import com.epcos.bidding.purchase.bargain.domain.dao.PurchaseBargainDao;
import com.epcos.bidding.purchase.bulletin.domain.dao.BulletinDao;
import com.epcos.bidding.purchase.remote.dto.SupplierBulletinPageDto;
import com.epcos.bidding.purchase.remote.vo.SupplierBulletinVo;

import java.util.List;
import java.util.Map;

/**
 * 供应商调用采购人的api
 * 这个接口提供所有供应商需要调用的api
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/28 10:53
 */
public interface ProvidedForSupplierApi {

    /**
     * 获取包对应的时间节点以及时间节点对应的时间信息
     *
     * @param buyItemCodeList 采购项目编号
     * @return Map<String, List < TimeNodeVo>>
     * 返回值格式：
     * key: 包的编号
     * value: 包对应的时间节点以及时间节点对应的时间信息
     */
    Map<String, List<TimeNodeVo>> getTimeNodeList(List<String> buyItemCodeList);

    /**
     * 提交答疑疑问内容
     *
     * @param dto
     * @return
     */
    Boolean submitAsk(AskDto dto);

    /**
     * 提交答疑回复内容
     * 这个是对原有记录进行修改
     * 不做新增操作
     *
     * @param dto
     * @return
     */
    Boolean replyAsk(ReplyDto dto);

    /**
     * 查询包下面的所有时间
     *
     * @param subpackageCode 包code
     * @return
     */
    BulletinAndItemTimeVo bulletinItemTimeInfo(String subpackageCode);

    /**
     * 判断 当前包下供应商是否是被邀请的供应商
     * 一、该标段不是邀请招标项目下的标段，则返回 null
     * 二、该标段是邀请招标项目下的标段
     * -    1、若包含此供应商id，则返回 true
     * -    2、若不包含此供应商id，则返回 false
     * -    3、若该标段所属的项目暂时未发布邀请招标公告，或该公告处于审核未通过的其他状态，则返回 false
     *
     * @param subpackageCode 包编号
     * @param supplierId     供应商id
     * @return
     */
    Boolean whetherInvite(String subpackageCode, Long supplierId);

    /**
     * 是否要求供应商填写股东信息
     *
     * @param subpackageCode 包编码
     * @return ture---要求，false---不要求
     */
    Boolean whetherWrite(String subpackageCode);


    /**
     * 查询供应商附加条件
     *
     * @param subpackageCode 包编号
     * @return
     */
    List<AttributeVo> querySupplierAddition(String subpackageCode);

    /**
     * true 表示 已经发送成交结果公示并且已经审核通过，即对应已完成
     * false 反之
     *
     * @return
     */
    Boolean queryComplete(String subpackageCode);

    /**
     * 查询指定类型以及审核状态的公告
     *
     * @param subpackageCode   包编号
     * @param bulletinTypeList 公告类型【类型请使用--BulletinTypeEnum】
     * @param auditStatusList  公告审核状态【状态请使用 PurchaseConstants.BulletinAudit】
     * @return
     */
    List<BulletinDao> queryBulletin(String subpackageCode, List<String> bulletinTypeList, List<String> auditStatusList);

    /**
     * 包编号必须要传，议价轮数可以不传。
     * 若不传议价轮数，则查询该报下所有的轮数状态信息
     *
     * @param subpackageCode 包编号
     * @param round          议价的轮数
     * @param supplierId     供应商id
     * @return
     */
    List<PurchaseBargainDao> getBargain(String subpackageCode, Integer round, Long supplierId);


    /**
     * 被邀请的供应商查询 邀请招标公告
     *
     * @param dto
     * @return
     */
    IPage<SupplierBulletinVo> supplierBulletinPage(PageSortEntity<SupplierBulletinPageDto> dto);

    PurchaseQuoteFormVo queryShopInfo(String subpackageCode);
}
