package com.epcos.bidding.purchase.extract.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/10 9:22
 */
@Data
public class MarkJudgeLogDto implements Serializable {

    private static final long serialVersionUID = 6327464322812295789L;

    @ApiModelProperty(value = "抽取记录名称")
    @NotBlank
    @Length(max = 256, message = "抽取记录名称超出长度限制")
    private String extractLongName;

    @ApiModelProperty(value = "抽取记录名称下的评委id")
    private List<Long> judgeIdList;

    @ApiModelProperty(value = "不能到场理由")
    private String refuseReason;
}
