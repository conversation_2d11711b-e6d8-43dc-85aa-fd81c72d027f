package com.epcos.bidding.purchase.project.bjxk.domain.dto;

import com.epcos.bidding.purchase.api.params.dto.BaseQueryDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Digits;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 北京胸科医院版本
 * @date 2024/4/23 14:40
 */
@Data
@ApiModel(description = "北京胸科医院版本")
public class BJXKBuyItemQueryDto extends BaseQueryDto {

    private static final long serialVersionUID = 1770521218768516531L;

    @ApiModelProperty(value = "采购类型")
    private String buyClass;

    @ApiModelProperty(value = "'预算总金额（元）")
    @Digits(integer = 18, fraction = 5, message = "预算金额【18位整数，5位小数】")
    private BigDecimal buyBudget;
}
