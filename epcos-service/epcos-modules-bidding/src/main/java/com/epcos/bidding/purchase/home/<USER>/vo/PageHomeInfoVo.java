package com.epcos.bidding.purchase.home.domain.vo;

import com.epcos.bidding.purchase.project.base.domain.vo.BuyItemBaseVo;
import com.epcos.common.core.domain.AttachmentDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/23 14:03
 */
@Data
public class PageHomeInfoVo extends BuyItemBaseVo {

    private static final long serialVersionUID = 5441645150027349577L;

    @ApiModelProperty(value = "公告id")
    private Long bulletinId;

    @ApiModelProperty(value = "公告名称")
    private String bulletinName;

    @ApiModelProperty(value = "公告附件key")
    private List<AttachmentDto> attachmentDtoList;

    @ApiModelProperty(value = "公告的pdf_key")
    private String bulletinContentKey;

    @ApiModelProperty(value = "公告类型[即后台配置的公告类型]")
    @NotBlank
    private String bulletinType;

    @ApiModelProperty(value = "公告类型中文名[即后台配置的公告类型中文名]")
    @NotBlank
    private String bulletinTypeName;

    @ApiModelProperty(value = "公告审核时间")
    private Date reviewTime;

    @ApiModelProperty(value = "是否要求供应商填写股东信息 0-不需要 1-需要")
    private Integer whetherWrite;

    @ApiModelProperty(value = "采购人组织名字")
    private String tenderName;

    @ApiModelProperty(value = "服务器当前时间")
    private Date serverTime;

    @ApiModelProperty(value = "公告标段信息")
    private List<PageHomeSimpleVo> simpleVoList;

    @ApiModelProperty(value = "某个项目下的所有公告")
    private List<BulletinSimpleVo> bulletinSimpleVoList;

    /**
     * 这里 不能随意更换 subpackageDtoList 名字 页面对应回显 需要 这个名字
     */
//    @ApiModelProperty(value = "标段信息")
//    private List<SubPackageInfoVo> subpackageDtoList;

}
