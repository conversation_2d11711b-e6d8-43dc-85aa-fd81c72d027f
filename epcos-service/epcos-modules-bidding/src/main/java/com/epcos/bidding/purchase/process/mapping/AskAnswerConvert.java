package com.epcos.bidding.purchase.process.mapping;

import com.epcos.bidding.purchase.api.params.dto.AskDto;
import com.epcos.bidding.purchase.api.params.vo.answer.AnswerVo;
import com.epcos.bidding.purchase.process.domain.dao.AskAnswerDao;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/6 11:46
 */
@Mapper
public interface AskAnswerConvert {

    AskAnswerConvert INSTANCE = Mappers.getMapper(AskAnswerConvert.class);


    AskAnswerDao convert(AskDto askDto);

    AnswerVo convert(AskAnswerDao askAnswerDao);
}
