package com.epcos.bidding.purchase.claims.domain.dao;

import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.bidding.common.utils.FileMenuEntityShared;
import com.epcos.bidding.common.utils.SectionList;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Optional;


@Data
@NoArgsConstructor
@ApiModel(description = "采购文件章节及章节子内容")
@TableName("claims_file_menu")
public class ClaimsFileMenuDao extends FileMenuEntityShared {

    public ClaimsFileMenuDao(String subpackageCode) {
        this.subpackageCode = subpackageCode;
    }

    @Override
    public String toString() {
        return "ClaimsFileMenuDao{" +
                "chapterType=" + chapterType +
                ", attachKey='" + attachKey + '\'' +
                ", chapterName='" + chapterName + '\'' +
                ", orderNum=" + orderNum +
                ", pid=" + pid +
                ", subpackageCode='" + subpackageCode + '\'' +
                ", createAt=" + createAt +
                ", createBy='" + createBy + '\'' +
                ", deleted=" + deleted +
                ", id=" + id +
                ", updateAt=" + updateAt +
                ", updateBy='" + updateBy + '\'' +
                '}';
    }

    public static ClaimsFileMenuDao by(String subpackageCode, Integer orderNum, String chapterName) {
        ClaimsFileMenuDao claimsFileMenuDao = new ClaimsFileMenuDao();
        claimsFileMenuDao.setSubpackageCode(subpackageCode);
        claimsFileMenuDao.setOrderNum(orderNum);
        claimsFileMenuDao.setChapterName(chapterName);
        return claimsFileMenuDao;
    }

    public static ClaimsFileMenuDao by(Long pid, String subpackageCode, SectionList section) {
        return Optional.ofNullable(section).map(s -> {
            ClaimsFileMenuDao claimsFileMenuDao = new ClaimsFileMenuDao();
            claimsFileMenuDao.setSubpackageCode(subpackageCode);
            claimsFileMenuDao.setOrderNum(s.getOrder());
            claimsFileMenuDao.setChapterName(s.getSectionName());
            claimsFileMenuDao.setPid(pid);
            claimsFileMenuDao.setChapterType(s.getChapterType());
            claimsFileMenuDao.setChapterContext(s.getSectionText());
            return claimsFileMenuDao;
        }).orElseGet(() -> {
            ClaimsFileMenuDao claimsFileMenuDao = new ClaimsFileMenuDao();
            claimsFileMenuDao.setSubpackageCode(subpackageCode);
            claimsFileMenuDao.setPid(pid);
            return claimsFileMenuDao;
        });
    }
}
