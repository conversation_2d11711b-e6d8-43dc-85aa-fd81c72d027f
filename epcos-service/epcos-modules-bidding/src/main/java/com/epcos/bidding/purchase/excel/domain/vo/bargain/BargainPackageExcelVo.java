package com.epcos.bidding.purchase.excel.domain.vo.bargain;

import com.epcos.bidding.purchase.excel.domain.Excel;
import com.epcos.bidding.supplier.api.params.SupplierQuoteFormVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/6 9:07
 */
@Data
public class BargainPackageExcelVo extends Excel {

    @ApiModelProperty(value = "采购项目code")
    private String buyItemCode;

    @ApiModelProperty(value = "采购项目名字")
    private String buyItemName;

    @ApiModelProperty(value = "标段编号")
    @Length(max = 64, message = "标段名称 超出长度限制")
    private String subpackageCode;

    @ApiModelProperty(value = "标段名称")
    @Length(max = 64, message = "标段名称 超出长度限制")
    private String subpackageName;

    @ApiModelProperty("当前包所有供应商的报价")
    private List<SupplierQuoteFormVo> supplierQuoteFormList;
}
