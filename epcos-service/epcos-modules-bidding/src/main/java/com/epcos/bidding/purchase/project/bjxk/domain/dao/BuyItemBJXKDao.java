package com.epcos.bidding.purchase.project.bjxk.domain.dao;

import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.bidding.common.BaseDao;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 采购项目-  江西肿瘤版本
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2023/8/12 11:29
 * @Copyright © 2023-2026 易建采科技（武汉）有限公司
 */

@Data
@ApiModel(description = " 采购项目-  北京胸科医院版本")
@NoArgsConstructor
@TableName("purchase_bjxk_buy_item")
public class BuyItemBJXKDao extends BaseDao implements Serializable {

    public BuyItemBJXKDao(String buyItemCode) {
        this.buyItemCode = buyItemCode;
    }

    private static final long serialVersionUID = -1184858430217163721L;

    @ApiModelProperty(value = "钉钉项目表主键id")
    private Long dingTalkId;

    @ApiModelProperty(value = "采购项目code")
    private String buyItemCode;

    @ApiModelProperty(value = "采购项目名称")
    private String buyItemName;

    @ApiModelProperty(value = "申请人")
    private String apply;

    @ApiModelProperty(value = "申请科室")
    private String applyDept;

    @ApiModelProperty(value = "预算号/课题号")
    private String budgetNumber;

    @ApiModelProperty(value = "采购目的")
    private String buyPurpose;

    @ApiModelProperty(value = "采购类型：货物/服务")
    private String buyClass;

    @ApiModelProperty(value = "采购标的")
    private String procurementType;

    @ApiModelProperty(value = "货物采购类型：医用设备采购/医用设备维修配件及维修服务采购/信息类办公设备及维修配件采购/其他")
    private String goodsType;

    @ApiModelProperty(value = "是否超过5万")
    private String middleAmount;

    @ApiModelProperty(value = "详细参数附件")
    private String paramsAtt;

    @Digits(integer = 18, fraction = 5, message = "预算总金额（元）【18位整数，5位小数】")
    @ApiModelProperty(value = "预算总金额（元）")
    private BigDecimal buyBudget;

    @ApiModelProperty(value = "是否涉及大额资金（20万元以上）")
    private String largeAmount;

    @ApiModelProperty(value = "资金来源：研究所/医院/工会")
    private String capitalSource;

    @ApiModelProperty(value = "预算类型：财政项目/科研项目/自有资金")
    private String budgetType;

    @ApiModelProperty(value = "项目/课题负责人")
    private String buyPerson;

    @ApiModelProperty(value = "课题号")
    private String classNum;

    @ApiModelProperty(value = "院长办公纪要")
    private String meetingContent;

    @ApiModelProperty(value = "备注")
    private String buyRemark;
}
