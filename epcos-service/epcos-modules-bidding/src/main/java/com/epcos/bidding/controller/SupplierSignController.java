package com.epcos.bidding.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.epcos.bidding.audit.api.BizAuditRelationDto;
import com.epcos.bidding.biz.business.api.IBizAuditRelationApi;
import com.epcos.bidding.common.AuditTypeEnum;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.common.annotaion.*;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.common.aspects.filter.validator.*;
import com.epcos.bidding.common.crossapi.ProvidedForSupplierApi;
import com.epcos.bidding.purchase.api.params.AttributeVo;
import com.epcos.bidding.purchase.bulletin.domain.dao.BulletinDao;
import com.epcos.bidding.purchase.opening.business.api.IPurchaseBidOpeningApi;
import com.epcos.bidding.purchase.remote.dto.SupplierBulletinPageDto;
import com.epcos.bidding.purchase.remote.vo.SupplierBulletinVo;
import com.epcos.bidding.purchase.win.business.api.IWinBidResultApi;
import com.epcos.bidding.purchase.win.domain.vo.WinBidVo;
import com.epcos.bidding.supplier.api.params.SupplierSignUpVo;
import com.epcos.bidding.supplier.contract.business.api.ISupplierContractApi;
import com.epcos.bidding.supplier.sign.business.api.ISupplierProcessNodeApi;
import com.epcos.bidding.supplier.sign.business.api.ISupplierSignApi;
import com.epcos.bidding.supplier.sign.business.api.ISupplierSignUpApi;
import com.epcos.bidding.supplier.sign.domain.dto.*;
import com.epcos.bidding.supplier.sign.domain.vo.AnnouncementAdditionalInformationVo;
import com.epcos.bidding.supplier.sign.domain.vo.SupplierProcessNodeVo;
import com.epcos.bidding.supplier.win.business.api.IWinBidNoticeDownloadApi;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.web.page.TableDataVo;
import com.epcos.common.log.annotation.Log;
import com.epcos.common.log.enums.BusinessType;
import com.epcos.common.redis.annotation.RedisLock;
import com.epcos.common.security.annotation.RequiresPermissions;
import com.epcos.system.api.domain.assmble.vo.FunctionKV;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.epcos.common.core.constant.PurchaseConstants.BulletinAudit.PASS;
import static com.epcos.common.core.constant.PurchaseConstants.UserType.PURCHASER;
import static com.epcos.common.core.constant.PurchaseConstants.UserType.SUPPLIER;

@Slf4j
@Api(tags = "供应商报名信息")
@RestController
@AllArgsConstructor
@RequestMapping(value = "/supplier")
public class SupplierSignController {

    private final ISupplierSignUpApi supplierSignUpService;
    private final ISupplierSignApi supplierSignService;
    private final ProvidedForSupplierApi providedForSupplierApi;
    private final IWinBidResultApi winBidResultApi;
    private final IPurchaseBidOpeningApi purchaseBidOpeningApi;
    private final ISupplierProcessNodeApi supplierProcessNodeApi;
    private final ISupplierContractApi supplierContractApi;
    private final IBizAuditRelationApi bizAuditRelationApi;
    private final IWinBidNoticeDownloadApi winBidNoticeDownloadApi;




    @NotNullUserId
    @ApiOperation("查询当前流程")
    @GetMapping("/nodes")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "subpackageCode", value = "包编号", paramType = "query", dataTypeClass = String.class, required = true),
            @ApiImplicitParam(name = "processRole", value = "流程所属角色[1-采购人，2-供应商，3-专家]", paramType = "query",
                    dataTypeClass = Integer.class)
    })
    public R<List<FunctionKV>> nodes(@RequestParam("subpackageCode") String subpackageCode, @RequestParam("processRole") Integer processRole) {
        Long userId = PURCHASER.equals(processRole.toString()) ? null : SecurityUtils.getUserId();
        SupplierProcessNodeVo processNodeVo = supplierProcessNodeApi.query(subpackageCode, userId, processRole);
        if (Objects.isNull(processNodeVo) || CollUtil.isEmpty(processNodeVo.getFvs())) {
            return R.ok(Collections.emptyList());
        }
        // 兼容之前数据
        boolean compatible = false;
        List<FunctionKV> fvs = processNodeVo.getFvs();
        if (fvs.stream().filter(f -> Objects.isNull(f.getRanking())).anyMatch(i -> Objects.equals("0", i.getIsShow()))) {
            setIsShow(fvs);
            compatible = true;
        }
        Optional<FunctionKV> any = fvs.stream().filter(f -> Objects.equals(f.getPurchaseFunctionKey(), processNodeVo.getPurchaseFunctionKey())).findAny();
        if (!any.isPresent()) {
            return R.ok(fvs);
        }
        FunctionKV curFv = any.get();
        if (notIsShow(fvs, curFv.getRanking())) {
            Integer ranking = curFv.getRanking();
            while (ranking > 0) {
                final Integer finalRank = ranking;
                FunctionKV kv = fvs.stream().filter(f -> Objects.equals(f.getRanking(), finalRank)).findAny().get();
                kv.setIsShow("1");
                compatible = true;
                ranking--;
            }
        }
        if (compatible) {
            // 更新之前旧数据
            supplierProcessNodeApi.updatePurchaseFunctionKey(processNodeVo, processNodeVo.getPurchaseFunctionKey());
        }
        return R.ok(fvs);
    }

    private boolean notIsShow(List<FunctionKV> fvs, Integer rank) {
        while (rank > 0) {
            Integer finalRank = rank;
            Optional<FunctionKV> kv = fvs.stream()
                    .filter(f -> Objects.nonNull(f.getRanking()) && Objects.equals(finalRank, f.getRanking()))
                    .findAny();
            if (!kv.isPresent()) {
                throw new ServiceException("请正确配置流程节点");
            }
            String isShow = kv.get().getIsShow();
            if (Objects.equals("0", isShow)) {
                return true;
            }
            rank--;
        }
        return false;
    }

    private void setIsShow(List<FunctionKV> fvs) {
        fvs.stream().filter(f -> Objects.isNull(f.getRanking()))
                .forEach(i -> i.setIsShow("1"));
    }

    @NotNullUserId
    @ApiOperation("根据包编码查询报名信息")
    @GetMapping("/sign/query/subpackageCode")
    public R<SupplierSignUpVo> queryBySubpackageCode(@RequestParam("subpackageCode") String subpackageCode) {
        List<SupplierSignUpVo> list = supplierSignService.query(new SupplierSignQueryDto(subpackageCode), SecurityUtils.getUserId());
        if (CollUtil.isEmpty(list)) {
            return R.ok(new SupplierSignUpVo());
        }
        if (list.size() != 1) {
            log.error("查询的报名信息数据异常,  subpackageCode={},list={}", subpackageCode, list);
            return R.fail("查询的报名信息数据异常");
        }
        SupplierSignUpVo vo = list.get(0);
        Optional.ofNullable(providedForSupplierApi.bulletinItemTimeInfo(subpackageCode)).ifPresent(vo::setTimeVo);
        Optional.ofNullable(purchaseBidOpeningApi.query(subpackageCode)).ifPresent(i -> {
            vo.setRecordFile(i.getRecordFile());
            vo.setStatus(i.getStatus());
        });
        return R.ok(vo);
    }

    @NotNullUserId
    @ApiOperation("根据采购项目编码查询当前供应商报名信息")
    @GetMapping("/sign/query/buyItemCode")
    public R<List<SupplierSignUpVo>> queryByBuyItemCode(@RequestParam("buyItemCode") String buyItemCode) {
        return R.ok(supplierSignService.query(new SupplierSignQueryDto(buyItemCode, null), SecurityUtils.getUserId()));
    }

    @NotNullUserId
    @ApiOperation("根据条件查询报名信息")
    @PostMapping("/sign/list")
    public R<List<SupplierSignUpVo>> list(@RequestBody @Valid SupplierSignQueryDto dto) {
        PageSortEntity<SupplierSignQueryDto> of = new PageSortEntity<>(dto);
        IPage<SupplierSignUpVo> list = supplierSignService.findSignUpWithBiddingAndSearch(of, SecurityUtils.getUserId());
        return R.ok(list.getRecords());
    }

    @NotNullUserId
    @ApiOperation("分页查询报名信息")
    @PostMapping("/sign/page")
    public TableDataVo<SupplierSignUpVo> page(@RequestBody @Valid PageSortEntity<SupplierSignQueryDto> pageDto) {
        IPage<SupplierSignUpVo> page = supplierSignService.findSignUpWithBiddingAndSearch(pageDto, SecurityUtils.getUserId());
        return new TableDataVo<>(page.getRecords(), page.getTotal());
    }

    @NotNullUserId
    @ApiOperation("查询被邀请项目的采购公告列表")
    @PostMapping("/invite/bulletin/page")
    public TableDataVo<SupplierBulletinVo> inviteBulletinPage(@RequestBody @Valid PageSortEntity<SupplierBulletinPageDto> pageDto) {
        pageDto.getEntity().setSupplierId(SecurityUtils.getUserId());
        pageDto.getEntity().setAuditStatus(PASS);
        IPage<SupplierBulletinVo> page = providedForSupplierApi.supplierBulletinPage(pageDto);
        page.getRecords().stream()
                .filter(i -> Objects.nonNull(i.getSignTimeVoList()))
                .flatMap(i -> i.getSignTimeVoList().stream())
                .forEach(i -> i.setWhetherSign(Objects.nonNull(
                        supplierSignUpService.query(i.getSubpackageCode(), SecurityUtils.getUserId()))));
        return new TableDataVo<>(page.getRecords(), page.getTotal());
    }


    @Log(title = "报名", businessType = BusinessType.INSERT)
    @RedisLock(second = 30)
    @NotNullUserId
    @GetTime(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode",
            afters = {RegistrationEndTimeIsNullValidator.class, RegistrationEndTimeHasPassedValidator.class}))
    @GetSupplier(orgCodeEl = "#dto.orgCode", common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode",
            afters = SupplierCompanyInfoAuditFailedValidator.class))
    @GetSignUp(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode", afters = SignUpExistsValidator.class))
    @ApiOperation("报名")
    @PostMapping("/sign/up")
    public R<Boolean> signUp(@RequestBody @Valid SupplierSignUpRequestDto dto) {
        supplierSignService.signUp(SecurityUtils.getUserId(), dto);
        return R.ok();
    }

    @Log(title = "报名信息审核失败时，修改报名信息重新提交(供应商重新报名)", businessType = BusinessType.UPDATE)
    @NotNullUserId
    @RedisLock(second = 30)
    @GetTime(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode",
            afters = {RegistrationEndTimeIsNullValidator.class, RegistrationEndTimeHasPassedValidator.class}))
    @GetSupplier(orgCodeEl = "#dto.orgCode", common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode",
            afters = SupplierCompanyInfoAuditFailedValidator.class))
    @GetSignUp(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode",
            afters = {SignUpNotExistsValidator.class, SignUpNotReviewStatusPassedValidator.class}))
    @ApiOperation("报名信息审核失败时，修改报名信息重新提交((供应商重新报名))")
    @PostMapping("/sign/modify")
    public R<Boolean> modify(@RequestBody @Valid SupplierSignUpRequestDto dto) {
        supplierSignService.modify(SecurityUtils.getUserId(), dto, GetUtil.getSignUp());
        return R.ok();
    }

    @Log(title = "供应商保存付款凭证", businessType = BusinessType.UPDATE)
    @NotNullUserId
    @RedisLock(second = 30)
    @RequiresPermissions("supplier:process:operate")
    @ApiOperation("供应商保存付款凭证")
    @PostMapping("/paymentVoucher")
    public R<Boolean> paymentVoucher(@RequestBody @Valid SupplierPaymentVoucherDto dto) {
        supplierSignService.paymentVoucher(dto, SecurityUtils.getUserId());
        return R.ok();
    }

    @NotNullUserId
    @ApiOperation("查询公告附加信息")
    @GetMapping("/additionalInformation")
    public R<AnnouncementAdditionalInformationVo> additionalInformation(@RequestParam("subpackageCode")
                                                                        @NotBlank(message = "包code必填")
                                                                        String subpackageCode) {
        AnnouncementAdditionalInformationVo vo = new AnnouncementAdditionalInformationVo();
        List<AttributeVo> head = providedForSupplierApi.querySupplierAddition(subpackageCode);
        if (CollUtil.isNotEmpty(head)) {
            Optional.ofNullable(supplierSignUpService.query(subpackageCode, SecurityUtils.getUserId()))
                    .ifPresent(f -> vo.setAdditionalInformationMap(f.getAdditionalInformationMap()));
        }
        vo.setAdditionalInformation(head);
        return R.ok(vo);
    }

    @Log(title = "保存公告附加信息", businessType = BusinessType.UPDATE)
    @NotNullUserId
    @RedisLock(second = 30)
    @Jump(subpackageCodeEL = "#dto.subpackageCode", processRole = SUPPLIER)
    @GetSignUp(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode",
            afters = {SignUpNotExistsValidator.class, SignUpReviewStatusPassedValidator.class}))
    @ApiOperation("保存公告附加信息")
    @PostMapping("/additionalInformation")
    public R<Boolean> additionalInformation(@RequestBody @Valid AnnouncementAdditionalInformationDto dto) {
        supplierSignUpService.additionalInformation(
                dto.getSubpackageCode(), dto.getAdditionalInformation(),
                dto.getAdditionalInformationMap(), SecurityUtils.getUserId());
        return R.ok();
    }


    @NotNullUserId
    @ApiOperation("查询中标结果")
    @GetMapping("/bidWinningResult")
    public R<WinBidVo> queryBidWinningResult(@RequestParam("subpackageCode") @NotBlank(message = "包code必填") String subpackageCode) {
        return R.ok(Optional.ofNullable(winBidResultApi.findBy(subpackageCode, SecurityUtils.getUserId()))
                .map(vo -> {
                    Boolean winAudit = providedForSupplierApi.queryComplete(subpackageCode);
                    vo.setResultAnnouncementReviewStatus(winAudit);
                    if (Boolean.TRUE.equals(winAudit) && "1".equals(vo.getWinBid())) {
                        // 中标
                    }
                    vo.setResultNoticeSendingStatus(vo.getResultNotice() != null && !vo.getResultNotice().isEmpty());
                    boolean hasPermission = winBidNoticeDownloadApi.checkDownloadPermission(subpackageCode, SecurityUtils.getUserId());
                    if (!hasPermission){
                        vo.setResultNotice("");
                    }
                    Optional.ofNullable(supplierContractApi.findOneVo(subpackageCode, SecurityUtils.getUserId()))
                            .ifPresent(c -> {
                                vo.setSupplierContractVo(c);
                                vo.setAuditInfoVo(
                                        bizAuditRelationApi.queryAuditInfoVo(
                                                BizAuditRelationDto.builder()
                                                        .auditType(AuditTypeEnum.WINNING_CONTRACT.getType())
                                                        .buyItemCode(c.getBuyItemCode())
                                                        .subpackageCode(subpackageCode)
                                                        .userId(SecurityUtils.getUserId())
                                                        .businessId(c.getId())
                                                        .build()
                                        )
                                );
                            });
                    return vo;
                }).orElse(null));
    }

    @ApiOperation("查询公告")
    @PostMapping("/queryBulletin")
    public R<List<BulletinDao>> queryBulletin(@RequestBody @Valid QueryBulletinDto dto) {
        List<BulletinDao> bulletinDaos = providedForSupplierApi.queryBulletin(
                dto.getSubpackageCode(),
                Objects.isNull(dto.getBulletinType()) ? null : Collections.singletonList(dto.getBulletinType().getKey()),
                Collections.singletonList(PASS)
        );
        return R.ok(bulletinDaos);
    }

//    @ApiOperation("添加邀请供应商")
//    @GetMapping("/addInviteSupplier")
//    public R<Boolean> addInviteSupplier(@RequestParam("bulletinId") Long bulletinId, @RequestParam("bulletinSupplierDtoList") List<BulletinSupplierDto> bulletinSupplierDtoList) {
//        return supplierSignUpService.addInviteSupplier(bulletinId, bulletinSupplierDtoList);
//    }


}
