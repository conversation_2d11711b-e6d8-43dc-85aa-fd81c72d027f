package com.epcos.bidding.purchase.home.business.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.bulletin.business.api.IBulletinApi;
import com.epcos.bidding.purchase.bulletin.domain.dao.BulletinDao;
import com.epcos.bidding.purchase.ev.factory.EvFactory;
import com.epcos.bidding.purchase.home.business.api.HomePageApi;
import com.epcos.bidding.purchase.home.domain.dto.HomePageDto;
import com.epcos.bidding.purchase.home.domain.vo.PageHomeInfoVo;
import com.epcos.bidding.purchase.home.domain.vo.PageHomeVo;
import com.epcos.bidding.purchase.home.domain.vo.WhetherSignVo;
import com.epcos.bidding.purchase.home.mapping.PageHomeVoStruct;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.purchase.remote.RemoteToOtherServiceApi;
import com.epcos.bidding.supplier.api.params.SupplierSignUpVo;
import com.epcos.bidding.supplier.sign.business.api.ISupplierSignApi;
import com.epcos.bidding.supplier.sign.domain.dto.SupplierSignQueryDto;
import com.epcos.system.api.domain.SysOrganize;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.epcos.bidding.common.enums.BulletinTypeEnum.BIDDING_ANNOUNCEMENT;
import static com.epcos.common.core.constant.PurchaseConstants.BulletinAudit.PASS;
import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/7 15:38
 */
@Service
@RequiredArgsConstructor
public class HomePageService implements HomePageApi {

    private final IBulletinApi bulletinApi;
    private final IBuyItemApi buyItemApi;
    private final ISubPackageApi subPackageApi;
    private final ISupplierSignApi supplierSignApi;
    private final RemoteToOtherServiceApi remoteToOtherServiceApi;
    private final PageHomeVoStruct pageHomeVoStruct;

    /**
     * 分页查询首页公告
     *
     * @param dto
     * @return
     */
    @Override
    public IPage<PageHomeVo> homePage(PageSortEntity<HomePageDto> dto) {
        IPage<PageHomeVo> page = bulletinApi.homePage(Page.of(dto.getCalcPageNum(), dto.getCalcPageSize()),
                dto.getEntity());
        //采购人组织名称及报名截止时间
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return page;
        }
        assembleVo(page.getRecords());
        return page;
    }

    /**
     * 组装首页公告信息
     *
     * @param voList
     */
    private void assembleVo(List<PageHomeVo> voList) {
        List<String> orgCodeList = voList.stream().map(PageHomeVo::getOrgCode).collect(Collectors.toList());
        List<String> buyItemCodeList = voList.stream().map(PageHomeVo::getBuyItemCode).collect(Collectors.toList());
        List<SysOrganize> orgInfo = remoteToOtherServiceApi.getOrgInfo(orgCodeList);
        Map<String, String> orgMap = orgInfo.stream().collect(Collectors.toMap(SysOrganize::getOrgCode, SysOrganize::getOrgName));
        //包信息
        List<SubpackageDao> subpackageDaoList = subPackageApi.packageListInfo(buyItemCodeList);
        Map<String, SubpackageDao> subpackageHealthDaoMap =
                subpackageDaoList.stream().collect(Collectors.toMap(SubpackageDao::getSubpackageCode, Function.identity()));
        Map<String, List<SubpackageDao>> subMap = subpackageDaoList.stream().collect(Collectors.groupingBy(SubpackageDao::getBuyItemCode));
        voList.forEach(v -> {
            String subpackageCode = BIDDING_ANNOUNCEMENT.getKey().equals(v.getBulletinType().trim())
                    ? subMap.get(v.getBuyItemCode()).get(0).getSubpackageCode() : v.getSubpackageCode();
            v.setTenderName(orgMap.get(v.getOrgCode()));
            v.setRegistrationEndTime(subpackageHealthDaoMap.get(subpackageCode).getRegistrationEndTime());
        });
    }


    /**
     * 公告详细信息
     *
     * @param bulletinId
     * @return
     */
    @Override
    public PageHomeInfoVo homePageInfo(Long bulletinId) {
        BulletinDao bulletinDao = bulletinApi.getById(bulletinId);
        BuyItemDao buyItemDao = buyItemApi.findBuyItemInfo(bulletinDao.getBuyItemCode());
        String innerCode = EvFactory.getInstance().queryBuyItemMap(bulletinDao.getBuyItemCode()).get("innerCode");
        List<SubpackageDao> subpackageDaoList = subPackageApi.findByBuyItemCode(bulletinDao.getBuyItemCode());
        //查询除当前公告外的其他项目公告
        List<BulletinDao> otherBulletinList = bulletinApi.findBySubpackageCodeAndBulletinTypeInAndAuditStatusIn(buyItemDao.getBuyItemCode(),
                null, null, Collections.singletonList(PASS));
        if (!CollectionUtils.isEmpty(otherBulletinList)) {
            otherBulletinList = otherBulletinList.stream().filter(o -> !o.getId().equals(bulletinId)).collect(Collectors.toList());
        }
        //组织信息
        List<SysOrganize> orgInfo = remoteToOtherServiceApi.getOrgInfo(Arrays.asList(buyItemDao.getOrgCode()));
        return pageHomeVoStruct.asVo(orgInfo, subpackageDaoList, bulletinDao, buyItemDao, otherBulletinList, innerCode);
    }

    @Override
    public BulletinDao find(Long bulletinId) {
        return bulletinApi.getById(bulletinId);
    }


    /**
     * 没有报名则返回false
     * 已经过报名返回true
     *
     * @param buyItemCode
     * @param supplierId
     * @return
     */
    public List<WhetherSignVo> whetherSign(String buyItemCode, Long supplierId) {
        List<SubpackageDao> subpackageDaoList = subPackageApi.findByBuyItemCode(buyItemCode);
        List<WhetherSignVo> whetherSignVoList = subpackageDaoList.stream().map(h -> {
            List<SupplierSignUpVo> voList =
                    supplierSignApi.query(new SupplierSignQueryDto(null, h.getSubpackageCode()), supplierId);
            WhetherSignVo vo = new WhetherSignVo(h.getSubpackageCode(), h.getSubpackageName(),
                    CollectionUtils.isEmpty(voList) ? FALSE : TRUE);
            return vo;
        }).collect(Collectors.toList());
        return whetherSignVoList;
    }
}
