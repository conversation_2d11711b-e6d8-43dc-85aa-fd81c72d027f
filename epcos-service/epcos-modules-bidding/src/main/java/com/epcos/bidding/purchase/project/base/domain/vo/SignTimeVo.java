package com.epcos.bidding.purchase.project.base.domain.vo;

import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/22 10:11
 */
@Data
public class SignTimeVo extends SuperPackageVo {

    @ApiModelProperty(value = "是否报名[true 已报名 false 未报名]")
    private Boolean whetherSign;

    @ApiModelProperty(value = "是否报名截止[true 进行中  false 已截止]")
    private Boolean whetherSignEnd;

}
