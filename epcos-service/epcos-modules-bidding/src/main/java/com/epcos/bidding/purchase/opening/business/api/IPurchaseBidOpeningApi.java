package com.epcos.bidding.purchase.opening.business.api;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.IBaseService;
import com.epcos.bidding.purchase.api.params.PurchaseBidOpeningVo;
import com.epcos.bidding.purchase.opening.domain.dao.PurchaseBidOpeningDao;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public interface IPurchaseBidOpeningApi extends IBaseService<PurchaseBidOpeningDao> {
    @Override
    default LambdaQueryWrapper<PurchaseBidOpeningDao> queryWrapper(PurchaseBidOpeningDao dao) {
        LambdaQueryWrapper<PurchaseBidOpeningDao> query = Wrappers.lambdaQuery(PurchaseBidOpeningDao.class);
        if (Objects.nonNull(dao.getId())) {
            return query.eq(PurchaseBidOpeningDao::getId, dao.getId());
        }
        return query
                .eq(StringUtils.hasText(dao.getSubpackageCode()), PurchaseBidOpeningDao::getSubpackageCode, dao.getSubpackageCode())
                .eq(Objects.nonNull(dao.getStatus()), PurchaseBidOpeningDao::getStatus, dao.getStatus())
                .orderByDesc(PurchaseBidOpeningDao::getId);
    }

    default LambdaQueryWrapper<PurchaseBidOpeningDao> queryWrapper(String subpackageCode, Integer status) {
        return queryWrapper(new PurchaseBidOpeningDao(subpackageCode, status));
    }

    default LambdaQueryWrapper<PurchaseBidOpeningDao> queryWrapperIn(Collection<String> subpackageCodeList) {
        return Wrappers.lambdaQuery(PurchaseBidOpeningDao.class)
                .in(CollUtil.isNotEmpty(subpackageCodeList), PurchaseBidOpeningDao::getSubpackageCode, subpackageCodeList);
    }

    default LambdaUpdateWrapper<PurchaseBidOpeningDao> updateWrapper(String subpackageCode) {
        return Wrappers.lambdaUpdate(PurchaseBidOpeningDao.class)
                .eq(StringUtils.hasText(subpackageCode), PurchaseBidOpeningDao::getSubpackageCode, subpackageCode);
    }

    default PurchaseBidOpeningDao findOne(String subpackageCode) {
        return getOne(queryWrapper(subpackageCode, null));
    }

    default List<PurchaseBidOpeningDao> find(Collection<String> subpackageCodeList) {
        return list(queryWrapperIn(subpackageCodeList));
    }

    PurchaseBidOpeningVo query(String subpackageCode);

    List<PurchaseBidOpeningVo> query(List<String> subpackageCodes);

}
