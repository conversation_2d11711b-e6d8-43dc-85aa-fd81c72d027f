package com.epcos.bidding.purchase.project.base.business.api;

import com.epcos.bidding.purchase.api.params.dto.SubPackageBaseDto;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.base.domain.dto.BuyItemBaseDto;

import java.util.List;

/**
 * 基础项目对外暴露的api  其他子项目只调用 本 api 进行 进本项目的 操作
 *
 * <AUTHOR>
 * @version 1.0
 * @description 公用基础信息
 * @date 2024/4/23 12:25
 */
public interface IPublicBaseApi {

    /**
     * 保存基础项目信息
     * 以及包信息
     *
     * @param baseDto
     * @return
     */
    BuyItemDao insertBaseInfo(BuyItemBaseDto baseDto, List<SubPackageBaseDto> subpackageDtoList);

    BuyItemDao findOneByBuyItemCode(String buyItemCode);

    BuyItemDao updateBaseInfo(BuyItemBaseDto dto, List<SubPackageBaseDto> subpackageDtoList);

    <PERSON><PERSON><PERSON> delBuyItemInfo(String buyItemCode);
}
