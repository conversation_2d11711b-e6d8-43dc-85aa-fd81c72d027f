package com.epcos.bidding.purchase.claims.business.api;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.IBaseService;
import com.epcos.bidding.common.SubpackageCodeEntity;
import com.epcos.bidding.common.utils.BidFileJson;
import com.epcos.bidding.purchase.api.domian.cliams.ClaimsFileReleaseAndStampDto;
import com.epcos.common.core.domain.AuditStatusDto;
import com.epcos.bidding.purchase.api.params.EpcFileContentVo;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileDao;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.DateUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface IClaimsFileApi extends IBaseService<ClaimsFileDao> {

    @Override
    default LambdaQueryWrapper<ClaimsFileDao> queryWrapper(ClaimsFileDao dao) {
        LambdaQueryWrapper<ClaimsFileDao> query = Wrappers.lambdaQuery(ClaimsFileDao.class);
        if (Objects.nonNull(dao.getId())) {
            return query.eq(ClaimsFileDao::getId, dao.getId());
        }
        return query.eq(StringUtils.hasText(dao.getSubpackageCode()), ClaimsFileDao::getSubpackageCode, dao.getSubpackageCode())
                .eq(StringUtils.hasText(dao.getEpcFile()), ClaimsFileDao::getEpcFile, dao.getEpcFile())
                .eq(StringUtils.hasText(dao.getPdfFile()), ClaimsFileDao::getPdfFile, dao.getPdfFile())
                .eq(Objects.nonNull(dao.getReleaseStatus()), ClaimsFileDao::getReleaseStatus, dao.getReleaseStatus())
                .between(Objects.nonNull(dao.getReleaseTime()), ClaimsFileDao::getReleaseTime,
                        DateUtils.start(dao.getReleaseTime()),
                        DateUtils.end(dao.getReleaseTime()))
                .like(StringUtils.hasText(dao.getAppName()), ClaimsFileDao::getAppName, dao.getAppName())
                .like(StringUtils.hasText(dao.getAppVersion()), ClaimsFileDao::getAppVersion, dao.getAppVersion())
                .like(StringUtils.hasText(dao.getZepcKey()), ClaimsFileDao::getZepcKey, dao.getZepcKey());
    }

    default LambdaQueryWrapper<ClaimsFileDao> queryWrapper(String subpackageCode) {
        return queryWrapper(new ClaimsFileDao(subpackageCode));
    }

    default LambdaQueryWrapper<ClaimsFileDao> queryWrapper(Collection<String> subpackageCodeList) {
        return Wrappers.lambdaQuery(ClaimsFileDao.class)
                .in(!CollectionUtils.isEmpty(subpackageCodeList), SubpackageCodeEntity::getSubpackageCode, subpackageCodeList);
    }

    default LambdaUpdateWrapper<ClaimsFileDao> updateWrapper(String subpackageCode) {
        return Wrappers.lambdaUpdate(ClaimsFileDao.class)
                .eq(ClaimsFileDao::getSubpackageCode, subpackageCode);
    }

    default void checkResLog(boolean res, Object... arg) {
        if (!res) {
            log.error("采购文件信息操作异常, args={}", Arrays.toString(arg));
            throw new ServiceException("采购文件信息操作异常");
        }
    }


    Long saveOrUpdate(String subpackageCode, String zipUrl, String pdfUrl, BidFileJson bidFileJson);

    EpcFileContentVo findBySubpackageCodeVo(String subpackageCode);

    ClaimsFileDao findBySubpackageCode(String subpackageCode);

    List<ClaimsFileDao> findBySubpackageCodes(Set<String> subpackageCodes);

    void updateReleaseStatus(ClaimsFileReleaseAndStampDto dto);

    void importOther(String importSubpackageCode, String subpackageCode);

    void updateAuditStatus(AuditStatusDto dto);

}
