package com.epcos.bidding.purchase.opening.business.service;

import cn.hutool.core.io.file.PathUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.epcos.bidding.common.annotaion.*;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.common.aspects.convert.GetBidOpeningSupplierIdParamConvert;
import com.epcos.bidding.common.aspects.convert.PurchaseBidOpeningDtoConvert;
import com.epcos.bidding.common.aspects.filter.validator.BidOpeningStartHasFunctionValidator;
import com.epcos.bidding.common.aspects.vo.GetItemVo;
import com.epcos.bidding.common.enums.FunctionEnum;
import com.epcos.bidding.purchase.api.params.PurchaseBidOpeningVo;
import com.epcos.bidding.purchase.api.params.dto.monitor.CreateMonitorDto;
import com.epcos.bidding.purchase.api.params.vo.buyitem.BuyItemVo;
import com.epcos.bidding.purchase.bulletin.domain.vo.SupplierSignVo;
import com.epcos.bidding.purchase.ev.factory.EvFactory;
import com.epcos.bidding.purchase.monitor.business.api.IMonitorBidApi;
import com.epcos.bidding.purchase.opening.business.api.IBidOpenApi;
import com.epcos.bidding.purchase.opening.business.api.IPurchaseBidOpeningApi;
import com.epcos.bidding.purchase.opening.domain.dao.PurchaseBidOpeningDao;
import com.epcos.bidding.purchase.opening.domain.dto.PurchaseBidOpeningDto;
import com.epcos.bidding.purchase.process.business.api.IReviewBeforeApi;
import com.epcos.bidding.purchase.process.domain.dao.ReviewBeforeDao;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.purchase.project.mapping.DefaultVoAndDtoConvert;
import com.epcos.bidding.supplier.answer.business.api.IAnswerFileQuoteFormApi;
import com.epcos.bidding.supplier.api.params.MultiSupplierQuoteFormVo;
import com.epcos.bidding.supplier.api.params.SupplierSignUpUpdateDto;
import com.epcos.bidding.supplier.api.params.SupplierSignUpVo;
import com.epcos.bidding.supplier.sign.business.api.ISupplierBidderApi;
import com.epcos.bidding.supplier.sign.business.api.ISupplierBiddingApi;
import com.epcos.bidding.supplier.sign.business.api.ISupplierSignApi;
import com.epcos.bidding.supplier.sign.business.api.ISupplierSignUpApi;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierBidderDao;
import com.epcos.bidding.supplier.sign.domain.dto.SupplierSignQueryDto;
import com.epcos.common.core.constant.FileTypeNameConstants;
import com.epcos.common.core.constant.RoleConstants;
import com.epcos.common.core.constant.SecurityConstants;
import com.epcos.common.core.enums.ClientEnum;
import com.epcos.common.core.enums.SmsEnum;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.bean.BeanUtils;
import com.epcos.common.redis.publish.BusinessTypeEnum;
import com.epcos.system.api.RemoteTenderInfoService;
import com.epcos.system.api.domain.common.TenderInfoVo;
import com.epcos.system.api.model.FUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.nio.file.Path;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.epcos.common.core.constant.PurchaseConstants.BidOpenState.FINISH;
import static com.epcos.common.core.constant.PurchaseConstants.UserType.PURCHASER;

@Slf4j
@Service
@AllArgsConstructor
public class BidOpenService implements IBidOpenApi {

    private final DefaultVoAndDtoConvert defaultVoAndDtoConvert;
    private final IPurchaseBidOpeningApi purchaseBidOpeningApi;
    private final RemoteTenderInfoService remoteTenderInfoService;
    private final IBuyItemApi buyItemApi;
    private final ISubPackageApi subPackageApi;
    private final IAnswerFileQuoteFormApi answerFileQuoteFormApi;
    private final ISupplierBidderApi supplierBidderApi;
    private final ISupplierBiddingApi supplierBiddingApi;
    private final IReviewBeforeApi reviewBeforeApi;
    private final ISupplierSignUpApi supplierSignUpApi;
    private final ISupplierSignApi supplierSignApi;
    private final IMonitorBidApi monitorBidApi;

    @Override
    @GetItem(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode"))
    public void sing(PurchaseBidOpeningDto dto) {
        GetItemVo item = GetUtil.getItemVo();
        item.setInnerCode(getInnerCode(dto.getBuyItemCode()));
        PurchaseBidOpeningDao dao = new PurchaseBidOpeningDao().sing();
        Path temp = null;
        try {
            temp = generateBidOpeningRecordFile(dto, item);
            BuyItemVo itemVo = item.getBuyItemVo();
            final String fileUrl = FUtil.upFile(itemVo.getBuyItemCode(), itemVo.getYearMonthSplit(),
                    temp.toFile(), FileTypeNameConstants.BIDOPENING_SHEET,
                    dto.getSubpackageCode(), null);
            dao.setRecordFile(fileUrl);
            purchaseBidOpeningApi.update(dao, purchaseBidOpeningApi.updateWrapper(dto.getSubpackageCode()));
        } finally {
            Optional.ofNullable(temp).ifPresent(PathUtil::del);
        }
    }

    /**
     * 查询各家医院的院内编号
     */
    private String getInnerCode(String buyItemCode) {
        Map<String, String> map = EvFactory.getInstance().queryBuyItemMap(buyItemCode);
        if (CollectionUtils.isEmpty(map)) {
            return "";
        } else {
            return map.getOrDefault("innerCode", "");
        }
    }

    // 生成开标记录文件
    private Path generateBidOpeningRecordFile(PurchaseBidOpeningDto dto, GetItemVo itemVo) {
        String tenderer = Optional.ofNullable(remoteTenderInfoService.queryTenderInfo(
                                SecurityConstants.INNER, itemVo.getBuyItemVo().getOrgCode(), null)
                        .getData())
                .map(TenderInfoVo::getTendererName).orElse("");
        Date meetingTime = Optional.ofNullable(subPackageApi.findBySub(dto.getSubpackageCode()))
                .map(SubpackageDao::getMeetingTime)
                .orElse(null);
        Set<Long> supplierIds = dto.getSignUpList()
                .stream()
                .map(SupplierSignUpVo::getSupplierId)
                .collect(Collectors.toSet());
        MultiSupplierQuoteFormVo quoteFormVo = answerFileQuoteFormApi.findVo(dto.getSubpackageCode(), supplierIds, 0);
        Map<Long, SupplierBidderDao> supplierBidderDaoMap = supplierBidderApi.findBy(dto.getSubpackageCode(), supplierIds)
                .stream().collect(Collectors.toMap(SupplierBidderDao::getSupplierId, Function.identity()));
        return new GenerateBidOpeningRecordFile()
                .generate(tenderer, itemVo, meetingTime, quoteFormVo, supplierBidderDaoMap);
    }

    @Override
    @Jump(subpackageCodeEL = "#dto.subpackageCode", processRole = PURCHASER, triggerPoint = "/purchase/bidOpening/start")
    @HasFunction(functionEnum = FunctionEnum.PURCHASER_START_VALID_NUMBER_OF_CHECK_IN, belongRole = "1",
            common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode",
                    afters = BidOpeningStartHasFunctionValidator.class))
    @Msg(
            fromRole = RoleConstants.PURCHASER,
            businessType = BusinessTypeEnum.BID_OPENING,
            msg = "开标开始",
            buyItemCodeEL = "#dto.buyItemCode",
            subpackageCodeEL = "#dto.subpackageCode",
            toUserIdsConvert = GetBidOpeningSupplierIdParamConvert.class
    )
    @Sms(clients = ClientEnum.XY, smsEnum = SmsEnum.SMS_OPEN_DECRYPT, convert = PurchaseBidOpeningDtoConvert.class)
    @Transactional(rollbackFor = Exception.class)
    public void start(PurchaseBidOpeningDto dto) {
        if (Objects.nonNull(purchaseBidOpeningApi.findOne(dto.getSubpackageCode()))) {
            log.error("项目已开标 dto={}", dto);
            return;
        }
        PurchaseBidOpeningDao dao = new PurchaseBidOpeningDao();
        dao.setSubpackageCode(dto.getSubpackageCode());
        dao.start();
        purchaseBidOpeningApi.save(dao);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void complete(PurchaseBidOpeningDto dto) {
        PurchaseBidOpeningVo query = purchaseBidOpeningApi.query(dto.getSubpackageCode());
        if (query.getStatus().equals(FINISH)) {
            throw new ServiceException("已开标完成 请勿重复点击");
        }
        PurchaseBidOpeningDao dao = new PurchaseBidOpeningDao().complete();
        if (purchaseBidOpeningApi.update(dao, purchaseBidOpeningApi.updateWrapper(dto.getSubpackageCode()))) {
            //true表示有开标
            reviewBeforeApi.save(dto, true);
            //设置监标人信息
            PurchaseBidOpeningVo bidOpeningVo = purchaseBidOpeningApi.query(dto.getSubpackageCode());
            SubpackageDao subpackageDao = subPackageApi.findBySubpackageCode(dto.getSubpackageCode());
            CreateMonitorDto monitorDto = new CreateMonitorDto();
            BeanUtils.copyProperties(subpackageDao, monitorDto);
            monitorDto.setMonitorBidType("1");
            monitorDto.setFileKey(bidOpeningVo.getRecordFile());
            monitorBidApi.addMonitorInfo(monitorDto);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void restart(PurchaseBidOpeningDto dto) {
        restartValidate(dto.getSubpackageCode());
        PurchaseBidOpeningDao bidOpeningDao = purchaseBidOpeningApi.findOne(dto.getSubpackageCode());
        if (CharSequenceUtil.isNotEmpty(bidOpeningDao.getRecordFile())) {
            FUtil.delFile(bidOpeningDao.getRecordFile());
        }
        purchaseBidOpeningApi.removeById(bidOpeningDao.getId());
        // 删除开标完成之后产生的后续数据
        reviewBeforeApi.remove(reviewBeforeApi.updateWrapper(dto.getSubpackageCode(), null));
        monitorBidApi.remove(dto.getSubpackageCode(), "1");
        supplierBiddingApi.restoreInitialBidOpeningStatus(dto.getSubpackageCode(),
                dto.getSignUpList()
                        .stream()
                        .map(SupplierSignUpVo::getSupplierId)
                        .collect(Collectors.toSet()));
        SupplierSignUpUpdateDto signUpUpdateDto =
                new SupplierSignUpUpdateDto(dto.getSubpackageCode())
                        .restartBidOpening(dto.getSignUpList()
                                .stream()
                                .map(SupplierSignUpVo::getSupplierId)
                                .collect(Collectors.toSet()));
        supplierSignUpApi.batchUpdateSignUpStatus(signUpUpdateDto);
    }

    private void restartValidate(String subpackageCode) {
        //确认会签之后 不允许重新开标
        List<ReviewBeforeDao> reviewBeforeDaoList = reviewBeforeApi.find(subpackageCode, null);
        if (!CollectionUtils.isEmpty(reviewBeforeDaoList)) {
            String confirmCounterSign = reviewBeforeDaoList.get(0).getConfirmCounterSign();
            if (Objects.nonNull(confirmCounterSign) && confirmCounterSign.equals("1")) {
                throw new ServiceException("已确认会签 不可重新开标");
            }
        }
    }

    @Override
    public List<SupplierSignVo> bidderList(String subpackageCode) {
        BuyItemDao buyItemDao;
        try {
            buyItemDao = buyItemApi.findBySubpackageCode(subpackageCode);
        } catch (NullPointerException e) {
            log.error("查询项目信息为空,subpackageCode={}", subpackageCode);
            return Collections.emptyList();
        }
        SupplierSignQueryDto queryDto = defaultVoAndDtoConvert.dtoAndVoConvert(subpackageCode, buyItemDao);
        List<SupplierSignUpVo> voList = supplierSignApi.query(queryDto, null);
        List<Long> supplierIdList = voList.stream()
                .map(SupplierSignUpVo::getSupplierId)
                .collect(Collectors.toList());
        List<ReviewBeforeDao> beforeDaoList = reviewBeforeApi.find(subpackageCode, supplierIdList);
        List<SupplierSignVo> subVoList = defaultVoAndDtoConvert.dtoAndVoConvert(voList, beforeDaoList);
        return subVoList;
    }

    public PurchaseBidOpeningDao findOne(String subpackageCode) {
        return purchaseBidOpeningApi.findOne(subpackageCode);
    }
}
