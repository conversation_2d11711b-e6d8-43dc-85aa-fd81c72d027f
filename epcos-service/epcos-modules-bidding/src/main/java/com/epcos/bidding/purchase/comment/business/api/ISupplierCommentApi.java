package com.epcos.bidding.purchase.comment.business.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.epcos.bidding.common.IBaseService;
import com.epcos.bidding.purchase.api.params.dto.BaseQueryDto;
import com.epcos.bidding.purchase.comment.domain.dao.SupplierCommentDao;
import com.epcos.bidding.workbench.vo.BidOpenTodayVo;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/19 17:25
 */
public interface ISupplierCommentApi extends IBaseService<SupplierCommentDao> {


    IPage<BidOpenTodayVo> supplierCommentPage(Page<Object> of, Boolean status, Integer days, BaseQueryDto baseQueryDto);
}
