package com.epcos.bidding.purchase.claims.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class TenderFileRequirementDelDto implements Serializable {
    private static final long serialVersionUID = -2761941721986175856L;

    @ApiModelProperty("采购要求id")
    @NotNull(message = "采购要求id不能为空")
    private Long id;
}
