package com.epcos.bidding.purchase.home.business.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.bulletin.domain.dao.BulletinDao;
import com.epcos.bidding.purchase.home.domain.dto.HomePageDto;
import com.epcos.bidding.purchase.home.domain.vo.PageHomeInfoVo;
import com.epcos.bidding.purchase.home.domain.vo.PageHomeVo;
import com.epcos.bidding.purchase.home.domain.vo.WhetherSignVo;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/7 15:38
 */
public interface HomePageApi {

    IPage<PageHomeVo> homePage(PageSortEntity<HomePageDto> dto);

    PageHomeInfoVo homePageInfo(Long bulletinId);

    BulletinDao find(Long bulletinId);

    List<WhetherSignVo> whetherSign(String buyItemCode, Long supplierId);
}
