package com.epcos.bidding.purchase.process.business.api;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.IBaseService;
import com.epcos.bidding.purchase.opening.domain.dto.PurchaseBidOpeningDto;
import com.epcos.bidding.purchase.process.domain.dao.ReviewBeforeDao;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/12 8:54
 */
public interface IReviewBeforeApi extends IBaseService<ReviewBeforeDao> {
    @Override
    default LambdaQueryWrapper<ReviewBeforeDao> queryWrapper(ReviewBeforeDao dao) {
        LambdaQueryWrapper<ReviewBeforeDao> query = Wrappers.lambdaQuery(ReviewBeforeDao.class);
        if (Objects.nonNull(dao.getId())) {
            return query.eq(ReviewBeforeDao::getId, dao.getId());
        }
        return query
                .eq(StringUtils.hasText(dao.getSubpackageCode()), ReviewBeforeDao::getSubpackageCode, dao.getSubpackageCode())
                .eq(Objects.nonNull(dao.getSupplierId()), ReviewBeforeDao::getSupplierId, dao.getSupplierId())
                .eq(StringUtils.hasText(dao.getBidEnterExpert()), ReviewBeforeDao::getBidEnterExpert, dao.getBidEnterExpert())
                .eq(StringUtils.hasText(dao.getConfirmCounterSign()), ReviewBeforeDao::getConfirmCounterSign, dao.getConfirmCounterSign())
                .eq(StringUtils.hasText(dao.getConfirmReview()), ReviewBeforeDao::getConfirmReview, dao.getConfirmReview())
                .eq(StringUtils.hasText(dao.getWhetherShowJudge()), ReviewBeforeDao::getWhetherShowJudge, dao.getWhetherShowJudge())
                .eq(StringUtils.hasText(dao.getEnterTheReview()), ReviewBeforeDao::getEnterTheReview, dao.getEnterTheReview())
                .eq(StringUtils.hasText(dao.getIsBargaining()), ReviewBeforeDao::getIsBargaining, dao.getIsBargaining())
                .orderByDesc(ReviewBeforeDao::getId);
    }

    default LambdaQueryWrapper<ReviewBeforeDao> queryWrapper(String subpackageCode, Collection<Long> supplierIdList) {
        return Wrappers.lambdaQuery(ReviewBeforeDao.class)
                .eq(StringUtils.hasText(subpackageCode), ReviewBeforeDao::getSubpackageCode, subpackageCode)
                .in(CollUtil.isNotEmpty(supplierIdList), ReviewBeforeDao::getSupplierId, supplierIdList);
    }

    default LambdaQueryWrapper<ReviewBeforeDao> queryWrapper(Collection<String> subpackageCodeList) {
        return Wrappers.lambdaQuery(ReviewBeforeDao.class)
                .in(CollUtil.isNotEmpty(subpackageCodeList), ReviewBeforeDao::getSubpackageCode, subpackageCodeList);
    }

    default LambdaUpdateWrapper<ReviewBeforeDao> updateWrapper(String subpackageCode, Long supplierId) {
        return Wrappers.lambdaUpdate(ReviewBeforeDao.class)
                .eq(StringUtils.hasText(subpackageCode), ReviewBeforeDao::getSubpackageCode, subpackageCode)
                .eq(Objects.nonNull(supplierId), ReviewBeforeDao::getSupplierId, supplierId);
    }

    default List<ReviewBeforeDao> find(Collection<String> subpackageCodeList) {
        return list(queryWrapper(subpackageCodeList));
    }

    default List<ReviewBeforeDao> find(String subpackageCode, Collection<Long> supplierIdList) {
        return list(queryWrapper(subpackageCode, supplierIdList));
    }

    default Boolean del(List<String> subpackageCodeList) {
        return remove(queryWrapper(subpackageCodeList));
    }

    void save(PurchaseBidOpeningDto dto, Boolean bool);

    void updateBy(String confirmCounterSign, String subpackageCode, String isBargaining);

    void updateBy(String confirmReview, String subpackageCode);

    void rollbackData(String subpackageCode);
}
