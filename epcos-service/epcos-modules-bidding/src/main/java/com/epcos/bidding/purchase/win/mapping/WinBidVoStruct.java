package com.epcos.bidding.purchase.win.mapping;

import com.epcos.bidding.common.DefaultMapper;
import com.epcos.bidding.purchase.win.domain.dao.WinBidResultDao;
import com.epcos.bidding.purchase.win.domain.vo.WinBidVo;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/18 16:05
 */
@Mapper(componentModel = "spring")
public interface WinBidVoStruct extends DefaultMapper<WinBidVo, WinBidResultDao> {
}
