package com.epcos.bidding.purchase.process.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/3 10:47
 */
@Data
public class MeetDto {

    @ApiModelProperty(value = "采购项目名称")
    private String buyItemName;

    @ApiModelProperty(value = "采购项目创建时间。查询区间的开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createStartTime;

    @ApiModelProperty(value = "采购项目创建时间。查询区间的结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createEndTime;

    @ApiModelProperty(value = "供应商公司名称")
    private String supplierCompanyName;
}
