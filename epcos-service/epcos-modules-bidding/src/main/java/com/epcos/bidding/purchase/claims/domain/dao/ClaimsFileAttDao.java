package com.epcos.bidding.purchase.claims.domain.dao;

import com.baomidou.mybatisplus.annotation.*;
import com.epcos.bidding.common.SubpackageCodeEntity;
import com.epcos.bidding.common.utils.BidFileJson;
import com.epcos.common.core.utils.bean.BeanValidators;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 采购文件附件
 */
@Data
@ApiModel(description = "采购文件附件信息")
@NoArgsConstructor
@TableName("claims_file_att")
public class ClaimsFileAttDao implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("包编码")
    private String subpackageCode;

    @ApiModelProperty("文件key")
    private String fileKey;

    @ApiModelProperty("文件名")
    private String fileName;

    @ApiModelProperty("创建者")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createAt;

    @ApiModelProperty("更新者")
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateAt;

}
