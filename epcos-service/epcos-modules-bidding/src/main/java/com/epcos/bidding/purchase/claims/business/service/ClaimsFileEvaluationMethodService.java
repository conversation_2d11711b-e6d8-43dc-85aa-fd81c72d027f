package com.epcos.bidding.purchase.claims.business.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.common.utils.EvaluationMethod;
import com.epcos.bidding.common.utils.FileEvaluationMethodRemarkEntityShared;
import com.epcos.bidding.common.utils.ReviewItem;
import com.epcos.bidding.purchase.api.params.EvaluationMethodVo;
import com.epcos.bidding.purchase.api.params.ReviewItemVo;
import com.epcos.bidding.purchase.claims.business.api.IClaimsFileEvaluationMethodApi;
import com.epcos.bidding.purchase.claims.business.api.IClaimsFileEvaluationMethodRemarkApi;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileEvaluationMethodDao;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileEvaluationMethodRemarkDao;
import com.epcos.bidding.purchase.claims.repository.ClaimsFileEvaluationMethodMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ClaimsFileEvaluationMethodService extends ServiceImpl<ClaimsFileEvaluationMethodMapper, ClaimsFileEvaluationMethodDao> implements IClaimsFileEvaluationMethodApi {

    private final IClaimsFileEvaluationMethodRemarkApi claimsFileEvaluationMethodRemarkApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delAndCreate(EvaluationMethod evaluationMethod, String subpackageCode) {
        delete(subpackageCode);
        List<ReviewItem> reviewItemList = evaluationMethod.getConformityReview();
        reviewItemList.addAll(evaluationMethod.getScoreReview());
        List<ClaimsFileEvaluationMethodDao> list = reviewItemList.stream().map(i -> {
            ClaimsFileEvaluationMethodDao dao = new ClaimsFileEvaluationMethodDao(subpackageCode);
            dao.setUuid(i.getUuid());
            dao.setReviewType(i.getReviewType());
            dao.setReviewItem(i.getReviewItem());
            dao.setReviewCriteria(i.getReviewCriteria());
            dao.setSubjective(i.getIsSubjective());
            dao.setReviewScore(i.getReviewScore());
            return dao;
        }).collect(Collectors.toList());
        saveBatch(list);
        if (StringUtils.hasText(evaluationMethod.getRemark())) {
            claimsFileEvaluationMethodRemarkApi.save(new ClaimsFileEvaluationMethodRemarkDao(subpackageCode, evaluationMethod.getRemark()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String subpackageCode) {
        if (StringUtils.hasText(subpackageCode)) {
            remove(queryWrapper(subpackageCode));
            claimsFileEvaluationMethodRemarkApi.remove(subpackageCode);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importOther(String importSubpackageCode, String subpackageCode) {
        List<ClaimsFileEvaluationMethodDao> daoList = findBySubpackageCode(importSubpackageCode);
        if (CollectionUtils.isEmpty(daoList)) {
            return;
        }
        delete(subpackageCode);
        List<ClaimsFileEvaluationMethodDao> daos = daoList.stream()
                .map(i -> {
                    ClaimsFileEvaluationMethodDao dao = new ClaimsFileEvaluationMethodDao(subpackageCode);
                    dao.setUuid(i.getUuid());
                    dao.setReviewType(i.getReviewType());
                    dao.setReviewItem(i.getReviewItem());
                    dao.setReviewCriteria(i.getReviewCriteria());
                    dao.setSubjective(i.getSubjective());
                    dao.setReviewScore(i.getReviewScore());
                    return dao;
                }).collect(Collectors.toList());
        saveBatch(daos);
        Optional.ofNullable(claimsFileEvaluationMethodRemarkApi.findBySubpackageCode(importSubpackageCode))
                .ifPresent(f -> claimsFileEvaluationMethodRemarkApi.save(
                        new ClaimsFileEvaluationMethodRemarkDao(subpackageCode, f.getRemark())));
    }

    @Override
    public EvaluationMethodVo findBySubpackageCodeVo(String subpackageCode) {
        List<ClaimsFileEvaluationMethodDao> methodList = findBySubpackageCode(subpackageCode);
        if (CollUtil.isEmpty(methodList)) {
            return null;
        }
        EvaluationMethodVo vo = new EvaluationMethodVo(subpackageCode,
                Optional.ofNullable(claimsFileEvaluationMethodRemarkApi.findBySubpackageCode(subpackageCode))
                        .map(FileEvaluationMethodRemarkEntityShared::getRemark).orElse(null), null);
        vo.setConformityReview(filter(methodList, c -> Objects.equals(1, c.getReviewType())));
        vo.setScoreReview(filter(methodList, c -> !Objects.equals(1, c.getReviewType())));
        return vo;
    }

    private List<ReviewItemVo> filter(List<ClaimsFileEvaluationMethodDao> methodList,
                                      Predicate<ClaimsFileEvaluationMethodDao> predicate) {
        return methodList.stream().filter(predicate).map(i -> {
            ReviewItemVo reviewItem = new ReviewItemVo();
            reviewItem.setReviewType(i.getReviewType());
            reviewItem.setReviewItem(i.getReviewItem());
            reviewItem.setReviewCriteria(i.getReviewCriteria());
            reviewItem.setIsSubjective(i.getSubjective());
            reviewItem.setReviewScore(i.getReviewScore());
            reviewItem.setUuid(i.getUuid());
            return reviewItem;
        }).collect(Collectors.toList());
    }
}
