package com.epcos.bidding.purchase.project.fjnx.domain.dao;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.bidding.common.BaseDao;
import com.epcos.common.core.domain.AttachmentDto;
import com.epcos.common.core.utils.StringUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 采购项目-  福建省农信版本
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2023/8/12 11:29
 * @Copyright © 2023-2026 易建采科技（武汉）有限公司
 */

@Data
@ApiModel(description = " 采购项目-  福建省农信版本")
@NoArgsConstructor
@TableName("purchase_fjnx_buy_item")
public class BuyItemFJNXDao extends BaseDao implements Serializable {

    public BuyItemFJNXDao(String buyItemCode) {
        this.buyItemCode = buyItemCode;
    }

    private static final long serialVersionUID = -1184858430217163721L;

    @ApiModelProperty(value = "项目外资料分配之后的主键id")
    private Long allocateId;

    @ApiModelProperty(value = "采购项目code")
    private String buyItemCode;

    @ApiModelProperty(value = "采购项目名称")
    private String buyItemName;

    @ApiModelProperty(value = "申报科室")
    private String applyDept;

    @ApiModelProperty(value = "采购编号(院方使用)")
    private String innerCode;

    @ApiModelProperty(value = "申请人")
    private String applyPerson;

    @ApiModelProperty(value = "申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;

    @ApiModelProperty(value = "需购数量")
    private Integer buyCnt;

    @ApiModelProperty(value = "单价限价")
    private BigDecimal buyFixePrice;

    @ApiModelProperty(value = "总价限价")
    private BigDecimal buyTotalPrice;

    @ApiModelProperty(value = "项目类型")
    private String buyClass;

    @ApiModelProperty(value = "是否可采购进口产品[0-否，1-是]")
    private Integer whetherImport;

    @ApiModelProperty(value = "是否有专机专用或耗材或试剂[0-否, 1-是]")
    private Integer whetherConsume;

    @ApiModelProperty(value = "项目附件")
    private String annexFile;

    @ApiModelProperty(value = "备注")
    private String buyRemark;


    public List<AttachmentDto> parseAnnexFile() {
        if (StringUtils.hasText(this.annexFile)) {
            return JSONArray.parseArray(this.annexFile, AttachmentDto.class);
        }
        return null;
    }
}
