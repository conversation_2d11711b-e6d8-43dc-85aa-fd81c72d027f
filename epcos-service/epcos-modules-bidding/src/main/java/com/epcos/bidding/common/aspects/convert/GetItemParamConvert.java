package com.epcos.bidding.common.aspects.convert;

import cn.hutool.core.util.ReflectUtil;
import com.epcos.bidding.common.aspects.params.GetItemParam;

/**
 * 对于默认名称 ==itemCode 或 ==subpackageCode
 *
 * <AUTHOR>
 */
public class GetItemParamConvert implements GetParamConvert<Object, GetItemParam> {

    @Override
    public GetItemParam doConvert(Object o) {
        String buyItemCode = (String) ReflectUtil.getFieldValue(o, "buyItemCode");
        String subpackageCode = (String) ReflectUtil.getFieldValue(o, "subpackageCode");
        String belongRole = (String) ReflectUtil.getFieldValue(o, "belongRole");
        return new GetItemParam(buyItemCode, subpackageCode, belongRole, null);
    }


}
