package com.epcos.bidding.audit.business.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.epcos.bidding.audit.api.AuditAttribute;
import com.epcos.bidding.audit.api.dto.AuditCreatorDto;
import com.epcos.bidding.audit.api.vo.AuditCommentVo;
import com.epcos.bidding.audit.api.vo.AuditInfoVo;
import com.epcos.bidding.audit.api.vo.AuditPersonVo;
import com.epcos.bidding.audit.api.vo.AuditVo;
import com.epcos.bidding.audit.business.api.IAuditApi;
import com.epcos.bidding.audit.business.api.IAuditCommentApi;
import com.epcos.bidding.audit.business.api.IAuditInfoApi;
import com.epcos.bidding.audit.business.api.IAuditPersonApi;
import com.epcos.bidding.audit.business.api.IAuditRecipientApi;
import com.epcos.bidding.audit.business.api.IAuditRecordApi;
import com.epcos.bidding.audit.domain.dao.AuditInfoDao;
import com.epcos.bidding.audit.domain.dao.AuditPersonDao;
import com.epcos.bidding.audit.domain.dao.AuditRecipientDao;
import com.epcos.bidding.audit.domain.dto.AuditPageDto;
import com.epcos.bidding.audit.domain.dto.AuditRecordDto;
import com.epcos.bidding.audit.domain.vo.AuditPageVo;
import com.epcos.bidding.audit.event.AuditDeleteEvent;
import com.epcos.bidding.audit.event.AuditResultEvent;
import com.epcos.bidding.audit.mapping.AuditMap;
import com.epcos.bidding.audit.repository.AuditJoinMapper;
import com.epcos.bidding.common.BaseDao;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.common.annotaion.NotNullUserId;
import com.epcos.bidding.purchase.api.params.EvaluationMethodVo;
import com.epcos.bidding.purchase.claims.business.service.ClaimsService;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileAttDao;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.common.core.constant.SecurityConstants;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.web.domain.user.SysUser;
import com.epcos.common.file.pdf.Itext7PdfUtil;
import com.epcos.system.api.RemoteUserService;
import com.epcos.system.api.model.FUtil;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.action.PdfAction;
import com.itextpdf.kernel.pdf.filespec.PdfFileSpec;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Link;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuditService implements IAuditApi {

    private final AuditMap auditMap;
    private final AuditJoinMapper auditJoinMapper;
    private final IAuditInfoApi auditInfoApi;
    private final IAuditPersonApi auditPersonApi;
    private final IAuditRecordApi auditRecordApi;
    private final IAuditCommentApi auditCommentApi;
    private final IAuditRecipientApi auditRecipientApi;
    private final IBuyItemApi buyItemApi;
    private final RemoteUserService remoteUserService;
    private final ApplicationContext ac;

    @Override
    @NotNullUserId
    @Transactional(rollbackFor = Exception.class)
    public String initiate(AuditCreatorDto dto) {
        // 格式转换--获取审批信息
        AuditInfoDao auditInfo = auditMap.toAuditInfo(dto.getAuditInfoDto(), SecurityUtils.getUserId());
        // 生成一个唯一的审计编号
        String code = auditInfoApi.generateAuditCode();
        auditInfo.setAuditCode(code);
        // 添加审批信息
        auditInfoApi.save(auditInfo);
        // 添加审批人信息
        if (CollUtil.isNotEmpty(dto.getAuditPersonList())) {
            List<AuditPersonDao> auditorList = auditMap.toAuditPersonList(dto.getAuditPersonList());
            auditorList.forEach(i -> i.setAuditInfoId(auditInfo.getId()));
            auditPersonApi.saveBatch(auditorList);
        }
        // 添加抄送人信息
        if (CollUtil.isNotEmpty(dto.getAuditRecipientList())) {
            List<AuditRecipientDao> carbonCopyList = auditMap.toAuditRecipientList(dto.getAuditRecipientList());
            carbonCopyList.forEach(i -> i.setAuditInfoId(auditInfo.getId()));
            auditRecipientApi.saveBatch(carbonCopyList);
        }
        return code;
    }

    @Override
    public IPage<AuditPageVo> queryByAuditor(PageSortEntity<AuditPageDto> pageSort, Long userId) {
        return auditJoinMapper.selectJoinPerson(
                        Page.of(pageSort.getPageNum(), pageSort.getPageSize()),
                        pageSort.getEntity(), userId)
                .convert(auditMap::toVo);
    }

    @Override
    public IPage<AuditPageVo> queryByCarbonCopy(PageSortEntity<AuditPageDto> pageSort, Long userId) {
        return auditJoinMapper.selectJoinRecipient(
                        Page.of(pageSort.getPageNum(), pageSort.getPageSize()),
                        pageSort.getEntity(),
                        userId)
                .convert(auditMap::toVo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void audit(AuditRecordDto dto) {
        // 添加 audit_record(审批记录表) 信息
        auditRecordApi.save(auditMap.toAuditorRecord(dto));
        AuditInfoDao auditInfoDao = new AuditInfoDao();
        auditInfoDao.setId(dto.getAuditInfoId());
        auditInfoDao.setStatus(dto.getStatus());
        if (dto.getStatus() == 0) {
            // 修改 audit_info(审批信息) 表
            auditInfoApi.updateById(auditInfoDao);
            ac.publishEvent(new AuditResultEvent(auditInfoApi.getById(dto.getAuditInfoId())));
        } else {
            List<Long> auditorIds = auditJoinMapper.selectJoinRecordById(dto.getAuditInfoId());
            auditorIds.removeIf(i -> Objects.equals(i, dto.getAuditPersonId()));
            if (CollUtil.isEmpty(auditorIds)) {
                auditInfoApi.updateById(auditInfoDao);
                ac.publishEvent(new AuditResultEvent(auditInfoApi.getById(dto.getAuditInfoId())));
            }
        }
    }

    @Override
    public AuditVo query(String auditCode) {
        return Optional.ofNullable(auditJoinMapper.selectJoinRecipientByAuditCode(auditCode))
                .map(this::getAuditVo)
                .orElse(null);
    }

    @Override
    public AuditInfoVo queryAuditResult(String auditCode) {
        return Optional.ofNullable(auditInfoApi.findOne(auditCode))
                .map(i -> {
                    AuditInfoVo vo = new AuditInfoVo();
                    BeanUtils.copyProperties(i, vo);
                    return vo;
                }).orElse(null);
    }

    @Override
    public List<AuditVo> query(List<String> auditCodeList) {
        return Optional.ofNullable(auditJoinMapper.selectJoinRecipientByAuditCodeIn(auditCodeList))
                .orElseGet(Collections::emptyList)
                .stream()
                .map(this::getAuditVo)
                .collect(Collectors.toList());
    }

    private AuditVo getAuditVo(AuditInfoVo v) {
        AuditVo vo = new AuditVo();
        Optional.ofNullable(StringUtils.hasText(v.getCreateBy()) ? null : getNickName(v.getCreateId()))
                .ifPresent(v::setCreateBy);
        Optional.ofNullable(v.getBuyItemCode())
                .flatMap(c -> Optional.ofNullable(buyItemApi.findBuyItemInfo(c)))
                .ifPresent(b -> v.setYearMonthSplit(b.getYearMonthSplit()));
        vo.setAuditInfoVo(v);
        vo.setAuditPersonList(auditJoinMapper.selectJoinRecordAndCommentById(v.getId()));
        return vo;
    }

    @Override
    public AuditVo query(Long id) {
        return Optional.ofNullable(auditJoinMapper.selectJoinRecipientById(id))
                .map(this::getAuditVo)
                .orElse(null);
    }

    @Override
    public String getNickName(Long createId) {
        if (Objects.nonNull(createId)) {
            R<SysUser> userInfoR = remoteUserService.getInfoById(createId, SecurityConstants.INNER);
            if (userInfoR.hasFail()) {
                log.error("远程调用用户信息异常，userId={},userInfoR={}", createId, userInfoR);
                throw new ServiceException(userInfoR.getMsg());
            }
            return userInfoR.getData().getNickName();
        }
        return null;
    }

    @Override
    public List<Long> queryParticipantId(Long auditInfoId) {
        return auditJoinMapper.selectAllUserId(auditInfoId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancel(List<Long> idList) {
        idList.forEach(id -> cancel(auditInfoApi.getById(id)));
    }

    private void cancel(AuditInfoDao dao) {
        Optional.ofNullable(dao)
                .ifPresent(a -> {
                    if (a.getStatus() == 2) {
                        auditInfoApi.updateById(new AuditInfoDao().cancel(dao.getId()));
                        ac.publishEvent(new AuditResultEvent(auditInfoApi.getById(dao.getId())));
                    }
                });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancel(String auditCode) {
        cancel(auditInfoApi.findOne(auditCode));
    }

    @Override
    public AuditPersonVo queryCurrentAuditName(Long auditInfoId) {
        return auditJoinMapper.selectCurrentAuditName(auditInfoId);
    }

    @Override
    public Path generate(String auditCode) {
        AuditVo auditVo = query(auditCode);
        if (Objects.isNull(auditVo)) {
            return null;
        }
        try {
            return generate(auditVo);
        } catch (IOException e) {
            log.error("生成审批单失败, auditCode={}, auditVo={}", auditCode, auditVo, e);
            throw new ServiceException("生成审批单失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String buyItemCode) {
        List<Long> auditIds = auditInfoApi.findList(buyItemCode, null)
                .stream().map(BaseDao::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(auditIds)) {
            List<Long> personIds = auditPersonApi.list(auditPersonApi.queryWrapperIn(auditIds))
                    .stream().map(BaseDao::getId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(personIds)) {
                auditPersonApi.removeByIds(personIds);
                List<Long> recordIds = auditRecordApi.list(auditRecordApi.queryWrapperIn(personIds))
                        .stream().map(BaseDao::getId).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(recordIds)) {
                    auditRecordApi.removeByIds(recordIds);
                    auditCommentApi.remove(auditCommentApi.queryWrapperIn(recordIds));
                }
            }
            auditRecipientApi.remove(auditRecipientApi.queryWrapperIn(auditIds));
            auditInfoApi.removeBatchByIds(auditIds);
        }
        ac.publishEvent(new AuditDeleteEvent(buyItemCode));
    }

    @Override
    public int queryNeedAudit(Long userId) {
        return auditJoinMapper.selectAuditCount(userId);
    }

    // 查询作为审批人参与项目的数量，待办+已办
    @Override
    public int countProjectAsApprover(Long userId) {
        return auditJoinMapper.countProjectAsApprover(userId);
    }

    // 查询当前用户待审批的审批单
    @Override
    public IPage<AuditPageVo> queryWaitAuditJoinPerson(Integer pageNum, Integer pageSize, Long userId) {
        return auditJoinMapper.selectWaitAuditJoinPerson(
                        Page.of(pageNum, pageSize), userId)
                .convert(auditMap::toVo);
    }

    // 查询当前用户已审批的审批单
    @Override
    public IPage<AuditPageVo> queryCompletedAuditJoinPerson(Integer pageNum, Integer pageSize, Long userId) {
        return auditJoinMapper.selectCompletedAuditJoinPerson(Page.of(pageNum, pageSize), userId)
                .convert(auditMap::toVo);
    }

    private Path generate(AuditVo vo) throws IOException {
        AuditInfoVo auditInfoVo = vo.getAuditInfoVo();
        Path path = Paths.get(FileUtil.getTmpDirPath(), auditInfoVo.getAuditTitle() + ".pdf");
        try (
                PdfWriter pdfWriter = new PdfWriter(path.toFile());
                PdfDocument pdfDoc = new PdfDocument(pdfWriter);
                Document doc = new Document(pdfDoc, PageSize.A4)
        ) {
            doc.setFont(Itext7PdfUtil.pdfFont());
            doc.add(Itext7PdfUtil.paragraphTitle(auditInfoVo.getAuditTitle()));
            doc.add(Itext7PdfUtil.paragraphTitle("审批单"));
            doc.add(Itext7PdfUtil.paragraphLeft("创建时间：" + DateUtil.formatDateTime(auditInfoVo.getCreateAt())));
            Table table = Itext7PdfUtil.initTable(4);
            // 审批简介
            table.addCell(Itext7PdfUtil.cellCenter(2, 4, Itext7PdfUtil.paragraphHeadCenter("审批简介")));
            table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter("审批编号")));
            table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter(auditInfoVo.getAuditCode())));
            table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter("审批结果")));
            table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter(auditInfoVo.convertStrWithStatus())));
            table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter("审批类型")));
            table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter(auditInfoVo.getAuditType())));
            table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter("发起人")));
            table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter(auditInfoVo.getCreateBy())));
            // 审批内容
            table.addCell(Itext7PdfUtil.cellCenter(2, 4, Itext7PdfUtil.paragraphHeadCenter("审批内容")));
            for (AuditAttribute att : auditInfoVo.getContentList()) {
                table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter(att.getName())));
                if (StringUtils.hasText(att.getValue())) {
                    if (Objects.equals("File", att.getType())) {
                        File file = downFile(att.getValue());
                        addFileAttachmentAndSetLink(file, pdfDoc, table);
//                        table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter(att.getValue())));
                    } else {
                        String val = att.getValue();
                        if (Objects.equals("Date", att.getType())) {
                            val = DateUtil.formatChineseDate(DateUtil.parseDate(att.getValue()), false, true);
                        }
                        table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter(val)));
                    }
                }
            }
            IntStream.rangeClosed(0, auditInfoVo.getContentList().size() % 4)
                    .forEach(i -> table.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter(""))));
            doc.add(table);
            // 审批流程
            Table tableNode = Itext7PdfUtil.initTable(6);
            tableNode.addCell(Itext7PdfUtil.cellCenter(2, 6, Itext7PdfUtil.paragraphHeadCenter("审批流程")));
            tableNode.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphHeadCenter("姓名")));
            tableNode.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphHeadCenter("审批状态")));
            tableNode.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphHeadCenter("要求")));
            tableNode.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphHeadCenter("备注")));
            tableNode.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphHeadCenter("附件")));
            tableNode.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphHeadCenter("审批时间")));
            for (AuditPersonVo ap : vo.getAuditPersonList()) {
                tableNode.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter(ap.getUserName())));
                tableNode.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter(ap.convertStrWithStatus())));
                tableNode.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter(ap.getRequirement())));
                tableNode.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter(ap.getRemark())));
                String attFileNames = ap.getAttList().stream().map(i -> {
                    try {
                        File file = downFile(i.getUrl());
                        addFileLink(file, doc, pdfDoc);
                    } catch (IOException e) {
                        log.error("下载审批流程附件异常，i={}", i, e);
                        throw new ServiceException("下载审批流程附件异常");
                    }
                    return i.getName();
                }).collect(Collectors.joining("、\n"));
                tableNode.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter(attFileNames)));
                tableNode.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter(DateUtil.formatDateTime(ap.getCreateAt()))));
                // 评论
                tableNode.addCell(Itext7PdfUtil.cellCenter(1, 6, Itext7PdfUtil.paragraphHeadCenter("评论")));
                for (AuditCommentVo ac : ap.getCommentVoList()) {
                    tableNode.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter(ac.getUserName())));
                    tableNode.addCell(Itext7PdfUtil.cellCenter(1, 4, Itext7PdfUtil.paragraphCenter(ac.getComment())));
                    tableNode.addCell(Itext7PdfUtil.cellCenter(Itext7PdfUtil.paragraphCenter(DateUtil.formatDateTime(ac.getCreateAt()))));
                }
            }
            doc.add(tableNode);
        } catch (IOException e) {
            log.error("生成审批单异常，vo={}", vo);
            throw new ServiceException(e.getMessage());
        }
        return path;
    }

    // 添加附件
    private void addFileAttachmentAndSetLink(File file, PdfDocument pdf, Table table) throws IOException {
        PdfFileSpec fileSpec = PdfFileSpec.createEmbeddedFileSpec(
                pdf, file.getAbsolutePath(),
                file.getName(), file.getName(),
                null, null);
        pdf.addFileAttachment(file.getName(), fileSpec);
        PdfAction action = PdfAction.createGoToE(fileSpec, null, true, null);
        Paragraph link = new Paragraph(new Link(file.getName(), action));
        table.addCell(Itext7PdfUtil.cellCenter(link));
    }


    // 添加附件
    private void addFileLink(File file, Document doc, PdfDocument pdf) throws IOException {
        PdfFileSpec fileSpec = PdfFileSpec.createEmbeddedFileSpec(
                pdf, file.getAbsolutePath(),
                file.getName(), file.getName(),
                null, null);
        pdf.addFileAttachment(file.getName(), fileSpec);
        PdfAction action = PdfAction.createGoToE(fileSpec, null, true, null);
        doc.add(new Paragraph(new Link(file.getName(), action)));
    }

    //    // 下载文件至临时目录
//    private File downFile(String url) throws IOException {
//        ResponseEntity<StreamingResponseBody> fileEntity = FUtil.downloadProjectFile(url);
//        String filename = fileEntity.getHeaders().getContentDisposition().getFilename();
//        Path tmpAttPath = Paths.get(FileUtil.getTmpDirPath(), filename);
//        Files.write(tmpAttPath, fileEntity.getBody());
//        return tmpAttPath.toFile();
//    }
    private File downFile(String url) throws IOException {
        ResponseEntity<Resource> fileEntity = FUtil.downloadProjectFile(url);
        // 获取文件名
        String filename = fileEntity.getHeaders().getContentDisposition().getFilename();
        if (filename == null || filename.isEmpty()) {
            throw new IOException("无法获取文件名");
        }
        // 创建临时路径
        Path tmpAttPath = Paths.get(FileUtil.getTmpDirPath(), filename);
        // 写入文件流
        try (InputStream inputStream = fileEntity.getBody().getInputStream();
             OutputStream os = Files.newOutputStream(tmpAttPath)) {
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
        }
        return tmpAttPath.toFile();
    }

}
