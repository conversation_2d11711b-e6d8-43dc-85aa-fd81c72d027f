package com.epcos.bidding.purchase.win.domain.dto;

import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/23 11:20
 */
@Data
public class SaveSourceDto extends SuperPackageVo {

    @ApiModelProperty(value = "报价总价")
    private BigDecimal priceTotalSource;
}
