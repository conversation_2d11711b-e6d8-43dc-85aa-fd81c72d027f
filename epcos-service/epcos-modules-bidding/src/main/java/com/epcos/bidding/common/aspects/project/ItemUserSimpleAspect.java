package com.epcos.bidding.common.aspects.project;

import com.epcos.bidding.common.annotaion.ItemUserSimple;
import com.epcos.bidding.common.aspects.AbstractMethod;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.purchase.api.params.vo.buyitem.ItemUserInfo;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.remote.RemoteToOtherServiceApi;
import com.epcos.common.core.web.domain.user.SysUser;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/4 17:23
 */
@Slf4j
@Aspect
@Component
public class ItemUserSimpleAspect extends AbstractMethod<ItemUserSimple, ItemUserInfo> {

    @Autowired
    private RemoteToOtherServiceApi remoteToOtherServiceApi;
    @Autowired
    private IBuyItemApi buyItemApi;

    public ItemUserSimpleAspect() {
        super(GetUtil.GET_ITEM_USER, "获取采购项目创建者信息");
    }

    @Override
    @Around(value = "@annotation(itemUserSimple)")
    public Object around(ProceedingJoinPoint point, ItemUserSimple itemUserSimple) {
        return super.around(point, itemUserSimple);
    }

    @Override
    public ItemUserInfo businessMethods(JoinPoint point, ItemUserSimple itemUserSimple) {
        String subPackageCode = threadLocal.get().getSubpackageCode();
        BuyItemDao buyItemDao = buyItemApi.findBySubpackageCode(subPackageCode);
        SysUser sysUser = remoteToOtherServiceApi.getSysUserInfo(buyItemDao.getUserId());
        ItemUserInfo vo = new ItemUserInfo();
        BeanUtils.copyProperties(sysUser, vo);
        BeanUtils.copyProperties(buyItemDao, vo);
        vo.setBuyItemId(buyItemDao.getId());
        return vo;
    }
}
