package com.epcos.bidding.audit.business.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.audit.business.api.IAuditRecordApi;
import com.epcos.bidding.audit.domain.dao.AuditRecordDao;
import com.epcos.bidding.audit.repository.AuditRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuditRecordService extends ServiceImpl<AuditRecordMapper, AuditRecordDao> implements IAuditRecordApi {
}
