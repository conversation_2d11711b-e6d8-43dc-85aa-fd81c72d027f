package com.epcos.bidding.purchase.comment.domain.dao;

import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.bidding.common.BaseDao;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/19 17:23
 */
@ApiModel(description = " 采购人评价供应商")
@Data
@TableName("supplier_comment")
public class SupplierCommentDao extends BaseDao implements Serializable {

    @ApiModelProperty(value = "采购项目code")
    private String buyItemCode;

    @ApiModelProperty(value = "采购项目名字")
    private String buyItemName;

    @ApiModelProperty(value = "标段编号")
    private String subpackageCode;

    @ApiModelProperty(value = "标段名称")
    private String subpackageName;

    @ApiModelProperty(value = "供应商id")
    private Long supplierId;

    @ApiModelProperty(value = "评论内容")
    private String commentContent;

    @ApiModelProperty(value = "评论时间")
    private Date commentTime;

    @ApiModelProperty(value = "创建者id")
    private Long createId;

}
