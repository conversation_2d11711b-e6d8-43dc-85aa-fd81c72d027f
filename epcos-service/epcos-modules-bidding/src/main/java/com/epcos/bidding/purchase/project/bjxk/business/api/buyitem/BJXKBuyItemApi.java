package com.epcos.bidding.purchase.project.bjxk.business.api.buyitem;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.IBaseService;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.project.bjxk.domain.dao.BuyItemBJXKDao;
import com.epcos.bidding.purchase.project.bjxk.domain.dto.BJXKBuyItemQueryDto;
import com.epcos.bidding.purchase.project.bjxk.domain.dto.BJXKCreateBuyItemDto;
import com.epcos.bidding.purchase.project.bjxk.domain.vo.BJXKBuyItemPageVo;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 江西省南昌市肿瘤医院版本
 * @date 2024/4/23 14:24
 */
public interface BJXKBuyItemApi extends IBaseService<BuyItemBJXKDao> {

    @Override
    default LambdaQueryWrapper<BuyItemBJXKDao> queryWrapper(BuyItemBJXKDao dao) {
        LambdaQueryWrapper<BuyItemBJXKDao> query = Wrappers.lambdaQuery(BuyItemBJXKDao.class);
        if (Objects.nonNull(dao.getId())) {
            return query.eq(BuyItemBJXKDao::getId, dao.getId());
        }
        return query.eq(StringUtils.hasText(dao.getBuyItemCode()), BuyItemBJXKDao::getBuyItemCode, dao.getBuyItemCode())
                .like(StringUtils.hasText(dao.getBuyItemName()), BuyItemBJXKDao::getBuyItemName, dao.getBuyItemName())
                .eq(StringUtils.hasText(dao.getBuyClass()), BuyItemBJXKDao::getBuyClass, dao.getBuyClass());
    }

    default LambdaQueryWrapper<BuyItemBJXKDao> queryWrapper(String buyItemCode) {
        return queryWrapper(new BuyItemBJXKDao(buyItemCode));
    }

    default LambdaQueryWrapper<BuyItemBJXKDao> queryWrapper(String buyItemCode, String innerCode) {
        return Wrappers.lambdaQuery(BuyItemBJXKDao.class)
                .eq(StringUtils.hasText(buyItemCode), BuyItemBJXKDao::getBuyItemCode, buyItemCode);
    }

    default LambdaUpdateWrapper<BuyItemBJXKDao> updateWrapper(String buyItemCode) {
        return Wrappers.lambdaUpdate(BuyItemBJXKDao.class)
                .eq(StringUtils.hasText(buyItemCode), BuyItemBJXKDao::getBuyItemCode, buyItemCode);
    }

    void insertBJXKBuyItem(BJXKCreateBuyItemDto dto, String buyItemCode);


    IPage<BJXKBuyItemPageVo> selectJoinBuyItemPage(PageSortEntity<BJXKBuyItemQueryDto> dto);

    BuyItemBJXKDao findOneByBuyItemCode(String buyItemCode);

    List<BuyItemBJXKDao> findOneByBuyItemCodeList(List<String> buyItemCodeList);

    void updateBJXKBuyItemInfo(BJXKCreateBuyItemDto dto);

    Boolean delBJXKBuyItemInfo(String buyItemCode);
}
