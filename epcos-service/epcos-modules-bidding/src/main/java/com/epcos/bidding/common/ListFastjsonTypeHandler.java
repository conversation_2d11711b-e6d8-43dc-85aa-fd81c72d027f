package com.epcos.bidding.common;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;

public class ListFastjsonTypeHandler extends FastjsonTypeHandler {
    private final Class<?> type;

    public ListFastjsonTypeHandler(Class<?> type) {
        super(type);
        this.type = type;
    }

    @Override
    protected Object parse(String json) {
        return JSON.parseArray(json, type);
    }
}
