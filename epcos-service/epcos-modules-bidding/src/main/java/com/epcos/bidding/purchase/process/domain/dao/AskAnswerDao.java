package com.epcos.bidding.purchase.process.domain.dao;

import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.bidding.common.SubpackageCodeEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/28 9:44
 */
@ApiModel(description = "答疑表")
@Data
@TableName("purchase_ask_answer")
public class AskAnswerDao extends SubpackageCodeEntity implements Serializable {

    @ApiModelProperty(value = "提问人类型[1-投标人, 2-采购办, 3-评委]")
    private String askUserType;

    @ApiModelProperty(value = "提问人id")
    private Long askUserId;

    @ApiModelProperty(value = "提问人名称[投标人公司名称/采购办操作人姓名/评委姓名]")
    private String askUserName;

    @ApiModelProperty(value = "答复人id")
    private Long answerUserId;

    @ApiModelProperty(value = "答复人名称[投标人公司名称/采购办操作人姓名/评委姓名]")
    private String answerUserName;

    @ApiModelProperty(value = "回复人类型[1-投标人, 2-采购办, 3-评委]")
    private String answerUserType;

    @ApiModelProperty(value = "提问内容")
    private String askContent;

    @ApiModelProperty(value = "答复内容")
    private String answerContent;

}
