package com.epcos.bidding.common.aspects.user;

import com.epcos.bidding.common.annotaion.NotNullUserId;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Aspect
@Component
public class NotNullUserIdAspect {

    @Before(value = "@annotation(required)")
    public void notNullUserIdHandler(JoinPoint joinPoint, NotNullUserId required) {
        if (required.required() && (Objects.isNull(SecurityUtils.getUserId()) || SecurityUtils.getUserId() == 0)) {
            log.error("请求用户id必须，userId={}", SecurityUtils.getUserId());
            throw new ServiceException("请求用户id必须");
        }
    }

}
