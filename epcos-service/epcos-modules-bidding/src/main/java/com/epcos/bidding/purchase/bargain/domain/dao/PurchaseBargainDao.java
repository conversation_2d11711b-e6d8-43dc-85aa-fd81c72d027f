package com.epcos.bidding.purchase.bargain.domain.dao;

import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.bidding.common.BaseDao;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/18 13:56
 */
@ApiModel(description = " 采购人发起议价表")
@Data
@TableName("purchase_bargain")
public class PurchaseBargainDao extends BaseDao implements Serializable {

    @ApiModelProperty(value = "标段code")
    private String subpackageCode;

    @ApiModelProperty("发起/结束议价人的id")
    private Long initiateId;

    @ApiModelProperty(value = "供应商id")
    private Long supplierId;

    @ApiModelProperty(value = "发起议价轮数： 1 一轮议价  2 二轮议价  3 三轮议价.....")
    private Integer round;

    @ApiModelProperty(value = "议价状态：1 发起  2 结束")
    private String launchStatus;

    @ApiModelProperty(value = "倒计时")
    private Date countdown;

    @ApiModelProperty(value = "服务需求(备注)")
    private String serviceRequire;

}
