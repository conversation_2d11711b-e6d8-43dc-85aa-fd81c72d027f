package com.epcos.bidding.purchase.bargain.mapping;

import com.epcos.bidding.purchase.api.params.dto.bargin.StartOrEndBargainDto;
import com.epcos.bidding.purchase.bargain.domain.dao.PurchaseBargainDao;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/19 9:13
 */
@Mapper
public interface PurchaseBargainConvert {

    PurchaseBargainConvert INSTANCE = Mappers.getMapper(PurchaseBargainConvert.class);

    @Mappings(value = {
            @Mapping(source = "afterDate", target = "countdown")
    })
    PurchaseBargainDao convert(Date afterDate, StartOrEndBargainDto dto);
}
