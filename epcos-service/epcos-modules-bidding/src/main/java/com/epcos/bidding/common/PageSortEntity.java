package com.epcos.bidding.common;

import com.epcos.common.core.utils.reflect.ReflectUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.util.CollectionUtils;

import javax.validation.Valid;
import java.util.*;

@Data
@ApiModel(description = "分页参数")
@NoArgsConstructor
@AllArgsConstructor
public class PageSortEntity<T> {

    public PageSortEntity(T entity, Integer pageNum, Integer pageSize) {
        this.entity = entity;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
    }

    @Valid
    @ApiModelProperty("请求实体参数")
    private T entity;
    private Integer pageNum;
    private Integer pageSize;
    /**
     * 举例：true升序，false降序。同时进行排序，id降序，createAt升序 {"id": "false", "createAt": "true"}
     */
    private LinkedHashMap<String, Boolean> sorts = new LinkedHashMap<>();

    public PageSortEntity(T entity) {
        this.entity = entity;
    }

    public int getCalcPageNum() {
        return (pageNum == null || pageNum <= 0) ? 1 : pageNum;
    }

    public int getCalcPageSize() {
        return (pageSize == null || pageSize < 1) ? 100 : pageSize;
    }

    private Map<String, Boolean> getValidSorts() {
        if (Objects.isNull(entity) || CollectionUtils.isEmpty(sorts)) {
            return null;
        }
        Map<String, Boolean> res = new LinkedHashMap<>();
        sorts.forEach((k, v) -> {
            if (ReflectUtils.getAccessibleField(entity, k) != null) {
                res.put(k, v);
            }
        });
        if (CollectionUtils.isEmpty(res)) {
            return null;
        }
        return res;
    }


    // 如果未指定，则默认返回 id desc
    public Sort getSortOrDefault() {
        Map<String, Boolean> validSorts = getValidSorts();
        if (validSorts == null) {
            return Sort.by(Sort.Direction.DESC, "id");
        }
        List<Sort.Order> orders = new ArrayList<>(validSorts.size());
        validSorts.forEach((k, v) -> {
            Sort.Order so;
            if (Boolean.TRUE.equals(v)) {
                so = Sort.Order.asc(k);
            } else {
                so = Sort.Order.desc(k);
            }
            orders.add(so);
        });
        return Sort.by(orders);
    }

//    public static <T> PageSortEntity<T> of(PageSortEntity<?> pageSort, T entity) {
//        return new PageSortEntity<>(entity, pageSort.getPageNum(), pageSort.getPageSize(), pageSort.getSorts());
//    }

    public <T> PageSortEntity<T> convert(T entity) {
        return new PageSortEntity<>(entity, this.getCalcPageNum(), this.getCalcPageSize(), this.sorts);
    }

//    public static <T> PageSortEntity<T> of(T entity) {
//        return new PageSortEntity<>(entity, null, null);
//    }

}
