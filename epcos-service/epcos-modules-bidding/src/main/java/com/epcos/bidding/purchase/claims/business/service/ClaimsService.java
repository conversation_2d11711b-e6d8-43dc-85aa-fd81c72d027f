package com.epcos.bidding.purchase.claims.business.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.epcos.bidding.audit.business.api.IAuditInfoApi;
import com.epcos.bidding.audit.domain.dao.AuditInfoDao;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.common.annotaion.GetClaimsFile;
import com.epcos.bidding.common.annotaion.GetCommon;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.common.utils.BidFileJson;
import com.epcos.bidding.common.utils.BidFileUtil;
import com.epcos.bidding.common.utils.ReviewItem;
import com.epcos.bidding.purchase.api.params.ClaimsFileAttVo;
import com.epcos.bidding.purchase.api.params.EpcFileContentVo;
import com.epcos.bidding.purchase.api.params.EvaluationMethodVo;
import com.epcos.bidding.purchase.api.params.PurchaseQuoteFormVo;
import com.epcos.bidding.purchase.claims.business.api.*;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileAttDao;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileDao;
import com.epcos.bidding.purchase.claims.domain.dto.ClaimsFileEvaluationMethodDto;
import com.epcos.bidding.purchase.claims.domain.dto.ClaimsFileImportDto;
import com.epcos.bidding.purchase.claims.domain.dto.ClaimsFilePageDto;
import com.epcos.bidding.purchase.claims.domain.vo.ClaimsFilePageVo;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.common.core.constant.FileTypeNameConstants;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.enums.FileTypeNameEnum;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.web.page.TableDataVo;
import com.epcos.common.redis.chunk.ChunkFileHelper;
import com.epcos.epcfile.api.RemoteProjectFileService;
import com.epcos.epcfile.api.domain.dto.FileUploadDto;
import com.epcos.epcfile.api.domain.vo.SysFileVo;
import com.epcos.system.api.model.FUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.File;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor
public class ClaimsService {

    private final IClaimsFileApi iClaimsFileApi;
    private final IClaimsFileMenuApi iClaimsFileMenuApi;
    private final IClaimsFileEvaluationMethodApi iClaimsFileEvaluationMethodApi;
    private final IClaimsFileQuoteFormApi iClaimsFileQuoteFormApi;
    private final ISubPackageApi iSubpackageApi;
    private final ThreadPoolTaskExecutor taskExecutor;

    private final ChunkFileHelper chunkFileHelper;
    private final RemoteProjectFileService remoteProjectFileService;
    private final IClaimsFileAttApi claimsFileAttApi;

    private final IAuditInfoApi auditInfoApi;

    @Transactional(rollbackFor = Exception.class)
    public void up(File zipFile, Future<String> zipUploadFuture, Future<String> mergedPdfUploadFuture,
                   String yearMonthSplit, BidFileJson bidFileJson, String buyItemCode, String subpackageCode) {
        try {
            // 120s
            iClaimsFileMenuApi.delAttachFileAndCreate(zipFile.getParentFile().toPath(), bidFileJson,
                    buyItemCode, subpackageCode, yearMonthSplit);
            iClaimsFileEvaluationMethodApi.delAndCreate(bidFileJson.getEvaluationMethod(), subpackageCode);
            String pdfUrl = mergedPdfUploadFuture.get(2, TimeUnit.MINUTES);
            String zipUrl = zipUploadFuture.get(2, TimeUnit.MINUTES);
            iClaimsFileApi.saveOrUpdate(subpackageCode, zipUrl, pdfUrl, bidFileJson);
        } catch (Exception e) {
            log.error("采购文件上传保存处理失败: zipFile={}, buyItemCode={}, subpackageCode={}",
                    zipFile, buyItemCode, subpackageCode, e);
            throw new ServiceException("采购文件上传保存处理失败: " + e.getMessage());
        }
    }

    public boolean areQuoteEqual(String subpackageCode, String otherSubpackageCode) {
        PurchaseQuoteFormVo current = iClaimsFileQuoteFormApi.query(subpackageCode);
        PurchaseQuoteFormVo other = iClaimsFileQuoteFormApi.query(otherSubpackageCode);
        return current.equals(other);
    }

    public TableDataVo<ClaimsFilePageVo> page(PageSortEntity<ClaimsFilePageDto> dto) {
        Page<SubpackageDao> page = iSubpackageApi.findByBuyItemCodeAndSubpackageCodeNotPage(dto);
        if (page.getTotal() == 0) {
            return new TableDataVo<>(Collections.emptyList(), 0);
        }
        List<ClaimsFileDao> claimsFileDaos = iClaimsFileApi.findBySubpackageCodes(page.getRecords().stream()
                .map(SubpackageDao::getSubpackageCode)
                .collect(Collectors.toSet()));
        List<ClaimsFilePageVo> voList = page.getRecords().stream()
                .map(i -> {
                    ClaimsFilePageVo vo = new ClaimsFilePageVo();
                    vo.setSubpackageCode(i.getSubpackageCode());
                    vo.setSubpackageName(i.getSubpackageName());
                    vo.setAreQuoteHeadEquals(areQuoteEqual(dto.getEntity().getSubpackageCode(), i.getSubpackageCode()));
                    claimsFileDaos.stream().filter(f -> Objects.equals(i.getSubpackageCode(), f.getSubpackageCode()))
                            .findAny()
                            .ifPresent(p -> {
                                vo.setEpcFile(p.getEpcFile());
                                vo.setPdfFile(p.getPdfFile());
                                vo.setReleaseStatus(p.getReleaseStatus());
                            });
                    return vo;
                }).collect(Collectors.toList());
        return new TableDataVo<>(voList, page.getTotal());
    }

    @GetClaimsFile(common = @GetCommon(subpackageCodeEL = "#dto.subpackageCode"))
    @Transactional(rollbackFor = Exception.class)
    public void importOtherFile(ClaimsFileImportDto dto) {
        Optional.ofNullable(GetUtil.getClaimsFile())
                .ifPresent(ClaimsFileDao::verifyPublished);
        String importSubpackageCode = dto.getImportSubpackageCode();
        String subpackageCode = dto.getSubpackageCode();
        iClaimsFileApi.importOther(importSubpackageCode, subpackageCode);
        iClaimsFileMenuApi.importOther(importSubpackageCode, subpackageCode);
        iClaimsFileEvaluationMethodApi.importOther(importSubpackageCode, subpackageCode);
    }

    public EpcFileContentVo queryAll(String subpackageCode) {
        ClaimsFileAttDao claimsFileAttDao = new ClaimsFileAttDao();
        claimsFileAttDao.setSubpackageCode(subpackageCode);
        List<ClaimsFileAttVo> attList = claimsFileAttApi.list(claimsFileAttDao).stream()
                .map(i -> {
                    ClaimsFileAttVo v = new ClaimsFileAttVo();
                    BeanUtils.copyProperties(i, v);
                    return v;
                }).collect(Collectors.toList());
        EpcFileContentVo vo = iClaimsFileApi.findBySubpackageCodeVo(subpackageCode);
        if (Objects.nonNull(vo)) {
            vo.setMenuData(iClaimsFileMenuApi.findBySubpackageCodeVo(subpackageCode));
            vo.setEvaluationMethod(iClaimsFileEvaluationMethodApi.findBySubpackageCodeVo(subpackageCode));
            vo.setClaimsFileAttVoList(attList);
            Optional.ofNullable(iClaimsFileQuoteFormApi.query(subpackageCode))
                    .ifPresent(i -> {
                        vo.setHeads(i.getHeads());
                        vo.setBodyMaps(i.getBodyMaps());
                    });
            return vo;
        } else {
            if (!CollectionUtils.isEmpty(attList)) {
                EpcFileContentVo vo2 = new EpcFileContentVo();
                vo2.setClaimsFileAttVoList(attList);
                return vo2;
            }
        }
        return null;
    }

    public EpcFileContentVo query(String subpackageCode) {
        return iClaimsFileApi.findBySubpackageCodeVo(subpackageCode);
    }


    /**
     * 获取招标文件审核状态
     * @param subpackageCode
     * @return true: 没有审核 , 审核被拒或撤回 , false : 审核待审批 , 同意
     */
    public Boolean getAuditStatus(String subpackageCode) {
        QueryWrapper<AuditInfoDao> qw = new QueryWrapper<>();
        qw.lambda().eq(AuditInfoDao::getSubpackageCode, subpackageCode)
                .eq(AuditInfoDao::getAuditType, FileTypeNameEnum.CLAIMS_FILE.getCode())
                .orderByDesc(AuditInfoDao::getCreateAt).last("limit 1");
        AuditInfoDao auditInfoDao = auditInfoApi.getOne(qw);
        if (auditInfoDao == null){
            return true;
        }
        if (auditInfoDao.getStatus() == 0 || auditInfoDao.getStatus() == 3){
            return true;
        }
        return false;
    }

    /**
     * 上传pdf采购文件
     *
     * @param file
     * @param buyItemCode
     * @param subpackageCode
     * @return
     */
    @Transactional
    public R<Boolean> uploadToPDF(MultipartFile file, String buyItemCode, String subpackageCode) {
        if (!getAuditStatus(subpackageCode)){
            return R.fail(false,"不允许上传");
        }
        QueryWrapper<ClaimsFileDao> qw = new QueryWrapper<>();
        qw.lambda().eq(ClaimsFileDao::getSubpackageCode, subpackageCode).last("limit 1");
        ClaimsFileDao one = iClaimsFileApi.getOne(qw);
        if (Objects.nonNull(one)) {
            FUtil.delFile(one.getPdfFile());
        }
        String fileCode = FUtil.upFile(buyItemCode, getYearMonthSplit(), file, FileTypeNameConstants.BIDDING_DOC_ENCLOSURE, subpackageCode, SecurityUtils.getUserId());
        ClaimsFileDao dao = new ClaimsFileDao();
        dao.setSubpackageCode(subpackageCode);
        dao.setEpcFile(null);
        dao.setPdfFile(fileCode);
        dao.setReleaseStatus(0);
        dao.setReleaseTime(DateTime.now());

        dao.setAppName("web");
        dao.setAppVersion("v1.1");
        dao.setZepcKey("web");
        dao.setDeleted(0);
        dao.setCreateAt(DateTime.now());
        dao.setCreateBy(String.valueOf(SecurityUtils.getUserId()));

        QueryWrapper<ClaimsFileDao> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("subpackage_code", subpackageCode);
        iClaimsFileApi.remove(queryWrapper);
        iClaimsFileApi.save(dao);

        return R.ok(true, "上传成功");
    }

    /**
     * 获取年月
     *
     * @return
     */
    public static String getYearMonthSplit() {
        int year = DateUtil.year(DateTime.now());
        String month = String.format("%02d", DateUtil.month(DateTime.now()) + 1);
        return year + month;
    }

    /**
     * 上传评审项
     *
     * @param dto
     * @return
     */
    @Transactional
    public R<Boolean> uploadEvaluation(@Valid ClaimsFileEvaluationMethodDto dto) {
        if (!getAuditStatus(dto.getSubpackageCode())){
            return R.fail(false,"不允许上传");
        }
        if (dto.getEvaluationMethod().getConformityReview() == null && dto.getEvaluationMethod().getScoreReview() == null) {
            return R.fail("评审项不能全为空");
        }

        // 生成uuid
        Stream.of(dto.getEvaluationMethod().getConformityReview(),
                        dto.getEvaluationMethod().getScoreReview())
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .forEach(item -> item.setUuid(IdUtil.randomUUID()));
        iClaimsFileEvaluationMethodApi.delAndCreate(dto.getEvaluationMethod(), dto.getSubpackageCode());
        return R.ok(true, "上传成功");
    }

    /**
     * 获取评审项
     *
     * @param subpackageCode
     * @return
     */
    public EvaluationMethodVo getEvaluation(String subpackageCode) {
        return iClaimsFileEvaluationMethodApi.findBySubpackageCodeVo(subpackageCode);
    }

    /**
     * 采购人上传附件
     *
     * @param file                文件
     * @param resumableIdentifier 文件标识
     * @param filename            文件名
     * @param buyItemCode         项目code
     * @param subpackageCode      标段code
     * @param chunkNumber         切片编号
     * @param totalChunks         总切片数
     * @return
     */
    @Transactional
    public R<Boolean> upAtt(MultipartFile file, String resumableIdentifier, String filename, String buyItemCode, String subpackageCode, Integer chunkNumber, Integer totalChunks) {
        if (!getAuditStatus(subpackageCode)){
            return R.fail(false,"不允许上传");
        }
        if (!BidFileUtil.isWhwsSupportedAttachment(filename)) {
            return R.fail("不支持的文件类型：" + filename);
        }
        Long userId = SecurityUtils.getUserId();
        String sessionId = chunkFileHelper.getSessionId(subpackageCode, userId, resumableIdentifier);
        FileUploadDto dto = new FileUploadDto();
        dto.setFile(file);
        dto.setFileDatabase(1);
        dto.setFileTypeName(FileTypeNameEnum.PURCHASE_ANSWER_UP_ATT.getCode());
        dto.setBuyItemCode(buyItemCode);
        dto.setSubpackageCode(subpackageCode);
        dto.setSupplierId(userId);
        dto.setSessionId(sessionId);
        dto.setFileName(filename);
        dto.setChunkNumber(chunkNumber);
        dto.setTotalChunks(totalChunks);
        R<SysFileVo> sysFileVoR = remoteProjectFileService.bigFileUpload(dto);
        if (sysFileVoR.hasFail()) {
            return R.fail(sysFileVoR.getMsg());
        }
        SysFileVo data = sysFileVoR.getData();
        if (data != null && StringUtils.hasText(data.getUrl())) {
            ClaimsFileAttDao dao = new ClaimsFileAttDao();
            dao.setSubpackageCode(subpackageCode);
            dao.setFileKey(data.getUrl());
            dao.setFileName(data.getName());
            dao.setCreateBy(String.valueOf(userId));
            dao.setCreateAt(new Date());
            claimsFileAttApi.save(dao);
        }
        return R.ok();

    }

    /**
     * 查询附件列表
     *
     * @param subpackageCode
     * @return
     */
    public List<ClaimsFileAttDao> getAtt(String subpackageCode) {
        QueryWrapper<ClaimsFileAttDao> qw = new QueryWrapper<>();
        qw.eq("subpackage_code", subpackageCode);
        return claimsFileAttApi.list(qw);

    }

    /**
     * 删除附件
     *
     * @param ids
     * @return
     */
    public R<Boolean> attDel(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return R.fail("附件id不能为空");
        }
        List<ClaimsFileAttDao> list = claimsFileAttApi.listByIds(ids);
        if (CollectionUtils.isEmpty(list)) {
            return R.fail("附件不存在");
        }
        list.forEach(i -> FUtil.delFile(i.getFileKey()));
        claimsFileAttApi.removeByIds(ids);
        return R.ok();
    }
}


