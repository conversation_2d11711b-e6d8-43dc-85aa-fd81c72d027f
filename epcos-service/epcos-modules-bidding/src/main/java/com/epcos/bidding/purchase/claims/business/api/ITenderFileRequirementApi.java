package com.epcos.bidding.purchase.claims.business.api;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.IBaseService;
import com.epcos.bidding.purchase.api.domian.cliams.TenderFileRejectionDao;
import com.epcos.bidding.purchase.claims.domain.dao.TenderFileRequirementDao;
import com.epcos.bidding.purchase.claims.domain.dto.*;
import com.epcos.bidding.purchase.claims.domain.vo.TenderFileInfoVo;
import com.epcos.bidding.purchase.claims.domain.vo.TenderFileRequirementVo;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


public interface ITenderFileRequirementApi extends IBaseService<TenderFileRequirementDao> {

    @Override
    default LambdaQueryWrapper<TenderFileRequirementDao> queryWrapper(TenderFileRequirementDao dao) {
        return null;
    }

    default Set<Long> selectIds(String subpackageCode) {
        return list(Wrappers.lambdaQuery(TenderFileRequirementDao.class)
                .eq(TenderFileRequirementDao::getSubpackageCode, subpackageCode)
                .select(TenderFileRequirementDao::getId))
                .stream()
                .map(TenderFileRequirementDao::getId)
                .collect(Collectors.toSet());
    }

    void add(TenderFileRequirementAddDto dto);

    void updates(List<TenderFileRequirementUpdateDto> dtos);

    void dels(List<TenderFileRequirementDelDto> dtos);

    void delBySubpackageCode(List<String> subpackageCodeList);

    List<TenderFileRequirementVo> list(TenderFileRequirementQueryDto dto);

    TenderFileRequirementVo query(TenderFileRequirementQueryDto dto);

    void rejection(TenderFileRejectionDto dto);

    List<TenderFileRejectionDao> queryRejectRecord(RejectRecordDto dto);

    TenderFileInfoVo queryReject(TenderFileRequirementQueryDto dto);
}
