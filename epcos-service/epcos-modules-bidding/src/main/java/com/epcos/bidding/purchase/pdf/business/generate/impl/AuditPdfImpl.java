package com.epcos.bidding.purchase.pdf.business.generate.impl;

import com.epcos.bidding.audit.api.dto.AttDto;
import com.epcos.bidding.audit.api.vo.AuditCommentVo;
import com.epcos.bidding.audit.api.vo.AuditPersonVo;
import com.epcos.bidding.audit.api.vo.AuditRecipientVo;
import com.epcos.bidding.purchase.pdf.business.generate.AbGeneratePdf;
import com.epcos.bidding.purchase.pdf.domian.vo.audit.AuditPdfVo;
import com.epcos.common.core.domain.pdf.Pdf;
import com.epcos.common.core.utils.DateUtils;
import com.epcos.common.core.utils.StringUtils;
import com.epcos.common.file.pdf.Itext7PdfUtil;
import com.epcos.system.api.domain.SysDictData;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Table;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.epcos.bidding.purchase.pdf.constans.PdfValues.AUDIT_PDF_FILE_NAME;
import static com.epcos.bidding.purchase.pdf.constans.PdfValues.AUDIT_PDF_HEAD_NAME;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/15 9:04
 */
public class AuditPdfImpl extends AbGeneratePdf {


    @Override
    protected String getFileName() {
        return AUDIT_PDF_FILE_NAME + super.getFileName();
    }

    @Override
    protected void generateTitle(Document document) {
        document.add(Itext7PdfUtil.paragraphTitle(AUDIT_PDF_HEAD_NAME));
    }

    @Override
    protected void generateTableContent(Document document, Pdf vo) {

        AuditPdfVo auditVo = (AuditPdfVo) vo;
        Map<String, String> dictMap = auditVo.getDictList().stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));


        //总列数
        int maxColumnWidth = 7;
        //行跨度，即合并几行
        int rowSpan = 1;
        //列跨度，即合并几列
        int columnSpan = 6;
        //常量用的默认列跨度
        int constColumnSpan = maxColumnWidth - columnSpan;
        Table table = Itext7PdfUtil.initTable(maxColumnWidth);

        //审批单基本信息
        Map<String, String> auditInfoVoMap = convertToMap(auditVo.getAuditInfoVo());
        auditInfoVoMap.forEach((k, v) -> {
            table.addCell(Itext7PdfUtil.cellCenter(rowSpan, (maxColumnWidth - columnSpan), Itext7PdfUtil.paragraphHeadCenter(k)));
            table.addCell(Itext7PdfUtil.cellCenter(rowSpan, columnSpan, Itext7PdfUtil.paragraphHeadCenter(v)));
        });
        table.addCell(Itext7PdfUtil.cellCenter(rowSpan, constColumnSpan, Itext7PdfUtil.paragraphHeadCenter("审批类型")));
        table.addCell(Itext7PdfUtil.cellCenter(rowSpan, columnSpan, Itext7PdfUtil.paragraphHeadCenter(dictMap.get(auditVo.getAuditInfoVo().getAuditType()))));

        //被审批内容
        Map<String, String> contentMap = new LinkedHashMap<>();
        StringBuilder builder = new StringBuilder();
        auditVo.getAuditInfoVo().getContentList().forEach(content -> {
            if (content.getType().equals("File")) {
                builder.append(content.getName()).append("\n");
                contentMap.put("审核文件", builder.toString());
            } else {
                contentMap.put(content.getName(), content.getValue());
            }
        });
        contentMap.forEach((k, v) -> {
            table.addCell(Itext7PdfUtil.cellCenter(rowSpan, constColumnSpan, Itext7PdfUtil.paragraphHeadCenter(k)));
            table.addCell(Itext7PdfUtil.cellCenter(rowSpan, columnSpan, Itext7PdfUtil.paragraphHeadCenter(v)));
        });

        table.addCell(Itext7PdfUtil.cellCenter(rowSpan, constColumnSpan, Itext7PdfUtil.paragraphHeadCenter("抄送人")));
        List<String> copyUserList = auditVo.getAuditInfoVo().getAuditRecipientList().stream().map(AuditRecipientVo::getCopyName).collect(Collectors.toList());
        String copyStr = copyUserList.stream().collect(Collectors.joining("、"));
        table.addCell(Itext7PdfUtil.cellCenter(rowSpan, columnSpan, Itext7PdfUtil.paragraphHeadCenter(copyStr)));

        //审批流程信息
        table.addCell(Itext7PdfUtil.cellCenter((auditVo.getAuditPersonList().size() + rowSpan), constColumnSpan, Itext7PdfUtil.paragraphHeadCenter("审批流程")));
        table.addCell(Itext7PdfUtil.cellCenter(rowSpan, constColumnSpan, Itext7PdfUtil.paragraphHeadCenter("审批人")));
        table.addCell(Itext7PdfUtil.cellCenter(rowSpan, constColumnSpan, Itext7PdfUtil.paragraphHeadCenter("审批状态")));
        table.addCell(Itext7PdfUtil.cellCenter(rowSpan, constColumnSpan, Itext7PdfUtil.paragraphHeadCenter("审核要求")));
        table.addCell(Itext7PdfUtil.cellCenter(rowSpan, constColumnSpan, Itext7PdfUtil.paragraphHeadCenter("审批意见")));
        table.addCell(Itext7PdfUtil.cellCenter(rowSpan, constColumnSpan, Itext7PdfUtil.paragraphHeadCenter("审批时间")));
        table.addCell(Itext7PdfUtil.cellCenter(rowSpan, constColumnSpan, Itext7PdfUtil.paragraphHeadCenter("附件")));
        List<AuditCommentVo> commentList = new ArrayList<>();
        for (AuditPersonVo audit : auditVo.getAuditPersonList()) {
            table.addCell(Itext7PdfUtil.cellCenter(rowSpan, constColumnSpan, Itext7PdfUtil.paragraphHeadCenter(audit.getUserName())));
            String auditDesc;
            if (Objects.isNull(audit.getStatus())) {
                auditDesc = "待审核";
            } else {
                if (audit.getStatus() == 2) {
                    auditDesc = "待审核";
                } else {
                    auditDesc = audit.getStatus() == 1 ? "同意" : "拒绝";
                }
            }
            table.addCell(Itext7PdfUtil.cellCenter(rowSpan, constColumnSpan, Itext7PdfUtil.paragraphHeadCenter(auditDesc)));
            table.addCell(Itext7PdfUtil.cellCenter(rowSpan, constColumnSpan, Itext7PdfUtil.paragraphHeadCenter(
                    StringUtils.hasText(audit.getRequirement()) ? audit.getRequirement() : ""))
            );
            table.addCell(Itext7PdfUtil.cellCenter(rowSpan, constColumnSpan, Itext7PdfUtil.paragraphHeadCenter(
                    StringUtils.hasText(audit.getRemark()) ? audit.getRemark() : ""))
            );
            table.addCell(Itext7PdfUtil.cellCenter(rowSpan, constColumnSpan, Itext7PdfUtil.paragraphHeadCenter(
                    Objects.isNull(audit.getCreateAt())
                            ? ""
                            : DateUtils.getTimeFormat("yyyy-MM-dd HH:mm:ss", audit.getCreateAt()))));
            String fileNameList = audit.getAttList().stream().map(AttDto::getName).collect(Collectors.joining("\n"));
            table.addCell(Itext7PdfUtil.cellCenter(rowSpan, constColumnSpan, Itext7PdfUtil.paragraphLeft(fileNameList)));
            commentList.addAll(audit.getCommentVoList());
        }

        //审批评论
        table.addCell(Itext7PdfUtil.cellCenter(commentList.size(), constColumnSpan, Itext7PdfUtil.paragraphHeadCenter("审批评论")));
        if (CollectionUtils.isEmpty(commentList)) {
            table.addCell(Itext7PdfUtil.cellCenter(rowSpan, columnSpan, Itext7PdfUtil.paragraphHeadLeft("")));
        } else {
            for (AuditCommentVo commentVo : commentList) {
                table.addCell(Itext7PdfUtil.cellCenter(rowSpan, columnSpan, Itext7PdfUtil.paragraphHeadLeft(commentVo.getComment() +
                        "\n\n" + commentVo.getUserName() + " 添加评论\t\t\t\t\t\t\t\t\t\t\t\t时间:" +
                        DateUtils.getTimeFormat("yyyy-MM-dd HH:mm:ss", commentVo.getCreateAt()))));
            }
        }
        document.add(table);
    }
}
