package com.epcos.bidding.purchase.project.xyzy.business.service;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.common.enums.BulletinTypeEnum;
import com.epcos.bidding.purchase.api.params.PurchaseQuoteFormVo;
import com.epcos.bidding.purchase.api.params.SubPackageInfoVo;
import com.epcos.bidding.purchase.api.params.dto.QuoteFormCreatorDto;
import com.epcos.bidding.purchase.api.params.vo.bulletin.TimeNodeVo;
import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import com.epcos.bidding.purchase.bulletin.business.api.IBulletinApi;
import com.epcos.bidding.purchase.bulletin.domain.dao.BulletinDao;
import com.epcos.bidding.purchase.claims.business.service.ClaimsFileQuoteFormService;
import com.epcos.bidding.purchase.project.base.business.api.IPublicBaseApi;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.purchase.project.base.domain.vo.LabelJson;
import com.epcos.bidding.purchase.project.base.domain.vo.SubPackageTimeVo;
import com.epcos.bidding.purchase.project.xyzy.business.api.XYZYBuyJoinSubApi;
import com.epcos.bidding.purchase.project.xyzy.business.api.buyitem.XYZYBuyItemApi;
import com.epcos.bidding.purchase.project.xyzy.domain.dao.BuyItemXYZYDao;
import com.epcos.bidding.purchase.project.xyzy.domain.dto.XYZYBuyItemQueryDto;
import com.epcos.bidding.purchase.project.xyzy.domain.dto.XYZYCreateBuyItemDto;
import com.epcos.bidding.purchase.project.xyzy.domain.vo.XYZYBuyItemInfoVo;
import com.epcos.bidding.purchase.project.xyzy.domain.vo.XYZYBuyItemPageVo;
import com.epcos.bidding.purchase.webservice.external.project.domain.dao.ExternalProjectAllocateDao;
import com.epcos.bidding.purchase.webservice.external.project.service.IExternalProjectAllocateApi;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.StringUtils;
import com.epcos.common.core.utils.bean.BeanUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.epcos.common.core.constant.PurchaseConstants.BulletinAudit.PASS;
import static java.util.Collections.EMPTY_LIST;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 湖北省襄阳市中医医院版本
 * @date 2024/4/23 13:59
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class XYZYBuyJoinSubService implements XYZYBuyJoinSubApi {

    private final IPublicBaseApi publicBaseApi;
    private final XYZYBuyItemApi xyzyBuyItemApi;
    private final ISubPackageApi subPackageApi;
    private final IBulletinApi bulletinApi;
    private final IExternalProjectAllocateApi externalProjectAllocateApi;
    private final ClaimsFileQuoteFormService claimsFileQuoteFormService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createXYZYBuyItem(XYZYCreateBuyItemDto dto) {
        //先保存基本信息
        BuyItemDao baseInfo = publicBaseApi.insertBaseInfo(dto, dto.getSubpackageDtoList());
        // 成功后则再保存扩展信息
        xyzyBuyItemApi.insertXYZYBuyItem(dto, baseInfo.getBuyItemCode());
        //修改项目资料信息
        externalProjectAllocateApi.updateById(new ExternalProjectAllocateDao() {{
            setId(dto.getAllocateId());
            setAllocateWhetherCreate(1);
        }});
    }

    @Override
    public IPage<XYZYBuyItemPageVo> xyzyBuyItemPage(PageSortEntity<XYZYBuyItemQueryDto> dto) {
        IPage<XYZYBuyItemPageVo> page = xyzyBuyItemApi.selectJoinBuyItemPage(dto);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return page;
        }
        List<String> buyItemCodeList = page.getRecords().stream().map(XYZYBuyItemPageVo::getBuyItemCode).collect(Collectors.toList());
        Map<String, List<TimeNodeVo>> timeNodeMap = bulletinApi.getTimeNodeList(buyItemCodeList);
        //查询标段包信息
        List<SubpackageDao> subpackageDaoList = subPackageApi.packageListInfo(buyItemCodeList);
        //组装标段以及获取项目列表时间节点
        assemble(page.getRecords(), subpackageDaoList, timeNodeMap);
        return page;
    }

    private List<XYZYBuyItemPageVo> assemble(List<XYZYBuyItemPageVo> voList, List<SubpackageDao> subpackageDaoList,
                                             Map<String, List<TimeNodeVo>> timeNodeMap) {
        voList.forEach(v -> {
            List<SubPackageTimeVo> packageVOList = subpackageDaoList.stream().map(sub -> {
                if (sub.getBuyItemCode().equals(v.getBuyItemCode())) {
                    SubPackageTimeVo packageVo = new SubPackageTimeVo();
                    BeanUtils.copyProperties(sub, packageVo);
                    List<LabelJson> jsonList = convertJson(sub.getLabelJson());
                    packageVo.setJsonList(jsonList);
                    packageVo.setTimeNodeList(timeNodeMap.get(sub.getSubpackageCode()));
                    return packageVo;
                }
                return null;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            v.setSubpackageTimeVoList(packageVOList);
        });
        return voList;
    }

    private List<LabelJson> convertJson(String labelJsonStr) {
        if (StringUtils.hasText(labelJsonStr)) {
            Map<String, String> lableMap = (Map<String, String>) JSON.parse(labelJsonStr);
            List<LabelJson> jsonList = lableMap.keySet().stream().map(k -> {
                LabelJson labelJson = new LabelJson();
                labelJson.setJsonKey(k);
                labelJson.setJsonLabel(lableMap.get(k));
                return labelJson;
            }).collect(Collectors.toList());
            return jsonList;
        }
        return EMPTY_LIST;
    }

    @Override
    public XYZYBuyItemInfoVo queryXYZYBuyItemInfo(String buyItemCode) {
        //查询项目基础信息
        BuyItemDao buyItemDao = publicBaseApi.findOneByBuyItemCode(buyItemCode);
        //查询医疗版本信息
        BuyItemXYZYDao xyzyDao = xyzyBuyItemApi.findOneByBuyItemCode(buyItemCode);
        //查询标段基本信息
        List<SubpackageDao> subpackageDaoList = subPackageApi.findByBuyItemCode(buyItemCode);
        // 查询报价信息
        Set<String> subpackageCodeList = subpackageDaoList.stream().map(SubpackageDao::getSubpackageCode).collect(Collectors.toSet());
        Map<String, List<PurchaseQuoteFormVo>> quoteMap = claimsFileQuoteFormService.list(subpackageCodeList);
        //组合数据
        return assembleData(buyItemDao, xyzyDao, subpackageDaoList, quoteMap);
    }

    private XYZYBuyItemInfoVo assembleData(BuyItemDao buyItemDao, BuyItemXYZYDao xyzyDao, List<SubpackageDao> subpackageDaoList,
                                           Map<String, List<PurchaseQuoteFormVo>> quoteMap) {
        XYZYBuyItemInfoVo vo = new XYZYBuyItemInfoVo();
        BeanUtils.copyProperties(buyItemDao, vo);
        BeanUtils.copyProperties(xyzyDao, vo);
        vo.setParamsAttList(xyzyDao.parseAnnexFile());
        List<SubPackageInfoVo> subpackageQuoteDtoList = new ArrayList<>();
        subpackageDaoList.forEach(s -> {
            SubPackageInfoVo subPackageInfoVo = new SubPackageInfoVo();
            BeanUtils.copyProperties(s, subPackageInfoVo);
            QuoteFormCreatorDto creatorDto = new QuoteFormCreatorDto();
            creatorDto.setSubpackageCode(s.getSubpackageCode());
            creatorDto.setBodyMaps(EMPTY_LIST);
            creatorDto.setHeads(EMPTY_LIST);
            if (MapUtil.isNotEmpty(quoteMap)) {
                List<LinkedHashMap<String, String>> bodysMapList = new ArrayList<>();
                Optional.ofNullable(quoteMap.get(s.getSubpackageCode()))
                        .ifPresent(list -> list.forEach(q -> {
                            creatorDto.setHeads(q.getHeads());
                            bodysMapList.addAll(q.getBodyMaps());
                        }));
                creatorDto.setBodyMaps(bodysMapList);
            }
            subPackageInfoVo.setClaimsFileQuoteFormCreatorDto(creatorDto);
            subpackageQuoteDtoList.add(subPackageInfoVo);
        });
        vo.setSubpackageDtoList(subpackageQuoteDtoList);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateXYZYBuyItemInfo(XYZYCreateBuyItemDto dto) {
        List<BulletinDao> bulletinDaoList = bulletinApi.findByBuyItemCode(dto.getBuyItemCode());
        if (!CollectionUtils.isEmpty(bulletinDaoList)) {
            throw new ServiceException("公告已发，不可修改");
        }
        //先修改基本项目信息
        publicBaseApi.updateBaseInfo(dto, dto.getSubpackageDtoList());
        //修改成功后 再修改 扩展信息
        xyzyBuyItemApi.updateXYZYBuyItemInfo(dto);
    }

    @Override
    public List<SuperPackageVo> findByBuyItemCode(String buyItemCode) {
        List<SubpackageDao> subpackageDaoList = subPackageApi.findByBuyItemCode(buyItemCode);
        List<SuperPackageVo> voList = subpackageDaoList.stream().map(s -> {
            SuperPackageVo vo = new SuperPackageVo();
            BeanUtils.copyProperties(s, vo);
            return vo;
        }).collect(Collectors.toList());
        return voList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delXYZYBuyItemInfo(String buyItemCode) {
        List<BulletinDao> bulletinDaoList = bulletinApi.findByBuyItemCodeAndBulletinTypeAndAuditStatusIn(
                buyItemCode,
                BulletinTypeEnum.BIDDING_ANNOUNCEMENT.getKey(),
                Collections.singletonList(PASS)
        );
        if (!CollectionUtils.isEmpty(bulletinDaoList)) {
            throw new ServiceException("公告已发，不可删除");
        }
        publicBaseApi.delBuyItemInfo(buyItemCode);
        xyzyBuyItemApi.delXYZYBuyItemInfo(buyItemCode);
        bulletinApi.delBulletin(buyItemCode, BulletinTypeEnum.BIDDING_ANNOUNCEMENT.getKey());
        return Boolean.TRUE;
    }

    /**
     * 查询流程编号
     *
     * @param buyItemCode
     * @return
     */
    @Override
    public String getInnerCode(String buyItemCode) {
        BuyItemXYZYDao oneByBuyItemCode = xyzyBuyItemApi.findOneByBuyItemCode(buyItemCode);
        return oneByBuyItemCode.getInnerCode();
    }
}
