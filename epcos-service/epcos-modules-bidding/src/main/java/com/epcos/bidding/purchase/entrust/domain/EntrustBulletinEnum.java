package com.epcos.bidding.purchase.entrust.domain;

public enum EntrustBulletinEnum {

    /**
     * 审核状态
     */
    AUDIT_CREATED(0),
    AUDIT_PASS(1),
    AUDIT_FAIL(2),

    /**
     * 公告是否展示
     */
    BULLETIN_SHOW(1),
    BULLETIN_HIDE(2);


    private final int code;

    EntrustBulletinEnum(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static EntrustBulletinEnum of(int code) {
        for (EntrustBulletinEnum v : EntrustBulletinEnum.values()) {
            if (v.code == code) {
                return v;
            }
        }
        return null;
    }

}
