package com.epcos.bidding.purchase.claims.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class BidFileRejectionDto implements Serializable {
    private static final long serialVersionUID = 8562429818277457228L;

    @ApiModelProperty("标段code")
    @NotBlank(message = "标段code不能为空")
    private String subpackageCode;

    @ApiModelProperty("供应商id")
    @NotNull(message = "供应商id不能为空")
    private Long supplierId;

    @ApiModelProperty("采购文件要求id")
    @NotEmpty(message = "采购文件要求id不能为空")
    private List<Long> requirementIdList;
}
