package com.epcos.bidding.purchase.entrust.business.api;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.IBaseService;
import com.epcos.bidding.purchase.entrust.domain.dao.EntrustBulletinDao;
import org.springframework.util.StringUtils;

import java.util.Objects;


public interface IEntrustBulletinApi extends IBaseService<EntrustBulletinDao> {

    @Override
    default LambdaQueryWrapper<EntrustBulletinDao> queryWrapper(EntrustBulletinDao dao) {
        LambdaQueryWrapper<EntrustBulletinDao> query = Wrappers.lambdaQuery(EntrustBulletinDao.class);
        if (Objects.nonNull(dao.getId())) {
            return query.eq(EntrustBulletinDao::getId, dao.getId());
        }
        return noneOrderByQueryWrapper(dao).orderByDesc(EntrustBulletinDao::getAuditTime);
    }

    default LambdaQueryWrapper<EntrustBulletinDao> noneOrderByQueryWrapper(EntrustBulletinDao dao) {
        return Wrappers.lambdaQuery(EntrustBulletinDao.class)
                .eq(Objects.nonNull(dao.getCreateUserId()), EntrustBulletinDao::getCreateUserId, dao.getCreateUserId())
                .like(StringUtils.hasText(dao.getCreateUserName()), EntrustBulletinDao::getCreateUserName, dao.getCreateUserName())
                .like(StringUtils.hasText(dao.getProjectName()), EntrustBulletinDao::getProjectName, dao.getProjectName())
                .like(StringUtils.hasText(dao.getBulletinName()), EntrustBulletinDao::getBulletinName, dao.getBulletinName())
                .eq(StringUtils.hasText(dao.getBulletinType()), EntrustBulletinDao::getBulletinType, dao.getBulletinType())
                .eq(Objects.nonNull(dao.getAuditStatus()), EntrustBulletinDao::getAuditStatus, dao.getAuditStatus())
                .eq(Objects.nonNull(dao.getShowStatus()), EntrustBulletinDao::getShowStatus, dao.getShowStatus())
                .eq(Objects.nonNull(dao.getAuditUserId()), EntrustBulletinDao::getAuditUserId, dao.getAuditUserId())
                .like(StringUtils.hasText(dao.getAuditUserName()), EntrustBulletinDao::getAuditUserName, dao.getAuditUserName());
    }

    default LambdaQueryWrapper<EntrustBulletinDao> orderAuditStatusWrapper(EntrustBulletinDao dao) {
        return noneOrderByQueryWrapper(dao).last("order by field(audit_status,0,2,1),id desc");
    }


}
