package com.epcos.bidding.purchase.goods.domain.vo;

import com.epcos.bidding.purchase.api.params.AttributeVo;
import com.epcos.bidding.supplier.goods.domain.vo.GoodsVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/12 18:33
 */
@Data
public class GoodsCartVo extends GoodsVo {

    private static final long serialVersionUID = 5656078022038447411L;

    @ApiModelProperty(value = "购物车id")
    private Long cartId;

    @ApiModelProperty(value = "购买者id")
    private Long buyerId;

    @ApiModelProperty(value = "购买数量")
    private Integer buyCount;

    @ApiModelProperty(value = "头")
    private List<AttributeVo> headeList;

    @ApiModelProperty("报价内容")
    @NotEmpty(message = "报价内容，必填")
    private List<LinkedHashMap<String, String>> bodyMaps;
}
