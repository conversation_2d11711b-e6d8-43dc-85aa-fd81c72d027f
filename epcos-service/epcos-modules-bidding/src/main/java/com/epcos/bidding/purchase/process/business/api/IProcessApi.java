package com.epcos.bidding.purchase.process.business.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.api.params.dto.BaseQueryDto;
import com.epcos.bidding.purchase.process.domain.dto.MeetDto;
import com.epcos.bidding.purchase.process.domain.vo.BuyItemFilePageVo;
import com.epcos.bidding.purchase.process.domain.vo.BuyItemMeetVo;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.xyzy.domain.dto.XYZYBuyItemQueryDto;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/3 10:11
 */
public interface IProcessApi {


    List<BuyItemDao> queryBuyItemDaoList(BaseQueryDto dto);


    /**
     * 采购评审准备会（上会安排）分页
     *
     * @param dto 条件
     * @return
     */
    IPage<BuyItemMeetVo> meetingPage(PageSortEntity<MeetDto> dto);

    /**
     * 采购项目文件分页
     *
     * @param dto 条件
     * @return
     */
    IPage<BuyItemFilePageVo> buyItemFilePage(PageSortEntity<XYZYBuyItemQueryDto> dto);
}
