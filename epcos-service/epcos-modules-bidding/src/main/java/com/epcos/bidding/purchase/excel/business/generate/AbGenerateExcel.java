package com.epcos.bidding.purchase.excel.business.generate;

import com.epcos.bidding.common.utils.excel.PoiExcelUtil;
import com.epcos.bidding.purchase.api.params.AttributeVo;
import com.epcos.bidding.purchase.excel.domain.Excel;
import com.epcos.bidding.purchase.excel.factory.IGenerateExcel;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.file.html2pdf.HtmlUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/6 9:14
 */
@Slf4j
public abstract class AbGenerateExcel implements IGenerateExcel {

    @Override
    public Path generateExcel(List<? extends Excel> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            throw new ServiceException("暂无数据");
        }
        Path tmpExcelFilePath = HtmlUtil.getTmpXlsxFilePath(this.getFileName(""));
        String titleName = this.getTitleName();
        try (
                XSSFWorkbook workbook = new XSSFWorkbook();
                OutputStream outputStream = Files.newOutputStream(tmpExcelFilePath, StandardOpenOption.CREATE)
        ) {
            // 样式
            CellStyle cellStyleTitle = PoiExcelUtil.cellStyleTitle(workbook);
            CellStyle cellStyleTableHead = PoiExcelUtil.cellStyleTableHead(workbook);
            CellStyle cellStyleContent = PoiExcelUtil.cellStyleContent(workbook);
            voList.forEach(vo -> {
                XSSFSheet sheet = workbook.createSheet(this.getSheetName(vo));
                PoiExcelUtil.initSheetStyle(sheet);
                int startRow = 0;
                PoiExcelUtil.createRowOneCellAndMergeAllColumn(sheet, startRow, PoiExcelUtil.DEFAULT_ROW_HEIGHT + 5,
                        titleName, cellStyleTitle, this.getColumnSize(vo.getHeads()).keySet().size() - 1);
                generateHead(sheet, cellStyleTableHead, vo.getHeads());
                generateBody(sheet, cellStyleContent, vo);
            });
            workbook.write(outputStream);
            return tmpExcelFilePath;
        } catch (IOException e) {
            log.error("生成 title:{} excel异常，voList:{}", titleName, voList, e);
            throw new ServiceException("生成 " + titleName + " excel异常");
        }
    }

    protected abstract void generateHead(XSSFSheet sheet, CellStyle cellStyle, List<AttributeVo> headList);

    protected abstract void generateBody(XSSFSheet sheet, CellStyle cellStyle, Excel vo);

    protected abstract String getFileName(String fileName);

    protected abstract String getSheetName(Excel sheetName);

    protected abstract String getTitleName();

    protected abstract Map<String, String> getColumnSize(List<AttributeVo> vo);
}
