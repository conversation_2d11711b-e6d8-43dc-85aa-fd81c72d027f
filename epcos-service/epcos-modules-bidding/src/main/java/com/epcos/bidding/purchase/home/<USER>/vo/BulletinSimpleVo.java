package com.epcos.bidding.purchase.home.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/13 17:20
 */
@Data
public class BulletinSimpleVo implements Serializable {

    private static final long serialVersionUID = 1117462253402271972L;


    @ApiModelProperty(value = "公告id")
    private Long bulletinId;

    @ApiModelProperty(value = "公告名称")
    private String bulletinName;

    @ApiModelProperty(value = "公告类型[即后台配置的公告类型]")
    private String bulletinType;

    @ApiModelProperty(value = "公告类型中文名[即后台配置的公告类型中文名]")
    private String bulletinTypeName;
}
