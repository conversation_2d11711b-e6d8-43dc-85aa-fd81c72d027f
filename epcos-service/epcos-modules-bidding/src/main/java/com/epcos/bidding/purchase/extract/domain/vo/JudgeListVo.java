package com.epcos.bidding.purchase.extract.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/20 16:56
 */
@Data
public class JudgeListVo implements Serializable {

    private static final long serialVersionUID = 8218677102912320386L;

    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * judgeIds  这个是数据库组合返回的 不展示 给前端
     */
    @ApiModelProperty(value = "评委id", hidden = true)
    private String judgeIds;

    /**
     * 最终 judgeIds 转换为 judgeIdList 展示给前端
     */
    @ApiModelProperty(value = "评委id")
    private List<Long> judgeIdList;


    @ApiModelProperty(value = "抽取记录名")
    private String extractLongName;

    /**
     * 此人数为，同一次抽取的人数
     * 同一个项目可以抽取多次
     */
    @ApiModelProperty(value = "人数")
    private Integer number;

    @ApiModelProperty(value = "抽取时间")
    private Date extractTime;

    @ApiModelProperty(value = "备注")
    private String remark;
}
