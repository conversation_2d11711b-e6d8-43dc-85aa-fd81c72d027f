package com.epcos.bidding.purchase.entrust.domain.dto;

import com.epcos.bidding.purchase.entrust.domain.dao.EntrustBulletinDao;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.web.page.PageDomain;
import com.epcos.common.core.web.page.TableSupport;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.web.domain.user.LoginUser;
import lombok.Getter;

import java.util.Objects;

@Getter
public class EntrustBulletinContext {

    private final EntrustBulletinDao dto;
    private final LoginUser user;
    private final Integer pageNum;
    private final Integer pageSize;

    public EntrustBulletinContext(EntrustBulletinDao dto) {
        if (Objects.isNull(dto)) {
            throw new ServiceException("参数不能为空");
        }
        this.dto = dto;
        this.user = SecurityUtils.getLoginUser();
        PageDomain pageDomain = TableSupport.buildPageRequest();
        this.pageNum = pageDomain.getPageNum();
        this.pageSize = pageDomain.getPageSize();
    }

}
