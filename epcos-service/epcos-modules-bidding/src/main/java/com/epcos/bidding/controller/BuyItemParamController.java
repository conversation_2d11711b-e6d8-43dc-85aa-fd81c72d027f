package com.epcos.bidding.controller;

import com.epcos.bidding.audit.business.api.IAuditInfoApi;
import com.epcos.bidding.audit.business.api.IAuditPersonApi;
import com.epcos.bidding.audit.domain.dao.AuditInfoDao;
import com.epcos.bidding.audit.domain.dao.AuditPersonDao;
import com.epcos.bidding.common.annotaion.GetCommon;
import com.epcos.bidding.common.annotaion.GetSimpleItem;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.purchase.api.params.vo.buyitem.ItemSubpackageVo;
import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import com.epcos.bidding.purchase.technology.business.api.BuyItemParamApi;
import com.epcos.bidding.purchase.technology.domain.dto.*;
import com.epcos.bidding.purchase.technology.domain.vo.BuyItemParamVo;
import com.epcos.common.core.domain.R;
import com.epcos.common.log.annotation.Log;
import com.epcos.common.log.enums.BusinessType;
import com.epcos.common.security.annotation.RequiresPermissions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/4 11:58
 */
@Api(tags = "项目技术参数")
@RestController
@RequestMapping("/param")
@RequiredArgsConstructor
public class BuyItemParamController {

    private final BuyItemParamApi buyItemParamApi;
    private final IAuditInfoApi auditInfoApi;
    private final IAuditPersonApi auditPersonApi;

    @ApiOperation(value = "选择推送人")
    @Log(title = "选择推送人[接口：choicePush]", businessType = BusinessType.INSERT)
    @PostMapping(value = "/choicePush")
    public R<Boolean> choicePush(@RequestBody BuyItemParamDto dto) {
        buyItemParamApi.choicePush(dto);
        return R.ok();
    }

//    @ApiOperation(value = "推送列表")
//    @PostMapping(value = "/pushPage")
//    public TableDataVo<BuyItemParamVo> pushPage(@RequestBody PageSortEntity<ParamQueryDto> dto) {
//        if (CollectionUtils.isEmpty(SecurityUtils.getLoginUser().getOrgCodes())) {
//            return new TableDataVo();
//        }
//        IPage<BuyItemParamVo> page = buyItemParamApi.pushPage(dto);
//        return new TableDataVo(page.getRecords(), page.getTotal());
//    }

//    @ApiOperation(value = "推送回复列表")
//    @PostMapping(value = "/answerPage")
//    public TableDataVo<BuyItemParamVo> replyPage(@RequestBody PageSortEntity<ParamQueryDto> dto) {
//        if (CollectionUtils.isEmpty(SecurityUtils.getLoginUser().getOrgCodes())) {
//            return new TableDataVo();
//        }
//        IPage<BuyItemParamVo> page = buyItemParamApi.replyPage(dto);
//        return new TableDataVo(page.getRecords(), page.getTotal());
//    }

    @ApiOperation(value = "添加备注")
    @Log(title = "添加备注[接口：addRemark]", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/addRemark")
    public R<Boolean> addRemark(@RequestBody ParamRemarkDto dto) {
        buyItemParamApi.addRemark(dto);
        return R.ok();
    }

    @ApiOperation(value = "填写参数")
    @Log(title = "填写参数[接口：writeParam]", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/writeParam")
    public R<Boolean> writeParam(@RequestBody AddParamDto dto) {
        buyItemParamApi.writeParam(dto);
        return R.ok();
    }

    @ApiOperation(value = "审核参数")
    @Log(title = "审核参数[接口：auditParam]", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/auditParam")
    public R<Boolean> auditParam(@RequestBody AuditParamDto dto) {
        buyItemParamApi.auditParam(dto);
        return R.ok();
    }

    @RequiresPermissions("process:projectApproval:add")
    @ApiOperation(value = "技术参数发起审核")
    @Log(title = "技术参数发起审核[接口：paramAudit]", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/paramAudit")
    public R<Boolean> paramAudit(@RequestBody ParamAuditDto dto) {
        buyItemParamApi.insertTo(dto);
        return R.ok();
    }

    @ApiOperation(value = "技术参数列表")
    @GetSimpleItem(common = @GetCommon(buyItemCodeEL = "#buyItemCode"))
    @GetMapping(value = "/paramList")
    public R<List<ItemSubpackageVo<List<BuyItemParamVo>>>> queryParam(@NotNull String buyItemCode) {
        List<AuditInfoDao> daoList = auditInfoApi.findList(buyItemCode, null);
        List<SuperPackageVo> superPackageVoList = GetUtil.getSimpleItemVo().getSuperPackageVoList();
        List<ItemSubpackageVo<List<BuyItemParamVo>>> voList = superPackageVoList.stream()
                .map(s -> {
                    ItemSubpackageVo<List<BuyItemParamVo>> vo = new ItemSubpackageVo<>
                            (
                                    s.getSubpackageCode(),
                                    s.getSubpackageName(),
                                    Collections.EMPTY_LIST
                            );
                    List<BuyItemParamVo> buyItemParamVos = daoList.stream().filter(f -> Objects.equals(f.getSubpackageCode(), s.getSubpackageCode()))
                            .map(a -> {
                                List<AuditPersonDao> auditPersonDaos = auditPersonApi.find(a.getId());
                                if (!CollectionUtils.isEmpty(auditPersonDaos)) {
                                    return BuyItemParamVo.builder()
                                            .auditId(a.getId())
                                            .paramApplyUser(auditPersonDaos.get(0).getUserName())
                                            .createAt(a.getCreateAt())
                                            .auditType(a.getAuditType())
                                            .build();
                                }
                                return null;
                            })
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    vo.setData(buyItemParamVos);
                    return vo;
                }).collect(Collectors.toList());
        return R.ok(voList);
    }
}
