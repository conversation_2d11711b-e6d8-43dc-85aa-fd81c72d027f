package com.epcos.bidding.common.aspects.sms.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class SmsToSupplierDto {

    /**
     * 供应商类型集合
     */
    @ApiModelProperty(value = "供应商类型")
    @NotEmpty(message = "供应商类型不能为空")
    private List<Integer> bidTypes;

    /**
     * 供应商存储类型(0:入库 , 1:非入库)
     */
    @ApiModelProperty(value = "供应商存储类型")
    private Integer storageType;

    /**
     * 短信内容
     */
    @ApiModelProperty(value = "短信内容")
    @NotBlank(message = "短信内容不能为空")
    private String content;
}
