package com.epcos.bidding.purchase.home.domain.vo;

import com.epcos.bidding.purchase.project.base.domain.vo.BuyItemBaseVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/8 14:56
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageHomeVo extends BuyItemBaseVo {

    private static final long serialVersionUID = 2189281670843082766L;

    @ApiModelProperty(value = "标段编号（业务使用代码）")
    private String subpackageCode;

    @ApiModelProperty(value = "标段名字")
    private String subpackageName;

    @ApiModelProperty(value = "公告id")
    private Long bulletinId;

    @ApiModelProperty(value = "公告名称")
    private String bulletinName;

    @ApiModelProperty(value = "公告附件key")
    private String annexKey;

    @ApiModelProperty(value = "公告的pdf_key")
    private String bulletinContentKey;

    @ApiModelProperty(value = "公告类型[即后台配置的公告类型]")
    @NotBlank
    private String bulletinType;

    @ApiModelProperty(value = "公告类型中文名[即后台配置的公告类型中文名]")
    @NotBlank
    private String bulletinTypeName;

    @ApiModelProperty(value = "公告审核时间")
    private Date reviewTime;

    @ApiModelProperty(value = "报名截止时间（公示期）")
    private Date registrationEndTime;

    @ApiModelProperty(value = "采购人组织名字")
    private String tenderName;

    /**
     * 这里 不能随意更换 subpackageDtoList 名字 页面对应回显 需要 这个名字
     */
//    @ApiModelProperty(value = "标段信息")
//    private List<SubPackageInfoVo> subpackageDtoList;
}
