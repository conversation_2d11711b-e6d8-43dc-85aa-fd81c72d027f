package com.epcos.bidding.purchase.win.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/17 15:50
 */
@Data
public class ResultBulletinVo implements Serializable {

    private static final long serialVersionUID = -5637062638093363178L;

    @ApiModelProperty(value = "公告id")
    private Long bulletinId;

    @ApiModelProperty(value = "审批code")
    private String auditCode;

    @ApiModelProperty(value = "中标候选人公示key/中标结果公示key")
    private String bulletinKey;

    @ApiModelProperty(value = "审核状态，0-不同意，1-同意，2-待审批，3-撤回")
    private String auditStatus;
}
