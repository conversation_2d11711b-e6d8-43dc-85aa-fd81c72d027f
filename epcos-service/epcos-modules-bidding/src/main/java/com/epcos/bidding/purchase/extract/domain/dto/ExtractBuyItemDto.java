package com.epcos.bidding.purchase.extract.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/11 18:02
 */
@Data
public class ExtractBuyItemDto implements Serializable {

    private static final long serialVersionUID = -1621114849824336379L;

    @ApiModelProperty("采购项目名称")
    @NotBlank
    private String buyItemName;
}
