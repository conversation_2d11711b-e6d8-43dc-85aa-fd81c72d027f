package com.epcos.bidding.common.aspects.filter.validator;

import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import com.epcos.bidding.purchase.api.params.vo.bulletin.BulletinAndItemTimeVo;

/**
 * 校验报名截止时间已过
 */
public class RegistrationEndTimeHasPassedValidator implements ResultPostHandlerFilterChain<BulletinAndItemTimeVo> {
    @Override
    public void postHandler(AspectContext context, BulletinAndItemTimeVo result) {
        result.verifyRegistrationEndTimeHasPassed();
    }

}
