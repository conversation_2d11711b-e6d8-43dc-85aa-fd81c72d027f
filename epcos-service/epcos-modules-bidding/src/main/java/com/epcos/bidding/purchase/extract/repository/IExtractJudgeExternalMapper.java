package com.epcos.bidding.purchase.extract.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.epcos.bidding.purchase.extract.domain.dao.ExtractJudgeExternalDao;
import com.epcos.bidding.purchase.extract.domain.dto.ExtractQueryDto;
import com.epcos.bidding.purchase.extract.domain.vo.JudgeListVo;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/25 14:24
 */
public interface IExtractJudgeExternalMapper extends BaseMapper<ExtractJudgeExternalDao> {

    IPage<JudgeListVo> judgePage(IPage page, @Param(value = "dto") ExtractQueryDto dto);
}
