package com.epcos.bidding.common.aspects.vo;

import com.epcos.bidding.purchase.api.params.vo.buyitem.BuyItemVo;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetItemVo {

    public GetItemVo(BuyItemVo buyItemVo) {
        this.buyItemVo = buyItemVo;
    }

    /**
     * 采购项目
     */
    BuyItemVo buyItemVo;

    /**
     * 院内编号
     */
    String innerCode;

    /**
     * 包
     */
    List<SubpackageDao> subpackageDaoList;
}
