package com.epcos.bidding.purchase.process.mapping;

import com.epcos.bidding.purchase.process.domain.dto.SignUpStatusDto;
import com.epcos.bidding.supplier.api.params.SupplierSignUpStatusDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/2 10:29
 */
@Mapper
public interface DtoVoConvert {

    DtoVoConvert INSTANCE = Mappers.getMapper(DtoVoConvert.class);

    SupplierSignUpStatusDto convert(SignUpStatusDto signUpStatusDto);
}
