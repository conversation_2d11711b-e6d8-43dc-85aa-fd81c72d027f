package com.epcos.bidding.common.aspects.filter.validator;

import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierSignUpDao;
import com.epcos.common.core.exception.ServiceException;

import java.util.Optional;

/**
 * 校验报名供应商不合格
 */
public class SignUpUnqualifiedValidator implements ResultPostHandlerFilterChain<SupplierSignUpDao> {
    @Override
    public void postHandler(AspectContext context, SupplierSignUpDao result) {
        Optional.ofNullable(result)
                .orElseThrow(() -> new ServiceException("未查询到报名信息"))
                .verifyUnqualified();
    }

}
