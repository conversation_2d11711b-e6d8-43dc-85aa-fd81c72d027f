package com.epcos.bidding.common.aspects.filter.validator;

import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierSignUpDao;
import com.epcos.common.core.exception.ServiceException;

import java.util.Optional;

/**
 * 报名信息不存在
 */
public class SignUpNotExistsValidator implements ResultPostHandlerFilterChain<SupplierSignUpDao> {
    @Override
    public void postHandler(AspectContext context, SupplierSignUpDao result) {
        Optional.ofNullable(result)
                .orElseThrow(() -> new ServiceException("您未报名"));
    }
}
