package com.epcos.bidding.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 切片上传配置
 * 
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "epcos.chunk-upload")
public class ChunkUploadConfig {
    
    /**
     * 切片临时存储目录
     */
    private String tempDir = System.getProperty("java.io.tmpdir") + "/epcos-chunks";
    
    /**
     * 切片过期时间（小时）
     */
    private int expireHours = 24;
    
    /**
     * 最大切片数量
     */
    private int maxChunks = 1000;
    
    /**
     * 单个切片最大大小（MB）
     */
    private int maxChunkSizeMB = 100;
    
    /**
     * 是否启用切片上传
     */
    private boolean enabled = true;
    
    /**
     * 切片上传超时时间（秒）
     */
    private int timeoutSeconds = 300;
    
    /**
     * 是否自动清理过期切片
     */
    private boolean autoCleanup = true;
    
    /**
     * 清理任务执行间隔（小时）
     */
    private int cleanupIntervalHours = 6;
}
