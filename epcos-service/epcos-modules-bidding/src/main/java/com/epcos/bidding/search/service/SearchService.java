package com.epcos.bidding.search.service;

import cn.hutool.core.util.ArrayUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.home.domain.dto.HomePageDto;
import com.epcos.bidding.purchase.home.domain.vo.PageHomeVo;
import com.epcos.common.core.web.page.TableDataVo;
import com.epcos.common.search.LuceneHelper;
import com.epcos.common.search.properties.index.BulletinIndex;
import com.epcos.common.search.utils.SearchHighlightUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.document.Document;
import org.apache.lucene.search.highlight.Highlighter;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

import static com.epcos.common.search.properties.index.BulletinIndex.NAME_INDEX;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/14 11:12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SearchService implements ISearchApi {

    private final LuceneHelper luceneHelper;

    @Override
    public TableDataVo<PageHomeVo> homePage(PageSortEntity<HomePageDto> dto) {

        int current = Math.toIntExact(dto.getCalcPageNum());
        int size = Math.toIntExact(dto.getCalcPageSize());
        String word;
        if (ArrayUtil.isEmpty(dto.getEntity().getWords())) {
            word = "";
        } else {
            word = dto.getEntity().getWords()[0];
        }
        String[] columns = BulletinIndex.getColumns();

        // 查询总记录数
        long total = luceneHelper.searchTotal(NAME_INDEX, word, columns);
        // 执行搜索（分页查询）
        List<Document> documents = luceneHelper.search(NAME_INDEX, word, columns, current, size);

        if (CollectionUtils.isEmpty(documents)) {
            return new TableDataVo<>();
        }

        // 创建高亮器
        Highlighter highlighter = SearchHighlightUtil.createHighlighter(dto.getEntity().getWords());

        // 转换文档为VO
        List<PageHomeVo> vos = convertDocumentsToVos(documents, highlighter);

        return new TableDataVo<>(vos, total);
    }

    /**
     * 转换文档列表为VO列表
     */
    private List<PageHomeVo> convertDocumentsToVos(List<Document> documents, Highlighter highlighter) {

        List<PageHomeVo> vos = Lists.newArrayList();
        for (Document document : documents) {
            try {
                String title = document.get(BulletinIndex.bulletinName);
                // 使用高亮处理方法
                String highlightedTitle = SearchHighlightUtil.getHighlightedText(title, BulletinIndex.bulletinName, highlighter);

                String id = document.get(BulletinIndex.id);
                Date reviewTime = new Date();
                Date signUpTime = new Date();
                String bulletinTypeName = document.get(BulletinIndex.bulletinTypeName);

                // 组装 VO
                PageHomeVo vo = PageHomeVo.builder()
                        .bulletinId(Long.valueOf(id))
                        .bulletinName(highlightedTitle)
                        .reviewTime(reviewTime)
                        .registrationEndTime(signUpTime)
                        .bulletinTypeName(bulletinTypeName)
                        .build();

                vos.add(vo);
            } catch (Exception e) {
                log.error("文档转换错误: ", e);
            }
        }
        return vos;
    }
}
