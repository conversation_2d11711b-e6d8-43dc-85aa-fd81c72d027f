package com.epcos.bidding.purchase.ev.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.enums.PurchaseMethodEnum;
import com.epcos.bidding.common.utils.AttributeUtil;
import com.epcos.bidding.common.utils.BiddingBaseUtil;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.purchase.project.wzlg.business.api.buyitem.WZLGBuyItemApi;
import com.epcos.bidding.purchase.project.wzlg.business.api.subpackage.WZLGSubpackageApi;
import com.epcos.bidding.purchase.project.wzlg.domain.dao.buyitem.BuyItemWZLGDao;
import com.epcos.bidding.purchase.project.wzlg.domain.dao.subPackage.SubpackageWZLGDao;
import com.epcos.bidding.purchase.remote.RemoteToOtherServiceApi;
import com.epcos.bidding.workbench.vo.SpecialFieldVo;
import com.epcos.system.api.domain.SysDictData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.epcos.common.core.constant.UserConstants.PROJECT_CATEGORY;
import static com.epcos.common.core.enums.ClientEnum.LG;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/21 15:49
 */
@Slf4j
@Service("lg")
public class WzlgEvService extends AbEvService {

    private final WZLGBuyItemApi wzlgBuyItemApi;
    private final WZLGSubpackageApi wzlgSubpackageApi;
    private final RemoteToOtherServiceApi remoteToOtherServiceApi;

    public WzlgEvService(IBuyItemApi buyItemApi, ISubPackageApi subPackageApi, WZLGBuyItemApi wzlgBuyItemApi,
                         WZLGSubpackageApi wzlgSubpackageApi, RemoteToOtherServiceApi remoteToOtherServiceApi) {
        super(buyItemApi, subPackageApi);
        this.wzlgBuyItemApi = wzlgBuyItemApi;
        this.wzlgSubpackageApi = wzlgSubpackageApi;
        this.remoteToOtherServiceApi = remoteToOtherServiceApi;
    }

    @Override
    public String ev() {
        return LG.getCode();
    }

    @Override
    public boolean isDing() {
        return false;
    }

    @Override
    public String getTableName() {
        return "purchase_wzlg_buy_item";
    }

    @Override
    public Map<String, String> queryBuyItemMap(Map<String, String> voMap, BuyItemDao buyItemInfo) {

        List<SysDictData> data = remoteToOtherServiceApi.getDict(PROJECT_CATEGORY);
        Map<String, String> collect = data.stream()
                .collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
        BuyItemWZLGDao wzlgDao = wzlgBuyItemApi.findOneByBuyItemCode(buyItemInfo.getBuyItemCode());
        voMap = BiddingBaseUtil.convert(wzlgDao);
        voMap.put("purchaseMethodType", PurchaseMethodEnum.getDesc(buyItemInfo.getPurchaseMethodType()));
        voMap.put("buyClass", collect.get(wzlgDao.getBuyClass()));
        return voMap;
    }

    @Override
    public List<SpecialFieldVo> queryDiffInfo(List<String> buyItemCodes, Map<String, List<SubpackageDao>> subMap, List<SpecialFieldVo> voList) {
        List<BuyItemWZLGDao> wzlgDaoList = wzlgBuyItemApi.findOneByBuyItemCodeList(buyItemCodes);
        wzlgDaoList.forEach(dao -> {
            List<String> subpackageCodeList = subMap.get(dao.getBuyItemCode()).stream()
                    .map(SubpackageDao::getSubpackageCode).collect(Collectors.toList());
            List<SubpackageWZLGDao> subpackageWZLGDaoList = wzlgSubpackageApi.findBySubpackageCode(subpackageCodeList);
            List<LinkedHashMap<String, String>> body = AttributeUtil.asBody(dao.getBuyBody());
            BigDecimal totalPrice = body.stream()
                    .map(b -> b.get("pc_totalPrice"))       // 提取 pc_totalPrice 值
                    .filter(s -> s != null && !s.isEmpty())  // 过滤空值和空字符串
                    .map(BigDecimal::new)                    // 转换为 BigDecimal
                    .reduce(BigDecimal.ZERO, BigDecimal::add);// 累加所有值
            subpackageWZLGDaoList.forEach(sub -> voList.add(new SpecialFieldVo(
                    dao.getBuyItemCode(), sub.getSubpackageCode(),
                    sub.getBidType(), dao.getBuyClass(), totalPrice)));
        });
        return voList;
    }

    @Override
    public Boolean delBuyItem(String buyItemCode, List<String> subpackageCodeList) {
        wzlgSubpackageApi.remove(subpackageCodeList);
        return wzlgBuyItemApi.delWZLGBuyItemInfo(buyItemCode);
    }


    @Override
    public List<String> queryAllBuyItemCode() {
        List<BuyItemWZLGDao> bjxkItemList = wzlgBuyItemApi.list(Wrappers.lambdaQuery(BuyItemWZLGDao.class)
                .select(BuyItemWZLGDao::getBuyItemCode));
        if (CollectionUtils.isEmpty(bjxkItemList)) {
            return Collections.emptyList();
        }
        List<String> codeList = bjxkItemList.stream().map(BuyItemWZLGDao::getBuyItemCode).collect(Collectors.toList());
        return codeList;
    }
}
