package com.epcos.bidding.audit.business.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.audit.domain.dao.AuditRecipientDao;
import com.epcos.bidding.audit.business.api.IAuditRecipientApi;
import com.epcos.bidding.audit.repository.AuditRecipientMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuditRecipientService extends ServiceImpl<AuditRecipientMapper, AuditRecipientDao>
        implements IAuditRecipientApi {
}
