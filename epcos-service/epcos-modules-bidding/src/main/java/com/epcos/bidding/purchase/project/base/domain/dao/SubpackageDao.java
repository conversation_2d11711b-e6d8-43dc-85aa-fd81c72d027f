package com.epcos.bidding.purchase.project.base.domain.dao;

import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.bidding.common.SubpackageCodeEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 包 信息表
 *
 * <AUTHOR>
 * @version V1.0
 * @Package com.epcos.bidding.purchase.project.domain.dao
 * @date 2023/8/12 15:35
 * @Copyright © 2023-2026 易建采科技（武汉）有限公司
 */
@Data
@ApiModel(description = "包 基础信息表")
@NoArgsConstructor
@TableName("purchase_subpackage")
public class SubpackageDao extends SubpackageCodeEntity implements Serializable {
    private static final long serialVersionUID = -1184858430217163721L;


    public SubpackageDao(String buyItemCode, String subpackageCode) {
        this.buyItemCode = buyItemCode;
        this.subpackageCode = subpackageCode;
    }

    @ApiModelProperty(value = "采购项目编号(业务使用代码)")
    private String buyItemCode;

    @ApiModelProperty(value = "标段名称")
    private String subpackageName;

    @ApiModelProperty(value = "院内采购项目标段编号")
    private String innerBuyItemPackageCode;

    @ApiModelProperty(value = "被邀请的供应商id")
    private String invitedSupplierJson;

    @ApiModelProperty(value = "采购文件pdf key")
    private String claimsFilePdfKey;

    @ApiModelProperty(value = "评审报告pdf key")
    private String reportFileKey;

    @ApiModelProperty(value = "评审报告html")
    private String reviewReportHtml;

    @ApiModelProperty(value = "是否终止采购 [0-正常，1-终止] 默认为0")
    private String abandon;

    @ApiModelProperty(value = "标签")
    private String labelJson;

    @ApiModelProperty(value = "商品关联信息")
    private String shopJson;

    @Digits(integer = 18, fraction = 5, message = "控制价【18位整数，5位小数】")
    @ApiModelProperty(value = "控制价")
    private BigDecimal contractPrice;

    @Digits(integer = 18, fraction = 5, message = "报价总分【18位整数，5位小数】")
    @ApiModelProperty(value = "价格总分")
    private BigDecimal priceTotalSource;

    @ApiModelProperty(value = "标段内容")
    private String subpackageContent;

    @ApiModelProperty(value = "开标地址")
    private String bidOpenAddress;

    @ApiModelProperty(value = "报名截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    protected Date registrationEndTime;

    @ApiModelProperty(value = "开标时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date meetingTime;

    @ApiModelProperty(value = "响应截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date responseFileEndTime;

    @ApiModelProperty(value = "监标人")
    private String monitorBidPerson;

    @ApiModelProperty(value = "监标人id")
    private Long monitorBidPersonId;
}
