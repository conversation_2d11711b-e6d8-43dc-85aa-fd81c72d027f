package com.epcos.bidding.purchase.process.domain.vo;

import com.epcos.bidding.purchase.api.params.AttributeVo;
import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperBuyItemVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.Date;
import java.util.List;

/**
 * 上会安排vo
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/3 10:20
 */
@Data
public class BuyItemMeetVo extends SuperBuyItemVo {

    @ApiModelProperty(value = "标段编号")
    @Length(max = 64, message = "标段名称 超出长度限制")
    private String subpackageCode;

    @ApiModelProperty(value = "标段名称")
    @Length(max = 64, message = "标段名称 超出长度限制")
    private String subpackageName;

    @ApiModelProperty(value = "议价需求(议价备注)")
    @Length(max = 255, message = "议价需求(议价备注)")
    private String serviceRequire;

    @ApiModelProperty(value = "议价倒计时(议价截止期)")
    @Length(max = 255, message = "议价倒计时(议价截止期)")
    private Date countdown;

    @ApiModelProperty("报价表头")
    private List<AttributeVo> heads;

    @ApiModelProperty(value = "上会安排包信息")
    private List<SupplierInfoVo> supplierInfoVoList;
}
