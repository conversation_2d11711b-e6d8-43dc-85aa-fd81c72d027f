package com.epcos.bidding.purchase.project.bjxk.business.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import com.epcos.bidding.purchase.project.bjxk.domain.dto.BJXKBuyItemQueryDto;
import com.epcos.bidding.purchase.project.bjxk.domain.dto.BJXKCreateBuyItemDto;
import com.epcos.bidding.purchase.project.bjxk.domain.vo.BJXKBuyItemInfoVo;
import com.epcos.bidding.purchase.project.bjxk.domain.vo.BJXKBuyItemPageVo;

import java.util.List;

/**
 * 项目 和 标段 共有的业务 api
 *
 * <AUTHOR>
 * @version 1.0
 * @description 北京胸科医院版本
 * @date 2024/4/23 13:57
 */
public interface BJXKBuyJoinSubApi {

    void createBJXKBuyItem(BJXKCreateBuyItemDto dto);

    IPage<BJXKBuyItemPageVo> bjxkBuyItemPage(PageSortEntity<BJXKBuyItemQueryDto> dto);

    BJXKBuyItemInfoVo queryBJXKBuyItemInfo(String buyItemCode);

    void updateBJXKBuyItemInfo(BJXKCreateBuyItemDto dto);

    List<SuperPackageVo> findByBuyItemCode(String buyItemCode);

    Boolean delBJXKBuyItemInfo(String buyItemCode);
}
