package com.epcos.bidding.audit.business.api;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.audit.domain.dao.AuditCommentDao;
import com.epcos.bidding.common.IBaseService;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;


public interface IAuditCommentApi extends IBaseService<AuditCommentDao> {

    @Override
    default LambdaQueryWrapper<AuditCommentDao> queryWrapper(AuditCommentDao dao) {
        LambdaQueryWrapper<AuditCommentDao> query = Wrappers.lambdaQuery(AuditCommentDao.class);
        if (Objects.nonNull(dao.getId())) {
            return query.eq(AuditCommentDao::getId, dao.getId());
        }
        return query
                .eq(Objects.nonNull(dao.getAuditRecordId()), AuditCommentDao::getAuditRecordId, dao.getAuditRecordId())
                .eq(Objects.nonNull(dao.getUserId()), AuditCommentDao::getUserId, dao.getUserId())
                .eq(StringUtils.hasText(dao.getUserName()), AuditCommentDao::getUserName, dao.getUserName())
                .like(StringUtils.hasText(dao.getComment()), AuditCommentDao::getComment, dao.getComment())
                .orderByDesc(AuditCommentDao::getId);
    }

    default Wrapper<AuditCommentDao> queryWrapperIn(List<Long> recordIds) {
        return Wrappers.lambdaQuery(AuditCommentDao.class)
                .in(AuditCommentDao::getAuditRecordId, recordIds);
    }
}
