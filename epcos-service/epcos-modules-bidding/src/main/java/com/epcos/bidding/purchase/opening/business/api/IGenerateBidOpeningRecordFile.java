package com.epcos.bidding.purchase.opening.business.api;

import com.epcos.bidding.common.aspects.vo.GetItemVo;
import com.epcos.bidding.supplier.api.params.MultiSupplierQuoteFormVo;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierBidderDao;

import java.nio.file.Path;
import java.util.Date;
import java.util.Map;

/**
 * 生成开标记录文件，分单项与多项
 */
public interface IGenerateBidOpeningRecordFile {


    /**
     * 生成文件
     */
    Path generate(String tenderer,  GetItemVo itemVo, Date meetingTime,
                  MultiSupplierQuoteFormVo quoteFormVo,
                  Map<Long, SupplierBidderDao> supplierBidderDaoMap);


}
