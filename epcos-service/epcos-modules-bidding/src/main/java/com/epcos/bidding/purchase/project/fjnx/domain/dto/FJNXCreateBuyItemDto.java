package com.epcos.bidding.purchase.project.fjnx.domain.dto;

import com.alibaba.fastjson.JSONArray;
import com.epcos.bidding.purchase.api.params.dto.SubPackageBaseDto;
import com.epcos.bidding.purchase.project.base.domain.dto.BuyItemBaseDto;
import com.epcos.common.core.annotation.valid.Add;
import com.epcos.common.core.annotation.valid.Up;
import com.epcos.common.core.domain.AttachmentDto;
import com.epcos.dingtalk.domain.dto.AttachFileDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 福建省农信版本
 * @date 2024/4/23 14:01
 */
@Data
@ApiModel(description = "福建省农信版本")
public class FJNXCreateBuyItemDto extends BuyItemBaseDto {

    @ApiModelProperty(value = "项目资料分配之后的id")
    private Long allocateId;

    @ApiModelProperty(value = "申报科室")
    @Length(max = 64, message = "申报科室 超出长度限制")
    private String applyDept;

    @ApiModelProperty(value = "采购编号(院方使用)")
    @Length(max = 60, message = "采购编号 超出长度限制")
    private String innerCode;

    @ApiModelProperty(value = "申请人")
    @Length(max = 64, message = "申请人 超出长度限制")
    private String applyPerson;

    @ApiModelProperty(value = "申请时间")
    private String applyTime;

    @ApiModelProperty(value = "需购数量")
    private Integer buyCnt;

    @ApiModelProperty(value = "单价限价")
    @DecimalMin(value = "0.0", message = "单价限价 必须大于等于0")
    @DecimalMax(value = "99999999.99999", message = "预算金额 不得超过99999999.99999")
    private BigDecimal buyFixePrice;

    @ApiModelProperty(value = "总价限价")
    @DecimalMin(value = "0.0", message = "总价限价 必须大于等于0")
    @DecimalMax(value = "99999999.99999", message = "预算金额 不得超过99999999.99999")
    private BigDecimal buyTotalPrice;

    @ApiModelProperty(value = "项目类型")
    @Length(max = 64, message = "项目类型 超出长度限制")
    private String buyClass;

    @ApiModelProperty(value = "是否可采购进口产品[0-否，1-是]")
    @Length(max = 1, message = "是否可采购进口产品 超出长度限制")
    private Integer whetherImport;

    @ApiModelProperty(value = "是否有专机专用或耗材或试剂[0-否, 1-是]")
    @Length(max = 1, message = "是否有专机专用或耗材或试剂 超出长度限制")
    private Integer whetherConsume;

    @ApiModelProperty(value = "项目附件")
    private List<AttachFileDto> paramsAttList;

    @ApiModelProperty(value = "标段信息")
    @NotEmpty(groups = {Add.class, Up.class}, message = "标段信息 不能为空")
    private List<SubPackageBaseDto> subpackageDtoList;


    public String toJsonAnnexFile() {
        if (!CollectionUtils.isEmpty(this.paramsAttList)) {
            List<AttachmentDto> dtoList = paramsAttList.stream()
                    .map(f -> new AttachmentDto(f.getName(), f.getUrl()))
                    .collect(Collectors.toList());
            return JSONArray.toJSONString(dtoList);
        }
        return null;
    }
}
