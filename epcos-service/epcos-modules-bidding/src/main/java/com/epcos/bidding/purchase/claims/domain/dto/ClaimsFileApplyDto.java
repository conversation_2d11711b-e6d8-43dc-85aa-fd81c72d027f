package com.epcos.bidding.purchase.claims.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(description = "将采购文件应用到其他包")
public class ClaimsFileApplyDto implements Serializable {
    private static final long serialVersionUID = -7394597697493703982L;

    @ApiModelProperty("应用的包code")
    @NotEmpty(message = "应用的包code,必填")
    @NotNull(message = "应用的包code,必填")
    private List<String> applySubpackageCodeList;

    @ApiModelProperty("包code")
    @NotBlank(message = "包code,必填")
    private String subpackageCode;
}
