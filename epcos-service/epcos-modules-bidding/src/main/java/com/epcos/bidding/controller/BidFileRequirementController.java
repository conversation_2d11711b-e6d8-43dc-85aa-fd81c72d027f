package com.epcos.bidding.controller;

import com.epcos.bidding.common.annotaion.GetCommon;
import com.epcos.bidding.common.annotaion.GetTime;
import com.epcos.bidding.common.aspects.filter.validator.ResponseFileEndTimeNonNullHasPassedValidator;
import com.epcos.bidding.purchase.api.params.vo.claims.BidFileRequirementVo;
import com.epcos.bidding.purchase.claims.domain.dto.BidFileRejectionDto;
import com.epcos.bidding.purchase.claims.domain.vo.RequirementVo;
import com.epcos.bidding.supplier.answer.business.api.IBidFileRequirementApi;
import com.epcos.bidding.supplier.api.params.dto.answer.BidFileRequirementDto;
import com.epcos.bidding.supplier.api.params.dto.answer.BidFileRequirementUpdateDto;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.log.annotation.Log;
import com.epcos.common.log.enums.BusinessType;
import com.epcos.common.redis.annotation.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Api(tags = "投标人对采购文件具体响应")
@RestController
@RequiredArgsConstructor
@RequestMapping("/bid.file/requirement")
public class BidFileRequirementController {

    private final IBidFileRequirementApi bidFileRequirementApi;

    @ApiOperation("查询投标人响应记录")
    @PostMapping("/get")
    public R<List<BidFileRequirementVo>> get(@RequestBody List<String> subpackageCodeList) {
        return R.ok(bidFileRequirementApi.query(subpackageCodeList));
    }

    @GetMapping("/getOne")
    public R<List<RequirementVo>> getOne(@RequestParam("subpackageCode") String subpackageCode,
                                         @RequestParam("supplierId") Long supplierId) {
        return R.ok(bidFileRequirementApi.query(subpackageCode, supplierId));
    }

    @GetMapping("/byRequirementId")
    public R<List<RequirementVo>> bidFileRequirement(@RequestParam("requirementId") Long requirementId) {
        return R.ok(bidFileRequirementApi.query(requirementId));
    }

    @RedisLock(second = 30)
    @ApiOperation("批量驳回")
    @Log(title = "批量驳回[接口：rejection]", businessType = BusinessType.UPDATE)
    @PostMapping("/rejection")
    public R<Boolean> rejection(@RequestBody BidFileRejectionDto dto) {
        bidFileRequirementApi.rejection(dto);
        return R.ok();
    }

    @ApiOperation("查询采购要求")
    @GetMapping("/query")
    public R<List<RequirementVo>> query(@RequestParam("subpackageCode") String subpackageCode) {
        return R.ok(bidFileRequirementApi.query(subpackageCode, SecurityUtils.getUserId()));
    }

    /**
     * 0-不允许，1-允许
     *
     * @param subpackageCode
     * @return
     */
    @ApiOperation("是否允许响应")
    @GetMapping("/permit")
    public R<Integer> permit(@RequestParam("subpackageCode") String subpackageCode) {
        return R.ok(bidFileRequirementApi.permit(subpackageCode, SecurityUtils.getUserId()));
    }

    @RedisLock(second = 30)
    @ApiOperation("响应采购要求")
    @Log(title = "响应采购要求[接口：saves]", businessType = BusinessType.INSERT)
    @PostMapping("/saves")
    @GetTime(common = @GetCommon(subpackageCodeEL = "#dto.subpackageCode", afters = ResponseFileEndTimeNonNullHasPassedValidator.class))
    public R<Boolean> saves(@RequestBody @Valid BidFileRequirementDto dto) {
        bidFileRequirementApi.saves(dto);
        return R.ok();
    }

    @RedisLock(second = 30)
    @ApiOperation("修改响应采购要求")
    @Log(title = "修改响应采购要求[接口：updates]", businessType = BusinessType.UPDATE)
    @PostMapping("/updates")
    public R<Boolean> updates(@RequestBody @Valid List<BidFileRequirementUpdateDto> dtos) {
        bidFileRequirementApi.updates(dtos);
        return R.ok();
    }


}
