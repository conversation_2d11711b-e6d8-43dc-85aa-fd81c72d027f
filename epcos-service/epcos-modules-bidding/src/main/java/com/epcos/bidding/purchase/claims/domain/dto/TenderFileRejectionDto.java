package com.epcos.bidding.purchase.claims.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@ApiModel("驳回记录")
public class TenderFileRejectionDto implements Serializable {
    private static final long serialVersionUID = 5587449894528575370L;

    @ApiModelProperty("标段code")
    @NotBlank(message = "标段code不能为空")
    private String subpackageCode;

    @ApiModelProperty("供应商id")
    @NotNull(message = "供应商id不能为空")
    private Long supplierId;

    @ApiModelProperty("采购文件要求id")
    @NotNull(message = "采购文件要求id不能为空")
    private Long requirementId;

    @ApiModelProperty("理由")
    @NotBlank(message = "理由不能为空")
    @Length(max = 500, min = 1, message = "理由长度不能小于1,最长500")
    private String reason;

    @ApiModelProperty("响应文件key")
    @NotBlank(message = "响应文件key不能为空")
    @Length(max = 100, min = 1, message = "响应文件key长度不能小于1,最长100")
    private String bidFileKey;


}
