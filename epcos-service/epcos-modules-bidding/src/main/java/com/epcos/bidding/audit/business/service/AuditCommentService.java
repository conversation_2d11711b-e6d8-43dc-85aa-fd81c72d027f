package com.epcos.bidding.audit.business.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.audit.business.api.IAuditCommentApi;
import com.epcos.bidding.audit.domain.dao.AuditCommentDao;
import com.epcos.bidding.audit.repository.AuditCommentMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@Slf4j
@Service
@RequiredArgsConstructor
public class AuditCommentService extends ServiceImpl<AuditCommentMapper, AuditCommentDao>
        implements IAuditCommentApi {

}
