package com.epcos.bidding.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.epcos.bidding.audit.api.BizAuditRelationDto;
import com.epcos.bidding.audit.api.dto.AuditCreatorDto;
import com.epcos.bidding.audit.api.vo.AuditVo;
import com.epcos.bidding.biz.business.api.IBizAuditRelationApi;
import com.epcos.bidding.common.AuditTypeEnum;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.common.annotaion.*;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.common.aspects.convert.BatchSaveJudgeConvert;
import com.epcos.bidding.common.aspects.convert.SaveJudgeConvert;
import com.epcos.bidding.common.aspects.convert.audit.JudgeConvert;
import com.epcos.bidding.purchase.api.domian.judge.judgeProjectVo;
import com.epcos.bidding.purchase.api.params.dto.BaseQueryDto;
import com.epcos.bidding.purchase.api.params.vo.buyitem.ItemSubpackageVo;
import com.epcos.bidding.purchase.extract.business.api.IExtractJudgeExternalApi;
import com.epcos.bidding.purchase.extract.business.api.IExtractJudgeInnerApi;
import com.epcos.bidding.purchase.extract.domain.dao.ExtractJudgeInnerDao;
import com.epcos.bidding.purchase.extract.domain.dto.*;
import com.epcos.bidding.purchase.extract.domain.vo.ExtractBuyItemVo;
import com.epcos.bidding.purchase.extract.domain.vo.ExtractLogVo;
import com.epcos.bidding.purchase.remote.RemoteSupplierApi;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.enums.ClientEnum;
import com.epcos.common.core.enums.SmsEnum;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.web.domain.user.SysUser;
import com.epcos.common.core.web.page.TableDataVo;
import com.epcos.common.log.annotation.Log;
import com.epcos.common.log.enums.BusinessType;
import com.epcos.common.security.annotation.RequiresPermissions;
import com.epcos.system.api.domain.dto.AgainExtractDto;
import com.epcos.system.api.domain.dto.ExtractJudgeInfoDto;
import com.epcos.system.api.domain.dto.JudgeConditionsDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.epcos.common.core.constant.PurchaseConstants.UserType.PURCHASER;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/11 16:46
 */
@Api(tags = "评委抽取", description = "评委抽取")
@RestController
@RequestMapping("/extract/judge")
@RequiredArgsConstructor
public class ExtractJudgeController {

    private final IExtractJudgeInnerApi extractJudgeInnerApi;
    private final IExtractJudgeExternalApi extractJudgeExternalApi;
    private final IBizAuditRelationApi bizAuditRelationApi;
    private final RemoteSupplierApi remoteSupplierApi;


    @ApiOperation(value = "模糊匹配项目，并返回固定值")
    @PostMapping(value = "/queryBuyItemName")
    public R<List<String>> queryBuyItemName(@RequestBody @Valid ExtractBuyItemDto dto) {
        return R.ok(extractJudgeExternalApi.queryProjectName(dto.getBuyItemName()));
    }

    @ApiOperation(value = "抽取评委")
    @Log(title = "抽取评委", businessType = BusinessType.INSERT)
    @PostMapping(value = "/extract")
    public R<List<ExtractLogVo>> extract(@RequestBody @Validated JudgeConditionsDto dto) {
        return R.ok(extractJudgeExternalApi.extract(dto));
    }

    @ApiOperation(value = "再次抽取（补抽）")
    @Log(title = "再次抽取（补抽）", businessType = BusinessType.INSERT)
    @PostMapping(value = "/againExtract")
    public R<List<ExtractLogVo>> againExtract(@RequestBody @Validated AgainExtractDto dto) {
        if (StringUtils.isBlank(dto.getSpecialtyCategory()) && StringUtils.isBlank(dto.getJudgeName())
                && StringUtils.isBlank(dto.getDept())) {
            return R.ok(Collections.EMPTY_LIST);
        }
        return R.ok(extractJudgeExternalApi.againExtract(dto));
    }

    @ApiOperation(value = "查询抽取评委记录详情列表")
    @PostMapping(value = "/queryJudgeInfo")
    public R<List<ExtractLogVo>> queryJudgeInfo(@RequestBody ExtractJudgeInfoDto dto) {
        return R.ok(extractJudgeExternalApi.queryJudgeInfo(dto));
    }

    @ApiOperation(value = "保存确认的专家")
    @Log(title = "保存确认的专家", businessType = BusinessType.INSERT)
    @PostMapping(value = "/saveJudge")
    public R<Boolean> saveJudge(@RequestBody @Validated ExtractNameDto dto) {
        return R.ok(extractJudgeExternalApi.saveJudge(dto));
    }

    @ApiOperation(value = "查询抽取专家记录列表")
    @PostMapping(value = "/extractJudgeLog")
    public TableDataVo extractJudgeLog(@RequestBody PageSortEntity<ExtractQueryDto> dto) {
        return extractJudgeExternalApi.extractJudgeLog(dto);
    }

    @ApiOperation(value = "标记抽取专家")
    @PostMapping(value = "/markJudge")
    public R<Boolean> markJudge(@RequestBody MarkJudgeLogDto dto) {
        return R.ok(extractJudgeExternalApi.markJudge(dto));
    }

    @ApiOperation(value = "删除抽取专家记录")
    @PostMapping(value = "/delJudgeLog")
    public R<Boolean> delJudgeLog(@RequestBody MarkJudgeLogDto dto) {
        return R.ok(extractJudgeExternalApi.delJudgeLog(dto));
    }

    //==================================以下是项目内评委接口===============================================================


    @ApiOperation("统计专家参与项目次数")
    @GetMapping("/getProjectNum")
    public R<Map<Long, judgeProjectVo>> getProjectNum(@RequestParam("judgeIdList") List<Long> judgeIdList) {
        return extractJudgeInnerApi.getProjectNum(judgeIdList);
    }


    /**
     * 搜索专家 system/user/getExpertInfoByName    ====>>>> getJudgeInfoByName
     * expert.record/queryLogExpert  =====>>>>>
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "项目内导入抽取专家--项目内")
    @PostMapping(value = "/queryLogJudge")
    public R<List<SysUser>> queryLogJudge(@RequestBody ImportJudgeDto dto) {
        return R.ok(extractJudgeInnerApi.queryLogJudge(dto));
    }

    /**
     * expert/getExtractExpertInfo  ====>>>>>
     *
     * @param buyItemCode
     * @return
     */
    @RequiresPermissions("project:process:query")
    @ApiOperation(value = "根据项目code查询评委--项目内")
    @ApiImplicitParam(name = "buyItemCode", value = "项目code", paramType = "query", dataTypeClass = String.class, required = true)
    @GetItem(common = @GetCommon(buyItemCodeEL = "#buyItemCode"))
    @GetMapping(value = "/queryJudgeList")
    public R<List<ItemSubpackageVo>> queryJudgeList(@NotBlank(message = "参数必填") String buyItemCode) {
        return R.ok(extractJudgeInnerApi.queryJudgeList(buyItemCode));
    }

    @ApiOperation(value = "批量保存抽取的评委信息--项目内")
    @Log(title = "批量保存抽取的评委信息", businessType = BusinessType.INSERT)
    @Sms(clients = {ClientEnum.XY, ClientEnum.ZL}, smsEnum = SmsEnum.JUDGE_EXTRACT, convert = BatchSaveJudgeConvert.class)
    @PostMapping(value = "/batchSaveExtractJudgeInfo")
    public R<Boolean> batchSaveExtractJudgeInfo(@RequestBody JudgeBatchDto dto) {
//        for (JudgeBatchSubDto subDto : dto.getSubDtoList()) {
//            List<JudgesVo> judgesVoList = extractJudgeInnerApi.queryBySub(subDto.getSubpackageCode());
//            SupplierProcessNodeDao process = remoteSupplierApi.findProcess(subDto.getSubpackageCode(), Integer.parseInt(PURCHASER));
//            if (CollectionUtils.isEmpty(judgesVoList)) {
//                if (Objects.nonNull(process)) {
//                    List<String> nodes = process.getNodes()
//                            .stream().filter(p -> Objects.nonNull(p.getRanking()))
//                            .map(FunctionKV::getPurchaseFunctionKey)
//                            .collect(Collectors.toList());
//                    if (!CollectionUtils.isEmpty(nodes) && nodes.contains(PURCHASER_REVIEW_EXPERT.getKey())
//                            && !PURCHASER_REVIEW_EXPERT.getKey().equalsIgnoreCase(process.getPurchaseFunctionKey())) {
//                        throw new ServiceException("不是保存评委环节，不可保存评委信息");
//                    }
//                }
//            }
//        }
        extractJudgeInnerApi.batchSaveExtractJudgeInfo(dto);
        return R.ok();
    }

    @ApiOperation(value = "保存抽取的评委信息--项目内")
    @Log(title = "保存抽取的评委信息", businessType = BusinessType.INSERT)
    @Jump(subpackageCodeEL = "#dto.subpackageCode", processRole = PURCHASER)
    @Sms(clients = {ClientEnum.XY, ClientEnum.ZL}, smsEnum = SmsEnum.JUDGE_EXTRACT, convert = SaveJudgeConvert.class)
    @PostMapping(value = "/saveExtractJudgeInfo")
    public R<Boolean> saveExtractJudgeInfo(@RequestBody JudgeExtractDto dto) {
        extractJudgeInnerApi.saveExtractJudgeInfo(dto);
        return R.ok();
    }

    @ApiOperation(value = "发起审批保存评委--项目内")
    @RequiresPermissions(value = "process:expert:audit")
    @Log(title = "发起审批保存评委--项目内", businessType = BusinessType.INSERT)
    @GetAuditAttribute(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode")
            , ignoreField = {"voList"}, toAuditAttributeConvert = JudgeConvert.class)
    @PostMapping(value = "/auditJudgeInfo")
    public R<Boolean> auditJudgeInfo(@RequestBody JudgeExtractDto dto) {

        List<ExtractJudgeInnerDao> bySubCodes = extractJudgeInnerApi.findBySubCode(dto.getSubpackageCode());
        Long id = bySubCodes.get(0).getId();

        AuditVo auditVo = bizAuditRelationApi.queryAuditVo(BizAuditRelationDto.builder()
                .buyItemCode(dto.getBuyItemCode())
                .subpackageCode(dto.getSubpackageCode())
                .businessId(id)
                .build()
        );
        if (Objects.nonNull(auditVo)) {
            if (auditVo.getAuditInfoVo().getStatus() == 2) {
                return R.fail("请先审核已有的评委信息");
            }
            if (auditVo.getAuditInfoVo().getStatus() == 1) {
                return R.fail("审批已通过，不可重复发起审批");
            }
        }


        BizAuditRelationDto bizAuditRelationDto = BizAuditRelationDto.builder()
                .auditType(AuditTypeEnum.EXTRACT_JUDGE.getType())
                .buyItemCode(dto.getBuyItemCode())
                .subpackageCode(dto.getSubpackageCode())
                .businessId(id)
                .userId(SecurityUtils.getUserId())
                .className(dto.getClass().getName())
                .data(JSON.toJSONString(dto))
                .build();
        AuditCreatorDto createDto = dto.convert(AuditTypeEnum.EXTRACT_JUDGE, dto.getBuyItemCode(), dto.getSubpackageCode(), GetUtil.getAuditAttribute());
        bizAuditRelationApi.initiateAndRecord(bizAuditRelationDto, createDto);
        return R.ok();
    }


    @ApiOperation(value = "查询可保存评委的项目")
    @PostMapping(value = "/queryExtractBuyItemInfo")
    public TableDataVo<ExtractBuyItemVo> queryExtractBuyItemInfo(@RequestBody PageSortEntity<BaseQueryDto> dto) {
        IPage<ExtractBuyItemVo> page = extractJudgeInnerApi.queryExtractBuyItemInfo(dto);
        List<ExtractBuyItemVo> voList = page.getRecords();
        return new TableDataVo(voList, page.getTotal());
    }
}
