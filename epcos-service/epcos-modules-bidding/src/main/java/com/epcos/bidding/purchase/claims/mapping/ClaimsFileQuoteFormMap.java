package com.epcos.bidding.purchase.claims.mapping;

import com.epcos.bidding.common.utils.AttributeUtil;
import com.epcos.bidding.purchase.api.params.AttributeVo;
import com.epcos.bidding.purchase.api.params.PurchaseQuoteFormVo;
import com.epcos.bidding.purchase.api.params.dto.QuoteFormCreatorDto;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileQuoteFormDao;
import com.epcos.bidding.purchase.claims.domain.dto.ClaimsFileQuoteFormQueryDto;
import com.epcos.common.core.utils.StringUtils;
import org.mapstruct.Mapper;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;


@Mapper(componentModel = "spring")
public interface ClaimsFileQuoteFormMap {


    ClaimsFileQuoteFormDao queryAsEntity(ClaimsFileQuoteFormQueryDto query);

    default List<ClaimsFileQuoteFormDao> asEntity(String subpackageCode, List<AttributeVo> heads,
                                                  List<LinkedHashMap<String, String>> bodyMaps) {
        if (CollectionUtils.isEmpty(heads)) {
            return Collections.emptyList();
        }
        String headJson = AttributeUtil.asJson(heads);
        if (CollectionUtils.isEmpty(bodyMaps)) {
            return Collections.singletonList(new ClaimsFileQuoteFormDao(subpackageCode, headJson));
        }
        List<ClaimsFileQuoteFormDao> vos = new ArrayList<>(bodyMaps.size());
        for (Map<String, String> row : bodyMaps) {
            vos.add(new ClaimsFileQuoteFormDao(subpackageCode, headJson, AttributeUtil.asJson(row)));
        }
        return vos;
    }

    default List<ClaimsFileQuoteFormDao> creatorAsEntity(QuoteFormCreatorDto creator) {
        if (Objects.isNull(creator)) {
            return Collections.emptyList();
        }
        return asEntity(creator.getSubpackageCode(), creator.getHeads(), creator.getBodyMaps());
    }

    default PurchaseQuoteFormVo asVo(ClaimsFileQuoteFormDao entity, List<String> bodyJsonList) {
        PurchaseQuoteFormVo vo = new PurchaseQuoteFormVo();
        vo.setSubpackageCode(entity.getSubpackageCode());
        vo.setHeads(AttributeUtil.asHead(entity.getHeadJson()));
        vo.setBodyMaps(bodyJsonList.stream().map(AttributeUtil::asMap).collect(Collectors.toList()));
        return vo;
    }

    default PurchaseQuoteFormVo asVo(ClaimsFileQuoteFormDao entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        return asVo(entity, Optional.ofNullable(entity.getBodyJson()).map(Collections::singletonList).orElseGet(Collections::emptyList));
    }


    default PurchaseQuoteFormVo asVos(List<ClaimsFileQuoteFormDao> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return null;
        }
        return asVo(entityList.get(0),
                entityList.stream()
                        .map(ClaimsFileQuoteFormDao::getBodyJson)
                        .filter(StringUtils::hasText)
                        .collect(Collectors.toList()));
    }


}
