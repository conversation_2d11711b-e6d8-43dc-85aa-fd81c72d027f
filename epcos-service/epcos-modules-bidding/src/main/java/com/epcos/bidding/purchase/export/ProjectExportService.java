package com.epcos.bidding.purchase.export;

import com.epcos.bidding.purchase.api.params.dto.BaseQueryDto;
import com.epcos.bidding.purchase.api.params.vo.bulletin.BulletinAndItemTimeVo;
import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import com.epcos.bidding.purchase.bargain.business.api.IPurchaseBargainApi;
import com.epcos.bidding.purchase.bulletin.business.api.IBulletinApi;
import com.epcos.bidding.purchase.ev.factory.EvFactory;
import com.epcos.bidding.purchase.extract.business.api.IExtractJudgeInnerApi;
import com.epcos.bidding.purchase.extract.domain.vo.ExtractLogVo;
import com.epcos.bidding.purchase.process.business.api.IProcessApi;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.purchase.project.base.domain.dto.ProjectExportDto;
import com.epcos.bidding.purchase.project.base.domain.vo.*;
import com.epcos.bidding.purchase.win.business.api.IWinBidResultApi;
import com.epcos.bidding.purchase.win.domain.vo.WinBidVo;
import com.epcos.bidding.supplier.answer.business.api.IAnswerFileQuoteFormApi;
import com.epcos.bidding.supplier.sign.business.api.ISupplierBidderApi;
import com.epcos.bidding.supplier.sign.business.api.ISupplierSignUpApi;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.web.domain.user.LoginUser;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class ProjectExportService {

    private final IProcessApi processApi;
    private final IBulletinApi bulletinApi;
    private final ISubPackageApi subPackageApi;
    private final ISupplierSignUpApi supplierSignUpApi;
    private final IWinBidResultApi winBidResultApi;
    private final ISupplierBidderApi supplierBidderApi;
    private final IAnswerFileQuoteFormApi answerFileQuoteFormApi;
    private final IPurchaseBargainApi purchaseBargainApi;
    private final IExtractJudgeInnerApi extractJudgeInnerApi;


    // 查询项目数据
    public List<ProjectExportVo> getProjectExportVoList(ProjectExportDto dto) {
        BaseQueryDto baseQueryDto = new BaseQueryDto();
        baseQueryDto.setBuyItemName(dto.getBuyItemName());
        baseQueryDto.setPurchaseMethodType(dto.getPurchaseMethodType());
        baseQueryDto.setEnd(dto.getEnd());
        baseQueryDto.setCreateAtRange(dto.getCreateAt());
        LoginUser user = SecurityUtils.getLoginUser();
        Long deptId = user.getSysUser().getDeptId();
        // 招标办主任查询所有，非招标办主任查询自己部门
        if (Objects.nonNull(deptId) && !user.getPermissions().contains("project:report:exportAll")) {
            baseQueryDto.setDeptId(deptId);
        }
        // 查询后台字典中
        List<String> quoteDictValues = answerFileQuoteFormApi.getQuoteDictValue();
        return processApi.queryBuyItemDaoList(baseQueryDto)
                .parallelStream()
                .map(i -> getProjectExportVo(i, quoteDictValues))
                .sequential().collect(Collectors.toList());
    }


    public ProjectExportVo getProjectExportVo(BuyItemDao item, List<String> quoteDictValues) {
        ProjectExportVo vo = new ProjectExportVo();
        // 项目信息
        vo.setProjectVo(getProjectVo(item));
        // 专家信息
        Map<String, String> judgeNames = extractJudgeInnerApi.queryJudgeList(item.getBuyItemCode())
                .stream().collect(Collectors.toMap(
                        SuperPackageVo::getSubpackageCode,
                        w -> ((List<ExtractLogVo>) w.getData()).stream()
                                .map(e -> String.join(",", e.getJudgeName(), e.getIdNumber()))
                                .collect(Collectors.joining(";"))));
        // 标段包信息
        vo.setSubpackageVoList(getSubpackageVos(item, quoteDictValues, judgeNames));
        return vo;
    }

    // 查询项目内标段包信息
    private List<SubpackageVo> getSubpackageVos(BuyItemDao i, List<String> quoteDictValues, Map<String, String> judgeNames) {
        return subPackageApi.findByBuyItemCode(i.getBuyItemCode())
                .parallelStream().map(s -> {
                    SubpackageVo svo = new SubpackageVo();
                    setSubpackageTime(s, svo);
                    svo.setJudgeNameAndIdNumber(judgeNames.getOrDefault(s.getSubpackageCode(), ""));
                    // 中标人信息
                    List<WinBidVo> winBidVos = winBidResultApi.findBy(s.getSubpackageCode())
                            .stream()
                            .filter(w -> Objects.equals("1", w.getWinBid()))
                            .collect(Collectors.toList());
                    // 采购人报价最大轮数
                    Integer maxRound = Optional.ofNullable(purchaseBargainApi.findMaxRound(s.getSubpackageCode())).orElse(0);
                    // 报名信息
                    List<SignUpBidderVo> vos = supplierSignUpApi.query(null, s.getSubpackageCode())
                            .parallelStream().map(su -> {
                                SignUpBidderVo suVo = new SignUpBidderVo();
                                setWinningBidder(su.getSupplierId(), winBidVos, suVo);
                                setSignUpBidderVoList(s.getSubpackageCode(), su.getSupplierId(), suVo);
                                suVo.setBidQuoteVoList(getBidQuoteVoList(s.getSubpackageCode(), su.getSupplierId(), maxRound, quoteDictValues));
                                return suVo;
                            }).sequential().collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(vos)) {
                        SignUpBidderVo bidderVo = new SignUpBidderVo();
                        bidderVo.setBidQuoteVoList(Collections.singletonList(new BidQuoteVo()));
                        List<SignUpBidderVo> signUpBidderVos = Collections.singletonList(bidderVo);
                        svo.setSignUpBidderVoList(signUpBidderVos);
                    } else {
                        svo.setSignUpBidderVoList(vos);
                    }
                    return svo;
                }).sequential().collect(Collectors.toList());
    }

    // 查询项目信息
    private ProjectVo getProjectVo(BuyItemDao i) {
        ProjectVo p = new ProjectVo();
        p.setBuyItemName(i.getBuyItemName());
        p.setBuyItemCode(i.getBuyItemCode());
        Map<String, String> map = EvFactory.getInstance().queryBuyItemMap(i.getBuyItemCode());
        p.setBuyBudget(map.getOrDefault("buyBudget", ""));
        p.setUseDept(map.getOrDefault("useDept", ""));
        p.setManagementDept(map.getOrDefault("managementDept", ""));
        p.setBuyPerson(map.getOrDefault("buyPerson", ""));
        p.setCreateBy(i.getCreateBy());
        return p;
    }

    // 设置标段包时间
    private void setSubpackageTime(SubpackageDao s, SubpackageVo svo) {
        svo.setSubpackageCode(s.getSubpackageCode());
        svo.setSubpackageName(s.getSubpackageName());
        // 时间
        BulletinAndItemTimeVo timeVo = bulletinApi.bulletinItemTimeInfo(s.getSubpackageCode());
        svo.setTenderPushTime(timeVo.getTenderPushTime());
        svo.setRegistrationEndTime(timeVo.getRegistrationEndTime());
        svo.setResponseFileEndTime(timeVo.getResponseFileEndTime());
        svo.setMeetingTime(timeVo.getMeetingTime());
        svo.setWinPushTime(timeVo.getWinPushTime());
    }

    // 设置中标人信息
    private void setWinningBidder(Long supplierId, List<WinBidVo> winBidVos, SignUpBidderVo suVo) {
        Optional<WinBidVo> winBidVoOp = winBidVos.stream()
                .filter(f -> Objects.equals(f.getSupplierId(), supplierId))
                .findAny();
        if (!CollectionUtils.isEmpty(winBidVos)) {
            if (winBidVoOp.isPresent()) {
                suVo.setSendTime(winBidVoOp.get().getSendTime());
                suVo.setIsWinningBidder(true);
            } else {
                suVo.setIsWinningBidder(false);
            }
        }
    }


    // 设置报名投标人信息
    private void setSignUpBidderVoList(String subpackageCode, Long supplierId, SignUpBidderVo suVo) {
        Optional.ofNullable(supplierBidderApi.findBy(subpackageCode, supplierId))
                .ifPresent(bi -> {
                    suVo.setSupplierId(bi.getSupplierId());
                    suVo.setBidderName(bi.getBidderName());
                    suVo.setLicNumber(bi.getLicNumber());
                    suVo.setCertificateName(bi.getCertificateName());
                    suVo.setCertificateCode(bi.getCertificateCode());
                    suVo.setInfoReporterName(bi.getInfoReporterName());
                    suVo.setInfoReporterContactNumber(bi.getInfoReporterContactNumber());
                });
    }

    // 查询供应商报价
    private List<BidQuoteVo> getBidQuoteVoList(String subpackageCode, Long supplierId, Integer maxRound, List<String> quoteDictValues) {
        List<LinkedHashMap<String, String>> maps = answerFileQuoteFormApi.queryByRounds(subpackageCode, supplierId, maxRound);
        if (CollectionUtils.isEmpty(maps)) {
            return Collections.singletonList(new BidQuoteVo());
        } else {
            return maps.stream().map(q -> {
                BidQuoteVo qv = new BidQuoteVo();
                qv.setName(q.getOrDefault(quoteDictValues.get(0), ""));
                qv.setQuantity(q.getOrDefault(quoteDictValues.get(1), ""));
                qv.setUnitPrice(q.getOrDefault(quoteDictValues.get(2), ""));
                qv.setQuotationTotalPrice(q.getOrDefault(quoteDictValues.get(3), ""));
                return qv;
            }).collect(Collectors.toList());
        }
    }


}
