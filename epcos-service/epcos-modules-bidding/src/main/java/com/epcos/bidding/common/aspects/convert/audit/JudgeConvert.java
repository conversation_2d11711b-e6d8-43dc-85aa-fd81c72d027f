package com.epcos.bidding.common.aspects.convert.audit;

import com.epcos.bidding.audit.api.AuditAttribute;
import com.epcos.bidding.common.aspects.convert.GetParamConvert;
import com.epcos.bidding.purchase.extract.domain.dto.JudgeExtractDto;
import com.epcos.bidding.purchase.extract.domain.vo.ExtractLogVo;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/25 14:54
 */
public class JudgeConvert implements GetParamConvert<JudgeExtractDto, List<AuditAttribute>> {


    @Override
    public List<AuditAttribute> doConvert(JudgeExtractDto dto) {

        List<ExtractLogVo> voList = dto.getVoList();
        List<AuditAttribute> attributeList = new ArrayList<>();
        StringBuilder builder = new StringBuilder();
        voList.forEach(v ->
                builder.append("【科室：").append(v.getDept()).append("，")
                        .append("专家名称：").append(v.getJudgeName()).append("，")
                        .append("专家账号：").append(v.getJudgeAccount()).append("】"));
        attributeList.add(convert("extractJudgeInfo", "评审专家信息", builder.toString(), "String"));
        return attributeList;
    }

    /**
     * 根据key获取对应的name和value，然后根据type进行转换
     *
     * @param key
     * @param name
     * @param value
     * @param type
     */
    private AuditAttribute convert(String key, String name, String value, String type) {
        return new AuditAttribute(key, name, value, type);
    }
}
