package com.epcos.bidding.common.aspects.audit;

import com.epcos.bidding.audit.api.AuditAttribute;
import com.epcos.bidding.common.annotaion.GetAuditAttribute;
import com.epcos.bidding.common.aspects.AbstractMethod;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.common.aspects.convert.GetParamConvert;
import com.epcos.common.core.utils.StringUtils;
import com.epcos.common.core.utils.reflect.ReflectUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * 获取类中字段，返回一个个描述对象
 * 默认取 @ApiModelProperty 中的 value 值
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class AuditAttributeAspectFilterChainPost extends AbstractMethod<GetAuditAttribute, List<AuditAttribute>> {
    private static final Set<String> DEFAULT_IGNORE_PROPERTIES = new HashSet<>();

    public AuditAttributeAspectFilterChainPost() {
        super(GetUtil.GET_AUDIT_ATTRIBUTE, "获取审批请求参数描述");
        // 默认忽略的字段
        DEFAULT_IGNORE_PROPERTIES.add("serialVersionUID");
        DEFAULT_IGNORE_PROPERTIES.add("auditProcessDto");
    }

    @Override
    @Around(value = "@annotation(annotation)")
    public Object around(ProceedingJoinPoint point, GetAuditAttribute annotation) {
        return super.around(point, annotation);
    }

    @Override
    public List<AuditAttribute> businessMethods(JoinPoint point, GetAuditAttribute annotation) {
        Object arg = point.getArgs()[0];
        if (Objects.isNull(arg)) {
            return new ArrayList<>();
        }
        Set<String> ignoreFields = getIgnoreFields(annotation.ignoreField());
        List<AuditAttribute> list = getAuditAttributeList(ignoreFields, arg);
        Class<? extends GetParamConvert> convert = annotation.toAuditAttributeConvert();
        if (GetParamConvert.isImpl(convert)) {
            List<AuditAttribute> customizeList = (List<AuditAttribute>) GetParamConvert.getBeanOrNewInstance(convert).doConvert(arg);
            list.addAll(customizeList);
        }
        return list;
    }

    public static Set<String> getIgnoreFields(String[] ignoreFields) {
        Set<String> set = new HashSet<>(DEFAULT_IGNORE_PROPERTIES);
        Optional.ofNullable(ignoreFields)
                .ifPresent(i -> set.addAll(Arrays.asList(i)));
        return set;
    }

    public static List<AuditAttribute> getAuditAttributeList(Set<String> ignoreFields, Object obj) {
        List<AuditAttribute> list = new ArrayList<>();
        // 获取所有未被忽略的字段
        for (Field field : ReflectUtils.getAllFields(obj.getClass(), ignoreFields)) {
            // 使用反射获取所有包含@ApiModelProperty注解的字段名、字段值、及@ApiModelProperty中value的值，
            processField(obj, field, list, ignoreFields);
        }
        return list;
    }

    static void processField(Object obj, Field field, List<AuditAttribute> list, Set<String> ignoreFields) {
        if (Objects.isNull(obj)) {
            return;
        }
        ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
        if (Objects.isNull(annotation)) {
            return;
        }
        String apiModelPropertyValue = annotation.value();
        Object fieldValue = ReflectUtils.invokeGetter(obj, field.getName());
        if (Objects.isNull(fieldValue)) {
            return;
        }
        if (isSimpleType(field.getType())) {
            // 判断filed是否为基本类型，不是复杂对象类型
            list.add(new AuditAttribute(field.getName(), apiModelPropertyValue, fieldValue.toString(), getFieldType(field, annotation)));
        } else if (Collection.class.isAssignableFrom(field.getType())) {
            Collection<Object> list1 = (Collection<Object>) fieldValue;
            if (CollectionUtils.isEmpty(list1)) {
                return;
            }
            for (Object item : list1) {
                processComplexObject(item, list, ignoreFields);
            }
        } else if (Map.class.isAssignableFrom(field.getType())) {
            Map<Object, Object> map = (Map<Object, Object>) fieldValue;
            for (Map.Entry<Object, Object> entry : map.entrySet()) {
                processComplexObject(entry.getValue(), list, ignoreFields);
            }
        } else {
            processComplexObject(fieldValue, list, ignoreFields);
        }
    }

    static void processComplexObject(Object item, List<AuditAttribute> list, Set<String> ignoreFields) {
        if (Objects.isNull(item)) {
            return;
        }
        for (Field field : ReflectUtils.getAllFields(item.getClass(), ignoreFields)) {
            processField(item, field, list, ignoreFields);
        }
    }

    /**
     * 判断是否是简单类型，基本类型，或不是 List 和 Map
     */
    static boolean isSimpleType(Class<?> clazz) {
        return clazz.isPrimitive() ||
                (
                        clazz.getName().startsWith("java.") &&
                                !clazz.isArray() &&
                                !clazz.isEnum() &&
                                !Collection.class.isAssignableFrom(clazz) &&
                                !Map.class.isAssignableFrom(clazz)
                );
    }

    static String getFieldType(Field field, ApiModelProperty annotation) {
        return StringUtils.hasText(annotation.dataType())
                ? annotation.dataType()
                : field.getType().getSimpleName();
    }


}
