package com.epcos.bidding.controller;

import com.epcos.bidding.common.annotaion.Jump;
import com.epcos.bidding.common.annotaion.Msg;
import com.epcos.bidding.common.aspects.convert.GetBidOpeningSupplierIdParamConvert;
import com.epcos.bidding.purchase.api.params.PurchaseBidOpeningVo;
import com.epcos.bidding.purchase.bulletin.domain.vo.SupplierSignVo;
import com.epcos.bidding.purchase.opening.business.api.IBidOpenApi;
import com.epcos.bidding.purchase.opening.business.api.IPurchaseBidOpeningApi;
import com.epcos.bidding.purchase.opening.business.service.AutoBidOpenService;
import com.epcos.bidding.purchase.opening.domain.dto.PurchaseBidOpeningDto;
import com.epcos.common.core.constant.RoleConstants;
import com.epcos.common.core.domain.R;
import com.epcos.common.log.annotation.Log;
import com.epcos.common.log.enums.BusinessType;
import com.epcos.common.redis.annotation.RedisLock;
import com.epcos.common.redis.publish.BusinessTypeEnum;
import com.epcos.common.security.annotation.InnerAuth;
import com.epcos.common.security.annotation.RequiresPermissions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

import static com.epcos.common.core.constant.PurchaseConstants.UserType.SUPPLIER;

/**
 * <AUTHOR>
 */
@Api(tags = "开标")
@RestController
@RequiredArgsConstructor
@RequestMapping("/purchase/bidOpening")
public class PurchaseBidOpeningController {

    private final IPurchaseBidOpeningApi purchaseBidOpeningApi;
    private final IBidOpenApi bidOpenApi;
    private final AutoBidOpenService autoBidOpenService;

    @RequiresPermissions("project:process:query")
    @ApiOperation("查询包开标状态")
    @GetMapping("/query")
    R<PurchaseBidOpeningVo> query(@RequestParam("subpackageCode") @NotBlank(message = "包code必填") String subpackageCode) {
        PurchaseBidOpeningVo vo = purchaseBidOpeningApi.query(subpackageCode);
        vo.setAutoBidOpenMsg(autoBidOpenService.getAutoBidOpenMsg(subpackageCode));
        return R.ok(vo);
    }

    @InnerAuth
    @GetMapping("/remote/query")
    R<PurchaseBidOpeningVo> remoteQuery(@RequestParam("subpackageCode") @NotBlank(message = "包code必填") String subpackageCode) {
        return R.ok(purchaseBidOpeningApi.query(subpackageCode));
    }

    @RequiresPermissions("process:bid:open")
    @Log(title = "开标开始", businessType = BusinessType.INSERT)
    @RedisLock(second = 30)
    @ApiOperation("开标开始")
    @PostMapping("/start")
    R<Boolean> start(@RequestBody @Valid PurchaseBidOpeningDto dto) {
        bidOpenApi.start(dto);
        return R.ok();
    }

    @RequiresPermissions("process:bid:open")
    @Log(title = "唱标", businessType = BusinessType.UPDATE)
    @RedisLock(second = 30)
    @ApiOperation("唱标")
    @Msg(
            fromRole = RoleConstants.PURCHASER,
            businessType = BusinessTypeEnum.BID_OPENING,
            msg = "唱标",
            buyItemCodeEL = "#dto.buyItemCode",
            subpackageCodeEL = "#dto.subpackageCode",
            toUserIdsConvert = GetBidOpeningSupplierIdParamConvert.class
    )
    @PostMapping("/sing")
    R<Boolean> sing(@RequestBody @Valid PurchaseBidOpeningDto dto) {
        bidOpenApi.sing(dto);
        return R.ok();
    }

    @RequiresPermissions("process:bid:open")
    @Log(title = "开标完成", businessType = BusinessType.UPDATE)
    @RedisLock(second = 30)
    @ApiOperation("开标完成")
    @Msg(
            fromRole = RoleConstants.PURCHASER,
            businessType = BusinessTypeEnum.BID_OPENING,
            msg = "开标完成",
            buyItemCodeEL = "#dto.buyItemCode",
            subpackageCodeEL = "#dto.subpackageCode",
            toUserIdsConvert = GetBidOpeningSupplierIdParamConvert.class
    )
    @Jump(subpackageCodeEL = "#dto.subpackageCode", processRole = SUPPLIER, toSupplierIdsConvert = GetBidOpeningSupplierIdParamConvert.class)
    @PostMapping("/complete")
    public R<Boolean> complete(@RequestBody @Valid PurchaseBidOpeningDto dto) {
        bidOpenApi.complete(dto);
        return R.ok();
    }

    @RequiresPermissions("process:bidOpen:restart")
    @ApiOperation("重新开标")
    @RedisLock(second = 30)
    @Log(title = "重新开标", businessType = BusinessType.DELETE)
    @Msg(
            fromRole = RoleConstants.PURCHASER,
            businessType = BusinessTypeEnum.BID_OPENING,
            msg = "重新开标",
            buyItemCodeEL = "#dto.buyItemCode",
            subpackageCodeEL = "#dto.subpackageCode",
            toUserIdsConvert = GetBidOpeningSupplierIdParamConvert.class
    )
    @PostMapping("/restart")
    R<Boolean> restart(@RequestBody @Valid PurchaseBidOpeningDto dto) {
        bidOpenApi.restart(dto);
        return R.ok();
    }

    @RequiresPermissions("project:process:query")
    @ApiOperation("查询开标投标人列表")
    @GetMapping("/bidder/list")
    public R<List<SupplierSignVo>> bidderList(@NotBlank(message = "参数必填") String subpackageCode) {
        return R.ok(bidOpenApi.bidderList(subpackageCode));
    }
}
