package com.epcos.bidding.common.aspects.filter.validator;

import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierSignUpDao;

/**
 * 校验已签到
 */
public class SignUpSignInValidator implements ResultPostHandlerFilterChain<SupplierSignUpDao> {

    @Override
    public void postHandler(AspectContext context, SupplierSignUpDao result) {
        result.verifySignIn();
    }

}
