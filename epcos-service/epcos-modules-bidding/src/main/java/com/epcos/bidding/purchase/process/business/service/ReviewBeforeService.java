package com.epcos.bidding.purchase.process.business.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.purchase.opening.domain.dto.PurchaseBidOpeningDto;
import com.epcos.bidding.purchase.process.business.api.IReviewBeforeApi;
import com.epcos.bidding.purchase.process.domain.dao.ReviewBeforeDao;
import com.epcos.bidding.purchase.process.mapping.ReviewBeforeConvert;
import com.epcos.bidding.purchase.process.repository.IReviewBeforeMapper;
import com.epcos.bidding.supplier.api.params.SupplierSignUpVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.epcos.common.core.constant.PurchaseConstants.Currency.ZERO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/12 8:55
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReviewBeforeService extends ServiceImpl<IReviewBeforeMapper, ReviewBeforeDao> implements IReviewBeforeApi {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(PurchaseBidOpeningDto dto, Boolean bool) {
        List<SupplierSignUpVo> upVoList;
        if (bool) {
            upVoList = dto.getSignUpList().stream()
                    .filter(s -> "1".equals(s.getEnterTheReview()))
                    .collect(Collectors.toList());
        } else {
            upVoList = dto.getSignUpList();
        }
        List<ReviewBeforeDao> beforeDaoList = upVoList.stream()
                .map(ReviewBeforeConvert.INSTANCE::convert)
                .collect(Collectors.toList());
        saveBatch(beforeDaoList);
    }

    @Override
    public void updateBy(String confirmCounterSign, String subpackageCode, String isBargaining) {
        ReviewBeforeDao reviewBeforeDao = new ReviewBeforeDao();
        reviewBeforeDao.setConfirmCounterSign(confirmCounterSign);
        reviewBeforeDao.setIsBargaining(isBargaining);
        update(reviewBeforeDao, updateWrapper(subpackageCode, null));
    }


    @Override
    public void updateBy(String confirmReview, String subpackageCode) {
        ReviewBeforeDao reviewBeforeDao = new ReviewBeforeDao();
        reviewBeforeDao.setConfirmReview(confirmReview);
        reviewBeforeDao.setConfirmReviewTime(new Date());
        update(reviewBeforeDao, updateWrapper(subpackageCode, null));
    }

    @Override
    public void rollbackData(String subpackageCode) {
        ReviewBeforeDao reviewBeforeDao = new ReviewBeforeDao();
        reviewBeforeDao.setBidEnterExpert(String.valueOf(ZERO));
        reviewBeforeDao.setConfirmCounterSign(String.valueOf(ZERO));
        reviewBeforeDao.setConfirmReview(String.valueOf(ZERO));
        update(reviewBeforeDao, updateWrapper(subpackageCode, null));
    }
}
