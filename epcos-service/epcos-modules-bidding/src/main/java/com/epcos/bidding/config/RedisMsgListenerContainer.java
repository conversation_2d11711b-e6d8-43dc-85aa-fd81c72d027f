package com.epcos.bidding.config;

import com.epcos.bidding.purchase.opening.business.service.AutoBidOpenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

/**
 * redis 消息监听
 */
@Configuration
public class RedisMsgListenerContainer {

    @Autowired
    private AutoBidOpenService autoBidOpenService;

    @Bean
    public RedisMessageListenerContainer biddingRedisListenerContainer(RedisConnectionFactory connectionFactory) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        return container;
    }

    @Bean
    public RedisKeyExpirationListener biddingKeyExpirationListener(RedisConnectionFactory connectionFactory) {
        return new RedisKeyExpirationListener(biddingRedisListenerContainer(connectionFactory), autoBidOpenService);
    }

    @Bean
    public RedisKeyExpirationCommentListener redisKeyExpirationCommentListener(RedisConnectionFactory connectionFactory) {
        return new RedisKeyExpirationCommentListener(biddingRedisListenerContainer(connectionFactory));
    }



}
