package com.epcos.bidding.purchase.claims.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/22 17:54
 */
@Data
public class RejectRecordDto implements Serializable {

    private static final long serialVersionUID = 6986590374988612102L;

    @ApiModelProperty(value = "标段code")
    @NotBlank(message = "标段code不能为空")
    private String subpackageCode;

    @ApiModelProperty(value = "供应商id")
    @NotNull(message = "供应商id")
    private Long supplierId;

    @ApiModelProperty(value = "采购文件要求id")
    private List<Long> requirementIdList;
}
