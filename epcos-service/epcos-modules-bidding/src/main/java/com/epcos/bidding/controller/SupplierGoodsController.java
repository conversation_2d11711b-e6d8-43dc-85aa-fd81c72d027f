package com.epcos.bidding.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.common.annotaion.NotNullUserId;
import com.epcos.bidding.supplier.goods.business.api.IGoodsApi;
import com.epcos.bidding.supplier.goods.domain.dto.*;
import com.epcos.bidding.supplier.goods.domain.vo.GoodsVo;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.web.page.TableDataVo;
import com.epcos.common.log.annotation.LogBySupplier;
import com.epcos.common.log.enums.BusinessType;
import com.epcos.common.core.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Api(tags = "供应商商品")
@RequiredArgsConstructor
@RestController
@RequestMapping("/supplier/goods")
public class SupplierGoodsController {

    private final IGoodsApi goodsApi;

    @LogBySupplier(title = "供应商保存商品", businessType = BusinessType.INSERT)
    @NotNullUserId
    @ApiOperation("供应商保存商品")
    @PostMapping("/save")
    public R<Boolean> save(@ModelAttribute @Valid GoodsSaveDto dto) {
        goodsApi.save(SecurityUtils.getLoginUser(), dto);
        return R.ok();
    }

    @LogBySupplier(title = "供应商修改商品", businessType = BusinessType.UPDATE)
    @NotNullUserId
    @ApiOperation("供应商修改商品")
    @PostMapping("/update")
    public R<Boolean> update(@ModelAttribute @Valid GoodsUpdateDto dto) {
        goodsApi.update(SecurityUtils.getUserId(), dto);
        return R.ok();
    }

    @LogBySupplier(title = "供应商下架商品", businessType = BusinessType.UPDATE)
    @ApiOperation("供应商下架商品")
    @PostMapping("/upAndDown")
    public R<Boolean> upAndDown(@RequestBody @Valid GoodsUpAndDownDto dto) {
        goodsApi.upAndDown(dto);
        return R.ok();
    }

    @ApiOperation("供应商商品分页列表")
    @NotNullUserId
    @PostMapping("/page")
    public TableDataVo<GoodsVo> pageList(@RequestBody @Valid PageSortEntity<GoodsPageDto> dto) {
        IPage<GoodsVo> pageVo = goodsApi.pageList(SecurityUtils.getUserId(), dto);
        return new TableDataVo<>(pageVo.getRecords(), pageVo.getTotal());
    }

    @NotNullUserId
    @ApiOperation("供应商商品分页列表")
    @PostMapping("/del")
    public R<Boolean> del(@RequestBody @Valid GoodsDelDto dto) {
        goodsApi.del(SecurityUtils.getLoginUser(), dto);
        return R.ok();
    }

}
