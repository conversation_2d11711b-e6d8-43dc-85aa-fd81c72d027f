package com.epcos.bidding.common.aspects.filter.validator;

import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileDao;

import java.util.Optional;

/**
 * 采购文件不为空且已发布
 */
public class ClaimsFileIfNotNullAndPublishedValidator implements ResultPostHandlerFilterChain<ClaimsFileDao> {
    @Override
    public void postHandler(AspectContext context, ClaimsFileDao result) {
        Optional.ofNullable(result).ifPresent(ClaimsFileDao::verifyPublished);
    }
}
