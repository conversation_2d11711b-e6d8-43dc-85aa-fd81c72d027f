package com.epcos.bidding.purchase.process.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/3 10:34
 */
@Data
public class SupplierInfoVo implements Serializable {

    private static final long serialVersionUID = -7808756808297682455L;

    @ApiModelProperty(value = "供应商id")
    protected Long supplierId;

    @ApiModelProperty(value = "供应商公司名称")
    protected String supplierCompanyName;

    @ApiModelProperty(value = "法定代表人联系方式")
    protected String contactNumber;

    @ApiModelProperty(value = "电子邮箱")
    protected String email;

    @ApiModelProperty(value = "法定代表人证件号码")
    protected String certificateCode;

    @ApiModelProperty(value = "法定代表人名称")
    protected String certificateName;

    @ApiModelProperty(value = "投标责任人姓名")
    protected String infoReporterName;

    @ApiModelProperty(value = "投标责任人证件号码")
    protected String infoReporterCode;

    @ApiModelProperty(value = "投标责任人联系电话")
    protected String infoReporterContactNumber;

    @ApiModelProperty("报价内容")
    private List<LinkedHashMap<String, String>> bodyMaps;

}
