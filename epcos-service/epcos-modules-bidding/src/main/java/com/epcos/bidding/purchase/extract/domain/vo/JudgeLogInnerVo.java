package com.epcos.bidding.purchase.extract.domain.vo;

import com.epcos.bidding.purchase.extract.domain.dao.ExtractJudgeExternalDao;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/19 15:37
 */
@Data
public class JudgeLogInnerVo extends ExtractJudgeExternalDao {

    private static final long serialVersionUID = -474036748773067456L;

    @ApiModelProperty(value = "抽取记录名称下的评委人数")
    private Integer judgeNumber;

    @ApiModelProperty(value = "抽取记录名称下的评委id")
    private List<Long> judgeIdList;
}
