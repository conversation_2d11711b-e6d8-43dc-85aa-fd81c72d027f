package com.epcos.bidding.purchase.opening.business.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.purchase.api.params.PurchaseBidOpeningVo;
import com.epcos.bidding.purchase.opening.business.api.IPurchaseBidOpeningApi;
import com.epcos.bidding.purchase.opening.domain.dao.PurchaseBidOpeningDao;
import com.epcos.bidding.purchase.opening.repository.PurchaseBidOpeningMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PurchaseBidOpeningService extends ServiceImpl<PurchaseBidOpeningMapper, PurchaseBidOpeningDao>
        implements IPurchaseBidOpeningApi {

    private final PurchaseBidOpeningMapper purchaseBidOpeningMapper;

    @Override
    public PurchaseBidOpeningVo query(String subpackageCode) {
        return daoToVo(findOne(subpackageCode));
    }

    private PurchaseBidOpeningVo daoToVo(PurchaseBidOpeningDao dao) {
        return Optional.ofNullable(dao).map(i -> {
            PurchaseBidOpeningVo vo = new PurchaseBidOpeningVo();
            vo.setSubpackageCode(i.getSubpackageCode());
            vo.setStatus(i.getStatus());
            vo.setRecordFile(i.getRecordFile());
            return vo;
        }).orElse(new PurchaseBidOpeningVo().notStarted());
    }

    @Override
    public List<PurchaseBidOpeningVo> query(List<String> subpackageCodes) {
        return find(subpackageCodes)
                .stream()
                .map(this::daoToVo)
                .collect(Collectors.toList());
    }

}
