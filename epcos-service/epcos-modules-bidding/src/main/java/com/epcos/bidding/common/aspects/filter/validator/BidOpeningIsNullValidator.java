package com.epcos.bidding.common.aspects.filter.validator;

import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import com.epcos.bidding.purchase.api.params.PurchaseBidOpeningVo;
import com.epcos.common.core.exception.ServiceException;

import java.util.Optional;

/**
 * 校验-开标信息为空
 */
public class BidOpeningIsNullValidator implements ResultPostHandlerFilterChain<PurchaseBidOpeningVo> {
    @Override
    public void postHandler(AspectContext context, PurchaseBidOpeningVo result) {
        Optional.ofNullable(result)
                .orElseThrow(() -> new ServiceException("开标信息为空"));
    }
}
