package com.epcos.bidding.purchase.win.domain.dto;

import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/17 9:26
 */
@Data
public class SelectWinDto extends SuperPackageVo {

    private static final long serialVersionUID = 3284005015043304531L;

    @ApiModelProperty(value = "中标供应商信息")
    List<WinSupplier> winSupplierList;
}
