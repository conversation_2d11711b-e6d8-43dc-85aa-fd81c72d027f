package com.epcos.bidding.common.aspects.convert;

import com.epcos.bidding.audit.api.AuditAttribute;
import com.epcos.bidding.audit.api.dto.AuditInitiateDto;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class AuditInitiateParamConvert implements GetParamConvert<AuditInitiateDto, List<AuditAttribute>> {

    @Override
    public List<AuditAttribute> doConvert(AuditInitiateDto dto) {
        List<AuditAttribute> list = new ArrayList<>();
        Optional.ofNullable(dto.getAttList()).ifPresent(
                a -> a.forEach(i -> list.add(i.convert())));
        Optional.ofNullable(dto.getDataFieldList()).ifPresent(
                d -> d.forEach(i -> list.add(i.convert())));
        return list;
    }
}
