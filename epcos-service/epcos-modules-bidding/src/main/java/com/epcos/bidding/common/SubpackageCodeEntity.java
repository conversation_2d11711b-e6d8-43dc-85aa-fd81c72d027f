package com.epcos.bidding.common;

import com.epcos.common.core.web.validator.CreateGroup;
import com.epcos.common.core.web.validator.UpdateGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

@Getter
@Setter
@NoArgsConstructor
public abstract class SubpackageCodeEntity extends BaseDao {

    @ApiModelProperty(value = "包编码")
    @NotBlank(message = "包编码，必填", groups = CreateGroup.class)
    @Length(max = 50, message = "包编码【最长：50】", groups = {CreateGroup.class, UpdateGroup.class})
    protected String subpackageCode;

}
