package com.epcos.bidding.common.aspects;

import com.epcos.bidding.common.annotaion.GetCommon;
import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.EvalSpelUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.expression.spel.SpelEvaluationException;

import java.lang.annotation.Annotation;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Objects;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractMethod<T extends Annotation, R> implements SyncMethod<T, R>, ResultPostHandlerFilter<PERSON>hain<R> {

    protected final String key;
    protected final String desc;
    protected final ThreadLocal<AspectContext> threadLocal = new ThreadLocal<>();

    protected AbstractMethod(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public Object around(ProceedingJoinPoint point, T annotation) {
        Method method = ((MethodSignature) point.getSignature()).getMethod();
        GetCommon common;
        try {
            Method commonMethod = annotation.annotationType().getMethod("common");
            common = (GetCommon) commonMethod.invoke(annotation);
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            log.error("{} 中必须包含 common 属性，且必须是 @GetCommon 类型", annotation, e);
            throw new RuntimeException(e);
        }
        AspectContext context = new AspectContext();
        context.setAsync(common.async());
        context.setAfters(Arrays.asList(common.afters()));
        context.setBuyItemCode(EvalSpelUtil.get(method, point.getArgs(), common.buyItemCodeEL(), String.class));
        context.setSubpackageCode(EvalSpelUtil.get(method, point.getArgs(), common.subpackageCodeEL(), String.class));
        context.setDto(point.getArgs()[0]);
        context.setMethod(method);
        context.setArgs(point.getArgs());
        threadLocal.set(context);
        try {
            return doAroundAdvice(point, annotation, desc);
        } finally {
            threadLocal.remove();
        }
    }

    private Object doAroundAdvice(ProceedingJoinPoint point, T annotation, String description) {
        try {
            invokeAndPostProcess(point, annotation);
            return point.proceed();
        } catch (ServiceException e) {
            log.error("业务异常：description={}, point={}, annotation={}", description, point, annotation);
            throw e;
        } catch (SpelEvaluationException e) {
            log.error("el表达式异常：description={}, point={}, annotation={}", description, point, annotation, e);
            throw new ServiceException(description + " | el表达式异常");
        } catch (Throwable e) {
            log.error("{}: point={}, annotation={}", description, point, annotation, e);
            throw new ServiceException(e.getMessage());
        } finally {
            removeValue();
        }
    }

    /**
     * 调用实现并设置值,并增加后续处理
     */
    private void invokeAndPostProcess(JoinPoint point, T annotation) {
        AspectContext context = threadLocal.get();
        if (Objects.nonNull(GetUtil.getRaw(key))) {
            context.setRes(GetUtil.getRaw(key));
            return;
        }
        if (context.isAsync()) {
            Future<R> future = EXECUTOR.submit(() -> {
                try {
                    if (Thread.interrupted()) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("线程：" + Thread.currentThread().getName() + "，被中断");
                    }
                    threadLocal.set(context);
                    R r = businessMethods(point, annotation);
                    context.setRes(r);
                    postHandler(context, r);
                    return r;
                } finally {
                    threadLocal.remove();
                }
            });
            context.setRes(future);
            GetUtil.set(key, future);
        } else {
            R res = businessMethods(point, annotation);
            context.setRes(res);
            postHandler(context, res);
            GetUtil.set(key, res);
        }
    }

    // 增加后续处理
    @Override
    public void postHandler(AspectContext context, R result) {
        for (Class<? extends ResultPostHandlerFilterChain> after : context.getAfters()) {
            if (!after.isInterface()) {
                ResultPostHandlerFilterChain handler = ResultPostHandlerFilterChain.createHandler(after);
                handler.postHandler(context, result);
            }
        }
    }

    private void removeValue() {
        if (Objects.nonNull(threadLocal.get())) {
            if (threadLocal.get().isAsync()) {
                Future<Object> future = GetUtil.get(key);
                if (!future.isDone()) {
                    future.cancel(true);
                }
            }
        }
        GetUtil.remove(key);
    }


}
