package com.epcos.bidding.purchase.remote;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.process.domain.vo.BuyItemMeetVo;
import com.epcos.bidding.purchase.process.domain.vo.SupplierInfoVo;
import com.epcos.bidding.supplier.answer.business.api.IAnswerFileQuoteFormApi;
import com.epcos.bidding.supplier.api.params.MultiSupplierQuoteFormVo;
import com.epcos.bidding.supplier.api.params.SubpackageCodeAndUserIdAndRoundDto;
import com.epcos.bidding.supplier.api.params.SupplierSignUpVo;
import com.epcos.bidding.supplier.bargain.business.api.ISupplierBargainApi;
import com.epcos.bidding.supplier.goods.business.api.IGoodsApi;
import com.epcos.bidding.supplier.goods.domain.dto.GoodsAuditDto;
import com.epcos.bidding.supplier.goods.domain.dto.GoodsPageDto;
import com.epcos.bidding.supplier.goods.domain.vo.GoodsVo;
import com.epcos.bidding.supplier.sign.business.api.ISupplierProcessNodeApi;
import com.epcos.bidding.supplier.sign.business.api.ISupplierSignApi;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierProcessNodeDao;
import com.epcos.bidding.supplier.sign.domain.dto.SupplierSignQueryDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/6 15:44
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RemoteSupplierApi {

    private ISupplierSignApi supplierSignApi;
    private final IAnswerFileQuoteFormApi answerFileQuoteFormApi;
    private final ISupplierBargainApi supplierBargainApi;
    private final IGoodsApi supplierGoodsApi;
    private final ISupplierProcessNodeApi supplierProcessNodeApi;

    @Autowired
    public void setSupplierSignApi(ISupplierSignApi supplierSignApi) {
        this.supplierSignApi = supplierSignApi;
    }

    /**
     * 查询指定报名供应商列表
     *
     * @param subpackageCode 包code
     * @return
     */
    public List<SupplierSignUpVo> getSignUp(String subpackageCode) {
        List<SupplierSignUpVo> upVoList = supplierSignApi.query(new SupplierSignQueryDto(null, subpackageCode), null);
        return upVoList;
    }

    /**
     * 获取供应商议价信息
     *
     * @param subpackageCode
     * @param supplierId
     * @param round
     * @return
     */
    public MultiSupplierQuoteFormVo getBargainInfo(String subpackageCode, Long supplierId, Integer round) {
        return answerFileQuoteFormApi.findVo(subpackageCode, supplierId, round);
    }

    /**
     * 获取供应商议价信息
     *
     * @param voMap
     * @return
     */
    public List<MultiSupplierQuoteFormVo> getBargainInfo(Map<String, List<BuyItemMeetVo>> voMap) {
        Set<SubpackageCodeAndUserIdAndRoundDto> dtoSet = new HashSet<>();
        voMap.forEach((subpackageCode, voList) -> {
            SubpackageCodeAndUserIdAndRoundDto dto = new SubpackageCodeAndUserIdAndRoundDto();
            dto.setSubpackageCode(subpackageCode);
            List<SupplierInfoVo> supplierInfoVoList = voList.get(0).getSupplierInfoVoList();
            Set<Long> supplierIdList = supplierInfoVoList.stream().map(SupplierInfoVo::getSupplierId).collect(Collectors.toSet());
            dto.setSupplierIds(supplierIdList);
            dtoSet.add(dto);
        });
        return answerFileQuoteFormApi.findVos(dtoSet);
    }

    /**
     * 删除供应商议价信息
     *
     * @param subpackageCode
     */
    public void delSupplierBargainInfo(String subpackageCode) {
        supplierBargainApi.del(subpackageCode);
    }


    /**
     * 采购人查询所有供应商商品分页列表
     *
     * @param dto
     * @return
     */
    public IPage<GoodsVo> pageList(PageSortEntity<GoodsPageDto> dto) {
        return supplierGoodsApi.pageList(dto);
    }

    /**
     * 采购人审批商品
     *
     * @param dto
     */
    public void audit(GoodsAuditDto dto) {
        supplierGoodsApi.audit(dto);
    }

    /**
     * 采购人查看商品详情
     *
     * @param goodsId
     */
    public GoodsVo goodsInfo(Long goodsId) {
        return supplierGoodsApi.query(goodsId);
    }

    public List<GoodsVo> goodsInfos(Set<Long> gooddsIdSet) {
        return supplierGoodsApi.query(gooddsIdSet);
    }


    public void addProcess(SupplierProcessNodeDao dao) {
        supplierProcessNodeApi.save(dao);
    }

    public SupplierProcessNodeDao findProcess(String subpackageCode, Integer processRile) {
        return supplierProcessNodeApi.findOne(subpackageCode, null, processRile);
    }
}
