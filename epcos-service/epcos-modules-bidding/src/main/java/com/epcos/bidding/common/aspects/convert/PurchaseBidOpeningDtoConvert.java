package com.epcos.bidding.common.aspects.convert;

import cn.hutool.core.collection.CollUtil;
import com.epcos.bidding.common.aspects.sms.SmsContent;
import com.epcos.bidding.purchase.opening.domain.dto.PurchaseBidOpeningDto;
import com.epcos.bidding.supplier.api.params.SupplierSignUpVo;
import com.epcos.bidding.supplier.common.SupplierInfoEntityShared;
import com.epcos.bidding.supplier.sign.business.api.ISupplierBidderApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@Component
public class PurchaseBidOpeningDtoConvert implements GetParamConvert<PurchaseBidOpeningDto, SmsContent> {

    @Autowired
    private ISupplierBidderApi supplierBidderApi;

    @Override
    public SmsContent doConvert(PurchaseBidOpeningDto dto) {
        SmsContent vo = new SmsContent();
        List<SupplierSignUpVo> signUpList = dto.getSignUpList();
        if (CollUtil.isEmpty(signUpList)) {
            return vo;
        }
        vo.setBuyItemName(signUpList.get(0).getBuyItemName());
        vo.setSubpackageName(signUpList.get(0).getSubpackageName());
        Set<Long> supplierIds = signUpList.stream()
                .filter(f -> Boolean.TRUE.equals(f.getBidOpeningSign()))
                .map(SupplierSignUpVo::getSupplierId)
                .collect(Collectors.toSet());
        if (CollUtil.isEmpty(supplierIds)) {
            return vo;
        }
        Set<String> mobiles = supplierBidderApi.findBy(dto.getSubpackageCode(), supplierIds)
                .stream()
                .map(SupplierInfoEntityShared::getInfoReporterContactNumber)
                .collect(Collectors.toSet());
        vo.setMobiles(mobiles);
        return vo;
    }

}
