package com.epcos.bidding.purchase.process.domain.vo;

import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperBuyItemVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 项目归档文件vo
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/25 8:47
 */
@Data
public class BuyItemFileVo extends SuperBuyItemVo {

    private static final long serialVersionUID = 8400313811576414262L;

    @ApiModelProperty(value = "项目创建年月")
    private String yearMonthSplit;

    @ApiModelProperty(value = "项目文件信息")
    private List<FileInfoVo> fileInfoVoList;

    @ApiModelProperty(value = "包文件")
    private List<SubpackageFileVo> subpackageFileVoList;


}
