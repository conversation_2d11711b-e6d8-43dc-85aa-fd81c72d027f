package com.epcos.bidding.purchase.monitor.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/5 14:07
 */
@Data
public class UpdateMonitorDto implements Serializable {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "监标人")
    @NotBlank(message = "监标人不能为空")
    private String monitorBidPerson;

    @ApiModelProperty(value = "监标人id")
    @NotNull(message = "监标人id不能为空")
    private Long monitorBidPersonId;

    @ApiModelProperty(value = "监标事项默认为两种值 ：1 监督开标  2监督评标")
    @NotBlank(message = "监标事项不能为空")
    private String monitorBidType;

    @ApiModelProperty(value = "是否签字（0未签字  1已签字)")
    private Integer signStatus;
}
