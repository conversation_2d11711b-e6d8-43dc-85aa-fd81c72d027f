package com.epcos.bidding.purchase.claims.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/9 9:32
 */
@Data
public class TenderFileRequirementDetailsDto implements Serializable {

    private static final long serialVersionUID = -8844667561433095082L;

    @ApiModelProperty("属性")
    @NotBlank(message = "属性不能为空")
    @Length(max = 500, message = "属性最长500字符")
    private String attribute;

    @ApiModelProperty("描述")
    @NotBlank(message = "描述不能为空")
    @Length(max = 1000, message = "描述最长1000字符")
    private String description;


}
