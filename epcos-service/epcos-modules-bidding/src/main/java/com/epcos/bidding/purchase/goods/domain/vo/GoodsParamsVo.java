package com.epcos.bidding.purchase.goods.domain.vo;

import com.epcos.bidding.purchase.api.params.AttributeVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/13 9:49
 */
@Data
public class GoodsParamsVo implements Serializable {

    @ApiModelProperty("商品分类")
    private String goodsCategory;

    @ApiModelProperty(value = "头")
    private List<AttributeVo> headeList;

    @ApiModelProperty("报价内容")
    @NotEmpty(message = "报价内容，必填")
    private List<LinkedHashMap<String, String>> bodyMaps;
}
