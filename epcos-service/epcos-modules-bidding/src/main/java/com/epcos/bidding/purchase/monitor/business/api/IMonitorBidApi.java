package com.epcos.bidding.purchase.monitor.business.api;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.IBaseService;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.api.params.dto.monitor.CreateMonitorDto;
import com.epcos.bidding.purchase.monitor.domain.dao.MonitorBidDao;
import com.epcos.bidding.purchase.monitor.domain.dto.RetrieveMonitorDto;
import com.epcos.bidding.purchase.monitor.domain.dto.UpdateMonitorDto;
import com.epcos.bidding.purchase.monitor.domain.vo.MonitorInfoVo;
import com.epcos.bidding.purchase.monitor.domain.vo.MonitorVo;
import com.epcos.common.core.utils.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/5 13:44
 */
public interface IMonitorBidApi extends IBaseService<MonitorBidDao> {

    @Override
    default LambdaQueryWrapper<MonitorBidDao> queryWrapper(MonitorBidDao dao) {
        return null;
    }

    default LambdaQueryWrapper<MonitorBidDao> queryWrapper(String buyItemCode, String subpackageCode) {
        return Wrappers.lambdaQuery(MonitorBidDao.class)
                .eq(StringUtils.hasText(buyItemCode), MonitorBidDao::getBuyItemCode, buyItemCode)
                .eq(StringUtils.hasText(subpackageCode), MonitorBidDao::getSubpackageCode, subpackageCode);
    }

    /**
     * @param subpackageCode 标段code
     * @param monitorBidType 监标类型 1 监督开标  2监督评标
     * @return
     */
    default LambdaQueryWrapper<MonitorBidDao> remove(String subpackageCode, String monitorBidType) {
        return Wrappers.lambdaQuery(MonitorBidDao.class)
                .eq(StringUtils.hasText(subpackageCode), MonitorBidDao::getSubpackageCode, subpackageCode)
                .eq(StringUtils.hasText(monitorBidType), MonitorBidDao::getMonitorBidType, monitorBidType);
    }

    default Boolean remove(String buyItemCode) {
        return remove(Wrappers.lambdaUpdate(MonitorBidDao.class)
                .eq(MonitorBidDao::getBuyItemCode, buyItemCode));
    }

    /**
     * 添加监标人信息，可能多条
     *
     * @param dto
     * @return
     */
    Boolean addMonitor(CreateMonitorDto dto);

    Boolean addMonitorInfo(CreateMonitorDto dto);

    Boolean editMonitor(UpdateMonitorDto dto);

    /**
     * 按项目编号查询监标人信息
     *
     * @param buyItemCode
     * @return
     */
    List<MonitorInfoVo> monitorInfo(String buyItemCode);

    IPage<MonitorVo> monitorPage(PageSortEntity<RetrieveMonitorDto> e);
}
