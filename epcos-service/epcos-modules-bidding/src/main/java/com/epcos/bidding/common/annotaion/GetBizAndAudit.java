package com.epcos.bidding.common.annotaion;

import com.epcos.bidding.common.AuditTypeEnum;
import org.springframework.core.annotation.Order;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 发起审批时，业务与审批信息
 *
 * <AUTHOR>
 */
@Order(570)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface GetBizAndAudit {

    GetCommon common() default @GetCommon;

    /**
     * 审批类型，如果不指定审批类型，则默认为 other 审批
     * AuditTypeEnum
     */
    AuditTypeEnum auditType() default AuditTypeEnum.OTHER;

    /**
     * 审批类型
     */
    String auditTypeEL() default "";

    /**
     * 用户id，如果为空，默认取绑定的用户id
     */
    String userIdEL() default "";

    /**
     * 业务id
     */
    String bizIdEL() default "";

    /**
     * 业务类型
     */
    String bizTypeEL() default "";

    /**
     * 审批忽略的字段
     * 首先会从 @GetAuditAttribute 获取审批参数，如果为空，然后再从 @GetBizAndAudit 获取业务参数
     */
    String[] ignoreField() default {};

}
