package com.epcos.bidding.common.aspects.filter.validator;

import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierDemoVideoDao;

import java.util.Optional;

/**
 * 演示视频已存在
 */
public class SupplierDemoVideoExistsValidator implements ResultPostHandlerFilterChain<SupplierDemoVideoDao> {
    @Override
    public void postHandler(AspectContext context, SupplierDemoVideoDao result) {
        Optional.ofNullable(result).ifPresent(SupplierDemoVideoDao::verifyVideoNotNull);
    }

}
