package com.epcos.bidding.purchase.claims.domain.dao;

import com.epcos.bidding.purchase.api.domian.cliams.TenderFileRejectionDao;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.util.List;

/**
 * 采购文件要求
 */
@Data
public class TenderFileRequirementDao implements Serializable {
    private static final long serialVersionUID = -7318802457576771788L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("标段编码")
    @Length(max = 64, message = "标段编码最长64字符")
    private String subpackageCode;

    @ApiModelProperty("属性")
    @Length(max = 500, message = "属性最长500字符")
    private String attribute;

    @ApiModelProperty("描述")
    @Length(max = 1000, message = "描述最长1000字符")
    private String description;

    @ApiModelProperty("驳回记录")
    private List<TenderFileRejectionDao> rejectionList;


}
