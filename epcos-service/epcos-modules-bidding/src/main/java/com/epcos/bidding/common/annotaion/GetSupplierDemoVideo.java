package com.epcos.bidding.common.annotaion;

import org.springframework.core.annotation.Order;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 获取演示视频
 * <AUTHOR>
 */
@Order(480)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface GetSupplierDemoVideo {

    GetCommon common() default @GetCommon;

    /**
     * 使用el获取
     */
    String supplierIdEL() default "";

}
