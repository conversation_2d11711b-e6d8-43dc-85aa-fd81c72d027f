package com.epcos.bidding.common.aspects.filter.validator;

import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import com.epcos.bidding.purchase.api.params.PurchaseBidOpeningVo;

/**
 * 不是唱标
 */
public class BidOpeningNotSingingValidator implements ResultPostHandlerFilterChain<PurchaseBidOpeningVo> {
    @Override
    public void postHandler(AspectContext context, PurchaseBidOpeningVo result) {
        result.verifyNotSinging();
    }
}
