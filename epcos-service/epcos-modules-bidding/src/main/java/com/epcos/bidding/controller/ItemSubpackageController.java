package com.epcos.bidding.controller;

import com.epcos.bidding.common.annotaion.GetCommon;
import com.epcos.bidding.common.annotaion.GetItem;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.common.aspects.vo.GetItemVo;
import com.epcos.bidding.purchase.api.params.vo.buyitem.BuyItemVo;
import com.epcos.bidding.purchase.project.IAllBuyItemInfoApi;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.vo.LabelJson;
import com.epcos.bidding.purchase.remote.RemoteToOtherServiceApi;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.utils.DateUtils;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.utils.StringUtils;
import com.epcos.common.core.web.domain.user.SysUser;
import com.epcos.common.log.annotation.Log;
import com.epcos.common.log.enums.BusinessType;
import com.epcos.common.redis.annotation.RedisLock;
import com.epcos.system.api.domain.SysOrganize;
import com.epcos.system.api.domain.assmble.vo.PurchaseMethodInfoVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @Package com.epcos.bidding.purchase.controller
 * @date 2023/8/12 13:38
 * @Copyright © 2023-2026 易建采科技（武汉）有限公司
 */
@RequestMapping("/itemSubpackage")
@RestController
@Slf4j
@Api(tags = "采购项目与标段--通用基础")
@RequiredArgsConstructor
public class ItemSubpackageController {

    private final ISubPackageApi subpackageApi;
    private final IAllBuyItemInfoApi allBuyItemInfoApi;
    private final RemoteToOtherServiceApi remoteToOtherServiceApi;

    @ApiOperation(value = "回显配置的采购方式")
    @ApiImplicitParam(name = "purchaseMethodCode", value = "采购方式code", paramType = "query", dataTypeClass = String.class)
    @GetMapping(value = "/queryPurchaseMethod")
    public R<PurchaseMethodInfoVo> queryPurchaseMethod(@RequestParam(value = "purchaseMethodCode", required = false) String purchaseMethodCode) {
        return R.ok(remoteToOtherServiceApi.getPurchaseMethodInfoToJson(purchaseMethodCode));
    }

    @ApiOperation(value = "查询某个项目的功能点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "buyItemCode", value = "采购项目编码", paramType = "query", dataTypeClass = String.class, required = true),
            @ApiImplicitParam(name = "belongRole", value = "所属角色[1-采购人，2-供应商，3-专家]", paramType = "query",
                    dataTypeClass = String.class)
    })
    @GetItem(belongRole = "#belongRole", common = @GetCommon(buyItemCodeEL = "#buyItemCode"))
    @GetMapping(value = "/queryItemFunctionInfo")
    public R<BuyItemVo> queryItemFunctionInfo(@RequestParam(value = "buyItemCode") String buyItemCode,
                                              @RequestParam(value = "belongRole", required = false) String belongRole) {
        GetItemVo vo = GetUtil.getItemVo();
        return R.ok(vo.getBuyItemVo());
    }

    @ApiOperation(value = "查询当前登录人所拥有的组织信息")
    @GetMapping(value = "/currentOrgInfo")
    public R<List<SysOrganize>> currentOrgInfo() {
        Long userId = SecurityUtils.getLoginUser().getUserId();
        if (Objects.isNull(userId)) {
            return R.fail("请登录重试");
        }
        return R.ok(remoteToOtherServiceApi.getOrgInfo(userId));
    }

    @ApiOperation(value = "查询当前组织下的所有人员")
    @ApiImplicitParam(name = "orgCode", value = "组织code", paramType = "query", dataTypeClass = String.class, required = true)
    @GetMapping(value = "/queryOrgPerson")
    public R<List<SysUser>> queryOrgPerson(@RequestParam(value = "orgCode") @NotBlank String orgCode) {
        return R.ok(remoteToOtherServiceApi.getOrgPerson(orgCode));
    }


    @ApiOperation(value = "添加标签")
    @Log(title = "添加标签[接口：addLabel]", businessType = BusinessType.INSERT)
    @PostMapping(value = "/addLabel")
    public R<Boolean> addLabel(@RequestBody @Validated LabelJson dto) {
        return R.ok(subpackageApi.addLabel(dto));
    }


    @ApiOperation(value = "删除标签")
    @Log(title = "删除标签[接口：delLabel]", businessType = BusinessType.DELETE)
    @PostMapping(value = "/delLabel")
    public R<Boolean> delLabel(@RequestBody @Validated LabelJson dto) {
        return R.ok(subpackageApi.delLabel(dto));
    }


    @ApiOperation(value = "究极删除接口 清除一切项目相关信息，无法恢复 慎用")
    @RedisLock(name = "delItem")
    @Log(title = "究极删除接口 清除一切项目相关信息，无法恢复 慎用[接口：delItem]", businessType = BusinessType.DELETE)
    @GetMapping(value = "/delItem")
    public R<Boolean> delItem(@RequestParam(value = "buyItemCode") String buyItemCode) {
        if (!StringUtils.hasText(buyItemCode)) {
            return R.fail("请输入有效参数");
        }
        log.error("{}于{}删除了项目{}", SecurityUtils.getNickName(), DateUtils.getTime(), buyItemCode);
        return R.ok(allBuyItemInfoApi.delAllBuyItemInfo(buyItemCode));
    }
}
