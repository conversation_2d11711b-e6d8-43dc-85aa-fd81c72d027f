package com.epcos.bidding.common.aspects.filter.validator;

import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierSignUpDao;

/**
 * 报名信息不是未通过状态
 */
public class SignUpNotReviewStatusPassedValidator implements ResultPostHandlerFilterChain<SupplierSignUpDao> {
    @Override
    public void postHandler(AspectContext context, SupplierSignUpDao result) {
        result.verifyNotReviewStatus();
    }
}
