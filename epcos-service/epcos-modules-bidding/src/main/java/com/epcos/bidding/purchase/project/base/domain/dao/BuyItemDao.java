package com.epcos.bidding.purchase.project.base.domain.dao;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.bidding.common.BaseDao;
import com.epcos.system.api.domain.assmble.vo.FunctionKV;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 采购项目 基础信息
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2023/8/12 11:13
 * @description 公用基础信息
 * @Copyright © 2023-2026 易建采科技（武汉）有限公司
 */
@Data
@ApiModel(description = " 采购项目 基础信息表")
@NoArgsConstructor
@TableName("purchase_buy_item")
public class BuyItemDao extends BaseDao implements Serializable {

    private static final long serialVersionUID = -1184858430217163721L;

    public BuyItemDao(String buyItemCode) {
        this.buyItemCode = buyItemCode;
    }

    @ApiModelProperty(value = "项目code 暂时用不到")
    private String itemCode;

    @ApiModelProperty(value = "采购项目code")
    private String buyItemCode;

    @ApiModelProperty(value = "采购项目名字")
    private String buyItemName;

    @ApiModelProperty(value = "采购方式code[存储 purchase_method表中的 purchase_method_code字段]")
    private String purchaseMethodCode;

    @ApiModelProperty(value = "采购方式类型[存储 purchase_method表中的 purchase_method_type字段]")
    private String purchaseMethodType;

    @ApiModelProperty(value = "采购方式名称[存储 purchase_method表中的 purchase_method_name字段]")
    private String purchaseMethodName;

    @ApiModelProperty(value = "招标项目创建时间的年月")
    private String yearMonthSplit;

    @ApiModelProperty(value = "完成并归档[0-未完成,1-已完成，未归档,2-已归档]")
    private Integer end;

    @ApiModelProperty(value = "项目点击完成的时间，不是归档完成时间")
    private Date endTime;

    @ApiModelProperty(value = "采购时间点json")
    private String purchaseTimeJson;

    @ApiModelProperty(value = "采购公示公告json")
    private String purchaseBulletinJson;

    @ApiModelProperty(value = "采购功能点json")
    private String purchaseFunctionJson;

    @ApiModelProperty(value = "创建人id")
    private Long userId;

    @ApiModelProperty(value = "部门id（后台配置的部门）")
    private Long deptId;
//
//    @ApiModelProperty(value = "角色id")
//    private Long roleId;

    @ApiModelProperty(value = "创建人组织代码")
    private String orgCode;


    /**
     * 解析采购功能json
     *
     * @return
     */
    public List<FunctionKV> parsePurchaseFunctionJson() {
        return JSONArray.parseArray(this.purchaseFunctionJson, FunctionKV.class);
    }


    /**
     * @param belongRole 所属角色[1-采购人，2-供应商，3-专家]
     * @return
     */
    public List<FunctionKV> parsePurchaseFunctionJsonByRole(String belongRole) {
        return this.parsePurchaseFunctionJson()
                .stream()
                .filter(f -> f.getBelongRole().contains(belongRole))
                .collect(Collectors.toList());
    }
}
