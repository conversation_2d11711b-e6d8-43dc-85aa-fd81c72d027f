package com.epcos.bidding.purchase.ev.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.utils.BiddingBaseUtil;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.purchase.project.smart.business.api.buyitem.SmartBuyItemApi;
import com.epcos.bidding.purchase.project.smart.domain.dao.BuyItemSmartDao;
import com.epcos.bidding.workbench.vo.SpecialFieldVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.epcos.common.core.enums.ClientEnum.SMART;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/21 15:39
 */
@Slf4j
@Service("smart")
public class SmartEvService extends AbEvService {

    private final SmartBuyItemApi smartBuyItemApi;

    public SmartEvService(IBuyItemApi buyItemApi, ISubPackageApi subPackageApi, SmartBuyItemApi smartBuyItemApi) {
        super(buyItemApi, subPackageApi);
        this.smartBuyItemApi = smartBuyItemApi;
    }

    @Override
    public String ev() {
        return SMART.getCode();
    }

    @Override
    public boolean isDing() {
        return false;
    }


    @Override
    public String getTableName() {
        return "purchase_smart_buy_item";
    }

    @Override
    public Map<String, String> queryBuyItemMap(Map<String, String> voMap, BuyItemDao buyItemInfo) {
        voMap = BiddingBaseUtil.convert(smartBuyItemApi.findOneByBuyItemCode(buyItemInfo.getBuyItemCode()));
        return voMap;
    }

    @Override
    public List<SpecialFieldVo> queryDiffInfo(List<String> buyItemCodes, Map<String, List<SubpackageDao>> subMap, List<SpecialFieldVo> voList) {
        List<BuyItemSmartDao> smartDaoList = smartBuyItemApi.findOneByBuyItemCodeList(buyItemCodes.stream().collect(Collectors.toList()));
        smartDaoList.forEach(dao -> subMap.get(dao.getBuyItemCode())
                .forEach(sub -> voList.add(new SpecialFieldVo(dao.getBuyItemCode(), sub.getSubpackageCode(), null, dao.getBuyClass(), null))));
        return voList;
    }

    @Override
    public Boolean delBuyItem(String buyItemCode, List<String> subpackageCodeList) {
        return smartBuyItemApi.delSmartBuyItemInfo(buyItemCode);
    }


    @Override
    public List<String> queryAllBuyItemCode() {
        List<BuyItemSmartDao> bjxkItemList = smartBuyItemApi.list(Wrappers.lambdaQuery(BuyItemSmartDao.class)
                .select(BuyItemSmartDao::getBuyItemCode));
        if (CollectionUtils.isEmpty(bjxkItemList)) {
            return Collections.emptyList();
        }
        List<String> codeList = bjxkItemList.stream().map(BuyItemSmartDao::getBuyItemCode).collect(Collectors.toList());
        return codeList;
    }
}
