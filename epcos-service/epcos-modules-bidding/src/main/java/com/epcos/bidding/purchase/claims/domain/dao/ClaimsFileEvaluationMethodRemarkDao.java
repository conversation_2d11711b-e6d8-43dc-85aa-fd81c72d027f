package com.epcos.bidding.purchase.claims.domain.dao;

import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.bidding.common.utils.FileEvaluationMethodRemarkEntityShared;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@ApiModel(description = "采购文件评审办法备注")
@TableName("claims_file_evaluation_method_remark")
public class ClaimsFileEvaluationMethodRemarkDao extends FileEvaluationMethodRemarkEntityShared {


    @Override
    public String toString() {
        return "ClaimsFileEvaluationMethodRemarkDao{" +
                "subpackageCode='" + subpackageCode + '\'' +
                ", createAt=" + createAt +
                ", createBy='" + createBy + '\'' +
                ", deleted=" + deleted +
                ", id=" + id +
                ", updateAt=" + updateAt +
                ", updateBy='" + updateBy + '\'' +
                '}';
    }

    public ClaimsFileEvaluationMethodRemarkDao(String subpackageCode, String remark) {
        this.subpackageCode = subpackageCode;
        this.remark = remark;
    }

    public ClaimsFileEvaluationMethodRemarkDao(String subpackageCode) {
        this.subpackageCode = subpackageCode;
    }
}
