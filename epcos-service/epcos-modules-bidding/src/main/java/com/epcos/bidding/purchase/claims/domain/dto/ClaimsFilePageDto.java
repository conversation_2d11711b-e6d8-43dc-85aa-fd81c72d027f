package com.epcos.bidding.purchase.claims.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(description = "查询采购项目下包")
public class ClaimsFilePageDto {

    @ApiModelProperty("采购项目code")
    @NotBlank(message = "采购项目code,必填")
    private String buyItemCode;

    @ApiModelProperty("需要排除的包code")
    private String subpackageCode;


}
