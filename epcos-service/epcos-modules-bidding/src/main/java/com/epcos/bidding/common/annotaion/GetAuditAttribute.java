package com.epcos.bidding.common.annotaion;

import com.epcos.bidding.common.aspects.convert.GetParamConvert;
import org.springframework.core.annotation.Order;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 获取类中字段，返回一个个描述对象
 * 默认取 @ApiModelProperty 中的 value 值
 *
 * <AUTHOR>
 */
@Order(550)
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface GetAuditAttribute {

    GetCommon common() default @GetCommon;

    /**
     * 忽略的字段
     */
    String[] ignoreField() default {};

    /**
     * 复杂类，自行实现
     */
    Class<? extends GetParamConvert> toAuditAttributeConvert() default GetParamConvert.class;

}
