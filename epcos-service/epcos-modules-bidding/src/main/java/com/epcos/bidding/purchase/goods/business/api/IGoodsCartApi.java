package com.epcos.bidding.purchase.goods.business.api;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.epcos.bidding.common.IBaseService;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.goods.domain.dao.GoodsCartDao;
import com.epcos.bidding.purchase.goods.domain.dto.AddCartDetail;
import com.epcos.bidding.purchase.goods.domain.dto.AddCartDto;
import com.epcos.bidding.purchase.goods.domain.vo.GoodsCartVo;
import com.epcos.bidding.purchase.goods.domain.vo.GoodsParamsVo;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/12 17:35
 */
public interface IGoodsCartApi extends IBaseService<GoodsCartDao> {

    @Override
    default LambdaQueryWrapper<GoodsCartDao> queryWrapper(GoodsCartDao dao) {
        return null;
    }

    Boolean addCart(AddCartDto dto);

    IPage<GoodsCartVo> cartList(PageSortEntity<GoodsCartDao> dto);

    Boolean updateCart(List<AddCartDetail> dto);

    Boolean delCart(List<Long> cartIdList);

    List<GoodsParamsVo> cartInfo(List<Long> cartIdList);
}
