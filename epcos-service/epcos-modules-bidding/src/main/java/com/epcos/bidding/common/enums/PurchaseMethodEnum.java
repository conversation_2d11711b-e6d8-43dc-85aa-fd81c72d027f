package com.epcos.bidding.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/12 8:56
 */
@Getter
public enum PurchaseMethodEnum {

    PUBLIC_BIDDING("publicBidding", "公开招标"),
    INVITED_BIDDING("invitedBidding", "邀请招标"),
    COMPETITIVE_NEGOTIATION("competitiveNegotiation", "竞争性谈判"),
    COMPETITIVE_CONSULTATION("competitiveConsultation", "竞争性磋商"),
    PRE_QUALIFICATION("preQualification", "资格预审"),
    SURVEY("survey", "市场调研论证"),
    SELF_PROCUREMENT("selfProcurement", "医院自采"),
    LG_GKZB("lg-gkzb", "公开招标"),
    LG_YQZB("lg-yqzb", "邀请招标"),
    LG_JZXTP("lg-jzxtp", "竞争性谈判"),
    lg_zjcg("lg-zjcg", "直接采购"),
    lg_cfcg("lg-cfcg", "重复采购"),
    lg_ddcg("lg-ddcg", "定点采购"),
    ;


    private final String key;
    private final String desc;

    PurchaseMethodEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public static String getDesc(String key) {
        // 遍历枚举值，匹配 key 后返回 desc
        for (PurchaseMethodEnum value : PurchaseMethodEnum.values()) {
            if (value.getKey().equals(key)) {
                return value.getDesc();
            }
        }
        // 若未匹配到，返回 null（或根据需求返回默认值/抛异常）
        return null;
    }
}
