package com.epcos.bidding.purchase.win.domain.dto;

import com.epcos.bidding.purchase.api.params.vo.buyitem.SuperPackageVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/13 11:19
 */
@Data
public class ResultNoticeDto extends SuperPackageVo implements Serializable {


    @ApiModelProperty(value = "采购项目编号")
    private String buyItemCode;

    @ApiModelProperty(value = "供应商id")
    @NotNull
    private Long supplierId;

    @ApiModelProperty(value = "投标单位")
    @NotNull
    private String supplierCompanyName;

    @ApiModelProperty(value = "中标通知书类型[0-未中标，1-中标")
    private String isWin;

    /**
     * 重新发送结果公告
     */
    @ApiModelProperty(value = "是否是重新发, true 表示重新发送")
    private Boolean isAgain = false;

    @ApiModelProperty(value = "公告模板H5")
    @NotBlank
    private String noticeTemplate;
}
