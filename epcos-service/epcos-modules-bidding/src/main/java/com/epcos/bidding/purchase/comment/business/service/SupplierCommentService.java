package com.epcos.bidding.purchase.comment.business.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.purchase.api.params.dto.BaseQueryDto;
import com.epcos.bidding.purchase.comment.business.api.ISupplierCommentApi;
import com.epcos.bidding.purchase.comment.domain.dao.SupplierCommentDao;
import com.epcos.bidding.purchase.comment.repository.ISupplierCommentMapper;
import com.epcos.bidding.workbench.vo.BidOpenTodayVo;
import com.epcos.common.core.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/19 17:25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierCommentService extends ServiceImpl<ISupplierCommentMapper, SupplierCommentDao> implements ISupplierCommentApi {

    private final ISupplierCommentMapper supplierCommentMapper;

    @Override
    public LambdaQueryWrapper<SupplierCommentDao> queryWrapper(SupplierCommentDao dao) {
        return null;
    }

    @Override
    public IPage<BidOpenTodayVo> supplierCommentPage(Page<Object> of, Boolean status, Integer month, BaseQueryDto baseQueryDto) {
        String nowDate = DateUtils.getTime();
        if (status) {
            //代办
            return supplierCommentMapper.supplierCommentPageWait(of, nowDate, month, baseQueryDto);
        }
        //已办
        return supplierCommentMapper.supplierCommentPage(of, baseQueryDto);
    }
}
