package com.epcos.bidding.purchase.project.base.business.service;

import com.epcos.bidding.common.utils.BiddingBaseUtil;
import com.epcos.bidding.purchase.api.params.dto.SubPackageBaseDto;
import com.epcos.bidding.purchase.api.params.vo.buyitem.BuyItemVo;
import com.epcos.bidding.purchase.claims.business.service.ClaimsFileQuoteFormService;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.business.api.IPublicBaseApi;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.base.domain.dto.BuyItemBaseDto;
import com.epcos.bidding.purchase.project.mapping.BuyItemConvert;
import com.epcos.bidding.purchase.remote.RemoteSupplierApi;
import com.epcos.bidding.purchase.remote.RemoteToOtherServiceApi;
import com.epcos.bidding.purchase.technology.business.api.BuyItemParamApi;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierProcessNodeDao;
import com.epcos.common.core.constant.PurchaseConstants;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.system.api.domain.assmble.vo.PurchaseMethodInfoVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 公用基础信息
 * @date 2024/4/23 12:35
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PublicBaseService implements IPublicBaseApi {

    private final IBuyItemApi buyItemApi;
    private final ISubPackageApi subPackageApi;
    private final BuyItemParamApi buyItemParamApi;
    private final ClaimsFileQuoteFormService claimsFileQuoteFormService;
    private final RemoteSupplierApi remoteSupplierApi;
    private final RemoteToOtherServiceApi remoteToOtherServiceApi;

    @Override
    public BuyItemDao insertBaseInfo(BuyItemBaseDto baseDto, List<SubPackageBaseDto> subpackageDtoList) {
        //保存基本项目信息
        PurchaseMethodInfoVo purchaseMethodInfoToJson =
                remoteToOtherServiceApi.getPurchaseMethodInfoToJson(baseDto.getPurchaseMethodCode());
        BuyItemDao buyItemDao = buyItemApi.insertBuyItemInfo(baseDto, purchaseMethodInfoToJson);
        // 保存标段及 标段以下 的 信息
        List<String> subpackageCodes = subPackageApi.addSubInfo(subpackageDtoList, buyItemDao.getBuyItemCode());
        //保存项目流程节点
        BuyItemVo buyItemVo = BuyItemConvert.INSTANCE.convert_1(buyItemDao);
        buyItemVo.setFunctionKVList(buyItemDao.parsePurchaseFunctionJsonByRole(PurchaseConstants.UserType.PURCHASER));
        subpackageCodes.forEach(subpackageCode -> {
            SupplierProcessNodeDao process = BiddingBaseUtil.process(
                    buyItemVo,
                    subpackageCode,
                    SecurityUtils.getUserId(),
                    PurchaseConstants.UserType.PURCHASER);
            if (!CollectionUtils.isEmpty(process.getNodes())) {
                remoteSupplierApi.addProcess(process);
            }
        });
        return buyItemDao;
    }

    @Override
    public BuyItemDao findOneByBuyItemCode(String buyItemCode) {
        return buyItemApi.getOne(buyItemApi.queryWrapper(buyItemCode));
    }

    @Override
    public BuyItemDao updateBaseInfo(BuyItemBaseDto dto, List<SubPackageBaseDto> subpackageDtoList) {
        //保存基本项目信息
        PurchaseMethodInfoVo purchaseMethodInfoToJson =
                remoteToOtherServiceApi.getPurchaseMethodInfoToJson(dto.getPurchaseMethodCode());
        //修改基本项目信息
        BuyItemDao buyItemDao = buyItemApi.updateBaseInfo(dto, purchaseMethodInfoToJson);
        //先删除后保存项目参数
        buyItemParamApi.deleteByItemCode(dto.getBuyItemCode());
        //先删除后保存标段信息
        List<String> subpackageCodeList = subPackageApi.delSubpackage(dto.getBuyItemCode());
        //删除 报价信息
        claimsFileQuoteFormService.delQuote(subpackageCodeList);
        // 保存标段及 标段以下 的 信息
        subPackageApi.addSubInfo(subpackageDtoList, dto.getBuyItemCode());
        return buyItemDao;
    }

    @Override
    public Boolean delBuyItemInfo(String buyItemCode) {
        return buyItemApi.remove(buyItemApi.updateWrapper(buyItemCode));
    }
}
