package com.epcos.bidding.common.aspects.filter.validator;

import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.filter.ResultPostHandlerFilterChain;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierBiddingDao;
import com.epcos.common.core.exception.ServiceException;

import java.util.Optional;

/**
 * 响应文件为空
 */
public class SupplierBiddingIsNullValidator implements ResultPostHandlerFilterChain<SupplierBiddingDao> {
    @Override
    public void postHandler(AspectContext context, SupplierBiddingDao result) {
        Optional.ofNullable(result)
                .orElseThrow(() -> new ServiceException("响应文件为空"));
    }
}
