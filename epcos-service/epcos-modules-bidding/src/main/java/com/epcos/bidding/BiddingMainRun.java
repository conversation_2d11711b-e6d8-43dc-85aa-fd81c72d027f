package com.epcos.bidding;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.BlockAttackInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.epcos.common.core.context.SecurityContextHolder;
import com.epcos.common.core.utils.EvUtils;
import com.epcos.common.security.config.ApplicationConfig;
import com.epcos.common.security.feign.FeignAutoConfiguration;
import com.epcos.common.swagger.config.SwaggerAutoConfiguration;
import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import feign.Logger;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.aop.interceptor.PerformanceMonitorInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.task.TaskExecutorCustomizer;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.*;
import org.springframework.core.env.Environment;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.FormHttpMessageConverter;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ThreadPoolExecutor;


@Slf4j
@EnableAsync
@EnableScheduling
@SpringBootApplication
@MapperScan(value = "com.epcos.bidding.**.repository")
@EnableAspectJAutoProxy(exposeProxy = true)
@EnableFeignClients(basePackages = "com.epcos.**.api")
@Import({ApplicationConfig.class, FeignAutoConfiguration.class, SwaggerAutoConfiguration.class})
public class BiddingMainRun {

    @Value("${environment}")
    private String active;
    @Autowired
    private Environment environment;

    public static void main(String[] args) {
        SpringApplication.run(BiddingMainRun.class, args);
    }

    @PostConstruct
    public void log() {
        log.error("激活的环境类型：{}", active);
    }

    @Bean
    @Profile(value = "dev")
    public Logger.Level feignLog() {
        return Logger.Level.FULL;
    }

    @Bean
    @Primary
    public TaskExecutorCustomizer taskExecutorCustomizer() {
        return taskExecutor -> {
            int core = Runtime.getRuntime().availableProcessors() + 1;
            taskExecutor.setCorePoolSize((int) (core * 1.5));
            taskExecutor.setMaxPoolSize(core * 3);
            taskExecutor.setKeepAliveSeconds(600);
            taskExecutor.setQueueCapacity(1000);
            taskExecutor.setThreadNamePrefix("bidding-async-");
            taskExecutor.setAllowCoreThreadTimeOut(false);
            taskExecutor.setPrestartAllCoreThreads(false);
            taskExecutor.setWaitForTasksToCompleteOnShutdown(true);
            taskExecutor.setAwaitTerminationSeconds(10);
            taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
            taskExecutor.setTaskDecorator(runnable -> {
                Map<String, Object> localMap = SecurityContextHolder.getLocalMap();
                return () -> {
                    try {
                        SecurityContextHolder.setLocalMap(localMap);
                        runnable.run();
                    } finally {
                        SecurityContextHolder.remove();
                    }
                };
            });
        };
    }

    private static final Set<String> trueValues = new HashSet<>(2);

    private static final Set<String> falseValues = new HashSet<>(2);

    static {
        trueValues.add("true");
//        trueValues.add("on");
//        trueValues.add("yes");
        trueValues.add("1");

        falseValues.add("false");
//        falseValues.add("off");
//        falseValues.add("no");
        falseValues.add("0");
    }

    private Boolean convert(String source) {
        if (CharSequenceUtil.isEmpty(source)) {
            return null;
        }
        String value = source.trim();
        if (value.isEmpty()) {
            return null;
        }
        value = value.toLowerCase();
        if (trueValues.contains(value)) {
            return Boolean.TRUE;
        } else if (falseValues.contains(value)) {
            return Boolean.FALSE;
        } else {
            throw new IllegalArgumentException("Invalid boolean value '" + source + "'");
        }
    }


    //    @Bean
    public SimpleModule stringAndBooleanModule() {
        SimpleModule simpleModule = new SimpleModule();
        // obj == >> str
        JsonSerializer<Boolean> booleanToStringSerializer = new JsonSerializer<Boolean>() {
            @Override
            public void serialize(Boolean value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
                gen.writeString(Boolean.TRUE.equals(value) ? "1" : "0");
            }
        };
        simpleModule.addSerializer(Boolean.class, booleanToStringSerializer);
        // str == >> obj
        JsonDeserializer<Boolean> stringToBooleanDeserializer = new JsonDeserializer<Boolean>() {
            @Override
            public Boolean deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JacksonException {
                return convert(p.getText());
            }
        };
        simpleModule.addDeserializer(Boolean.class, stringToBooleanDeserializer);
        return simpleModule;
    }

    @Bean
    public PaginationInnerInterceptor paginationInnerInterceptor() {
        PaginationInnerInterceptor paginationInnerInterceptor = new PaginationInnerInterceptor(DbType.MYSQL);
        paginationInnerInterceptor.setOptimizeJoin(true);
        return paginationInnerInterceptor;
    }

    @Bean
    public BlockAttackInnerInterceptor blockAttackInnerInterceptor() {
        return new BlockAttackInnerInterceptor();
    }

    @Bean
    @Profile(value = "dev")
    public PerformanceMonitorInterceptor performanceMonitorInterceptor() {
        return new PerformanceMonitorInterceptor();
    }

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor(PaginationInnerInterceptor paginationInnerInterceptor,
                                                         BlockAttackInnerInterceptor blockAttackInnerInterceptor) {
        MybatisPlusInterceptor plusInterceptor = new MybatisPlusInterceptor();
        plusInterceptor.addInnerInterceptor(blockAttackInnerInterceptor);
        plusInterceptor.addInnerInterceptor(paginationInnerInterceptor);
        return plusInterceptor;
    }

    @Bean
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(60000);
        requestFactory.setReadTimeout(60000);

        RestTemplate restTemplate = new RestTemplate(requestFactory);
        List<HttpMessageConverter<?>> list = restTemplate.getMessageConverters();

        // 设置 restTemplate FormHttpMessageConverter 编码方式
        for (HttpMessageConverter<?> httpMessageConverter : list) {
            if (httpMessageConverter instanceof FormHttpMessageConverter) {
                FormHttpMessageConverter httpMessageConverter1 = (FormHttpMessageConverter) httpMessageConverter;
                httpMessageConverter1.setCharset(StandardCharsets.UTF_8);
                httpMessageConverter1.setMultipartCharset(StandardCharsets.UTF_8);
            }
        }
        return restTemplate;
    }


    /**
     * 在应用启动后初始化 EvUtils
     */
    @PostConstruct
    public void initEvUtils() {
        EvUtils.init(environment, active);
        System.out.println("EvUtils 初始化完成！");
    }

}
