package com.epcos.bidding.common.aspects.function;

import cn.hutool.core.collection.CollUtil;
import com.epcos.bidding.common.annotaion.HasFunction;
import com.epcos.bidding.common.aspects.AbstractMethod;
import com.epcos.bidding.common.aspects.AspectContext;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.common.aspects.params.GetItemParam;
import com.epcos.bidding.common.aspects.vo.GetItemVo;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.system.api.domain.assmble.vo.FunctionKV;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Aspect
@Component
public class HasFunctionAspect extends AbstractMethod<HasFunction, Boolean> {

    @Autowired
    private IBuyItemApi buyItemApi;

    protected HasFunctionAspect() {
        super(GetUtil.HAS_FUNCTION, "查询是否有对应功能点");
    }

    @Override
    @Around(value = "@annotation(hasFunction)")
    public Object around(ProceedingJoinPoint point, HasFunction hasFunction) {
        return super.around(point, hasFunction);
    }

    @Override
    public Boolean businessMethods(JoinPoint point, HasFunction annotation) {
        AspectContext context = threadLocal.get();
        String buyItemCode = context.getBuyItemCode();
        String subpackageCode = context.getSubpackageCode();
        if (!StringUtils.hasText(buyItemCode) && !StringUtils.hasText(subpackageCode)) {
            throw new ServiceException("查询是否有对应功能点, buyItemCode | subpackageCode 必填");
        }
        return checkHasFunction(annotation, buyItemCode, subpackageCode);
    }

    private boolean checkHasFunction(HasFunction annotation, String buyItemCode, String subpackageCode) {
        GetItemVo itemVo = buyItemApi.getItemVo(new GetItemParam(buyItemCode, subpackageCode, annotation.belongRole(), false));
        if (Objects.isNull(itemVo) || Objects.isNull(itemVo.getBuyItemVo()) || CollUtil.isEmpty(itemVo.getBuyItemVo().getFunctionKVList())) {
            throw new ServiceException("查询是否有对应功能点, 项目信息中功能点为空");
        }
        return itemVo.getBuyItemVo().getFunctionKVList().stream()
                .map(FunctionKV::getPurchaseFunctionKey)
                .collect(Collectors.toList()).contains(annotation.functionEnum().getKey());
    }

}
