package com.epcos.bidding.purchase.extract.business.api;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.audit.api.AuditProcessDto;
import com.epcos.bidding.common.IBaseService;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.purchase.api.domian.judge.judgeProjectVo;
import com.epcos.bidding.purchase.api.params.JudgesVo;
import com.epcos.bidding.purchase.api.params.dto.BaseQueryDto;
import com.epcos.bidding.purchase.api.params.vo.buyitem.ItemSubpackageVo;
import com.epcos.bidding.purchase.extract.domain.dao.ExtractJudgeInnerDao;
import com.epcos.bidding.purchase.extract.domain.dto.ImportJudgeDto;
import com.epcos.bidding.purchase.extract.domain.dto.JudgeBatchDto;
import com.epcos.bidding.purchase.extract.domain.dto.JudgeExtractDto;
import com.epcos.bidding.purchase.extract.domain.vo.ExtractBuyItemVo;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.web.domain.user.SysUser;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/11 16:47
 */
public interface IExtractJudgeInnerApi extends IBaseService<ExtractJudgeInnerDao> {

    @Override
    default LambdaQueryWrapper<ExtractJudgeInnerDao> queryWrapper(ExtractJudgeInnerDao dao) {
        LambdaQueryWrapper<ExtractJudgeInnerDao> query = Wrappers.lambdaQuery(ExtractJudgeInnerDao.class);
        if (Objects.nonNull(dao.getId())) {
            return query.eq(ExtractJudgeInnerDao::getId, dao.getId());
        }
        return query.eq(StringUtils.hasText(dao.getSubpackageCode()), ExtractJudgeInnerDao::getSubpackageCode, dao.getSubpackageCode())
                .eq(StringUtils.hasText(dao.getBuyItemCode()), ExtractJudgeInnerDao::getBuyItemCode, dao.getBuyItemCode())
                .eq(Objects.nonNull(dao.getJudgeId()), ExtractJudgeInnerDao::getJudgeId, dao.getJudgeId())
                .eq(StringUtils.hasText(dao.getDeptRepresent()), ExtractJudgeInnerDao::getDeptRepresent, dao.getDeptRepresent())
                .eq(StringUtils.hasText(dao.getWhetherGroup()), ExtractJudgeInnerDao::getWhetherGroup, dao.getWhetherGroup())
                .eq(StringUtils.hasText(dao.getWhetherSign()), ExtractJudgeInnerDao::getWhetherSign, dao.getWhetherSign())
                .eq(StringUtils.hasText(dao.getStatus()), ExtractJudgeInnerDao::getStatus, dao.getStatus());
    }

    default LambdaQueryWrapper<ExtractJudgeInnerDao> queryWrapper(String subpackageCode) {
        return queryWrapper(subpackageCode, null);
    }

    default LambdaQueryWrapper<ExtractJudgeInnerDao> queryWrapper(String subpackageCode, Long judgeId) {
        ExtractJudgeInnerDao dao = new ExtractJudgeInnerDao();
        dao.setSubpackageCode(subpackageCode);
        dao.setJudgeId(judgeId);
        return queryWrapper(dao);
    }

    default LambdaUpdateWrapper<ExtractJudgeInnerDao> updateWrapper(String subpackageCode, Long judgeId) {
        return Wrappers.lambdaUpdate(ExtractJudgeInnerDao.class)
                .eq(StringUtils.hasText(subpackageCode), ExtractJudgeInnerDao::getSubpackageCode, subpackageCode)
                .eq(Objects.nonNull(judgeId), ExtractJudgeInnerDao::getJudgeId, judgeId);
    }

    default List<ExtractJudgeInnerDao> findBySubpackageCode(String subpackageCode) {
        return list(queryWrapper(subpackageCode, null));
    }

    default List<ExtractJudgeInnerDao> findBySubpackageCodes(List<String> subpackageCodeList) {
        return list(Wrappers.lambdaQuery(ExtractJudgeInnerDao.class)
                .in(CollectionUtils.isEmpty(subpackageCodeList), ExtractJudgeInnerDao::getSubpackageCode, subpackageCodeList));
    }

    default ExtractJudgeInnerDao findBySubpackageCodeAndJudgeId(String subpackageCode, Long judgeId) {
        return getOne(queryWrapper(subpackageCode, judgeId));
    }

    default List<ExtractJudgeInnerDao> find(String buyItemCode) {
        ExtractJudgeInnerDao dao = new ExtractJudgeInnerDao();
        dao.setBuyItemCode(buyItemCode);
        return list(queryWrapper(dao));
    }

    default List<ExtractJudgeInnerDao> findBySubCode(String subpackageCode) {
        return list(Wrappers.lambdaQuery(ExtractJudgeInnerDao.class)
                .eq(ExtractJudgeInnerDao::getSubpackageCode, subpackageCode))
                ;
    }

    default Boolean del(String buyItemCode) {
        return remove(queryWrapper(buyItemCode));
    }

    default List<ExtractJudgeInnerDao> findByJudgeId(Long judgeId) {
        ExtractJudgeInnerDao dao = new ExtractJudgeInnerDao();
        dao.setJudgeId(judgeId);
        return list(queryWrapper(dao));
    }

    List<SysUser> queryLogJudge(ImportJudgeDto dto);

    AuditProcessDto queryJudgeAudit(String buyItemCode, String subpackageCode, Long id);

    List<ItemSubpackageVo> queryJudgeList(String buyItemCode);

    List<JudgesVo> queryBySub(String subpackageCode);

    void saveExtractJudgeInfo(JudgeExtractDto dto);

    /**
     * @param status         评委状态[0-未评标 1-评标 2-评标完成]
     * @param subpackageCode 包code
     */
    void updateBy(String status, String subpackageCode);

    void deptRepresent(Long judgeId, String deptRepresent, String subpackageCode);

    void rollbackData(String subpackageCode);

    IPage<ExtractBuyItemVo> queryExtractBuyItemInfo(PageSortEntity<BaseQueryDto> dto);

    void batchSaveExtractJudgeInfo(JudgeBatchDto dto);

    /**
     * 统计专家参与项目次数
     *
     * @param judgeIdList@return
     */
    R<Map<Long, judgeProjectVo>> getProjectNum(List<Long> judgeIdList);
}
