package com.epcos.bidding.purchase.extract.domain.dto;

import com.epcos.bidding.purchase.extract.domain.vo.ExtractLogVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/20 17:03
 */
@Data
public class JudgeBatchSubDto {

    @ApiModelProperty(value = "采购项目编号")
    private String buyItemCode;

    @ApiModelProperty(value = "包code")
    private String subpackageCode;

    @ApiModelProperty(value = "专家信息")
    private List<ExtractLogVo> voList;
}
