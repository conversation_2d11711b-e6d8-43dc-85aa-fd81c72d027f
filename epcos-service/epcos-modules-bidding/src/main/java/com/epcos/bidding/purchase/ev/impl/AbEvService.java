package com.epcos.bidding.purchase.ev.impl;

import com.epcos.bidding.purchase.ev.EvService;
import com.epcos.bidding.purchase.project.base.business.api.IBuyItemApi;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.bidding.purchase.project.base.domain.dao.BuyItemDao;
import com.epcos.bidding.purchase.project.base.domain.dao.SubpackageDao;
import com.epcos.bidding.workbench.vo.SpecialFieldVo;
import com.epcos.system.api.domain.assmble.vo.FunctionKV;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.epcos.bidding.common.enums.BulletinTypeEnum.*;
import static com.epcos.bidding.common.enums.FunctionEnum.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/21 16:26
 */
@Slf4j
@RequiredArgsConstructor
public abstract class AbEvService implements EvService {

    private final IBuyItemApi buyItemApi;
    private final ISubPackageApi subPackageApi;

    /**
     * 采购公告是单独一个接口，所以可以单独判断
     * 其他所有公告均为一个接口，所以需要判断bulletinType,即公告类型
     * 先判断公告类型，再判断功能权限
     *
     * @param functionKVList 功能权限列表
     * @param bulletinType   公告类型
     * @return
     */

    @Override
    public boolean isAudit(List<FunctionKV> functionKVList, String bulletinType) {
        List<String> keyList = functionKVList.stream()
                .map(FunctionKV::getPurchaseFunctionKey)
                .collect(Collectors.toList());
        if (BIDDING_ANNOUNCEMENT.getKey().equals(bulletinType)) {
            if (keyList.contains(PURCHASER_TENDER_BULLETIN_NOT_APPROVED.getKey())) {
                return false;
            }
        }
        if (WIN_CANDIDATE_PUBLICITY.getKey().equals(bulletinType)) {
            if (keyList.contains(PURCHASER_CANDIDATE_PUBLICITY_NOT_APPROVED.getKey())) {
                return false;
            }
        }
        if (WIN_RESULT_PUBLICITY.getKey().equals(bulletinType)) {
            if (keyList.contains(PURCHASER_RESULT_PUBLICITY_NOT_APPROVED.getKey())) {
                return false;
            }
        }
        //不是招标公告、成交结果公示、成交结果候选人公示时，就走公告公示的逻辑
        if (!WIN_RESULT_PUBLICITY.getKey().equals(bulletinType)
                && !WIN_CANDIDATE_PUBLICITY.getKey().equals(bulletinType)
                && !BIDDING_ANNOUNCEMENT.getKey().equals(bulletinType)) {
            if (keyList.contains(PURCHASER_BULLETIN_NOT_APPROVED.getKey())) {
                return false;
            }
        }
        return true;
    }

    public List<SpecialFieldVo> queryDiffInfo(Set<String> buyItemCodes, Set<String> subpackageCodes) {

        if (CollectionUtils.isEmpty(buyItemCodes) && CollectionUtils.isEmpty(subpackageCodes)) {
            return Collections.EMPTY_LIST;
        }
        if (CollectionUtils.isEmpty(buyItemCodes)) {
            buyItemCodes = subPackageApi.findBySubpackageCodes(subpackageCodes.stream().collect(Collectors.toList()))
                    .stream()
                    .map(SubpackageDao::getBuyItemCode)
                    .collect(Collectors.toSet());
        }
        List<SubpackageDao> subpackageDaoList = subPackageApi.packageListInfo(buyItemCodes);
        Map<String, List<SubpackageDao>> subMap = subpackageDaoList.stream()
                .collect(Collectors.groupingBy(SubpackageDao::getBuyItemCode));
        List<SpecialFieldVo> voList = new ArrayList<>();
        return queryDiffInfo(buyItemCodes.stream()
                .collect(Collectors.toList()), subMap, voList);
    }

    public Map<String, String> queryBuyItemMap(String buyItemCode) {
        Map<String, String> voMap = new LinkedHashMap<>();
        BuyItemDao buyItemInfo = buyItemApi.findBuyItemInfo(buyItemCode);
        return queryBuyItemMap(voMap, buyItemInfo);
    }

    protected abstract List<SpecialFieldVo> queryDiffInfo(List<String> buyItemCodes,
                                                          Map<String, List<SubpackageDao>> subMap,
                                                          List<SpecialFieldVo> voList);

    protected abstract Map<String, String> queryBuyItemMap(Map<String, String> voMap, BuyItemDao buyItemInfo);
}
