package com.epcos.review.business.api;

import com.baomidou.mybatisplus.extension.service.IService;
import com.epcos.common.core.domain.review.IncompleteJudgeVo;
import com.epcos.common.core.domain.review.SupplierDetailsVo;
import com.epcos.review.domain.dao.RecordDao;
import com.epcos.review.domain.dao.SubpackageDao;
import com.epcos.review.domain.dao.SupplierDao;
import com.epcos.review.domain.dto.SubmitResultDto;
import com.epcos.review.domain.vo.NextStepVo;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 */
public interface IRecordApi extends IService<RecordDao> {
    /**
     * @param isType 0 合格制  1 打分制
     * @param subpackageCode 标段
     * @param judgesId 评委ID
     * @param supplierId 供应商ID
     * @return 评审记录
     */
    RecordDao queryReviewRecord(Integer isType, String subpackageCode, Long judgesId, Long supplierId);

    /**
     * 合格制评审结果汇总
     * @param subpackageCode 标段code
     * @return
     */
    NextStepVo nextStep(String subpackageCode);

    /**
     * 添加或修改review_record表信息
     * @param submitResultDto
     * @return
     */
    Boolean addReviewRecord(SubmitResultDto submitResultDto);

    /**
     * 根据标段code和评审类型查询review_record列表
     * @param isType          评审类型
     * @param subpackageCode  标段code
     * @return
     */
    List<RecordDao> queryRecord(Integer isType, String subpackageCode);


    /**
     * 获取未完成评审的评委名单
     * @param isType
     * @param subpackageCode  标段code
     * @return
     */
    List<IncompleteJudgeVo> incompleteStatus(Integer isType, String subpackageCode);

    /**
     * 获取评审结果
     * @param isType          评审类型
     * @param subpackageDao   标段类
     * @param subpackageCode  标段code
     * @return
     */
    List<SupplierDetailsVo> reviewResults(Integer isType, SubpackageDao subpackageDao, String subpackageCode);



    /**
     * 查询并封装供应商的评审结果列表
     * @param reviewMode       评审模式 : 1 只有合格  2- 只有打分  3 打分和合格并存
     * @param isType           打分类型 : 0-合格  1-打分
     * @param supplierDaoList  供应商列表
     * @param subpackageCode   标段code
     * @return
     */
    List<SupplierDetailsVo> supplierDetails(Integer reviewMode, Integer isType, List<SupplierDao> supplierDaoList, String subpackageCode);



    /**
     * 根据标段code删除review_record表数据
     * @param subpackageCode  标段code
     * @return
     */
    Boolean deleteRecord(String subpackageCode);


    /**
     * 查询评审结果
     * @param subpackageCode  标段code
     * @param judgeId         专家id
     * @param supplierId      供应商id
     * @return
     */
    Boolean isRecord(String subpackageCode, Long judgeId, Long supplierId);




    /**
     * 专家端投票
     * @param subpackageCode  标段code专家投票
     * @param judgeId         专家id
     * @param supplierId      供应商id
     * @return
     */
    Boolean vote(String subpackageCode, Long judgeId, Long supplierId);

    /**
     * 自动设置排名
     *
     * @param subpackageCode 标段code
     * @param reviewMethod   评审类型：  1-评审 ， 0-调研   2-投票
     */
    void rankAutomatic(String subpackageCode, Integer reviewMethod);

    /**
     * 获取投票信息
     *
     * @param subpackageCode 标段code专家投票
     * @param supplierId     供应商id
     * @return
     */
    SupplierDetailsVo getVoters(String subpackageCode, Long supplierId);


}
