package com.epcos.review.business.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.review.business.api.ISupplierChapterApi;
import com.epcos.review.domain.dao.SupplierChapterDao;
import com.epcos.review.domain.dto.SupplierChapterDto;
import com.epcos.review.mapper.SupplierChapterMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierChapterImpl extends ServiceImpl<SupplierChapterMapper, SupplierChapterDao> implements ISupplierChapterApi {


    /**
     * 添加review_supplier_chapter表
     * @param chapterDtoList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addBachChapter(List<SupplierChapterDto> chapterDtoList) {
        for (SupplierChapterDto dto : chapterDtoList) {
            SupplierChapterDao dao = new SupplierChapterDao();
            dao.setSupplierId(dto.getSupplierId());
            dao.setScoreChapter(JSON.toJSONString(dto.getScoreChapter()));
            dao.setSubpackageCode(dto.getSubpackageCode());
            dao.setUuid(dto.getUuid());
            boolean flag = addSupplierChapterDao(dao);
            if (!flag) {
                log.error("添加review_supplier_chapter表失败");
                throw new ServiceException("添加数据失败");
            }
        }
        return true;
    }


    private Boolean addSupplierChapterDao(SupplierChapterDao dao) {
        try {
            LambdaUpdateWrapper<SupplierChapterDao> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(SupplierChapterDao::getSubpackageCode, dao.getSubpackageCode());
            updateWrapper.eq(SupplierChapterDao::getUuid, dao.getUuid());
            updateWrapper.eq(SupplierChapterDao::getSupplierId, dao.getSupplierId());
            this.saveOrUpdate(dao, updateWrapper);
        } catch (Exception e) {
            log.error("初始化供应商响应文件章节数据失败",e);
            throw new ServiceException("初始化供应商响应文件章节数据失败");
        }
        return true;
    }


    @Override
    public List<SupplierChapterDao> querySupplierChapter(String subpackageCode, Long supplierId) {
        try {
            LambdaQueryWrapper<SupplierChapterDao> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SupplierChapterDao::getSubpackageCode, subpackageCode);
            queryWrapper.eq(SupplierChapterDao::getSupplierId, supplierId);
            return this.list(queryWrapper);
        } catch (Exception e) {
            log.error("查询供应商章节失败,subpackageCode={},supplierId={}",subpackageCode,supplierId, e);
            throw new ServiceException();
        }
    }

    /**
     * 根据标段code删除review_supplier_chapter表数据
     * @param subpackageCode
     * @return
     */
    @Override
    public Boolean deleteSupplierChapter(String subpackageCode) {
        try {
            LambdaUpdateWrapper<SupplierChapterDao> queryWrapper = new LambdaUpdateWrapper<>();
            queryWrapper.eq(SupplierChapterDao::getSubpackageCode, subpackageCode);
            this.remove(queryWrapper);
        } catch (Exception e) {
            log.error("删除供应商章节失败,subpackageCode={}",subpackageCode, e);
            throw new ServiceException();
        }
        return true;
    }


}
