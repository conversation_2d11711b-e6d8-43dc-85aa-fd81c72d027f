package com.epcos.review.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.epcos.review.domain.dao.SubpackageDao;
import com.epcos.review.domain.vo.SubpackageVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
/**
 * <AUTHOR>
 * @version V1.0
 */
@Mapper
public interface SubpackageMapper extends BaseMapper<SubpackageDao> {
   @Select(
                    "     SELECT rs.subpackage_code as subpackageCode," +
                    "            rs.subpackage_name as subpackageName," +
                    "            rs.buy_item_code as buyItemCode," +
                    "            rs.purchase_method_type as purchaseMethodType," +
                    "            rs.purchase_method_name as purchaseMethodName," +
                    "            rs.buy_item_name as buyItemName," +
                            "            rs.review_method as reviewMethod," +
                    "            rs.review_mode as reviewMode," +
                    "            rs.leader_reject as leaderIsReject," +
                    "            rs.claims_file_key as claimsFile<PERSON>ey," +
                    "            rs.change_file_keys as changeFileKeys," +
                    "            rs.purchase_function_json as purchaseFunctionJson," +
                    "            rs.monitor_bid_person_Id as monitorBidPersonId," +
                    "            rs.monitor_bid_person as monitorBidPerson," +
                    "            rs.team_leader_id as teamLeaderId," +
                    "            rs.team_leader_name as teamLeaderName," +
                    "            rs.remarks_bid_rejection as remarksBidRejection " +
                    "      FROM  review_subpackage rs ,review_judges rj " +
                    "      WHERE rj.judge_id = #{judgeId} AND rj.subpackage_code = rs.subpackage_code  ORDER BY rj.create_time DESC "
    )
    Page<SubpackageVo> querySubpackageList( Page<SubpackageVo> page , @Param("judgeId") Long judgeId);

    @Select(
            " SELECT rs.subpackage_code as subpackageCode, " +
                    "rs.subpackage_name as subpackageName ,  " +
                    "rs.buy_item_code as buyItemCode , " +
                    "rs.purchase_method_type as purchaseMethodType , " +
                    "rs.purchase_method_name as purchaseMethodName, " +
                    "rs.buy_item_name as buyItemName ,  " +
                    "rs.review_method as reviewMethod," +
                    "rs.review_mode as reviewMode , " +
                    "rs.leader_reject as leaderIsReject , " +
                    "rs.claims_file_key as claimsFileKey , " +
                    "rs.change_file_keys as changeFileKeys , " +
                    "rs.purchase_function_json as purchaseFunctionJson ," +
                    "rs.monitor_bid_person_Id as monitorBidPersonId , " +
                    "rs.monitor_bid_person as monitorBidPerson ," +
                    "rs.team_leader_id as teamLeaderId ," +
                    "rs.team_leader_name as teamLeaderName ," +
                    "rs.remarks_bid_rejection as remarksBidRejection " +
                    " FROM " +
                    "review_subpackage rs  " +
                    " WHERE " +
                    " rs.monitor_bid_person_Id = #{monitorBidPersonId}  ORDER BY rs.create_time DESC "
    )
    Page<SubpackageVo> monitorBidPersonList(Page<SubpackageVo> page, @Param("monitorBidPersonId") Long monitorBidPersonId);
}
