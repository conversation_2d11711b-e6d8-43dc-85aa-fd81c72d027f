package com.epcos.review.business.api;

import com.baomidou.mybatisplus.extension.service.IService;
import com.epcos.review.domain.dao.SupplierChapterDao;
import com.epcos.review.domain.dto.SupplierChapterDto;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 */
public interface ISupplierChapterApi   extends IService<SupplierChapterDao> {

    /**
     * 添加review_supplier_chapter表
     * @param chapterDtoList
     * @return
     */
    Boolean addBachChapter(List<SupplierChapterDto> chapterDtoList);


    /**
     * 根据标段code删除review_supplier_chapter表数据
     * @param subpackageCode
     * @return
     */
    Boolean deleteSupplierChapter(String subpackageCode);


    List<SupplierChapterDao>  querySupplierChapter(String subpackageCode , Long supplierId);
}
