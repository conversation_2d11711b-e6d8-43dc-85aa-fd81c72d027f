package com.epcos.review.business.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.common.core.enums.ClientEnum;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.bean.BeanUtils;
import com.epcos.review.business.api.ISubpackageApi;
import com.epcos.review.business.api.ISupplierApi;
import com.epcos.review.domain.bo.ProcessNodesBo;
import com.epcos.review.domain.dao.JudgeDao;
import com.epcos.review.domain.dao.SubpackageDao;
import com.epcos.review.domain.vo.SubpackageVo;
import com.epcos.review.mapper.SubpackageMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SubpackageImpl extends ServiceImpl<SubpackageMapper, SubpackageDao> implements ISubpackageApi {
    private static final int one = 1;
    private static final int zero = 0;
    private static final int two = 2;
    private static final int three = 3;
    private final SubpackageMapper subpackageMapper;
    private final ISupplierApi supplierImpl;

    @Value("${spring.profiles.active}")
    private String activeProfile;

    /**
     * 新增review_subpackage表信息
     *
     * @param dao
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveSubpackage(SubpackageDao dao) {
        try {
            dao.setUpdateTime((LocalDateTime.now()));
            dao.setCreateTime((LocalDateTime.now()));
            LambdaUpdateWrapper<SubpackageDao> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(SubpackageDao::getBuyItemCode, dao.getBuyItemCode());
            updateWrapper.eq(SubpackageDao::getSubpackageCode, dao.getSubpackageCode());
            this.saveOrUpdate(dao, updateWrapper);
        } catch (Exception e) {
            log.error("保存标段信息失败,dao={}", dao.toString(), e);
            throw new ServiceException();
        }
        return true;
    }

    /**
     * 修改review_subpackage表(评审标段基础信息列表)
     *
     * @param subpackageCode 标段code
     * @param reviewMode
     * @param isBargaining
     * @return
     */
    @Override
    public Boolean createProcessNodes(String subpackageCode, Integer reviewMode, Integer isBargaining) {
        try {
            LambdaUpdateWrapper<SubpackageDao> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(SubpackageDao::getSubpackageCode, subpackageCode);
            SubpackageDao dao = new SubpackageDao();
            dao.setSubpackageCode(subpackageCode);
            dao.setReviewMode(reviewMode);
            dao.setPurchaseFunctionJson(processNode(reviewMode, isBargaining));
            this.update(dao, wrapper);
        } catch (Exception e) {
            log.error("保存标段信息失败,subpackageCode={},reviewMode={},isBargaining={}", subpackageCode, reviewMode, isBargaining, e);
            throw new ServiceException();
        }
        return true;
    }

    /**
     * 采购人确认启动评审
     *
     * @param dao
     * @return
     */
    @Override
    public Boolean updateSubpackageDao(SubpackageDao dao) {
        try {
            LambdaUpdateWrapper<SubpackageDao> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(SubpackageDao::getSubpackageCode, dao.getSubpackageCode());
            this.update(dao, wrapper);
        } catch (Exception e) {
            log.error("修改标段信息(review_subpackage表)失败", e);
            throw new ServiceException();
        }
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean setExamineReviewMethod(String subpackageCode, Integer reviewMethod) {
        try {
            LambdaUpdateWrapper<SubpackageDao> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(SubpackageDao::getSubpackageCode, subpackageCode);
            if (ClientEnum.LG.getCode().equals(activeProfile)) {
                wrapper.set(SubpackageDao::getReviewMethod, two);
            }
            wrapper.set(SubpackageDao::getReviewMethod, reviewMethod);
            this.update(wrapper);
        } catch (Exception e) {
            log.error("设置调研失败,subpackageCode={}", subpackageCode, e);
            throw new ServiceException("设置调研失败");
        }
        return true;
    }


    /**
     * 根据标段code查询review_subpackage表单条数据
     *
     * @param subpackageCode
     * @return
     */
    @Override
    public SubpackageDao getOne(String subpackageCode) {
        try {
            LambdaQueryWrapper<SubpackageDao> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SubpackageDao::getSubpackageCode, subpackageCode);
            return this.getOne(wrapper);
        } catch (Exception e) {
            log.error("查询标段信息失败,subpackageCode={}", subpackageCode, e);
            throw new ServiceException();
        }
    }

    /**
     * 修改review_subpackage表(设置评审备注)
     *
     * @param subpackageCode
     * @param isReject
     * @param remarksBidRejection
     * @return
     */
    @Override
    public Boolean bidRejection(String subpackageCode, Integer isReject, String remarksBidRejection) {
        try {
            LambdaUpdateWrapper<SubpackageDao> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(SubpackageDao::getSubpackageCode, subpackageCode);
            SubpackageDao subpackage = new SubpackageDao();
            subpackage.setLeaderIsReject(isReject);
            subpackage.setRemarksBidRejection(remarksBidRejection);
            update(subpackage, wrapper);
        } catch (Exception e) {
            log.error("废标失败,subpackageCode={},isReject={},remarksBidRejection={}", subpackageCode, isReject, remarksBidRejection, e);
            throw new ServiceException("废标失败");
        }
        return true;
    }

    /**
     * review_subpackage表，设置组长
     *
     * @param subpackageCode 标段
     * @param judgesOne      组长
     * @return status
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean setTeamLeader(String subpackageCode, JudgeDao judgesOne) {
        try {
            LambdaUpdateWrapper<SubpackageDao> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(SubpackageDao::getSubpackageCode, subpackageCode);
            SubpackageDao subpackage = new SubpackageDao();
            subpackage.setTeamLeaderId(judgesOne.getJudgeId());
            subpackage.setTeamLeaderName(judgesOne.getJudgeName());
            update(subpackage, wrapper);
        } catch (Exception e) {
            log.error("设置组长失败,subpackageCode={},judgesOne={}", subpackageCode, judgesOne.toString(), e);
            throw new ServiceException("设置组长失败");
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean setReport(String subpackageCode, String reportFileKey) {
        try {
            LambdaUpdateWrapper<SubpackageDao> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(SubpackageDao::getSubpackageCode, subpackageCode);
            wrapper.set(SubpackageDao::getReportFileKey, reportFileKey);
            wrapper.set(SubpackageDao::getPurchaseOk, one);
            update(wrapper);
        } catch (Exception e) {
            log.error("添加评标报告失败,subpackageCode={},reportFileKey={}", subpackageCode, reportFileKey, e);
            throw new ServiceException("添加评标报告失败");
        }
        return true;
    }

    /**
     * 根据评审人id,分页查询review_subpackage列表
     *
     * @param judgeId 评审人id
     * @param page    页码
     * @param size    每页数量
     * @return
     */
    @Override
    public Page<SubpackageVo> querySubpackageList(Long judgeId, Integer page, Integer size) {
        Page<SubpackageVo> iPage = new Page<>(page, size);
        try {
            // 根据标段code, 查询review_subpackage列表
            return subpackageMapper.querySubpackageList(iPage, judgeId);
        } catch (Exception e) {
            log.error("查询标段信息失败,judgeId={},page={},size={}", judgeId, page, size, e);
            throw new ServiceException("查询标段信息失败");
        }
    }


    /**
     * 根据监督人id,分页查询review_subpackage表数据
     *
     * @param monitorBidPersonId
     * @param page
     * @param size
     * @return
     */
    @Override
    public Page<SubpackageVo> monitorBidList(Long monitorBidPersonId, Integer page, Integer size) {
        Page<SubpackageVo> iPage = new Page<>(page, size);
        try {
            return subpackageMapper.monitorBidPersonList(iPage, monitorBidPersonId);
        } catch (Exception e) {
            log.error("查询监督项目列表失败,monitorBidPersonId={},page={},size={}", monitorBidPersonId, page, size, e);
            throw new ServiceException("查询监督项目列表失败");
        }
    }


    /**
     * 根据标段code删除review_subpackage表数据
     *
     * @param subpackageCode
     * @return
     */
    @Override
    public Boolean deleteSubpackage(String subpackageCode) {
        try {
            LambdaUpdateWrapper<SubpackageDao> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(SubpackageDao::getSubpackageCode, subpackageCode);
            this.remove(wrapper);
        } catch (Exception e) {
            log.error("删除标段信息失败,subpackageCode={}", subpackageCode, e);
            throw new ServiceException();
        }
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean teamLeaderOk(String functionJson, String subpackageCode, Long[] supplierIds) {
        List<ProcessNodesBo> processNodesBoList = JSONArray.parseArray(functionJson, ProcessNodesBo.class);
        List<ProcessNodesBo> newPNodes = new ArrayList<>();
        for (ProcessNodesBo pn : processNodesBoList) {
            ProcessNodesBo processNodesBo = new ProcessNodesBo();
            BeanUtils.copyProperties(pn, processNodesBo);
            processNodesBo.setIsShow(one);
            newPNodes.add(processNodesBo);
        }
        SubpackageDao dao = new SubpackageDao();
        dao.setSubpackageCode(subpackageCode);
        dao.setPurchaseFunctionJson(JSON.toJSONString(newPNodes));
        dao.setJudgePreliminaryOk(one);
        Boolean status = updateSubpackageDao(dao);
        if (!ArrayUtils.isEmpty(supplierIds)) {
            if (status) {
                Set<Long> suppliers = Arrays.stream(supplierIds).collect(Collectors.toSet());
                for (Long supplier : suppliers) {
                    boolean eliminate = supplierImpl.eliminateSupplier(subpackageCode, supplier);
                    if (!eliminate) {
                        log.error("确认初步评审-淘汰供应商失败");
                        throw new ServiceException("确认初步评审-淘汰供应商失败");
                    }
                }
            }
        }
        return true;
    }

    /**
     * 创建动态流程节点
     */
    public List<ProcessNodesBo> examineNode() {
        List<ProcessNodesBo> nodesList = new ArrayList<>();
        nodesList.add(new ProcessNodesBo("调研", "all_review", one, zero));
        nodesList.add(new ProcessNodesBo("议价", "expert_bargaining", one, zero));
        nodesList.add(new ProcessNodesBo("调研报告", "expert_research_report", one, zero));
        return nodesList;
    }

    @Override
    public List<ProcessNodesBo> voteNode() {
        List<ProcessNodesBo> nodesList = new ArrayList<>();
        nodesList.add(new ProcessNodesBo("供应商报价", "expert_bargaining", one, zero));
        nodesList.add(new ProcessNodesBo("项目文件", "expert_project_files", one, zero));
        nodesList.add(new ProcessNodesBo("投票", "expert_vote_review", one, zero));
        nodesList.add(new ProcessNodesBo("评审结果", "expert_vote_result", one, zero));
        return nodesList;
    }

    public List<ProcessNodesBo> reviewNode() {
        List<ProcessNodesBo> nodesList = new ArrayList<>();
        nodesList.add(new ProcessNodesBo("资格性符合性评审", "conformity_review", one, one));
        nodesList.add(new ProcessNodesBo("技术与商务评审", "score_review", one, zero));
        nodesList.add(new ProcessNodesBo("议价", "expert_bargaining", one, zero));
        nodesList.add(new ProcessNodesBo("评审结果", "expert_result", one, zero));
        nodesList.add(new ProcessNodesBo("质疑", "expert_question", one, zero));
        nodesList.add(new ProcessNodesBo("答疑", "expert_answer_questions", one, zero));
        return nodesList;
    }


    /**
     * 创建动态流程节点
     */
    private String processNode(Integer reviewMode, Integer isBargaining) {
        List<ProcessNodesBo> nodesList = new ArrayList<>();
        if (reviewMode == three) {
            nodesList.add(new ProcessNodesBo("资格性符合性评审", "conformity_review", one, one));
            nodesList.add(new ProcessNodesBo("技术与商务评审", "score_review", zero, zero));
        }
        if (reviewMode == two) {
            nodesList.add(new ProcessNodesBo("技术与商务评审", "score_review", one, zero));
        }
        if (reviewMode == one) {
            nodesList.add(new ProcessNodesBo("资格性符合性评审", "conformity_review", one, zero));
        }
        return JSON.toJSONString(createSecondNodes(reviewMode, nodesList, isBargaining));
    }

    private List<ProcessNodesBo> createSecondNodes(Integer reviewMode, List<ProcessNodesBo> nodesList, Integer isBargaining) {
        int i = reviewMode == three ? zero : one;
        //isBargaining=1 评委议价， [isBargaining=0 采购人议价，isBargaining=2 没有议价]
        if (isBargaining == one) {
            nodesList.add(new ProcessNodesBo("议价", "expert_bargaining", i, zero));
            nodesList.add(new ProcessNodesBo("评审结果", "expert_result", i, zero));
            nodesList.add(new ProcessNodesBo("质疑", "expert_question", i, zero));
            nodesList.add(new ProcessNodesBo("答疑", "expert_answer_questions", i, zero));
        } else {
            nodesList.add(new ProcessNodesBo("评审结果", "expert_result", i, zero));
            nodesList.add(new ProcessNodesBo("质疑", "expert_question", i, zero));
            nodesList.add(new ProcessNodesBo("答疑", "expert_answer_questions", i, zero));
        }
        return nodesList;
    }


}
