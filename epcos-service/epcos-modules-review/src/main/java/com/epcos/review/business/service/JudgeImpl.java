package com.epcos.review.business.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.common.core.constant.SecurityConstants;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.enums.ClientEnum;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.EvUtils;
import com.epcos.review.business.api.IJudgeApi;
import com.epcos.review.domain.bo.ReportUserBo;
import com.epcos.review.domain.dao.JudgeDao;
import com.epcos.review.mapper.JudgesMapper;
import com.epcos.system.api.RemoteUserService;
import com.epcos.common.core.web.domain.user.SysUser;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 */
@Slf4j
@Service
@AllArgsConstructor
public class JudgeImpl extends ServiceImpl<JudgesMapper, JudgeDao> implements IJudgeApi {
    private final RemoteUserService remoteUserService;
    private static final int one = 1;


    /**
     * 添加或修改review_judges表数据
     * @param dao
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addJudgesDao(JudgeDao dao) {
        try {
            JudgeDao one = this.judgeOne(dao.getSubpackageCode(), dao.getJudgeId());
            if (ObjectUtils.isEmpty(one)) {
                dao.setUpdateTime((LocalDateTime.now()));
                dao.setCreateTime((LocalDateTime.now()));
                this.save(dao);
            } else {
                dao.setUpdateTime((LocalDateTime.now()));
                LambdaUpdateWrapper<JudgeDao> wrapper = new LambdaUpdateWrapper<>();
                wrapper.eq(JudgeDao::getSubpackageCode, dao.getSubpackageCode());
                wrapper.eq(JudgeDao::getJudgeId, dao.getJudgeId());
                this.update(dao, wrapper);
            }
        } catch (Exception e) {
            log.error("保存评委失败",e);
            throw new ServiceException("保存评委失败");
        }
        return true;
    }

    /**
     * 根据标段code查询review_judges表
     * @param subpackageCode  标段code
     * @return
     */
    @Override
    public List<JudgeDao> judgesDtoList(String subpackageCode) {
        LambdaQueryWrapper<JudgeDao> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(JudgeDao::getSubpackageCode, subpackageCode);
        return this.list(queryWrapper);
    }

    /**
     * 根据标段code删除review_judges表
     * @param subpackageCode  标段code
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteJudges(String subpackageCode) {
        try {
            LambdaUpdateWrapper<JudgeDao> queryWrapper = new LambdaUpdateWrapper<>();
            queryWrapper.eq(JudgeDao::getSubpackageCode, subpackageCode);
            this.remove(queryWrapper);
        } catch (Exception e) {
            log.error("清空评委失败,subpackageCode={}",subpackageCode,e);
            throw new ServiceException("清空评委失败");
        }
        return true;
    }


    /**
     * 根据标段code查询review_judges表列表,封装ReportUserBo列表并返回
     * @param subpackageCode  标段code
     * @return
     */
    @Override
    public   List<ReportUserBo> findReportUserBo(String subpackageCode){
        LambdaQueryWrapper<JudgeDao> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(JudgeDao::getSubpackageCode, subpackageCode);
        // 根据标段code查询review_judges表列表(评委表)
        List<JudgeDao> judges = this.list(queryWrapper);

        // 封装ReportUserBo列表并返回
        List<ReportUserBo>  reportUserBos =  new ArrayList<>();
        for (JudgeDao dao : judges) {
            ReportUserBo reportUser = new ReportUserBo();
            BeanUtils.copyProperties(dao,reportUser);
            SysUser sysUser = this.getSysUser(dao.getJudgeId());
            if (sysUser != null) {
                reportUser.setDepartment(sysUser.getDepartment());

                // 襄阳特有 查询流程编号
                if (EvUtils.ev().equals(ClientEnum.XY.getCode())) {
                    reportUser.setInsideIdentity(sysUser.getUserName());
                }else {
                    reportUser.setInsideIdentity(sysUser.getIdNumber());
                }
            }
            reportUserBos.add(reportUser);
        }
        return  reportUserBos;
    }

    private SysUser getSysUser(Long userId) {
        try {
            R<SysUser> r = remoteUserService.getInfoById(userId, SecurityConstants.INNER);

            if (r.hasFail()) {
                log.error("调用/user/getInfoById接口异常,userId={},r={}", userId, r);
                throw new ServiceException(r.getMsg());
            }
            if (!ObjectUtils.isEmpty(r) && r.getCode() == 200) {
                return r.getData();
            }
        } catch (Exception e) {
            log.error("获取用户信息失败",e);
            throw new RuntimeException("获取用户信息失败");
        }
        return null;
    }


    /**
     * review_judges表，设置组长
     * @param subpackageCode 标段
     * @param judgesId  评委ID
     * @return status
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean setJudgesLeader(String subpackageCode ,Long judgesId) {
        try {
            LambdaUpdateWrapper<JudgeDao> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(JudgeDao::getSubpackageCode, subpackageCode);
            wrapper.eq(JudgeDao::getJudgeId, judgesId);
            wrapper.set(JudgeDao::getJudgeType, one);
            update(wrapper);
        } catch (Exception e) {
            log.error("设置组长失败,subpackageCode={},judgesId={}",subpackageCode,judgesId,e);
            throw new ServiceException("设置组长失败");
        }
        return true;
    }


    /**
     * 设置签字状态
     * @param subpackageCode 标段
     * @param judgesId       评委ID
     * @return status
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean setJudgeSign(String subpackageCode, Long judgesId) {
        try {
            LambdaUpdateWrapper<JudgeDao> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(JudgeDao::getSubpackageCode, subpackageCode);
            wrapper.eq(JudgeDao::getJudgeId, judgesId);
            wrapper.set(JudgeDao::getIsSign, one);

            if (!update(wrapper)){
                log.error("设置签字状态失败,subpackageCode={},judgesId={}",subpackageCode,judgesId);
                throw new ServiceException("设置签字状态失败");
            }
        } catch (Exception e) {
            log.error("设置签字状态失败,subpackageCode={},judgesId={}",subpackageCode,judgesId,e);
            throw new ServiceException("设置签字状态失败");
        }
        return true;
    }

    /**
     * 根据标段code和评委id，查询review_judges表单条数据
     * @param subpackageCode  标段code
     * @param judgeId         评委id
     * @return
     */
    @Override
    public JudgeDao judgeOne(String subpackageCode, Long judgeId) {
        LambdaQueryWrapper<JudgeDao> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(JudgeDao::getSubpackageCode, subpackageCode);
        queryWrapper.eq(JudgeDao::getJudgeId, judgeId);
        return this.getOne(queryWrapper);
    }

    /**
     * 查询标段下的组长
     * @param subpackageCode 标段code
     * @return
     */
    @Override
    public JudgeDao judgeLeader(String subpackageCode) {
        LambdaQueryWrapper<JudgeDao> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(JudgeDao::getSubpackageCode, subpackageCode);
        queryWrapper.eq(JudgeDao::getJudgeType, one);
        return this.getOne(queryWrapper);
    }




}
