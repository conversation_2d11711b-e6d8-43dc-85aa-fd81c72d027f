package com.epcos.review.business.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.epcos.review.domain.bo.ProcessNodesBo;
import com.epcos.review.domain.dao.JudgeDao;
import com.epcos.review.domain.dao.SubpackageDao;
import com.epcos.review.domain.vo.SubpackageVo;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 */
public interface ISubpackageApi extends IService<SubpackageDao> {

    /**
     * 新增review_subpackage表信息
     *
     * @param dao
     * @return
     */
    Boolean saveSubpackage(SubpackageDao dao);

    /**
     * 根据评审人id,分页查询review_subpackage列表
     *
     * @param judgeId 评审人id
     * @param page    页码
     * @param size    每页数量
     * @return
     */
    Page<SubpackageVo> querySubpackageList(Long judgeId, Integer page, Integer size);


    /**
     * 修改review_subpackage表(设置评审备注)
     *
     * @param subpackageCode
     * @param isReject
     * @param remarksBidRejection
     * @return
     */
    Boolean bidRejection(String subpackageCode, Integer isReject, String remarksBidRejection);


    /**
     * 根据监督人id,分页查询review_subpackage表数据
     *
     * @param monitorBidPersonId
     * @param page
     * @param size
     * @return
     */
    Page<SubpackageVo> monitorBidList(Long monitorBidPersonId, Integer page, Integer size);


    /**
     * review_subpackage表，设置组长
     *
     * @param subpackageCode 标段
     * @param judgesOne      组长
     * @return status
     */
    Boolean setTeamLeader(String subpackageCode, JudgeDao judgesOne);


    Boolean setReport(String subpackageCode, String reportFileKey);

    /**
     * 采购人确认启动评审
     *
     * @param dao
     * @return
     */
    Boolean updateSubpackageDao(SubpackageDao dao);

    /**
     * 根据标段code查询review_subpackage表单条数据
     *
     * @param subpackageCode
     * @return
     */
    SubpackageDao getOne(String subpackageCode);

    /**
     * 根据标段code删除review_subpackage表数据
     *
     * @param subpackageCode
     * @return
     */
    Boolean deleteSubpackage(String subpackageCode);

    /**
     * 修改review_subpackage表(评审标段基础信息列表)
     *
     * @param subpackageCode
     * @param reviewMode
     * @param isBargaining
     * @return
     */
    Boolean createProcessNodes(String subpackageCode, Integer reviewMode, Integer isBargaining);

    Boolean teamLeaderOk(String functionJson, String subpackageCode, Long[] supplierIds);


    /**
     * 调研
     * @return
     */
    List<ProcessNodesBo> examineNode();

    /**
     * 投票
     * @return
     */
    List<ProcessNodesBo> voteNode();

    /**
     * 评审
     * @return
     */
    List<ProcessNodesBo> reviewNode();

    Boolean setExamineReviewMethod(String subpackageCode, Integer reviewMethod);

}
