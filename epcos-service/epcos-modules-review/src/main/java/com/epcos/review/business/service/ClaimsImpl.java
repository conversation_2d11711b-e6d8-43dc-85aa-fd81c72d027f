package com.epcos.review.business.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.bean.BeanUtils;
import com.epcos.review.business.api.IClaimsApi;
import com.epcos.review.domain.dao.ClaimsDao;
import com.epcos.review.domain.dto.ClaimsDto;
import com.epcos.review.mapper.ClaimsMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

import static org.apache.commons.lang3.ObjectUtils.isEmpty;

/**
 * <AUTHOR>
 * @version V1.0 采购要求规则集
 */
@Slf4j
@Service
public class ClaimsImpl extends ServiceImpl<ClaimsMapper, ClaimsDao> implements IClaimsApi {

    private static final int one = 1;
    private static final int zero = 0;
    /**
     * 添加或修改review_claims表(评审规则集)数据
     * @param claimsDtoList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdateBatch(List<ClaimsDto> claimsDtoList) {
        for (ClaimsDto dto : claimsDtoList) {
            ClaimsDao dao = new ClaimsDao();
            BeanUtils.copyProperties(dto, dao);
            Boolean status;
            if (CollectionUtils.isEmpty(claimsListByUUID(dto.getSubpackageCode(), dto.getUuid()))) {
                dao.setUpdateTime((LocalDateTime.now()));
                dao.setCreateTime((LocalDateTime.now()));
                status = this.save(dao);
            } else {
                dao.setUpdateTime((LocalDateTime.now()));
                status = updateClaims(dao);
            }
            if (isEmpty(status)) {
                log.error("评审办法保存失败");
                throw new ServiceException("评审办法保存失败");
            }
        }
        return true;
    }

    /**
     * 根据打分类型,标段code 查询review_claims列表(评审规则集)
     * @param isType
     * @param subpackageCode
     * @return
     */
    @Override
    public List<ClaimsDao> claimsListBySubpackage( Integer isType, String subpackageCode) {
        LambdaQueryWrapper<ClaimsDao> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ClaimsDao::getSubpackageCode, subpackageCode);
        if (isType == zero) {
            queryWrapper.eq(ClaimsDao::getReviewType, one);
        }else{
            queryWrapper.gt(ClaimsDao::getReviewType, one);
        }
        return this.list(queryWrapper);
    }

    /**
     * 根据标段code查询review_claims表
     * @param subpackageCode
     * @return
     */
    @Override
    public List<ClaimsDao> claimsListAll(String subpackageCode){
        LambdaQueryWrapper<ClaimsDao> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ClaimsDao::getSubpackageCode, subpackageCode);
        return this.list(queryWrapper);
    }

    /**
     * 根据标段code删除review_claims表数据
     * @param subpackageCode
     * @return
     */
    @Override
    public Boolean deleteClaims(String subpackageCode) {
        try {
            LambdaUpdateWrapper<ClaimsDao> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(ClaimsDao::getSubpackageCode, subpackageCode);
            this.remove(wrapper);
        } catch (Exception e) {
            log.error("清空评审办法失败",e);
            throw new ServiceException("清空评审办法失败");
        }
        return true;
    }

    /**
     * 修改评审模块id(uuid)
     * @param dao
     * @return
     */
    @Override
    public Boolean updateClaims(ClaimsDao dao) {
        try {
            LambdaUpdateWrapper<ClaimsDao> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(ClaimsDao::getSubpackageCode, dao.getSubpackageCode());
            wrapper.eq(ClaimsDao::getUuid, dao.getUuid());
            this.update(dao, wrapper);
        } catch (Exception e) {
            log.error("更新评审办法失败",e);
            throw new ServiceException("更新评审办法失败");
        }
        return true;
    }


    private List<ClaimsDao> claimsListByUUID(String subpackageCode, String uuid) {
        LambdaQueryWrapper<ClaimsDao> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ClaimsDao::getSubpackageCode, subpackageCode);
        queryWrapper.eq(ClaimsDao::getUuid, uuid);
        return this.list(queryWrapper);
    }

}
