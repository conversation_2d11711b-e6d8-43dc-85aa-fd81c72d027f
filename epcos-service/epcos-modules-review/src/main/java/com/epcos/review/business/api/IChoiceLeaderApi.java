package com.epcos.review.business.api;

import com.baomidou.mybatisplus.extension.service.IService;
import com.epcos.review.domain.dao.LeaderDao;
import com.epcos.review.domain.dto.ChoiceLeaderDto;
import com.epcos.review.domain.vo.VotingResultsVo;

import java.util.List;
/**
 * <AUTHOR>
 * @version V1.0
 */
public interface IChoiceLeaderApi extends IService<LeaderDao> {

    /**
     * 添加或修改review_leader表数据
     * @param dto
     * @return
     */
    Boolean addChoiceLeaderDao(LeaderDao dto);

    /**
     * 根据标段code删除review_leader表信息
     * @param subpackageCode
     * @return
     */
    Boolean cleanRecord(String subpackageCode);

    /**
     * 添加/修改 review_leader表(投票)
     * @param dto
     * @return
     */
    Boolean addChoiceLeader(ChoiceLeaderDto dto);


    /**
     * 根据标段code删除review_leader表数据
     * @param subpackageCode 标段code
     * @return
     */
    Boolean refreshChoiceLeader(String subpackageCode);


    /**
     * 根据标段code删除review_leader表数据
     * @param subpackageCode
     * @return
     */
    Boolean deleteChoiceInfo(String subpackageCode);

    /**
     * 根据标段code查询 review_leader表
     * @param subpackageCode
     * @return
     */
    List<LeaderDao> getInitChoiceLeader(String subpackageCode);

    /**
     * 获取评委投票结果
     * @param subpackageCode
     * @return
     */
    List<VotingResultsVo> getVotingResults(String subpackageCode);

    /**
     * review_leader表，设置组长(根据评委id和包code找到评委并将他设为组长)
     * @param subpackageCode 包code
     * @param judgeId 评委id
     * @return
     */
    Boolean setLeader(String subpackageCode , Long judgeId);

}
