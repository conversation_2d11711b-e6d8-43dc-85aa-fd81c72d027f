package com.epcos.review.business.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.common.core.constant.SecurityConstants;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.domain.review.IncompleteJudgeVo;
import com.epcos.common.core.domain.review.ReviewDetailsVo;
import com.epcos.common.core.domain.review.ReviewJudgesVo;
import com.epcos.common.core.domain.review.SupplierDetailsVo;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.web.domain.user.SysUser;
import com.epcos.review.business.api.*;
import com.epcos.review.domain.dao.*;
import com.epcos.review.domain.dto.SubmitResultDto;
import com.epcos.review.domain.dto.SupplierRankDto;
import com.epcos.review.domain.vo.*;
import com.epcos.review.mapper.ReviewRecordMapper;
import com.epcos.system.api.RemoteUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static org.springframework.util.ObjectUtils.isEmpty;

/**
 * <AUTHOR>
 * @version V1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RecordImpl extends ServiceImpl<ReviewRecordMapper, RecordDao> implements IRecordApi {

    private static final int zero = 0;
    private static final int one = 1;
    private static final int two = 2;
    private static final int three = 3;
    private final ISupplierApi supplierImpl;
    private final IJudgeApi judgesImpl;
    private final IClaimsApi claimsImpl;

    private final RemoteUserService remoteUserService;
    private final ISubpackageApi subpackageImpl;
    private final ReviewRecordMapper reviewRecordMapper;


    /**
     * 添加或修改review_record表信息
     *
     * @param submitResultDto
     * @return
     */
    @Override
    public Boolean addReviewRecord(SubmitResultDto submitResultDto) {
        RecordDao dao = reviewRecordDtoToDao(submitResultDto);
        LambdaQueryWrapper<RecordDao> query = new LambdaQueryWrapper<>();
        query.eq(RecordDao::getSubpackageCode, dao.getSubpackageCode());
        query.eq(RecordDao::getSupplierId, dao.getSupplierId());
        query.eq(RecordDao::getJudgeId, dao.getJudgeId());
        query.eq(RecordDao::getIsType, dao.getIsType());
        RecordDao tmpDao = this.getOne(query);
        try {
            if (ObjectUtils.isEmpty(tmpDao)) {
                dao.setUpdateTime((LocalDateTime.now()));
                dao.setCreateTime((LocalDateTime.now()));
                this.save(dao);
            } else {
                dao.setUpdateTime((LocalDateTime.now()));
                LambdaUpdateWrapper<RecordDao> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(RecordDao::getSubpackageCode, submitResultDto.getSubpackageCode());
                updateWrapper.eq(RecordDao::getJudgeId, submitResultDto.getJudgeId());
                updateWrapper.eq(RecordDao::getSupplierId, submitResultDto.getSupplierId());
                updateWrapper.eq(RecordDao::getIsType, submitResultDto.getIsType());
                this.update(dao, updateWrapper);
            }
        } catch (Exception e) {
            log.error("保存评审记录失败,submitResultDto={}", submitResultDto, e);
            throw new ServiceException("保存评审记录失败");
        }
        return true;
    }

    @Override
    public RecordDao queryReviewRecord(Integer isType, String subpackageCode, Long judgesId, Long supplierId) {
        try {
            LambdaQueryWrapper<RecordDao> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(RecordDao::getSubpackageCode, subpackageCode);
            queryWrapper.eq(RecordDao::getSupplierId, supplierId);
            queryWrapper.eq(RecordDao::getJudgeId, judgesId);
            queryWrapper.eq(RecordDao::getIsType, isType);
            return this.getOne(queryWrapper);
        } catch (Exception e) {
            log.error("查询评审记录失败,isType={},subpackageCode={},judgesId={},supplierId={}", isType, subpackageCode, judgesId, supplierId, e);
            throw new ServiceException("查询评审记录失败");
        }
    }

    /**
     * 根据标段code和评审类型查询review_record列表
     *
     * @param isType         评审类型
     * @param subpackageCode 标段code
     * @return
     */
    @Override
    public List<RecordDao> queryRecord(Integer isType, String subpackageCode) {
        try {
            LambdaQueryWrapper<RecordDao> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(RecordDao::getSubpackageCode, subpackageCode);
            wrapper.eq(RecordDao::getIsType, isType);
            return this.list(wrapper);
        } catch (Exception e) {
            log.error("查询评审记录失败,isType={},subpackageCode={}", isType, subpackageCode, e);
            throw new ServiceException("查询评审记录失败");
        }
    }


    /**
     * 根据标段code删除review_record表数据
     *
     * @param subpackageCode 标段code
     * @return
     */
    @Override
    public Boolean deleteRecord(String subpackageCode) {
        try {
            LambdaUpdateWrapper<RecordDao> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(RecordDao::getSubpackageCode, subpackageCode);
            this.remove(wrapper);
        } catch (Exception e) {
            log.error("清空评审记录失败,subpackageCode={}", subpackageCode, e);
            throw new ServiceException("清空评审记录失败");
        }
        return true;
    }

    /**
     * 查询评审结果
     *
     * @param subpackageCode 标段code
     * @param judgeId        专家id
     * @param supplierId     供应商id
     * @return
     */
    @Override
    public Boolean isRecord(String subpackageCode, Long judgeId, Long supplierId) {
        LambdaQueryWrapper<RecordDao> qw = new LambdaQueryWrapper<>();
        qw.eq(RecordDao::getSubpackageCode, subpackageCode)
                .eq(RecordDao::getJudgeId, judgeId)
                .eq(RecordDao::getSupplierId, supplierId)
                .last("LIMIT 1");
        RecordDao recordDao = getOne(qw);
        if (recordDao == null || recordDao.getQualifiedResult() == null) {
            log.error("查询评审结果失败,subpackageCode={},judgeId={},supplierId={}", subpackageCode, judgeId, supplierId);
            return false;
        }
        return recordDao.getQualifiedResult() == 1;
    }


    /**
     * 专家端投票
     *
     * @param subpackageCode 标段code专家投票
     * @param judgeId        专家id
     * @param supplierId     供应商id
     * @return
     */
    @Transactional
    @Override
    public Boolean vote(String subpackageCode, Long judgeId, Long supplierId) {
        SubpackageDao subpackage = subpackageImpl.getOne(subpackageCode);
        if (!isEmpty(subpackage)) {
            if (subpackage.getPurchaseOk() == one) {
                throw new ServiceException("评审结果已确定,不允许再次投票");
            }
        }
        // 删除旧的评审信息
        LambdaUpdateWrapper<RecordDao> qw = new LambdaUpdateWrapper<>();
        qw.eq(RecordDao::getSubpackageCode, subpackageCode)
                .eq(RecordDao::getJudgeId, judgeId);
        remove(qw);

        R<SysUser> judge = remoteUserService.getInfoById(judgeId, SecurityConstants.INNER);
        R<SysUser> supplier = remoteUserService.getInfoById(supplierId, SecurityConstants.INNER);

        String judgeName = (judge.hasFail() || judge.getData() == null || judge.getData().getNickName().isEmpty()) ? "" : judge.getData().getNickName();
        String supplierName = (supplier.hasFail() || supplier.getData() == null || supplier.getData().getNickName().isEmpty()) ? "" : supplier.getData().getNickName();

        RecordDao recordDao = new RecordDao();

        recordDao.setSubpackageCode(subpackageCode);
        recordDao.setCreateTime(LocalDateTime.now());
        recordDao.setUpdateTime(LocalDateTime.now());

        recordDao.setIsType(2);
        recordDao.setJudgeId(judgeId);
        recordDao.setJudgeName(judgeName);
        recordDao.setSupplierId(supplierId);

        recordDao.setSupplierCompanyName(supplierName);
        recordDao.setResultJson("[{\"score\":1.0,\"reviewItem\":\"投票\",\"uuid\":\"173035793255302\"}]");
        recordDao.setScoreResult(null);
        recordDao.setQualifiedResult(1);
        if (!save(recordDao)) {
            log.error("投票失败,subpackageCode={},judgeId={},supplierId={}", subpackageCode, judgeId, supplierId);
            throw new ServiceException("投票失败,请重新投票");
        }

        // 设置排名
        rankAutomatic(subpackageCode, subpackage.getReviewMethod());
        return true;
    }

    /**
     * 自动设置排名
     *
     * @param subpackageCode 标段code
     * @param reviewMethod
     */
    @Override
    public void rankAutomatic(String subpackageCode, Integer reviewMethod) {
        List<SupplierRankDto> ranks = new ArrayList<>();
        List<RecordVo> recordVos = new ArrayList<>();
        switch (reviewMethod) {
            case 0:
                return;
            case 1:
                // 评审方式为:评分时 直接return
                recordVos = reviewRecordMapper.selRecordSum(subpackageCode);
                break;
            case 2:
                recordVos = reviewRecordMapper.selRecordNum(subpackageCode);
                break;
        }
        for (int i = 0, j = 1; i < recordVos.size(); i++, j++) {
            if (i == 0) {
                SupplierRankDto rankDto = new SupplierRankDto();
                rankDto.setSupplierId(recordVos.get(i).getSupplierId());
                rankDto.setRank(j);
                ranks.add(rankDto);
                continue;
            }
            if (Objects.equals(recordVos.get(i).getRecordNum(), recordVos.get(i - 1).getRecordNum())) {
                j--;
            }
            SupplierRankDto rankDto = new SupplierRankDto();
            rankDto.setSupplierId(recordVos.get(i).getSupplierId());
            rankDto.setRank(j);
            ranks.add(rankDto);

        }
        supplierImpl.setSupplierRank(ranks, subpackageCode);
    }

    /**
     * 获取投票信息
     *
     * @param subpackageCode 标段code专家投票
     * @param supplierId     供应商id
     * @return
     */
    @Override
    public SupplierDetailsVo getVoters(String subpackageCode, Long supplierId) {
        SupplierDetailsVo supplierDetailsVo = new SupplierDetailsVo();
        LambdaQueryWrapper<RecordDao> qw = new LambdaQueryWrapper<>();
        qw.eq(RecordDao::getSubpackageCode, subpackageCode)
                .eq(RecordDao::getSupplierId, supplierId)
                .eq(RecordDao::getQualifiedResult, 1);
        List<RecordDao> list = list(qw);
        if (list == null || list.isEmpty()) {
            log.error("异常,投票项为空,subpackageCode={},supplierId={}", subpackageCode, supplierId);
            supplierDetailsVo.setVotersNames("/");
            supplierDetailsVo.setVotesNum(0);
            return supplierDetailsVo;
        }
        String votersNames = list.stream()
                .map(recordDao -> remoteUserService.getInfoById(recordDao.getJudgeId(), SecurityConstants.INNER))
                .filter(r -> !r.hasFail() && r.getData() != null)
                .map(r -> r.getData().getNickName())
                .collect(Collectors.joining("、"));
        supplierDetailsVo.setVotersNames(votersNames);
        supplierDetailsVo.setVotesNum(list.size());
        return supplierDetailsVo;
    }

    /**
     * 合格制评审结果汇总
     *
     * @param subpackageCode 标段code
     * @return
     */
    @Override
    public NextStepVo nextStep(String subpackageCode) {
        //本标段下所有的供应商
        List<SupplierDao> supplierDaoList = supplierImpl.getSupplierList(subpackageCode, zero);
        //本标段下所有的评委
        List<JudgeDao> judgeDaoList = judgesImpl.judgesDtoList(subpackageCode);
        List<RecordDao> recordList = this.queryRecord(zero, subpackageCode);
        int number = supplierDaoList.size() * judgeDaoList.size();
        NextStepVo nsv = new NextStepVo();
        if (number > recordList.size()) {
            nsv.setSupplierDetailsVoList(null);
            nsv.setIncompleteJudge(getIncompleteJudgesListById(zero, subpackageCode, supplierDaoList, judgeDaoList));
            return nsv;
        }
        nsv.setSupplierDetailsVoList(supplierDetails(three, zero, supplierDaoList, subpackageCode));
        nsv.setIncompleteJudge(null);
        return nsv;
    }

    /**
     * 获取未完成评审的评委名单
     *
     * @param isType         评审类型
     * @param subpackageCode 标段code
     * @return
     */
    @Override
    public List<IncompleteJudgeVo> incompleteStatus(Integer isType, String subpackageCode) {
        //本标段下所有的供应商
        List<SupplierDao> supplierDaoList = supplierImpl.getSupplierList(subpackageCode, one);
        //本标段下所有的评委
        List<JudgeDao> judgeDaoList = judgesImpl.judgesDtoList(subpackageCode);
        List<RecordDao> recordList = this.queryRecord(isType, subpackageCode);
        if (supplierDaoList.size() * judgeDaoList.size() != recordList.size()) {
            return getIncompleteJudgesListById(isType, subpackageCode, supplierDaoList, judgeDaoList);
        }
        return null;
    }

    /**
     * 获取评审结果
     *
     * @param isType         评审类型
     * @param subpackageDao  标段类
     * @param subpackageCode 标段code
     * @return
     */
    @Override
    public List<SupplierDetailsVo> reviewResults(Integer isType, SubpackageDao subpackageDao, String subpackageCode) {
        //本标段下所有的供应商
        List<SupplierDao> supplierDaoList;
        if (subpackageDao.getReviewMode() == three && isType == one) {
            supplierDaoList = supplierImpl.getSupplierList(subpackageCode, one);
        } else {
            supplierDaoList = supplierImpl.getSupplierList(subpackageCode, zero);
        }
        return supplierDetails(subpackageDao.getReviewMode(), isType, supplierDaoList, subpackageCode);
    }


    /**
     * 查询并封装供应商的评审结果列表
     *
     * @param reviewMode      评审模式 : 1 只有合格  2- 只有打分  3 打分和合格并存
     * @param isType          打分类型 : 0-合格  1-打分
     * @param supplierDaoList 供应商列表
     * @param subpackageCode  标段code
     * @return
     */
    @Override
    public List<SupplierDetailsVo> supplierDetails(Integer reviewMode, Integer isType, List<SupplierDao> supplierDaoList, String subpackageCode) {
        List<SupplierDetailsVo> supplierDetailsVoList = new ArrayList<>();
        for (SupplierDao supplier : supplierDaoList) {
            SupplierDetailsVo sdv = new SupplierDetailsVo();
            // 根据标段code,供应商id,打分类型,查询review_record表数据
            List<RecordDao> supplierAndJudges = supplierRecordList(isType, subpackageCode, supplier.getSupplierId());
            if (CollectionUtils.isEmpty(supplierAndJudges)) {
                log.error("根据标段code={},供应商id={},打分类型={},查询review_record表数据为空", subpackageCode, supplier.getSupplierId(), reviewMode);
                return null;
            }
            sdv.setSubpackageCode(subpackageCode);
            sdv.setSupplierId(supplier.getSupplierId());
            sdv.setSupplierCompanyName(supplier.getSupplierCompanyName());
            if (reviewMode != two) {
                // 根据标段code和供应商id,在合格制的打分情况下,查询专家的打分列表,看是否是全部合格
                sdv.setQualifiedResult(transit(supplier.getSupplierId(), subpackageCode));
            }
            if (reviewMode != one) {
                // 查询打分制专家打分列表,计算分数的平均值
                sdv.setScoreResult(averageScore(supplier.getSupplierId(), subpackageCode));
            }
            // 组装专家的评审列表并返回
            sdv.setJudgesVoList(judgesVoList(reviewMode, subpackageCode, isType, supplierAndJudges));
            sdv.setIsType(isType);
            // 根据标段code,供应商id查询review_supplier表,查询到该供应商的评委推荐名次并返回
            sdv.setRank(supplierImpl.getRank(subpackageCode, supplier.getSupplierId()));
            supplierDetailsVoList.add(sdv);
        }
        return supplierDetailsVoList;
    }


    /**
     * 根据标段code,供应商id和打分类型查询review_record表
     *
     * @param isType
     * @param supplierId     供应商id
     * @param subpackageCode 标段code
     * @return
     */
    private List<RecordDao> queryRecordBySupplier(Integer isType, Long supplierId, String subpackageCode) {
        try {
            LambdaQueryWrapper<RecordDao> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(RecordDao::getSubpackageCode, subpackageCode);
            wrapper.eq(RecordDao::getSupplierId, supplierId);
            wrapper.eq(RecordDao::getIsType, isType);
            return this.list(wrapper);
        } catch (Exception e) {
            log.error("查询评审记录失败,isType={},supplierId={},subpackageCode={}", isType, supplierId, subpackageCode, e);
            throw new ServiceException("查询评审记录失败");
        }
    }

    /**
     * 组装专家的评审列表并返回
     *
     * @param reviewMode        评审模式 : 1 只有合格  2- 只有打分  3 打分和合格并存
     * @param subpackageCode    标段code
     * @param isType            打分类型 : 0-合格  1-打分
     * @param supplierAndJudges 评审记录
     * @return
     */
    private List<ReviewJudgesVo> judgesVoList(Integer reviewMode, String subpackageCode, Integer isType, List<RecordDao> supplierAndJudges) {
        List<ReviewJudgesVo> list = new ArrayList<>();
        // 根据打分类型,标段code 查询review_claims列表(评审规则集)
        Map<String, List<ClaimsDao>> resultMap = claimsImpl.claimsListBySubpackage(isType, subpackageCode).stream().collect(Collectors.groupingBy(ClaimsDao::getUuid));
        // 评审记录
        for (RecordDao dao : supplierAndJudges) {
            // 封装ReviewDetailsVo列表集合并返回
            List<ReviewDetailsVo> reviewDetailsVoList = collateReviewDetails(resultMap, JSON.parseObject(dao.getResultJson(), new TypeReference<List<ReviewDetailsVo>>() {
            }));
            ReviewJudgesVo vo = new ReviewJudgesVo();
            if (reviewMode != two) {
                vo.setQualifiedResult(one == dao.getQualifiedResult()); // 合格结果
            }
            if (reviewMode != one) {
                vo.setScoreResult(dao.getScoreResult());                // 平均分
            }
            vo.setJudgeName(dao.getJudgeName());                        // 专家名称
            vo.setJudgeId(dao.getJudgeId());                            //专家id
            vo.setReviewDetailsVoList(reviewDetailsVoList);             //评审详情
            list.add(vo);
        }
        return list;
    }

    /**
     * 封装ReviewDetailsVo列表集合并返回
     *
     * @param resultMap
     * @param reviewDetailsVoList
     * @return
     */
    private List<ReviewDetailsVo> collateReviewDetails(Map<String, List<ClaimsDao>> resultMap, List<ReviewDetailsVo> reviewDetailsVoList) {
        List<ReviewDetailsVo> details = new ArrayList<>();
        for (ReviewDetailsVo vo : reviewDetailsVoList) {
            ReviewDetailsVo detailsVo = new ReviewDetailsVo();
            BeanUtils.copyProperties(vo, detailsVo);
            resultMap.get(vo.getUuid()).stream().findFirst().ifPresent(newDao -> detailsVo.setReviewCriteria(newDao.getReviewCriteria()));
            details.add(detailsVo);
        }
        return details;
    }

    /**
     * 根据标段code,供应商id,打分类型,查询review_record表数据
     * 求 供应商 + 评委 是否合格 和 总分
     * 求平均 ：  detailsVos.stream().collect(Collectors.averagingDouble(ReviewDetailsVo::getScore));
     */
    private List<RecordDao> supplierRecordList(Integer isType, String subpackageCode, Long supplierId) {
        LambdaQueryWrapper<RecordDao> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RecordDao::getSubpackageCode, subpackageCode);
        wrapper.eq(RecordDao::getSupplierId, supplierId);
        wrapper.eq(RecordDao::getIsType, isType);
        return list(wrapper);
    }

    private RecordDao reviewRecordDtoToDao(SubmitResultDto dto) {
        RecordDao dao = new RecordDao();
        dao.setIsType(dto.getIsType());
        dao.setSubpackageCode(dto.getSubpackageCode());
        dao.setJudgeId(dto.getJudgeId());
        dao.setJudgeName(dto.getJudgeName());
        dao.setSupplierId(dto.getSupplierId());
        dao.setSupplierCompanyName(dto.getSupplierCompanyName());
        String json = JSON.toJSON(dto.getResultBoList()).toString();
        if (dto.getIsType() == zero) {
            dao.setResultJson(json);
            int sum = dto.getResultBoList().stream().mapToInt(ReviewDetailsVo::getQualified).sum();
            if (sum == dto.getResultBoList().size()) {
                dao.setQualifiedResult(one);
            } else {
                dao.setQualifiedResult(zero);
            }
        }
        if (dto.getIsType() == one) {
            dao.setResultJson(json);
            double sumScore = BigDecimal.valueOf(dto.getResultBoList().stream().mapToDouble(ReviewDetailsVo::getScore).sum()).setScale(two, RoundingMode.HALF_UP).doubleValue();
            dao.setScoreResult(sumScore);
        }
        return dao;
    }

    /**
     * 根据标段code和供应商id,在合格制的打分情况下,查询专家的打分列表,看是否是全部合格
     *
     * @param supplierId     供应商id
     * @param subpackageCode 标段code
     * @return true:全部合格,false:非全部合格
     */
    private Boolean transit(Long supplierId, String subpackageCode) {
        try {
            // 根据标段code,供应商id和打分类型查询review_record表(合格制的列表)
            List<RecordDao> supplierAndJudges = queryRecordBySupplier(zero, supplierId, subpackageCode);
            // 计算专家打分的总和
            int salarySum = supplierAndJudges.stream().mapToInt(RecordDao::getQualifiedResult).sum();
            return salarySum == supplierAndJudges.size();
        } catch (Exception e) {
            log.error("符合性审查数据异常,supplierId={},subpackageCode={}", supplierId, subpackageCode, e);
            throw new ServiceException("符合性审查数据异常");
        }
    }

    /**
     * 查询打分制专家打分列表,计算分数的平均值
     *
     * @param supplierId     供应商id
     * @param subpackageCode 标段code
     * @return
     */
    private Double averageScore(Long supplierId, String subpackageCode) {
        // 根据标段code,供应商id和打分类型查询review_record表
        List<RecordDao> supplierAndJudges = queryRecordBySupplier(one, supplierId, subpackageCode);
        OptionalDouble average = supplierAndJudges.stream().mapToDouble(RecordDao::getScoreResult).average();
        if (average.isPresent()) {
            return BigDecimal.valueOf(average.getAsDouble()).setScale(two, RoundingMode.HALF_UP).doubleValue();
        }
        return null;
    }

    private List<IncompleteJudgeVo> getIncompleteJudgesListById(Integer isType, String subpackageCode, List<SupplierDao> supplierDaoList, List<JudgeDao> judgeDaoList) {
        List<RecordDao> records = this.queryRecord(isType, subpackageCode);
        List<IncompleteJudgeVo> incompleteJudges = new ArrayList<>();
        for (JudgeDao judge : judgeDaoList) {
            for (SupplierDao supplier : supplierDaoList) {
                List<RecordDao> results = records.stream().filter(record -> record.getJudgeId().longValue() == judge.getJudgeId() && record.getSupplierId().longValue() == supplier.getSupplierId()).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(results)) {
                    //未完成评审的专家
                    incompleteJudges.add(new IncompleteJudgeVo(judge.getJudgeId(), judge.getJudgeName(), supplier.getSupplierCompanyName()));
                }
            }
        }
        if (!CollectionUtils.isEmpty(incompleteJudges)) {
            // 剔除重复未完成评审的专家
            return incompleteJudges.stream().distinct().collect(Collectors.toList());
        }
        return null;
    }
}
