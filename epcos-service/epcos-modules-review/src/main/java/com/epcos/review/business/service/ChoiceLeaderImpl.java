package com.epcos.review.business.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.bean.BeanUtils;
import com.epcos.review.business.api.IChoiceLeaderApi;
import com.epcos.review.business.api.IJudgeApi;
import com.epcos.review.domain.dao.JudgeDao;
import com.epcos.review.domain.dao.LeaderDao;
import com.epcos.review.domain.dto.ChoiceLeaderDto;
import com.epcos.review.domain.vo.VotingResultsVo;
import com.epcos.review.mapper.ChoiceLeaderMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 */
@Slf4j
@Service
public class ChoiceLeaderImpl extends ServiceImpl<ChoiceLeaderMapper, LeaderDao> implements IChoiceLeaderApi {
    private static final int one = 1;
    private static final int zero = 0;
    private final IJudgeApi judgesImpl;

    public ChoiceLeaderImpl(IJudgeApi judgesImpl) {
        this.judgesImpl = judgesImpl;
    }

    /**
     * 获取评委投票结果
     *
     * @param subpackageCode
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<VotingResultsVo> getVotingResults(String subpackageCode) {
        // 根据标段code查询 review_leader表
        List<LeaderDao> pee = this.getInitChoiceLeader(subpackageCode);

        if (CollectionUtils.isEmpty(pee)) {
            // 根据标段code查询review_leader表,修改review_leader表(投票)
            Boolean status = initDataJudges(subpackageCode);
            if (status) {
                pee = this.getInitChoiceLeader(subpackageCode);
            } else {
                log.error("初始化评委数据失败,subpackageCode:{}", subpackageCode);
                throw new RuntimeException("初始化评委数据失败");
            }
        }
        // 统计投票评委
        List<VotingResultsVo> voteStatusList = new ArrayList<>();
        for (LeaderDao p : pee) {
            VotingResultsVo vr = new VotingResultsVo();
            BeanUtils.copyProperties(p, vr);
            vr.setJudgeId(p.getJudgeId());      // 评委id
            vr.setJudgeName(p.getJudgeName());  // 评委名字
            vr.setVoting(zero);                 // 是否投票(否,0)

            int i = zero; // 票数
            List<String> ls = new ArrayList<>(); // 投票人姓名

            for (LeaderDao cl : pee) {
                if (!ObjectUtils.isEmpty(cl.getJudgeLeaderId())) {
                    if (p.getJudgeId().longValue() == cl.getJudgeLeaderId()) {
                        i = i + one;                //得票数量
                        ls.add(cl.getJudgeName());  //投票人姓名
                    }
                }
                if (p.getJudgeId().longValue() == cl.getJudgeId()) {
                    if (!ObjectUtils.isEmpty(cl.getJudgeLeaderId())) {
                        vr.setVoting(one); //是否投票
                    }
                }
            }
            vr.setNumberVotes(i);
            vr.setJudgeNames(ls);
            voteStatusList.add(vr);
        }
        return voteStatusList;
    }


    /**
     * 添加/修改 review_leader表(投票)
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addChoiceLeader(ChoiceLeaderDto dto) {
        LeaderDao dao = new LeaderDao();
        BeanUtils.copyProperties(dto, dao);
        try {
            // 根据标段code和评委id查询review_leader表
            List<LeaderDao> recordList = queryRecordList(dao.getSubpackageCode(), dao.getJudgeId());
            // 如果为空,则添加
            if (CollectionUtils.isEmpty(recordList)) {
                dao.setUpdateTime(LocalDateTime.now());
                dao.setUpdateTime(LocalDateTime.now());
                this.save(dao);
            } else { // 修改
                dao.setUpdateTime(LocalDateTime.now());
                LambdaUpdateWrapper<LeaderDao> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(LeaderDao::getSubpackageCode, dao.getSubpackageCode()).eq(LeaderDao::getJudgeId, dao.getJudgeId());
                this.update(dao, updateWrapper);
            }
        } catch (Exception e) {
            log.error("投票失败", e);
            throw new ServiceException("投票失败");
        }
        return true;
    }

    /**
     * 添加或修改review_leader表数据
     *
     * @param dao
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addChoiceLeaderDao(LeaderDao dao) {
        dao.setUpdateTime(LocalDateTime.now());
        try {
            List<LeaderDao> recordList = queryRecordList(dao.getSubpackageCode(), dao.getJudgeId());
            if (CollectionUtils.isEmpty(recordList)) {
                this.save(dao);
            } else {
                LambdaUpdateWrapper<LeaderDao> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(LeaderDao::getSubpackageCode, dao.getSubpackageCode()).eq(LeaderDao::getJudgeId, dao.getJudgeId());
                this.update(dao, updateWrapper);
            }
        } catch (Exception e) {
            log.error("投票失败", e);
            throw new ServiceException("投票失败");
        }
        return true;
    }


    /**
     * 根据标段code删除review_leader表数据
     * @param subpackageCode 标段code
     * @return
     */
    @Override
    public Boolean refreshChoiceLeader(String subpackageCode) {
        try {
            LambdaUpdateWrapper<LeaderDao> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(LeaderDao::getSubpackageCode, subpackageCode);
            this.remove(wrapper);
        } catch (Exception e) {
            log.error("清空投票失败", e);
            throw new ServiceException("清空投票失败");
        }
        return true;
    }


    /**
     * 根据标段code删除review_leader表数据
     *
     * @param subpackageCode 标段code
     * @return
     */
    @Override
    public Boolean deleteChoiceInfo(String subpackageCode) {
        try {
            LambdaUpdateWrapper<LeaderDao> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(LeaderDao::getSubpackageCode, subpackageCode);
            this.remove(wrapper);
        } catch (Exception e) {
            log.error("清空抽取记录失败", e);
            throw new ServiceException("清空抽取记录失败");
        }
        return true;
    }


    /**
     * 根据标段code查询 review_leader表
     *
     * @param subpackageCode 标段code
     * @return
     */
    @Override
    public List<LeaderDao> getInitChoiceLeader(String subpackageCode) {
        LambdaQueryWrapper<LeaderDao> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LeaderDao::getSubpackageCode, subpackageCode);
        return this.list(wrapper);
    }


    /**
     * review_leader表，设置组长(根据评委id和包code找到评委并将他设为组长)
     *
     * @param subpackageCode 包code
     * @param judgeId        评委id
     * @return
     */
    @Override
    public Boolean setLeader(String subpackageCode, Long judgeId) {
        try {
            LambdaUpdateWrapper<LeaderDao> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(LeaderDao::getSubpackageCode, subpackageCode);
            wrapper.eq(LeaderDao::getJudgeId, judgeId);
            wrapper.set(LeaderDao::getIsLeader, one);
            this.update(wrapper);
        } catch (Exception e) {
            log.error("设置组长失败", e);
            throw new ServiceException("设置组长失败");
        }
        return true;
    }

    /**
     * 根据标段code删除review_leader表信息
     *
     * @param subpackageCode 标段code
     * @return
     */
    @Override
    public Boolean cleanRecord(String subpackageCode) {
        try {
            LambdaUpdateWrapper<LeaderDao> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(LeaderDao::getSubpackageCode, subpackageCode);
            this.remove(updateWrapper);
        } catch (Exception e) {
            log.error("设置组长失败", e);
            throw new ServiceException("设置组长失败");
        }
        return true;
    }

    /**
     * 根据标段code查询review_leader表,修改review_leader表(投票)
     *
     * @param subpackageCode  标段code
     * @return                true:成功,false:失败
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean initDataJudges(String subpackageCode) {

        // 根据标段code查询 review_leader表
        List<LeaderDao> choices = this.getInitChoiceLeader(subpackageCode);
        if (CollectionUtils.isEmpty(choices)) {
            // 根据标段code查询review_judges表
            List<JudgeDao> judges = judgesImpl.judgesDtoList(subpackageCode);
            for (JudgeDao judge : judges) {
                ChoiceLeaderDto dto = new ChoiceLeaderDto();
                dto.setJudgeId(judge.getJudgeId());
                dto.setJudgeName(judge.getJudgeName());
                dto.setSubpackageCode(subpackageCode);
                dto.setDelegate(judge.getDelegate());
                // 修改 review_leader表(投票)
                Boolean status = this.addChoiceLeader(dto);
                if (!status) {
                    log.error("获取评委投票结果失败,subpackageCode={}", subpackageCode);
                    throw new ServiceException("获取评委投票结果失败");
                }
            }
        }
        return true;
    }

    /**
     * 查询投票人是否有投票记录
     * 根据标段code和
     */
    private List<LeaderDao> queryRecordList(String subpackageCode, Long judgeId) {
        LambdaQueryWrapper<LeaderDao> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LeaderDao::getSubpackageCode, subpackageCode);
        wrapper.eq(LeaderDao::getJudgeId, judgeId);
        return this.list(wrapper);
    }


}
