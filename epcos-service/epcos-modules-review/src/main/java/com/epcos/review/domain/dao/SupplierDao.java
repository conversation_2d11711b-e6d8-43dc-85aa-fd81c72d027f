package com.epcos.review.domain.dao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 */

@EqualsAndHashCode(callSuper = true)
@TableName("review_supplier")
@Data
public class SupplierDao extends BaseTableDao implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 供应商id
     */
    @TableField(value = "supplier_id")
    private Long supplierId;

    /**
     * 供应商单位名字
     */
    @TableField(value = "supplier_company_name")
    private String supplierCompanyName;

    /**
     * 是否被淘汰：1-合格 0-淘汰
     */
    @TableField(value = "review_score")
    private Integer reviewScore;
    /**
     * 合格制评审结果 ：1-合格，2-不合格
     */
    @TableField(value = "qualified_result")
    private Integer qualifiedResult;

    /**
     * 打分制评审结果最终分值
     */
    @TableField(value = "score_result")
    private Integer scoreResult;

    /**
     * 中标人名次 1-第一中标人 2 - 第二中标人 3 - 第三中标人
     */
    @TableField(value = "judge_ranking")
    private Integer judgeRanking;

    /**
     * 招标办确认中标人排名次序 1-第一中标人 2 - 第二中标人 3-第三中标人
     */
    @TableField(value = "purchase_ranking")
    private Integer purchaseRanking;

}
