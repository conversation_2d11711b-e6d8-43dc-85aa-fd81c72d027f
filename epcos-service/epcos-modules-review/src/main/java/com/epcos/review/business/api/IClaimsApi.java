package com.epcos.review.business.api;

import com.baomidou.mybatisplus.extension.service.IService;
import com.epcos.review.domain.dao.ClaimsDao;
import com.epcos.review.domain.dto.ClaimsDto;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 */
public interface IClaimsApi  extends IService<ClaimsDao> {


    /**
     * 根据打分类型,标段code 查询review_claims列表(评审规则集)
     * @param isType
     * @param subpackageCode
     * @return
     */
    List<ClaimsDao> claimsListBySubpackage( Integer isType, String subpackageCode);


    /**
     * 根据标段code查询review_claims表
     * @param subpackageCode
     * @return
     */
    List<ClaimsDao> claimsListAll(String subpackageCode);


    /**
     * 添加或修改review_claims表(评审规则集)数据
     * @param claimsDtoList
     * @return
     */
    Boolean  saveOrUpdateBatch (List<ClaimsDto> claimsDtoList);


    /**
     * 根据标段code删除review_claims表数据
     * @param subpackageCode
     * @return
     */
    Boolean deleteClaims(String subpackageCode);

    /**
     * 修改评审模块id(uuid)
     * @param dao
     * @return
     */
    Boolean updateClaims(ClaimsDao dao);
}
