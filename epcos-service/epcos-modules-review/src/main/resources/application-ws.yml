nacos:
  server-addr: ************:8848
  namespace: 2d9737c5-64b3-4d09-b738-6abce2d8cf34
  username: appnacos
  password: App@Whws#Nacos@_1688!

redis:
  host: ************
  port: 6379
  password: Epc#ws!@1688#epC!
  database: 1
  timeout: 5000

mysql:
  class-name: com.mysql.cj.jdbc.Driver
  url: *******************************************************************************************************************************************************************************************************************************************************************************
  username: root
  password: Epc@ws#E1688pC!#

server:
  port: 9204

spring:
  cloud:
    nacos:
      discovery:
        username: ${nacos.username}
        password: ${nacos.password}
        server-addr: ${nacos.server-addr}
        namespace: ${nacos.namespace}
        register-enabled: true
        service: ${spring.application.name}
        group: DEFAULT_GROUP
  redis:
    host: ${redis.host}
    port: ${redis.port}
    password: ${redis.password}
    database: ${redis.database}
    timeout: ${redis.timeout}
    lettuce:
      pool:
        enabled: true
        max-active: 50
        max-idle: 50
        min-idle: 2
        max-wait: 1000ms
        time-between-eviction-runs: 60000ms
  datasource:
    dynamic:
      druid:
        async-init: true
        initial-size: 5
        min-idle: 5
        max-active: 10
        max-wait: 30000
        time-between-eviction-runs-millis: 60000
        test-while-idle: true
        keep-alive: true
        min-evictable-idle-time-millis: 12000000
        validation-query: SELECT 1
        validation-query-timeout: 1
        pool-prepared-statements: false
        filters: stat,slf4j
        connection-properties:
          "druid.stat.mergeSql": true
          "druid.stat.slowSqlMillis": 5000
      datasource:
        # 主库数据源
        master:
          driver-class-name: ${mysql.class-name}
          url: ${mysql.url}
          username: ${mysql.username}
          password: ${mysql.password}

swagger:
  enabled: false

environment: ws



