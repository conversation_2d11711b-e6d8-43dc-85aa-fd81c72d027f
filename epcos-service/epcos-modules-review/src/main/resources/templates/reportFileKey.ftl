<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Title</title>
    <style>
        body {
            line-height: 1.5;
            font-size: 14px;
        }

        p {
            margin: 10px 0;
        }

        h2 {
            font-size: 24px;
            margin: 20px 0;
        }

        h3 {
            font-size: 20px;
            margin: 15px 0;
        }

        h4 {
            font-size: 16px;
            margin: 12px 0;
        }

        h5 {
            font-size: 14px;
            margin: 5px 0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            border-spacing: 0;
        }

        td, th {
            border: 1px solid #cbcbcb;
            padding: .5em;
            font-size: 14px;
            text-align: center;
            box-sizing: border-box;
        }

        .clearfix:after {
            visibility: hidden;
            display: block;
            font-size: 0;
            content: " ";
            clear: both;
            height: 0;
        }
        ul {
            padding: 0;
            list-style: none;
        }
        li {
            border-bottom: 1px solid #cbcbcb;
            padding: 5px 0;
        }
    </style>
</head>
<body>

<h2 style="text-align: center;"> ${eBase.projectName}${eBase.bidSectionName}</h2>
<h4 style="text-align: center;">${pMethod.procurementMethod} ${(eBase.abandonMark == 1)?string('废标情况说明','报告') }</h4>
<div style="width: 320px;margin: 0 auto 50px;">
    <p>招标人：XX 医院 </p>
    <p>招标项目：${eBase.projectName?default("/")}</p>
    <p>评委组长：${eBase.expertName?default("/")}</p>
    <p>报告日期：${reportDate?string("yyyy-MM-dd")}</p>
</div>

<div style="margin-bottom: 50px;">
    <h3>专家声明书</h3>
    <p>本人承诺在参与项目评审过程中：</p>
    <p>1、不接受任何单位或者个人明示或者暗示提出的倾向或者排斥特定投标人的要求；</p>
    <p>2、不对依法应当否决的投标不提出否决意见，不暗示或者诱导投标人作出澄清、说明或者接受投标人主动提出的澄清、说明；</p>
    <p>3、在中标结果确定之前，不向外透露对投标文件的评审、中标候选人的推荐情况以及与评标有关的其他情况；</p>
    <p>4、不收受招标人超出合理报酬以外的任何现金、有价证券和礼物；不收受有关利害关系人的任何财物和好处；</p>
    <p>
        5、在评标过程中，遵守有评标纪律，不向招标人征询其确定中标人的意向，服从评标委员会的统一安排；独立、客观、公正地履行评标专家职责。</p>
    <p>本人接受有关监督部门依法实施监督，如违反上述承诺或者不能履行评标专家职责，本人愿意承担一切由此带来的法律责任。</p>
    <p style="text-align: right;">特此声明</p>

    <table style="margin-top: 20px;">
        <tr>
            <td>评委成员签名</td>
            <td>
                <#if JudgesList ?exists>
                    <#list JudgesList as judges>
                        <span style="color:#fff;">${judges.expertId?c + "epcos"}-----</span>
                    </#list>
                </#if>
            </td>
        </tr>
    </table>
</div>


<div style="margin-bottom: 50px;">
    <h3>评审概况</h3>
    <table>
        <tr>
            <td>项目名称</td>
            <td>${eBase.projectName?default("/")}</td>
        </tr>
        <tr>
            <td>当前状态</td>
            <td> ${ (eBase.abandonMark == 1)?string('废标','正常') }</td>
        </tr>
        <tr>
            <td>招标内容</td>
            <td>详见招标文件</td>
        </tr>
        <tr>
            <td>评审时间</td>
            <td>${reportDate?string("yyyy-MM-dd")}</td>
        </tr>
        <tr>
            <td>评审方法</td>
            <td>综合评估法</td>
        </tr>
    </table>

    <#if JudgesList ?exists>
        <h4>评标委员会组成</h4>
        <table>
            <thead>
            <tr>
                <th>姓名</th>
                <th>专家科室</th>
                <th>证件号码</th>
                <th>在评标小组中所承担的工作</th>
            </tr>
            </thead>
            <tbody>
            <#list JudgesList as judges>
                <tr>
                    <td>${judges.expertName?default("/")}</td>
                    <td>${judges.department?default("/")}</td>
                    <td>${judges.idNumber?default("/")}</td>
                    <td>${(judges.judgesType == 1)?string('评委组长', '评委组员')} </td>
                </tr>
            </#list>
            </tbody>
        </table>
    </#if>

    <h4>评审结果</h4>
    <table>
        <thead>
        <tr>
            <th>参与单位名称</th>
            <th>初步评审结果</th>
            <th>详细评审结果</th>
            <th>专家打分明细</th>
            <th>评审结果</th>
        </tr>
        </thead>
        <tbody>
        <#list resultsList as rl>
            <tr>
                <td> ${rl.supplierCompanyName?default("/")}  </td>
                <td> ${rl.qualifiedResult?default("/")} </td>
                <td> ${rl.scoreResult?default("/")} </td>
                <#if  rl.avgScore ?? && (rl.avgScore?size > 0) >
                    <td style="padding: 0">
                        <table>
                            <#list rl.avgScore as avg>
                                <tr>
                                    <td>${avg.expertName}</td>
                                    <td>${avg.keepTwoScore}</td>
                                </tr>
                            </#list >
                        </table>
                    </td>
                <#else>
                    <td> ${rl.avgScore?default("/")} </td>
                </#if>
                <td> ${rl.ranking?default("/")}</td>
            </tr>
        </#list >
        </tbody>
    </table>

    <table>
        <tr>
            <td style="width: 30%;border-top: none;">评审情况说明</td>
            <td style="border-top: none;">
                <#if eBase.abandonMark == 1 >
                    ${eBase.remarksBidRejection}
                <#else>
                    /
                </#if>
            </td>
        </tr>
        <tr>
            <td>评委签名</td>
            <td>
                <#if JudgesList ?exists>
                    <#list JudgesList as judges>
                        <span style="color:#fff;">${judges.expertId?c + "epcos"}----</span>
                    </#list>
                </#if>
            </td>
        </tr>
    </table>
    <p>说明：附专家过程评审表、汇总表</p>
</div>

<!--报价详情 结束-->

<#if qualifiedInfo ?exists>
    <div>
        <h3>初步评审详情</h3>
        <#list qualifiedInfo  as qi >
            <div style="margin-bottom: 50px;">
                <div class="clearfix">
                    <h4 style="float: left;margin-top: 0;">合格制</h4>
                    <span style="float: right;margin-bottom: 10px;">评委： ${qi.expertName}</span>
                </div>
                <table>
                    <thead>
                    <tr>
                        <th style="width: 50px;">序号</th>
                        <th style="width: 30%;">审查因素</th>
                        <th style="width: 50%;">审查标准</th>
                        <th>${qi.supplierCompanyName}</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list qi.supplierByExpertReviewInfolist  as qc >
                        <tr>
                            <td>${qc?index + 1 } </td>
                            <td>${qc.reviewItem?default("/")}</td>
                            <td>${qc.reviewCriteria?default("/")}</td>
                            <td>${(qc.qualified == 1)?string('合格', '不合格')}</td>
                        </tr>
                    </#list>
                    </tbody>
                </table>

                <p style="text-align: right;">
                    评委签名：<span style="color:#fff;">${qi.expertId?c + "epcos"}</span>
                </p>
            </div>
        </#list>
    </div>
</#if>

<#if scoreInfo ?exists>
    <div style="margin-bottom: 50px;">
        <#list scoreInfo  as si >
            <div class="clearfix">
                <h4 style="float: left;margin-top: 0;">详细评审详情 </h4>
                <span style="float: right;margin-bottom: 10px;">评委： ${si.expertName}</span>
            </div>
            <table>
                <thead>
                <tr>
                    <th style="width: 50px;">序号</th>
                    <th style="width: 30%;">审查因素</th>
                    <th style="width: 50%;">审查标准</th>
                    <th>${si.supplierCompanyName}</th>
                </tr>
                </thead>

                <tbody>
                <#list si.supplierByExpertReviewInfolist as sc >
                    <tr>
                        <td>${sc?index + 1 } </td>
                        <td>${sc.reviewItem?default("/")}</td>
                        <td>${sc.reviewCriteria?default("/")}</td>
                        <td>${sc.scoreValue?default("/")}</td>
                    </tr>
                </#list >
                </tbody>
            </table>
            <p style="text-align: right;">
                总分: ${si.countScoreValue} <span style="color:#fff;"> ${si.expertId?c +"epcos"} </span>
            </p>
        </#list >
    </div>
</#if>

<div style="margin-bottom: 50px;">
    <h4 style="text-align: left">答疑记录</h4>
    <ul>
        <#if taqList ?exists>
            <#list taqList as tl >
                <li>
                    <p>
                        <span>【${tl.userName}-问】 ${tl.questionContent} </span>
                        <span style="margin-left: 10px;color: #999999;"> ${(tl.createTime)?string('dd.MM.yyyy HH:mm:ss')}</span>
                    </p>
                    <p>
                        <span>【${tl.answerName?default("/")}-答】${tl.answeringContent?default("/")}</span>
                    </p>
                </li>
            </#list >
        </#if>
    </ul>
</div>

</body>
</html>
