<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epcos.review.mapper.ReviewRecordMapper">


    <select id="selRecordNum" resultType="com.epcos.review.domain.vo.RecordVo">
        SELECT a.*, IFNULL(b.num, 0) AS recordNum
        FROM review_supplier a
                 left join (SELECT supplier_id, count(supplier_id) num
                            FROM review_record
                            WHERE subpackage_code = #{subpackageCode}
                            GROUP BY supplier_id) b on a.supplier_id = b.supplier_id
        WHERE a.subpackage_code = #{subpackageCode}
        ORDER BY recordNum desc
    </select>

    <select id="selRecordSum" resultType="com.epcos.review.domain.vo.RecordVo">
        SELECT a.*, IFNULL(b.num, 0) AS recordNum
        FROM review_supplier a
                 left join (SELECT supplier_id, sum(score_result) num
                            FROM review_record
                            WHERE subpackage_code = #{subpackageCode}
                            GROUP BY supplier_id) b on a.supplier_id = b.supplier_id
        WHERE a.subpackage_code = #{subpackageCode}
        ORDER BY recordNum desc
    </select>
</mapper>
