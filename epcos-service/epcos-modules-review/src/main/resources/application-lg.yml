nacos:
  server-addr: ************:8848
  namespace: 2d9737c5-64b3-4d09-b738-6abce2d8cf34
  username: appnacos
  password: App@BJxk#Nacos@_1688!

redis:
  host: ************
  port: 6379
  password: Epc#wzlg!@1688#epC!
  database: 1
  timeout: 5000

mysql:
  class-name: com.mysql.cj.jdbc.Driver
  url: ******************************************************************************************************************************************************************************************************************************************************************************
  username: root
  password: Epc@wzlg#1688epC!#


spring:
  cloud:
    nacos:
      username: ${nacos.username}
      password: ${nacos.password}
      server-addr: ${nacos.server-addr}
      discovery:
        heart-beat-interval: 10000
        heart-beat-timeout: 30000
        ip-delete-timeout: 60000
        namespace: ${nacos.namespace}
  redis:
    host: ${redis.host}
    port: ${redis.port}
    password: ${redis.password}
    database: ${redis.database}
    timeout: ${redis.timeout}
    lettuce:
      pool:
        enabled: true
        max-active: 50
        max-idle: 50
        min-idle: 2
        max-wait: 1000ms
        time-between-eviction-runs: 60000ms
  datasource:
    dynamic:
      druid:
        async-init: true
        initial-size: 25
        min-idle: 20
        max-active: 150
        max-wait: 30000
        time-between-eviction-runs-millis: 60000
        test-while-idle: true
        keep-alive: true
        min-evictable-idle-time-millis: 12000000
        validation-query: SELECT 1 FROM DUAL
        validation-query-timeout: 1
        pool-prepared-statements: false
        filters: stat,slf4j
        connection-properties:
          "druid.stat.mergeSql": true
          "druid.stat.slowSqlMillis": 5000
      datasource:
        # 主库数据源
        master:
          driver-class-name: ${mysql.class-name}
          url: ${mysql.url}
          username: ${mysql.username}
          password: ${mysql.password}

# mybatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.epcos.review
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath:mapper/**/*.xml

server:
  port: 9204

# swagger配置
swagger:
  title: 评委组评分接口文档
  license: Powered By epc
  licenseUrl: https://www.google.com
  enabled: false

environment: lg



