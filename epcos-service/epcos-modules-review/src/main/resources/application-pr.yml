nacos:
  server-addr: epcdb:8848
  namespace: 2d9737c5-64b3-4d09-b738-6abce2d8cf34
  username: appnacos
  password: Epc_nacos!#

redis:
  host: epcdb
  port: 6379
  password: YjcPr123!redis@#
  timeout: 5000

mysql:
  class-name: com.mysql.cj.jdbc.Driver
  url: **********************************************************************************************************************************************************************************************************************************************************************
  username: root
  password: YjcPr123!@mysql#

spring:
  cloud:
    nacos:
      username: ${nacos.username}
      password: ${nacos.password}
      server-addr: ${nacos.server-addr}
      discovery:
        heart-beat-interval: 10000
        heart-beat-timeout: 30000
        ip-delete-timeout: 60000
        namespace: ${nacos.namespace}
  redis:
    host: ${redis.host}
    port: ${redis.port}
    password: ${redis.password}
    timeout: ${redis.timeout}
  datasource:
    dynamic:
      druid:
        async-init: true
        initial-size: 5
        min-idle: 5
        max-active: 50
        max-wait: 5000
        time-between-eviction-runs-millis: 30000
        test-while-idle: true
        keep-alive: true
        min-evictable-idle-time-millis: 600000
        test-on-borrow: false
        test-on-return: false
        validation-query: SELECT 1
        validation-query-timeout: 2000
        pool-prepared-statements: false
        filters: stat,slf4j
        connection-properties:
          "druid.stat.mergeSql": true
          "druid.stat.slowSqlMillis": 3000
      datasource:
        # 主库数据源
        master:
          driver-class-name: ${mysql.class-name}
          url: ${mysql.url}
          username: ${mysql.username}
          password: ${mysql.password}

# mybatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.epcos.review
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath:mapper/**/*.xml

# swagger配置
swagger:
  enabled: false

environment: pr



