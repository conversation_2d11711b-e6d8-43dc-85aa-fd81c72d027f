package com.epcos.system.manage.mapper;

import com.epcos.system.api.domain.common.SupplierCertificate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


@Mapper
public interface SupplierCertificateMapper {

    /**
     * 批量添加供应商证书表
     * @param supplierCertificateList
     * @return
     */
    Integer insertToList(@Param("supplierCertificateList") List<SupplierCertificate> supplierCertificateList);

    /**
     * 根据供应商id删除供应商证书
     * @param supplierId
     * @return
     */
    Integer deleteBySupplierId(Long supplierId);

    /**
     * 根据供应商id查询供应商证书
     * @param supplierId
     * @return
     */
    List<SupplierCertificate> selBySupplierId(Long supplierId);

    /**
     * 根据供应商id查询供应商证书
     * @param supplierId
     * @return
     */
    List<SupplierCertificate> selByTime(@Param("supplierId") Long supplierId ,@Param("timeNum") Integer timeNum);
}
