package com.epcos.system.manage.business.api;

import com.epcos.common.core.domain.R;
import com.epcos.common.core.web.page.TableDataVo;
import com.epcos.system.api.domain.common.SupplierCertificate;
import com.epcos.system.api.domain.common.SupplierInfoShare;
import com.epcos.system.api.model.SupplierCompanyAndOrgPageDto;
import com.epcos.system.api.model.SupplierCompanyAndSingleOrgVO;
import com.epcos.system.api.model.SupplierCompanyPageDto;
import com.epcos.system.api.model.SupplierCompanyVO;

import java.util.List;
import java.util.Set;

public interface ISupplierCertificateService {

    /**
     * 根据供应商ids删除供应商证书
     * @param supplierUserIdList
     */
    void delBySupplierId(List<Long> supplierUserIdList);
}
