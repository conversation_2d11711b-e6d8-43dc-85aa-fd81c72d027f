package com.epcos.system.webservice.jxzl.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;

public class SyncWebserviceUtil {

    private static final XmlMapper XML_MAPPER = new XmlMapper();

    public static String responseXml(boolean success, String msg) {
        return "<RESPONSE>\n" +
                "<RESULT_CODE>" + success + "</RESULT_CODE>\n" +
                "<RESULT_CONTENT>" + msg + "</RESULT_CONTENT>\n" +
                "</RESPONSE>";
    }

    public static <T> T readXml(String xml, Class<T> clazz) throws JsonProcessingException {
        String valueXml = XML_MAPPER.readTree(xml).get("REQUEST").get("PARG").get("REQUEST").get("VALUE").asText();
        return XML_MAPPER.readValue(valueXml, clazz);
    }

}
