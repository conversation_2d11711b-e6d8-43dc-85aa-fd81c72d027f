package com.epcos.system.manage.business.service;

import cn.hutool.core.date.DateTime;
import com.epcos.bidding.purchase.api.RemotePurchaserService;
import com.epcos.common.core.constant.SecurityConstants;
import com.epcos.common.core.constant.UserConstants;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.web.domain.user.SysUser;
import com.epcos.common.core.web.page.TableDataVo;
import com.epcos.system.api.domain.SysDictData;
import com.epcos.system.api.domain.SysUserOrg;
import com.epcos.system.api.domain.common.SupplierCertificate;
import com.epcos.system.api.domain.common.SupplierInfoShare;
import com.epcos.system.api.domain.dto.EsignAndUserDto;
import com.epcos.system.api.model.*;
import com.epcos.system.manage.business.api.*;
import com.epcos.system.manage.domain.SupplierCompany;
import com.epcos.system.manage.mapper.SupplierCertificateMapper;
import com.epcos.system.manage.mapper.SupplierCompanyMapper;
import com.epcos.system.manage.mapper.SysDictDataMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class SupplierCertificateServiceImpl implements ISupplierCertificateService {

    @Autowired
    private SupplierCertificateMapper supplierCertificateMapper;

    /**
     * 根据供应商ids删除供应商证书
     *
     * @param supplierUserIdList
     */
    @Override
    public void delBySupplierId(List<Long> supplierUserIdList) {
        if (supplierUserIdList != null && !supplierUserIdList.isEmpty()){
            for (Long supplierId : supplierUserIdList) {
                supplierCertificateMapper.deleteBySupplierId(supplierId);
            }
        }
    }
}
