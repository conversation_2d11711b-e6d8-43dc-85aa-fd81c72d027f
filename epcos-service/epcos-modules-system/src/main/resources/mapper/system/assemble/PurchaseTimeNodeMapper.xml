<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epcos.system.assmble.mapper.PurchaseTimeNodeMapper">

    <resultMap type="PurchaseTimeNode" id="PurchaseTimeNodeResult">
        <result property="purchaseTimeNodeId" column="purchase_time_node_id"/>
        <result property="purchaseMethodCode" column="purchase_method_code"/>
        <result property="timeKeyName" column="time_key_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectPurchaseTimeNodeVo">
        select purchase_time_node_id, purchase_method_code, time_key_name, create_time, update_time
        from purchase_time_node
    </sql>

    <select id="selectPurchaseTimeNodeList" parameterType="PurchaseTimeNode" resultMap="PurchaseTimeNodeResult">
        <include refid="selectPurchaseTimeNodeVo"/>
        <where>
            <if test="purchaseMethodCode != null  and purchaseMethodCode != ''">and purchase_method_code =
                #{purchaseMethodCode}
            </if>
            <if test="timeKeyName != null  and timeKeyName != ''">and time_key_name like concat('%', #{timeKeyName},
                '%')
            </if>
        </where>
    </select>

    <select id="selectPurchaseTimeNodeByPurchaseTimeNodeId" parameterType="Long" resultMap="PurchaseTimeNodeResult">
        <include refid="selectPurchaseTimeNodeVo"/>
        where purchase_time_node_id = #{purchaseTimeNodeId}
    </select>

    <insert id="insertPurchaseTimeNode" parameterType="PurchaseTimeNode" useGeneratedKeys="true"
            keyProperty="purchaseTimeNodeId">
        insert into purchase_time_node
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="purchaseMethodCode != null and purchaseMethodCode != ''">purchase_method_code,</if>
            <if test="timeKeyName != null and timeKeyName != ''">time_key_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="purchaseMethodCode != null and purchaseMethodCode != ''">#{purchaseMethodCode},</if>
            <if test="timeKeyName != null and timeKeyName != ''">#{timeKeyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updatePurchaseTimeNode" parameterType="PurchaseTimeNode">
        update purchase_time_node
        <trim prefix="SET" suffixOverrides=",">
            <if test="purchaseMethodCode != null and purchaseMethodCode != ''">purchase_method_code =
                #{purchaseMethodCode},
            </if>
            <if test="timeKeyName != null and timeKeyName != ''">time_key_name = #{timeKeyName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where purchase_time_node_id = #{purchaseTimeNodeId}
    </update>

    <delete id="deletePurchaseTimeNodeByPurchaseTimeNodeId" parameterType="Long">
        delete
        from purchase_time_node
        where purchase_time_node_id = #{purchaseTimeNodeId}
    </delete>

    <delete id="deletePurchaseTimeNodeByPurchaseTimeNodeIds" parameterType="String">
        delete from purchase_time_node where purchase_time_node_id in
        <foreach item="purchaseTimeNodeId" collection="array" open="(" separator="," close=")">
            #{purchaseTimeNodeId}
        </foreach>
    </delete>

    <insert id="insertBatch">
        INSERT INTO `purchase_time_node` (
        `purchase_method_code`,
        `time_key_name`,
        `create_time`
        ) VALUES
        <foreach collection="timeKeyNameList" item="timeKeyName" separator=",">
            (
            #{purchaseMethodCode},
            #{timeKeyName},
            now()
            )
        </foreach>
    </insert>

    <delete id="deleteTimeNodeByMethodCode" parameterType="string">
        delete
        from purchase_time_node
        where purchase_method_code = #{purchaseMethodCode}
    </delete>

    <select id="selectByPurchaseMethodCode" parameterType="string" resultMap="PurchaseTimeNodeResult">
        <include refid="selectPurchaseTimeNodeVo"/>
        where purchase_method_code = #{purchaseMethodCode}
    </select>


</mapper>