package com.epcos.file.common.seal;

import com.epcos.common.core.exception.ServiceException;
import com.timevale.esign.sdk.tech.bean.OrganizeBean;
import com.timevale.esign.sdk.tech.bean.PersonBean;
import com.timevale.esign.sdk.tech.bean.result.AddAccountResult;
import com.timevale.esign.sdk.tech.bean.result.GetAccountProfileResult;
import com.timevale.esign.sdk.tech.bean.result.Result;
import com.timevale.esign.sdk.tech.impl.constants.LicenseQueryType;
import com.timevale.esign.sdk.tech.service.AccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.text.MessageFormat;

/**
 * description 证书服务辅助类
 *
 * <AUTHOR>
 * date  2022年7月1日上午10:46:44
 */
@Slf4j
@Service
public class AccountHelper {

    @Autowired
    private ClientHelper clientHelper;

    /**
     * description 创建个人账户
     * 创建个人账户，所创建账户是半实名的，即在快捷签对接项目中可以正常签署，
     * 在e签宝平台中无法使用，必须重新通过实名认证后才可以正常使用
     * {@link AccountService#addAccount(PersonBean)}
     *
     * @param personBean {@link PersonBean}个人信息详情
     * <AUTHOR>
     */
    public String addAccount(PersonBean personBean) {
        AddAccountResult acctRst = clientHelper.getAccountService().addAccount(personBean);
        return castAddAccount(acctRst, "个人","OTHER",personBean.getIdNo());
    }

    /**
     * description 创建企业账户
     * 创建企业账户，所创建账户是半实名的，即在快捷签对接项目中可以正常签署，
     * 在e签宝平台中无法使用，必须重新通过实名认证后才可以正常使用
     * {@link AccountService#addAccount(OrganizeBean)}
     *
     * @param organizeBean {@link OrganizeBean}企业信息详情
     * <AUTHOR>
     */
    public String addAccount(OrganizeBean organizeBean) {
        AddAccountResult acctRst = clientHelper.getAccountService().addAccount(organizeBean);
        return castAddAccount(acctRst, "企业","MERGE",organizeBean.getOrganCode());
    }

    /**
     * description 注销账户
     * 注销账户，注销后账户将不可再使用，请谨慎调用
     * {@link AccountService#deleteAccount(String)}
     *
     * @param accountId {@link String}待注销账号的标识
     */
    public void deleteAccount(String accountId) {
        Result rst = clientHelper.getAccountService().deleteAccount(accountId);

        if (rst.getErrCode() == 0) {
            log.info("注销账户成功，accountId={}", accountId);
            return;
        }
        if (rst.getErrCode() == 1007) {
            log.error("注销账户失败,accountId={},msg={}", accountId, rst.getMsg());
            return;
        }
        log.error("注销账户失败,accountId={},r={}", accountId, rst);
        throw new ServiceException(rst.getMsg());
    }

    /**
     * 查询e签宝账户id
     * @param licenseQueryType
     * @param idNo
     * @return
     */
    public String organAccountId(String licenseQueryType, String idNo){
        GetAccountProfileResult acctRst = null;
        if (licenseQueryType.equals("MERGE")) {
            acctRst = clientHelper.getAccountService().getAccountInfoByIdNo(idNo, LicenseQueryType.MERGE);
        }
        if (licenseQueryType.equals("OTHER")) {
            acctRst = clientHelper.getAccountService().getAccountInfoByIdNo(idNo, LicenseQueryType.OTHER);
        }
        if (acctRst == null) {
            log.error("查询账号信息失败,licenseQueryType={},idNo={}", licenseQueryType, idNo);
            throw new ServiceException("查询账号信息失败");
        }
        if (ObjectUtils.isEmpty(acctRst) || acctRst.getErrCode() != 0) {
            log.error("查询账号信息失败,licenseQueryType={},idNo={}", licenseQueryType, idNo);
            throw new ServiceException(acctRst.getMsg());
        }
        return acctRst.getAccountInfo().getAccountUid();
    }

    /**
     * 处理创建账户返回结果
     * @param acctRst
     * @param typeMsg
     * @param licenseQueryType
     * @param idNo  e签宝 错误码: 1500012 (name字段冲突异常)
     * @return
     */
    private String castAddAccount(AddAccountResult acctRst, String typeMsg, String licenseQueryType, String idNo) {
        if (acctRst.getErrCode() != 0 || acctRst.getAccountId() == null || acctRst.getAccountId().isEmpty()) {
            log.error("创建e签宝账户报错,code={},msg={},accountId={}", acctRst.getErrCode(),acctRst.getMsg(),acctRst.getAccountId());
            deleteAccount(organAccountId(licenseQueryType,idNo));
            throw new ServiceException(typeMsg + "账户创建失败:" + acctRst.getMsg());
        }
        log.info("创建{}账号成功:accountId={},请妥善保管AccountId以便后续场景存证使用", typeMsg, acctRst.getAccountId());
        return acctRst.getAccountId();
    }
}
