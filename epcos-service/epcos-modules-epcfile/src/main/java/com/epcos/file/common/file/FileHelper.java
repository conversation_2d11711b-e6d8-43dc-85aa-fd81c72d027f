package com.epcos.file.common.file;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ZipUtil;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.utils.StringUtils;
import com.epcos.epcfile.api.domain.dto.FileUploadDto;
import com.epcos.epcfile.api.domain.vo.SysFileVo;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import javax.activation.MimetypesFileTypeMap;
import javax.servlet.ServletException;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.LinkOption;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.rmi.ServerException;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 */
@Log4j2
public class FileHelper {

    private static final Set<String> allowedFileTypes = new HashSet<>();
    private static final Map<Integer, String> fileType = new HashMap<>();

    // 初始化允许的文件类型集合
    static {

        // 文件格式
        allowedFileTypes.add("pdf");
        allowedFileTypes.add("doc");
        allowedFileTypes.add("docx");
        allowedFileTypes.add("xlsx");
        allowedFileTypes.add("xls");
        allowedFileTypes.add("txt");
        // 图片格式
        allowedFileTypes.add("jpg");
        allowedFileTypes.add("jpeg");
        allowedFileTypes.add("png");
        allowedFileTypes.add("gif");
        allowedFileTypes.add("bmp");
        allowedFileTypes.add("heic");
        // 音频
        allowedFileTypes.add("mp3");
        allowedFileTypes.add("wav");
        allowedFileTypes.add("aac");
        allowedFileTypes.add("m4a");
        // 视频
        allowedFileTypes.add("mp4");
        allowedFileTypes.add("mov");
        allowedFileTypes.add("avi");
        allowedFileTypes.add("mkv");
        // 其它格式
        allowedFileTypes.add("zip");
        allowedFileTypes.add("zepc");
        allowedFileTypes.add("tepc");
        allowedFileTypes.add("xml");
        allowedFileTypes.add("dwg");


        fileType.put(0, "nonFile");
        fileType.put(1, "projectFile");
        fileType.put(2, "enclosureFile");
        fileType.put(3, "agentFile");
        fileType.put(4, "goodsFile");


    }

    /**
     * 通过数字拿文件大类型 例: nonFile/
     *
     * @param fileTypeI
     * @return
     */
    public static String getFileType(Integer fileTypeI) {
        return fileType.get(fileTypeI);
    }

    /**
     * 校验文件名
     *
     * @param fileName 文件名
     */
    public static void fileNameCheck(String fileName) {
        // 文件名基本校验
        if (fileName == null || StringUtils.isEmpty(fileName) || !fileName.contains(".")) {
            log.error("文件名不存在或文件名没有后缀,fileName:{}", fileName);
            throw new ServiceException("文件名不存在或文件名没有后缀");
        }
        // 判断文件类型
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        // 将文件名后缀改为小写
//        String lowerSuffix = suffix.toLowerCase();
        if (!allowedFileTypes.contains(suffix)) {
            log.error("非法文件后缀,fileName:{}", fileName);
            throw new ServiceException("非法文件后缀:" + suffix);
        }
        // 判断文件中是否含有非法字符
        if (!fileName.matches("[^\\s\\\\/:*?\"<>|](\\x20|[^\\s\\\\/:*?\"<>|])*[^\\s\\\\/:*?\"<>|.]$")) {
            log.error("文件名中包含非法字符,fileName:{}", fileName);
        }
        // 文件不能以.结尾
        if (fileName.endsWith(".")) {
            log.error("文件名不能以.结尾,fileName:{}", fileName);
            throw new ServiceException("文件名不能以.结尾");
        }
    }

    /**
     * 校验pdf文件是否合法
     *
     * @param file
     * @return
     */
    public static void isValidPDF(MultipartFile file) {
        String suffix = Objects.requireNonNull(file.getOriginalFilename()).substring(file.getOriginalFilename().lastIndexOf(".") + 1);
        if ("pdf".equalsIgnoreCase(suffix)) {
            Itext7PdfScriptDetector scriptDetector = new Itext7PdfScriptDetector();
            try {
                Itext7PdfScriptDetector.ScriptDetectionResult scripts = scriptDetector.detectScripts(file.getInputStream(), file.getOriginalFilename());
                if (scripts.isContainsScript()) {
                    log.error("pdf文件中存在脚本,suffix={}", suffix);
                    throw new ServiceException("pdf文件中存在异常,禁止上传!!!");
                }
            } catch (Exception e) {
                log.error("MultipartFile文件写入内存失败,file={}", file.getOriginalFilename());
                throw new ServiceException("MultipartFile文件写入内存失败");
            }
        }
    }

    /**
     * 文件校验
     *
     * @param file 文件
     */
    public static void fileCheck(MultipartFile file) {
        // 校验空文件
        if (file.getSize() < 1) {
            log.error("空文件不允许上传,file:{}", file.getOriginalFilename());
            throw new ServiceException("空文件不允许上传");
        }
        // 校验文件名
        fileNameCheck(file.getOriginalFilename());
        // 检验过大文件 (限制大小: 1G)
//        if (file.getSize() > 1024 * 1024 * 1024){
//            log.error("文件过大不允许上传,file:{}", file.getOriginalFilename());
//            throw new ServiceException("文件过大不允许上传");
//        }
        // 校验pdf文件是否合法
        isValidPDF(file);
    }

    /**
     * 修改文件名(如果文件名过长,自动截取文件名)
     *
     * @param fileName 文件名
     * @return
     */
    public static String updateFileName(String fileName) {
        log.error("文件名去特殊字符前,fileName={}", fileName);
        fileName = fileName.replaceAll("[\\\\/:*?\"<>|]", "-");
        log.error("文件名去特殊字符后,fileName={}", fileName);
        // 纯文件名,不包含后缀
        String frontFileName = fileName.substring(0, fileName.lastIndexOf("."));
        // 文件后缀
        String fileSuffix = fileName.substring(fileName.lastIndexOf("."));
        if (frontFileName.length() > 128) {
            frontFileName = fileName.substring(0, 128);
        }
        return frontFileName.trim() + getRandom() + fileSuffix;
    }


    /**
     * 根据文件路径上传
     *
     * @param baseDir 相对应用的基目录
     * @param file    上传的文件
     * @return 文件绝对路径 + 文件名称
     */
    public static SysFileVo upload(String baseDir, MultipartFile file) {
        // 文件校验
        fileCheck(file);
        // 修改后的文件名
        String fileName = FileHelper.updateFileName(Objects.requireNonNull(file.getOriginalFilename()));
        // 文件绝对路径
        String filepath = baseDir + fileName;
        try {
            File dest = new File(filepath);
            // 检查父目录是否存在,不存在则创建
            if (!dest.getParentFile().exists()) {
                boolean bool = dest.getParentFile().mkdirs();
            }
            // 文件复制到本地
            file.transferTo(dest);
            // 判断文件后缀是否为pdf和是否包含评审报告
            if (getFileSuffix(fileName).equalsIgnoreCase("PDF") && fileName.contains("评审报告")) {
                String newPageFilePath = baseDir + FileHelper.updateFileName(Objects.requireNonNull(file.getOriginalFilename()));
                // 为pdf文件添加页码
                boolean b = AddPageNum.addPageNumbers(dest, newPageFilePath);
                if (b) {
                    delFile(filepath);
                    return createSysFile(fileName, newPageFilePath);
                } else {
                    return createSysFile(fileName, filepath);
                }
            }
            return createSysFile(fileName, filepath);
        } catch (Exception e) {
            log.error("文件上传失败:", e);
            throw new ServiceException("upload,文件上传失败");
        }
    }


    /**
     * 封装数据（文件名称、文件地址）
     *
     * @param fileName 文件名
     * @param fileCode 文件唯一code/文件路径(共用)
     * @return
     */
    private static SysFileVo createSysFile(String fileName, String fileCode) {
        SysFileVo sysFile = new SysFileVo();
        sysFile.setName(fileName);
        sysFile.setUrl(fileCode);
        return sysFile;
    }


//    /**
//     * 判断文件扩展名是否是pdf
//     *
//     * @param input 文件扩展名
//     * @return true：是pdf，false：不是pdf
//     */
//    private static Boolean filenameIgnoreCase(String input) {
//        // 去除文件扩展名上的.
//        String fileType = getFileType(input);
//        String[] strArray = fileType.split("\\.");
//        String fileTypeName = strArray[strArray.length - 1];
//        return fileTypeName.equalsIgnoreCase("PDF");
//    }
//
//    /**
//     * 去除文件扩展名上的. ， 例：.txt -> txt
//     *
//     * @param fileName 文件扩展名
//     * @return
//     */
//    public static String getFileType(String fileName) {
//        String[] strArray = fileName.split("\\.");
//        return strArray[strArray.length - 1];
//    }

//    /**
//     * 保存pdf文件到本地
//     *
//     * @param orgPdfPath
//     * @param outputPdfPath
//     */
//    private static void addPageNum(String orgPdfPath, String outputPdfPath) {
//        AddPageNum.addPageNum(orgPdfPath, outputPdfPath);
//    }

//    /**
//     * 生成新文件名，拼接上之前的目录，形成文件的绝对路径
//     *
//     * @param baseDir  文件完整路径
//     * @param fileName 文件名称
//     * @return
//     */
//    private static String fileAbsolutePath(String baseDir, String fileName) {
//        StringBuilder stringBuffer = new StringBuilder(baseDir);
//        // 获取文件扩展名
//        String fileHandling = getFileSuffix(fileName);
//        // 去除文件名的扩展名，并且去除文件名中的所有“/”
//        String fileNameByHandling = fileName.replace("." + fileHandling, "").replaceAll("/", "");
//        // 获取一个随机6位字符串
//        String fileRandomName = getRandom();
//        // 拼接新文件名
//        stringBuffer.append(fileNameByHandling).append(fileRandomName).append(".").append(fileHandling);
//        return stringBuffer.toString();
//    }


    /**
     * 获取一个随机6位字符串，例：-7g6jf6
     *
     * @return
     */
    private static String getRandom() {
        String source = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
        Random r = new Random();
        StringBuilder rs = new StringBuilder();
        for (int j = 0; j < 6; j++) {
            rs.append(source.charAt(r.nextInt(62)));
        }
        return "-" + rs;
    }

    /**
     * 将用户ID转换成一个路径字符串
     *
     * @param userId
     * @return
     */
    public static String longToPath(Long userId) {
        String n = String.valueOf(userId);
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < n.length(); i++) {//for循环遍历
            sb.append(n.charAt(i)).append("/");
        }
        return sb.toString();
    }


    /**
     * 通过路径获取文件名
     *
     * @param filePath
     * @return
     */
    public static String pathToFileName(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return "";
        }
        int lastSlashIndex = Math.max(filePath.lastIndexOf('/'), filePath.lastIndexOf('\\'));
        return lastSlashIndex >= 0 ? filePath.substring(lastSlashIndex + 1) : filePath;
    }


    /**
     * 压缩文件到指定目录下
     *
     * @param files           需要压缩的文件map
     * @param zipByCreatePath 得到的压缩文件路径
     */
    public static void zipPersonPhotoFile(Map<String, String> files, String zipByCreatePath) {
        ZipOutputStream outputStream = null;
        try {
            File folder = new File(zipByCreatePath);
            if (!folder.getParentFile().exists()) {
                folder.getParentFile().mkdirs();
            }
            if (folder.exists()) {
                folder.delete();
            }
            outputStream = new ZipOutputStream(Files.newOutputStream(Paths.get(zipByCreatePath)));
            Set<Map.Entry<String, String>> entrySet = files.entrySet();
            for (Map.Entry<String, String> file : entrySet) {
                try {
                    String filePath = file.getValue();
                    if (filePath.contains(zipByCreatePath)) {
                        continue;
                    }
                    File fileTo = new File(filePath);
                    boolean flag = fileTo.exists();
                    if (flag) {
                        zipFile(getInputStream(filePath), file.getKey(), outputStream , fileTo.length());
                        log.error("【zipFile 完成】 zipByCreatePath = {}", zipByCreatePath);
                    }
                } catch (Exception e) {
                    log.error("zipFile失败e={},zipByCreatePath={}", e, zipByCreatePath);
                }
            }
        } catch (Exception e) {
            log.error("压缩文件失败,e={}, zipByCreatePath={}", e, zipByCreatePath);
        } finally {
            try {
                outputStream.close();
            } catch (IOException e) {
                log.error("压缩文件失败,e={},zipByCreatePath={}", e, zipByCreatePath);
            }
        }
    }


    /**
     * 将文件写入到zip文件中
     */
    public static void zipFile(InputStream is, String fileName, ZipOutputStream outputStream , Long fileSize) throws IOException, ServletException {
        if (is == null) {
            log.error("文件不存在！");
            throw new IOException("文件不存在！");
        }
        try (BufferedInputStream bInStream = new BufferedInputStream(is)) {
            ZipEntry entry = new ZipEntry(fileName);
            outputStream.putNextEntry(entry);
            byte[] buffer = new byte[getBufferSize(fileSize)];
            int len;
            while ((len = bInStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, len);
            }
            outputStream.closeEntry();
        } catch (IOException e) {
            log.error("压缩文件失败, fileName={}, e={}", fileName, e);
            throw new ServletException("压缩文件失败！");
        }
    }

    /**
     * 根据文件大小返回合适的缓冲区大小（单位：字节）
     */
    private static int getBufferSize(long fileSize) {
        if (fileSize < 1024L) { // < 1KB
            return 1024; // 1KB
        } else if (fileSize < 10 * 1024L * 1024L) { // < 10MB
            return 8 * 1024; // 8KB
        } else if (fileSize < 100 * 1024L * 1024L) { // < 100MB
            return 64 * 1024; // 64KB
        } else if (fileSize < 500 * 1024L * 1024L) { // < 500MB
            return 128 * 1024; // 128KB
        } else if (fileSize < 1024L * 1024L * 1024L) { // < 1GB
            return 256 * 1024; // 256KB
        } else if (fileSize < 2 * 1024L * 1024L * 1024L) { // < 2GB
            return 512 * 1024; // 512KB
        } else { // > 2GB
            return 1024 * 1024; // 1MB
        }
    }


//    /**
//     * 将文件写入到zip文件中
//     */
//    public static void zipFile(InputStream is, String fileName, ZipOutputStream outputstream) throws IOException, ServletException {
//        if (is != null) {
//            BufferedInputStream bInStream = new BufferedInputStream(is);
//            ZipEntry entry = new ZipEntry(fileName);
//            outputstream.putNextEntry(entry);
//            int len;
//            byte[] buffer = new byte[10 * 1024];
//            while ((len = is.read(buffer)) > 0) {
//                outputstream.write(buffer, 0, len);
//                outputstream.flush();
//            }
//            outputstream.closeEntry();
//            bInStream.close();//关闭
//            is.close();
//        } else {
//            log.error("文件不存在！");
//            throw new ServletException("文件不存在！");
//        }
//    }

    /**
     * 获取本地文件流
     */
    public static InputStream getInputStream(String localFilePath) throws IOException {
        return Files.newInputStream(Paths.get(localFilePath));
    }

    /**
     * 生成UUID 并删除"-"、"/"
     *
     * @return
     */
    public static String getUuid() {
        return UUID.randomUUID().toString().replaceAll("-", "").replaceAll("/", "");
    }

    /**
     * file 转 byte
     *
     * @param file
     * @return
     */
    public static byte[] fileToByte(File file) {
        try {
            if (!file.exists() || file.length() < 1) {
                log.error("文件为空,file={}", file.toPath());
                throw new ServiceException("文件为空");
            }
            Path path = file.toPath();
            return Files.readAllBytes(path);
        } catch (IOException e) {
            log.error("file 转 byte失败,file={}", file.toPath(), e);
            throw new ServiceException("file 转 byte失败");
        }
    }


    /**
     * 文件下载
     *
     * @param filePath 文件路径
     * @return
     */
    public static ResponseEntity<Resource> downloadFile(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            log.error("文件路径不能为空");
            throw new ServiceException("文件路径不能为空");
        }
        File file = new File(filePath);
        if (!file.exists()) {
            log.error("文件不存在");
            throw new ServiceException("文件不存在");
        }
        return FileHelper.downloadUtilsV3(file);
    }

    /**
     * 返回一个字符串资源
     *
     * @param message
     * @return
     */
    public static ResponseEntity<Resource> returnResource (String message){
        // 使用 ByteArrayResource 包装字符串
        Resource resource = new ByteArrayResource(message.getBytes(StandardCharsets.UTF_8));
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_PLAIN_VALUE)
                .body(resource);
    }

    /**
     * 文件下载工具
     *
     * @param bytes
     * @return
     */
    public static ResponseEntity<byte[]> downloadUtils(byte[] bytes, String fileName) {
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            MimetypesFileTypeMap mimetypesFileTypeMap = new MimetypesFileTypeMap();
            String mediaType = mimetypesFileTypeMap.getContentType(fileName);
            MediaType fileMediaType = MediaType.APPLICATION_OCTET_STREAM;
            if (StringUtils.isNotBlank(mediaType)) {
                fileMediaType = MediaType.parseMediaType(mediaType);
            }
            // httpHeaders.set("X-File-Type", ""); // 自定义传输
            httpHeaders.setContentType(fileMediaType);
            httpHeaders.setAccessControlExposeHeaders(Collections.singletonList("Content-Disposition"));
            httpHeaders.setContentDispositionFormData("attachment", URLEncoder.encode(getNewName(fileName), "UTF-8"));
            httpHeaders.setContentLength(bytes.length);
//            log.error("下载文件结束，fileName={},文件大小=={}",fileName,bytes.length);
            return new ResponseEntity<>(bytes, httpHeaders, HttpStatus.OK);
        } catch (Exception e) {
            log.error("文件传输异常", e);
            return failToJson("文件传输异常");
        }
    }

    /**
     * 文件下载工具(v3)
     *
     * @param file
     * @return
     */
    public static ResponseEntity<Resource> downloadUtilsV3(File file) {
        try {
            // 获取 MIME 类型
            MimetypesFileTypeMap mimetypesFileTypeMap = new MimetypesFileTypeMap();
            String mediaType = mimetypesFileTypeMap.getContentType(file.getName());
            MediaType fileMediaType = MediaType.APPLICATION_OCTET_STREAM;
            if (StringUtils.isNotBlank(mediaType)) {
                fileMediaType = MediaType.parseMediaType(mediaType);
            }
            // 设置响应头
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(fileMediaType);
            httpHeaders.setAccessControlExposeHeaders(Collections.singletonList("Content-Disposition"));
            httpHeaders.setContentDispositionFormData("attachment", URLEncoder.encode(file.getName(), "UTF-8"));

            // 用 InputStreamResource 包装 FileInputStream
            InputStreamResource resource = new InputStreamResource(Files.newInputStream(file.toPath()));
            return ResponseEntity.ok()
                    .headers(httpHeaders)
                    .contentLength(file.length())
                    .body(resource);
        } catch (IOException e) {
            log.error("文件下载异常:", e);
            throw new ServiceException("文件下载异常");
        }
    }

    /**
     * 文件下载工具(minio)
     *
     * @param file
     * @return
     */
    public static ResponseEntity<Resource> downloadUtilsToMinio(InputStream inputStream, String fileName , Long fileSize) {
        try {
            // 获取 MIME 类型
            MimetypesFileTypeMap mimetypesFileTypeMap = new MimetypesFileTypeMap();
            String mediaType = mimetypesFileTypeMap.getContentType(fileName);
            MediaType fileMediaType = MediaType.APPLICATION_OCTET_STREAM;
            if (StringUtils.isNotBlank(mediaType)) {
                fileMediaType = MediaType.parseMediaType(mediaType);
            }
            // 设置响应头
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(fileMediaType);
            httpHeaders.setAccessControlExposeHeaders(Collections.singletonList("Content-Disposition"));
            httpHeaders.setContentDispositionFormData("attachment", URLEncoder.encode(fileName, "UTF-8"));

            // 用 InputStreamResource 包装 FileInputStream
            InputStreamResource resource = new InputStreamResource(inputStream);
            return ResponseEntity.ok()
                    .headers(httpHeaders)
                    .contentLength(fileSize)
                    .body(resource);
        } catch (IOException e) {
            log.error("文件下载异常:", e);
            throw new ServiceException("文件下载异常");
        }
    }

    /**
     * 文件下载工具(大文件版本)
     *
     * @param file
     * @return
     */
    public static ResponseEntity<StreamingResponseBody> downloadUtilsTo(File file, String fileName) {
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            MimetypesFileTypeMap mimetypesFileTypeMap = new MimetypesFileTypeMap();
            String mediaType = mimetypesFileTypeMap.getContentType(fileName);
            MediaType fileMediaType = MediaType.APPLICATION_OCTET_STREAM;
            if (StringUtils.isNotBlank(mediaType)) {
                fileMediaType = MediaType.parseMediaType(mediaType);
            }
            httpHeaders.setContentType(fileMediaType);
            httpHeaders.setAccessControlExposeHeaders(Collections.singletonList("Content-Disposition"));
            httpHeaders.setContentDispositionFormData("attachment", URLEncoder.encode(getNewName(fileName), "UTF-8"));

            StreamingResponseBody body = out -> {
                try (InputStream in = Files.newInputStream(file.toPath())) {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = in.read(buffer)) != -1) {
                        out.write(buffer, 0, bytesRead);
                    }
                } catch (IOException e) {
                    log.error("文件流读取异常", e);
                    throw new ServiceException("文件流读取异常");
                }
            };
            return ResponseEntity.ok()
                    .headers(httpHeaders)
                    .contentLength(file.length())
                    .body(body);
        } catch (Exception e) {
            log.error("文件传输异常", e);
            throw new ServiceException("文件传输异常");
        }
    }

    /**
     * 返回文件的文件名
     *
     * @param filename
     * @return
     */
    public static String getNewName(String filename) {
        File fileTmp = new File(Objects.requireNonNull(filename));
        return fileTmp.getName();
    }

    /**
     * 获取文件扩展名
     *
     * @param filename
     * @return
     */
    private static String getFileSuffix(String filename) {
        String file;
        try {
            file = Optional.ofNullable(filename).filter(f -> f.contains(".")).map(f -> f.substring(filename.lastIndexOf(".") + 1)).get();
            return file;
        } catch (Exception e) {
            log.error("获取文件扩展名错误", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取年月
     *
     * @return
     */
    public static String getYearMonthSplit() {
        int year = DateUtil.year(DateTime.now());
        String month = String.format("%02d", DateUtil.month(DateTime.now()) + 1);
        return year + month;
    }


    public static ResponseEntity<byte[]> failToJson(String msg) {
        try {
            return ResponseEntity.status(HttpStatus.OK).headers(headers()).body(new ObjectMapper().writeValueAsBytes(R.fail(msg)));
        } catch (JsonProcessingException e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 大文件版
     *
     * @param msg
     * @return
     */
    public static ResponseEntity<StreamingResponseBody> failToJsonTo(String msg) {
        return ResponseEntity.status(HttpStatus.OK).headers(headers()).body(out -> out.write(msg.getBytes(StandardCharsets.UTF_8)));
    }

    private static HttpHeaders headers() {
        return new HttpHeaders() {{
            set(CONTENT_TYPE, MediaType.TEXT_PLAIN_VALUE + ";charset=UTF-8");
        }};
    }

    /**
     * 组装路径参数并返回
     *
     * @param yearMonthSplit 项目年月
     * @param buyItemCode    项目code
     * @param subpackageCode 标段code
     * @param supplierNumber 供应商id
     * @param fileTypeName   文件类型
     * @return 文件二段路径 例: 项目年月\项目code\标段code\供应商id\文件类型
     */
    public static String filePath(String yearMonthSplit, String buyItemCode, String subpackageCode, String supplierNumber, String fileTypeName) {
        if (!StringUtils.isEmpty(subpackageCode) && !StringUtils.isEmpty(supplierNumber)) {
            return File.separator +
                    yearMonthSplit.trim() + File.separator +
                    buyItemCode.trim() + File.separator +
                    subpackageCode.trim() + File.separator +
                    supplierNumber.trim() + File.separator +
                    fileTypeName.trim() + File.separator;
        }
        if (!StringUtils.isEmpty(subpackageCode) && StringUtils.isEmpty(supplierNumber)) {
            return File.separator +
                    yearMonthSplit.trim() + File.separator +
                    buyItemCode.trim() + File.separator +
                    subpackageCode.trim() + File.separator +
                    fileTypeName.trim() + File.separator;
        }
        return File.separator + yearMonthSplit.trim() + File.separator + buyItemCode.trim() + File.separator + fileTypeName.trim() + File.separator;
    }

    /**
     * 获取时间路径(minio) 例: 2020/01/
     *
     * @return
     */
    public static String getYearMonthPath() {
        return DateUtil.year(DateTime.now()) + "/" + String.format("%02d", DateUtil.month(DateTime.now()) + 1) + "/";
    }


    /**
     * 组装路径参数并返回
     */
    public static String getFilePath(FileUploadDto dto) {

        // 非项目文件 使用用户id拼接地址
        if (dto.getFileDatabase().equals(0)) {
            return "/" + longToPath(SecurityUtils.getUserId());
        }

        List<String> parameterList = new ArrayList<>();
        parameterList.add(getYearMonthSplit());
        parameterList.add(dto.getBuyItemCode());
        parameterList.add(dto.getSubpackageCode());
        parameterList.add(dto.getSupplierId() == null ? null : String.valueOf(dto.getSupplierId()));
        parameterList.add(dto.getFileTypeName());

        StringBuilder filePath = new StringBuilder("/");
        for (String parameter : parameterList) {
            if (parameter != null && !parameter.trim().isEmpty()) {
                filePath.append(parameter).append("/");
            }
        }
        return filePath.toString();
    }


    /**
     * 判断文件是否存在
     *
     * @param filePath 文件路径
     * @return
     */
    public static void checkFileExists(String filePath) {
        try {
            log.error("检查文件是否存在,filePath={}", filePath);
            Path path = Paths.get(filePath);
            boolean exists = Files.exists(path, LinkOption.NOFOLLOW_LINKS);
            if (!exists || Files.size(path) == 0) {
                log.error("文件不存在,filePath={}", filePath);
                throw new ServiceException("新文件不存在");
            }
        } catch (IOException e) {
            log.error("检查文件是否存在报错,filePath={},e={}", filePath, e);
            throw new ServiceException("检查文件是否存在报错");
        }
    }


    /**
     * 删除本地文件(物理)
     *
     * @param filePath 文件路径
     * @return
     */
    public static void delFile(String filePath) {
        try {
            Files.deleteIfExists(Paths.get(filePath));
        } catch (Exception e) {
            log.error("删除(物理)文件异常,filePath={},e={}", filePath, e);
        }
    }


}


