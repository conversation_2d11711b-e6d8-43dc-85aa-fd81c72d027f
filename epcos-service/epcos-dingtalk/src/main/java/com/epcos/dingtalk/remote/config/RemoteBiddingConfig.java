package com.epcos.dingtalk.remote.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.util.UriComponentsBuilder;

@Data
@Configuration
@ConfigurationProperties(prefix = "api.bidding")
public class RemoteBiddingConfig extends BaseRemoteConfig {
    private static final String AUDIT_BULLETIN_PATH = "/bulletin/auditBulletin";
    private static final String SIMPLE_INFO_PATH = "/bulletin/simpleInfo";
    private static final String AUDIT_PURCHASE_PATH = "/purchase/claims/updateAuditStatus";

    public String getAuditBulletinUrl() {
        return UriComponentsBuilder.fromHttpUrl(getBaseUrl())
                .path(AUDIT_BULLETIN_PATH)
                .toUriString();
    }

    public String getSimpleInfoUrl(String processInstanceId) {
        return UriComponentsBuilder.fromHttpUrl(getBaseUrl())
                .path(SIMPLE_INFO_PATH)
                .queryParam("processInstanceId", processInstanceId)
                .toUriString();
    }

    public String getAuditPurchaseFileUrl() {
        return UriComponentsBuilder.fromHttpUrl(getBaseUrl())
                .path(AUDIT_PURCHASE_PATH)
                .toUriString();
    }
}
