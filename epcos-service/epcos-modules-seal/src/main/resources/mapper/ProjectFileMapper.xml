<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epcos.seal.mapper.ProjectFileMapper">

    <resultMap type="com.epcos.seal.domain.ProjectFile" id="FileStorageInfoResult">
        <result property="id" column="id"/>
        <result property="fileCode" column="file_code"/>
        <result property="buyItemCode" column="buy_item_code"/>
        <result property="subpackageCode" column="subpackage_code"/>
        <result property="supplierNumber" column="supplier_number"/>
        <result property="fileTypeName" column="file_type_name"/>
        <result property="yearMonthSplit" column="year_month_split"/>
        <result property="fileStoragePath" column="file_storage_path"/>
        <result property="createAt" column="create_at"/>
        <result property="updateAt" column="update_at"/>
    </resultMap>

    <sql id="selectFileStorageInfoVo">
        select id, file_code, buy_item_code, subpackage_code, supplier_number, file_type_name, year_month_split, file_storage_path, create_at, update_at
        from epcfile_project
    </sql>
    <select id="selByFileKey" parameterType="String" resultMap="FileStorageInfoResult">
        <include refid="selectFileStorageInfoVo"/>
        where file_code = #{fileKey}
    </select>
    <update id="upFileStoragePath"   parameterType="com.epcos.seal.domain.ProjectFile" >
        update
            epcfile_project
        set
            file_storage_path = #{fileStoragePath} where file_code = #{fileCode}
    </update>
</mapper>
