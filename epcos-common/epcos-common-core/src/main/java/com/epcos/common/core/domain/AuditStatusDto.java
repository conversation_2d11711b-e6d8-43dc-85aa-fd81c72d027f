package com.epcos.common.core.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

@ApiModel("钉钉审批信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuditStatusDto implements Serializable {
    private static final long serialVersionUID = 1725262568193344281L;

    @ApiModelProperty("钉钉审批实例id")
    @NotBlank(message = "钉钉审批实例id-必填")
    private String auditCode;

    @ApiModelProperty("业务审核状态，0-不同意，1-同意，2-待审批，3-撤回'")
    @NotNull(message = "业务审核状态-不能为空")
    @Range(min = 0, max = 3, message = "业务审核状态-范围0-3")
    private Integer auditStatus;

    @ApiModelProperty("审批备注")
    @Length(max = 500, message = "审批备注【最长：500】")
    private String remark;

}
