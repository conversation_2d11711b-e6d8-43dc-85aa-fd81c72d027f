package com.epcos.common.core.factory.pdf;

import com.epcos.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/21 9:06
 */
@Slf4j
public class PdfFactory {

    private PdfFactory() {
    }

    public static IGeneratePdf GeneratePdfFactory(Class clazz) {
        try {
            return (IGeneratePdf) clazz.newInstance();
        } catch (Exception e) {
            log.error("实例化对象失败,clazz:{}, e:{}", clazz, e);
            throw new ServiceException("报表导出失败，请联系管理员");
        }
    }
}
