package com.epcos.common.core.web.domain.user;

import com.epcos.common.core.annotation.Excel;
import com.epcos.common.core.annotation.Excel.Type;
import com.epcos.common.core.annotation.Excels;
import com.epcos.common.core.annotation.valid.Add;
import com.epcos.common.core.annotation.valid.Up;
import com.epcos.common.core.web.domain.BaseEntity;
import com.epcos.common.core.xss.Xss;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * 用户对象 sys_user
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class SysUser extends BaseEntity {

    private static final long serialVersionUID = 1L;

    public SysUser(Long userId) {
        this.userId = userId;
    }

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "id", hidden = true)
    @NotNull(message = "用户id必填", groups = Up.class)
    private Long userId;

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门编号")
    @Excel(name = "部门编号", type = Type.IMPORT)
    private Long deptId;

    /**
     * 用户账号
     */
    @ApiModelProperty(value = "登录名称", hidden = true)
    @Xss(message = "用户账号不能包含脚本字符")
    @Size(min = 0, max = 30, message = "用户账号长度不能超过30个字符")
    @Excel(name = "登录名称")
    @NotBlank(message = "用户登录名称必填", groups = Add.class)
    private String userName;

    /**
     * 用户昵称
     */
    @ApiModelProperty(value = "用户名称")
    @Xss(message = "用户昵称不能包含脚本字符")
    @Size(min = 0, max = 100, message = "用户昵称长度不能超过100个字符")
    @Excel(name = "用户名称")
    private String nickName;

    /**
     * 用户邮箱
     */
    @ApiModelProperty(value = "用户邮箱", hidden = true)
    @Email(message = "邮箱格式不正确")
    @Size(min = 0, max = 50, message = "邮箱长度不能超过50个字符")
    @Excel(name = "用户邮箱")
    private String email;

    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码", hidden = true)
    @Size(min = 0, max = 30, message = "手机号码长度不能超过30个字符")
    @Excel(name = "手机号码")
    private String phonenumber;

    /**
     * 用户性别
     */
    @ApiModelProperty(value = "用户性别", hidden = true)
    @Excel(name = "用户性别", readConverterExp = "0=男,1=女,2=未知")
    private String sex;

    /**
     * 用户头像
     */
    @ApiModelProperty(value = "用户头像", hidden = true)
    private String avatar;

    /**
     * 密码
     */
    @ApiModelProperty(value = "密码", hidden = true)
    private String password;

    /**
     * 帐号状态（0正常 1停用）
     */
    @ApiModelProperty(value = "帐号状态", hidden = true)
    @Excel(name = "帐号状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @ApiModelProperty(value = "删除标志", hidden = true)
    private String delFlag;

    /**
     * 最后登录IP
     */
    @ApiModelProperty(value = "最后登录IP", hidden = true)
    @Excel(name = "最后登录IP", type = Type.EXPORT)
    private String loginIp;

    /**
     * 最后登录时间
     */
    @ApiModelProperty(value = "最后登录时间", hidden = true)
    @Excel(name = "最后登录时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Type.EXPORT)
    private Date loginDate;

    /**
     * 部门对象
     */
    @ApiModelProperty(value = "部门对象", hidden = true)
    @Excels({
            @Excel(name = "部门名称", targetAttr = "deptName", type = Type.EXPORT),
            @Excel(name = "部门负责人", targetAttr = "leader", type = Type.EXPORT)
    })
    private SysDept dept;

    /**
     * 角色对象
     */
    @ApiModelProperty(value = "角色对象", hidden = true)
    private List<SysRole> roles;

    /**
     * 角色组
     */
    @ApiModelProperty(value = "角色组", hidden = true)
    private Long[] roleIds;

    /**
     * 岗位组
     */
    @ApiModelProperty(value = "岗位组", hidden = true)
    private Long[] postIds;

    /**
     * 角色ID
     */
    @ApiModelProperty(value = "角色ID")
    private Long roleId;

    /**
     * 用户类型（00系统用户）
     */
    @ApiModelProperty(value = "用户类型", hidden = true)
    @Size(min = 0, max = 2, message = "用户类型长度不能超过2个字符")
//    @Excel(name = "用户类型", type = Type.EXPORT, prompt = "用户类型")
    private String userType;

    @ApiModelProperty("供应商：统一信用代码，专家：身份证号")
    @Excel(name = "身份证号")
    @Length(max = 25)
    private String idNumber;

    @ApiModelProperty("专家类型")
    @Length(max = 100)
    @Excel(name = "专家类型")
    private String type;

    @ApiModelProperty("专家科室")
    @Length(max = 100)
    @Excel(name = "专家科室")
    private String department;

    @ApiModelProperty("院内身份（评审专家）")
    @Length(max = 100)
    private String insideIdentity;

    @ApiModelProperty("e签宝个人账户id")
    @Length(max = 60)
    private String psnId;

    @ApiModelProperty("e签宝企业账户id")
    @Length(max = 60)
    private String orgId;

    @ApiModelProperty("修改密码时间")
    private Date updatePwdTime;

    @ApiModelProperty("黑名单状态(0:正常状态,1:黑名单用户)")
    private Integer blacklistStatus;

    @ApiModelProperty("黑名单加入时间")
    private Date blacklistLiftStartTime;

    @ApiModelProperty("黑名单解除时间")
    private Date blacklistLiftEndTime;

    @ApiModelProperty("加入黑名单原因")
    private String blacklistReason;

    @ApiModelProperty("加入黑名单操作人")
    private Long addBlacklistUser;

    @ApiModelProperty("用户角色(100:采购人 , 102:专家)")
    @Excel(name = "用户角色")
    private Integer roleNumber;

    @Length(max = 50)
    private String deptName;

    @ApiModelProperty(value = "当前组织编号")
    private String orgCode;

    @ApiModelProperty(value = "组织编号")
    private List<String> orgCodeList;

    @ApiModelProperty(value = "排除查询的角色Id")
    private Long[] excludeRoleIds;

    @ApiModelProperty(value = "组织信息", hidden = true)
    private List<UserOrgVO> sysOrganizes;

    @ApiModelProperty(value = "供应商信息")
    private SupplierCompanyVo supplierCompanyVo;



}
