package com.epcos.common.core.utils.file;

import com.epcos.common.core.exception.UtilException;
import com.epcos.common.core.utils.uuid.IdUtils;
import org.apache.poi.util.IOUtils;
import org.bytedeco.opencv.global.opencv_imgcodecs;
import org.bytedeco.opencv.global.opencv_imgproc;
import org.bytedeco.opencv.opencv_core.Mat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.nio.file.Files;
import java.util.Arrays;
import java.util.Base64;

import static org.bytedeco.opencv.global.opencv_core.CV_64F;
import static org.bytedeco.opencv.global.opencv_core.meanStdDev;

/**
 * 图片处理工具类
 *
 * <AUTHOR>
 */
public class ImageUtils {
    private static final Logger log = LoggerFactory.getLogger(ImageUtils.class);

    public static byte[] getImage(String imagePath) {
        InputStream is = getFile(imagePath);
        try {
            return IOUtils.toByteArray(is);
        } catch (Exception e) {
            log.error("图片加载异常 {}", e);
            return null;
        } finally {
            IOUtils.closeQuietly(is);
        }
    }

    public static InputStream getFile(String imagePath) {
        try {
            byte[] result = readFile(imagePath);
            result = Arrays.copyOf(result, result.length);
            return new ByteArrayInputStream(result);
        } catch (Exception e) {
            log.error("获取图片异常 {}", e);
        }
        return null;
    }

    /**
     * 读取文件为字节数据
     *
     * @param url 地址
     * @return 字节数据
     */
    public static byte[] readFile(String url) {
        InputStream in = null;
        try {
            // 网络地址
            URL urlObj = new URL(url);
            URLConnection urlConnection = urlObj.openConnection();
            urlConnection.setConnectTimeout(30 * 1000);
            urlConnection.setReadTimeout(60 * 1000);
            urlConnection.setDoInput(true);
            in = urlConnection.getInputStream();
            return IOUtils.toByteArray(in);
        } catch (Exception e) {
            log.error("访问文件异常 {}", e);
            return null;
        } finally {
            IOUtils.closeQuietly(in);
        }
    }

    /**
     * 获取临时目录
     *
     * @return
     */
    public static String tmp() {
        return System.getProperty("java.io.tmpdir") + File.separator;
    }

    /**
     * 读取文件为base64
     */
    public static String getBase64(File img) {
        if (!Files.exists(img.toPath())) {
            throw new UtilException("文件不存在，" + img);
        }
        try (final FileInputStream fileInputStream = new FileInputStream(img)) {
            final byte[] bytes = new byte[fileInputStream.available()];
            fileInputStream.read(bytes);
            return Base64.getEncoder().encodeToString(bytes);
        } catch (IOException e) {
            log.error("读取文件为base64异常，img:{}, e:{}", img, e);
            throw new UtilException("读取文件为base64异常");
        }
    }

    /**
     * imgBase64 转 byte[]
     */
    public static byte[] toBytes(String imgBase64) {
        final String[] split = imgBase64.split(",");
        final String img = split.length > 1 ? split[1] : split[0];
        return Base64.getDecoder().decode(img);
    }

    /**
     * imgBase64 转文件
     */
    public static File toFile(String imgBase64) {
        final String tmp = tmp();
        final String[] split = imgBase64.split(",");
        final String img = split.length > 1 ? split[1] : split[0];
        final String format = format(img);
        final byte[] imgBytes = Base64.getDecoder().decode(img);
        final File file = new File(tmp + IdUtils.fastSimpleUUID() + "." + format);
        try (final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(imgBytes); final FileOutputStream fileOutputStream = new FileOutputStream(file)) {
            final byte[] bytes = new byte[byteArrayInputStream.available()];
            byteArrayInputStream.read(bytes);
            fileOutputStream.write(bytes);
            return file;
        } catch (IOException e) {
            log.error("imgBase64 转文件异常，e: " + e);
            throw new UtilException("imgBase64 转文件异常");
        }
    }

    /**
     * 返回格式
     */
    public static String format(String imgBase64) {
        final byte[] b = Base64.getDecoder().decode(imgBase64);
        String type = null;
        if (0x424D == ((b[0] & 0xff) << 8 | (b[1] & 0xff))) {
            type = "bmp";
        } else if (0x8950 == ((b[0] & 0xff) << 8 | (b[1] & 0xff))) {
            type = "png";
        } else if (0xFFD8 == ((b[0] & 0xff) << 8 | (b[1] & 0xff))) {
            type = "jpg";
        } else {
            log.error("imgBase64: " + imgBase64);
            throw new UtilException("不支持【bmp,png,jpg,jpeg】以外格式的base64");
        }
        return type;
    }

    /**
     * javacv 检测图片清晰度
     * 标准差越大说明图像质量越好
     *
     * @return 返回清晰度值
     */
    public static double clarity(File jpegFile) {
        String path = tmp();
        Mat srcImage = opencv_imgcodecs.imread(jpegFile.getAbsolutePath());
        Mat dstImage = new Mat();
        //转化为灰度图
        opencv_imgproc.cvtColor(srcImage, dstImage, opencv_imgproc.COLOR_BGR2GRAY);
        //在gray目录下生成灰度图片
        opencv_imgcodecs.imwrite(path + "gray-" + jpegFile.getName(), dstImage);

        Mat laplacianDstImage = new Mat();
        //阈值太低会导致正常图片被误断为模糊图片，阈值太高会导致模糊图片被误判为正常图片
        opencv_imgproc.Laplacian(dstImage, laplacianDstImage, CV_64F);
        //在laplacian目录下升成经过拉普拉斯掩模做卷积运算的图片
        opencv_imgcodecs.imwrite(path + "laplacian-" + jpegFile.getName(), laplacianDstImage);

        //矩阵标准差
        Mat stddev = new Mat();

        //求矩阵的均值与标准差
        meanStdDev(laplacianDstImage, new Mat(), stddev);
        // ((全部元素的平方)的和)的平方根
        // double norm = Core.norm(laplacianDstImage);
        // System.out.println("\n矩阵的均值：\n" + mean.dump());
//        System.out.println(jpegFile.getName() + "矩阵的标准差：\n" + stddev.createIndexer().getDouble());
        // System.out.println(jpegFile.getName()+"平方根：\n" + norm);
        return stddev.createIndexer().getDouble();
    }

    /**
     * <20 不合格
     *
     * @param imgBase64 imgBase64
     * @return true: 不合格
     */
    public static boolean clarity(String imgBase64) {
        Assert.hasText(imgBase64, "imgBase64不能为空");
        final File file = toFile(imgBase64);
        return clarity(file) < 20d;
    }

}
