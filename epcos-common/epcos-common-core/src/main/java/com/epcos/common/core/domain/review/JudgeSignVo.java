package com.epcos.common.core.domain.review;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class JudgeSignVo implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("评审报告KEY")
    private String reportFileKey;
    @ApiModelProperty("签字详情")
    private List<JudgeSignInfoVo> judgeSignInfo;
}
