package com.epcos.common.core.web.controller;

import com.epcos.common.core.constant.HttpStatus;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.utils.DateUtils;
import com.epcos.common.core.utils.PageUtils;
import com.epcos.common.core.utils.purchase.GzipUtils;
import com.epcos.common.core.web.domain.AjaxResult;
import com.epcos.common.core.web.page.TableDataVo;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;

import java.beans.PropertyEditorSupport;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * web层通用数据处理
 *
 * <AUTHOR>
 */
@Slf4j
public class BaseController {
    /**
     * 将前台传递过来的日期格式的字符串，自动转化为Date类型
     */
    @InitBinder
    public void initBinder(WebDataBinder binder) {
        // Date 类型转换
        binder.registerCustomEditor(Date.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) {
                setValue(DateUtils.parseDate(text));
            }
        });
    }

    /**
     * 设置请求分页数据
     */
    protected void startPage() {
        PageUtils.startPage();
    }

    /**
     * 响应请求分页数据
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    protected TableDataVo getDataTable(List<?> list) {
        TableDataVo rspData = new TableDataVo();
        if (CollectionUtils.isEmpty(list)) {
            list = Collections.emptyList();
        }
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(list);
        rspData.setMsg("查询成功");
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }

    protected TableDataVo getDataTable(String errorMsg) {
        TableDataVo rspData = new TableDataVo();
        rspData.setRows(Collections.emptyList());
        rspData.setCode(HttpStatus.ERROR);
        rspData.setMsg(errorMsg);
        return rspData;
    }

    /**
     * 响应返回结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    protected AjaxResult toAjax(int rows) {
        return rows > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 响应返回结果
     *
     * @param result 结果
     * @return 操作结果
     */
    protected AjaxResult toAjax(boolean result) {
        return result ? success() : error();
    }

    /**
     * 返回成功
     */
    public AjaxResult success() {
        return AjaxResult.success();
    }

    /**
     * 返回失败消息
     */
    public AjaxResult error() {
        return AjaxResult.error();
    }

    /**
     * 返回成功消息
     */
    public AjaxResult success(String message) {
        return AjaxResult.success(message);
    }

    /**
     * 返回失败消息
     */
    public AjaxResult error(String message) {
        return AjaxResult.error(message);
    }

    /**
     * 文件解压
     *
     * @param target
     * @return
     */
    protected R<String> decodeAndUnCompress(String target) {
        if (StringUtils.isEmpty(target)) {
            return R.ok("", "");
        }
        byte[] asBytes = Base64.getDecoder().decode(target);
        String unCompress;
        try {
            unCompress = GzipUtils.unCompress(new String(asBytes, StandardCharsets.UTF_8));
        } catch (IOException e) {
            log.error("getCateLogContent :{}", e);
            return R.fail("解压文件失败!" + e.getMessage());
        }
        return R.ok("", unCompress);
    }
}
