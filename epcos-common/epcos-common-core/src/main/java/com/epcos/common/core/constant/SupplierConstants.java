package com.epcos.common.core.constant;

/**
 * 投标人常量
 */
public class SupplierConstants {

    /**
     * 正则
     */
    public static interface Regular {
        // 3位数字码
        String THREE_DIGITAL = "^\\d{3}$";
        // 6位数字码
        String SIX_DIGITAL = "^\\d{6}$";
        // 行业规范代码 A01
        String INDUSTRY_CODE = "^[A-Z]\\d{2}$";
        // 是否是数字, 正整数，2位小数
        String NUMBER = "^\\d+(\\.\\d{1,2})?$";
        // 正整数包括0
        String NUMBER_GREATER_THAN_OR_EQUAL = "^[1-9]\\d{0,8}|0$";
        // 座机或手机号
        String LANDLINE_OR_MOBILE = "\\d{3}-\\d{8}$|^\\d{4}-\\d{7}$|^1\\d{10}$";
        // 手机号正则
        String MOBILE_PHONE = "^1\\d{10}$";
        // 信用代码正则
        String LIC_NUMBER = "^[0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}$";
        // 投标人代码类型
        String BIDDER_CODE_TYPE = "^(0[1-9]|1d|2[0-6])|9[5-9]|90$";
        // 银行账号
        String BANK_ACCOUNT = "^\\d{1,30}$";
        // 注册资本
        String REG_CAPITAL = "(?:^[1-9]([0-9]+)?(?:\\.[0-9]{1,2})?$)|(?:^(?:0)$)|(?:^[0-9]\\.[0-9](?:[0-9])?$)";
        // 1-元，2-万元，注册资本单位
        String REG_CAPITAL_UNIT = "^1|2$";
        // 身份证号
//         String ID_NUMBER = "(^\\d{8}(0\\d|10|11|12)([0-2]\\d|30|31)\\d{3}$)|(^\\d{6}(18|19|20)\\d{2}(0[1-9]|10|11|12)([0-2]\\d|30|31)\\d{3}(\\d|X|x)$)";
        String ID_NUMBER = "^(^[1-9]\\d{7}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}$)|(^[1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])((\\d{4})|\\d{3}[Xx])$)$";
        // 护照号
        String PASSPORT = "^[a-zA-Z0-9]{8,19}$";
        // 军（警）官证
        String MILITARY_ID = "^[a-zA-Z]{1,2}\\d{5,8}$";
        // 户口簿
        String HOUSEHOLD_REGISTER = "[A-Za-z0-9-]{6,18}$";
        // 港澳通行证
        String HK_MACAO_PASS = "^[A-Z]\\d{6,10}([A-Za-z0-9])?$";
        // 台胞证
        String TAIWAN_PASS = "^([0-9]{8}|[0-9]{10})$";
        // 邮政编码
        String ZIP_CODE = "^(0[1-7]|1[0-356]|2[0-7]|3[0-6]|4[0-7]|5[1-7]|6[1-7]|7[0-5]|8[013-6])\\d{4}$";
        // 邮箱
        String EMAIL = "^\\w+@[a-zA-Z0-9]{2,10}(?:\\.[a-z]{2,4}){1,3}$";
        // 报价表code正则
        String QUOTE = "^LT\\d{6,7}$";
        // 两位小数
        String DOUBLE = "^[1-9]\\.[0-9]$";
        // 中文
        String CHINESE = "^(?:[\u4e00-\u9fa5·]{2,4})$";
    }

    /**
     * 0-非资格预审，1-资格预审
     */
    public static interface PreTrial {
        // 0-招标，1-资格预审
        Integer TENDER = 0;
        Integer PRE_TRIAL = 1;
    }

    /**
     * 投标文件流程状态
     */
    public static interface BidFileConfirm {
        // 0待上传1已上传 2已盖章
        Integer PENDING_UPLOAD = 0;
        Integer UPLOADED = 1;
        Integer STAMPED = 2;
    }

    /**
     * 企业审核常量
     */
    public static interface PlatformStatus {
        // 等待平台进行审核
        int WAITING = 0;
        // 审核失败
        int FAIL = 1;
        // 审核通过
        int OK = 2;
    }

    /**
     * 投标或招标文件
     */
    public static interface BidFileType {
        // 招标文件
        int PURCHASE = 1;
        // 投标文件
        int BIDDER = 2;
    }



}
