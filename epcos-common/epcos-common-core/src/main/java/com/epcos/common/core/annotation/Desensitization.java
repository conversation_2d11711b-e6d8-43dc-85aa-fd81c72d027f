package com.epcos.common.core.annotation;

import com.epcos.common.core.annotation.desensitization.DesensitizationSerializer;
import com.epcos.common.core.annotation.desensitization.DesensitizationStrategy;
import com.epcos.common.core.annotation.desensitization.strategy.AddStrSensitization;
import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * 脱敏注解
 */
@JacksonAnnotationsInside
@JsonSerialize(using = DesensitizationSerializer.class)
@Retention(RetentionPolicy.RUNTIME)
public @interface Desensitization {

    Class<? extends DesensitizationStrategy> strategy() default AddStrSensitization.class;

}
