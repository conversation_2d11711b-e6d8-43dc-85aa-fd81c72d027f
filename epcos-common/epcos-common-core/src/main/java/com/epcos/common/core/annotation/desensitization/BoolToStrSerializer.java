package com.epcos.common.core.annotation.desensitization;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/20 16:47
 */
@Slf4j
public class BoolToStrSerializer extends JsonSerializer<Boolean> {
    @Override
    public void serialize(Boolean value, JsonGenerator gen,
                          SerializerProvider serializers) throws IOException {
        gen.writeString(value ? "1" : "0");
    }
}
