package com.epcos.common.core.annotation.impl;

import com.epcos.common.core.annotation.Watch;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

@Slf4j
@Aspect
@Component
public class WatchAspect {

    @Around(value = "@annotation(com.epcos.common.core.annotation.Watch)")
    public Object doWatch(ProceedingJoinPoint joinPoint) throws Throwable {
        StopWatch stopWatch = new StopWatch(((MethodSignature) joinPoint.getSignature()).getMethod().getAnnotation(Watch.class).name());
        stopWatch.start();
        Object proceed = joinPoint.proceed();
        stopWatch.stop();
        log.error(String.format("'%s' running time = : %.3f \n\n", stopWatch.getId(), stopWatch.getTotalTimeSeconds()));
        return proceed;
    }

}
