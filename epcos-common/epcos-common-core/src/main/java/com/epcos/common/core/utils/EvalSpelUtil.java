package com.epcos.common.core.utils;

import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.util.Objects;

/**
 * 表达式计算
 *
 * <AUTHOR>
 */
public class EvalSpelUtil {

    /**
     * 返回解析el表达式的值
     *
     * @param method 方法
     * @param args   方法参数列表
     * @param el     spring el表达式
     * @return 默认返回 string
     */
    public static <T> T get(Method method, Object[] args, String el, Class<T> aclass) {
        if (!StringUtils.hasText(el)) {
            return null;
        }
        SpelExpressionParser parser = new SpelExpressionParser();
        LocalVariableTableParameterNameDiscoverer discoverer = new LocalVariableTableParameterNameDiscoverer();
        String[] parameterNames = discoverer.getParameterNames(method);
        if (Objects.isNull(parameterNames)) {
            return null;
        }
        StandardEvaluationContext context = new StandardEvaluationContext();
        for (int len = 0; len < parameterNames.length; len++) {
            context.setVariable(parameterNames[len], args[len]);
        }
        Expression expression = parser.parseExpression(el);
        return expression.getValue(context, aclass);
    }

    public static Object get(Method method, Object[] args, String el) {
        return get(method, args, el, Object.class);
    }


}
