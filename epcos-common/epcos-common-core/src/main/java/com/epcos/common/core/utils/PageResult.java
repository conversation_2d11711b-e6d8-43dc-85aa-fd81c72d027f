package com.epcos.common.core.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageSerializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Title:
 * @Description:TODO
 * @author:moyu
 * @version:1.0
 * @since 2022-05-31 14:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
public class PageResult<T> extends PageSerializable<T> {

    @JsonProperty("rows")
    protected List<T> list;
    /**
     * 扩展字段
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Object expand;

    /**
     * 扩展字段2
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Object expand2;


    /**
     * 使用page info初始化
     *
     * @param pageSerializa
     */
    public PageResult(PageSerializable<T> pageSerializa) {
        this.setList(pageSerializa.getList());
        this.setTotal(pageSerializa.getTotal());
    }

    public PageResult(Page<T> page, List<T> list) {
        this.setList(list);
        this.setTotal(page.getTotal());
    }

    public PageResult(long total, List<T> list) {
        this.setList(list);
        this.setTotal(total);
    }

    public PageResult(List<T> list) {
        if (list instanceof Page) {
            this.setList(list);
            this.setTotal(((Page<T>) list).getTotal());
        } else {
            this.setList(list);
            this.setTotal(list.size());
        }
    }
}
