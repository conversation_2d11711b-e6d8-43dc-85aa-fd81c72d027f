package com.epcos.common.core.utils.procurement.item.classification;

import com.epcos.common.core.domain.ProcurementItemClassification;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;

public class ProcurementItemClassificationUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final List<ProcurementItemClassification> classificationList;

    static {
        try {
            InputStream resourceAsStream = ProcurementItemClassificationUtil.class.getClassLoader().getResourceAsStream("采购项目分类.json");
            classificationList = objectMapper.readValue(resourceAsStream, new TypeReference<List<ProcurementItemClassification>>() {
            });
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static List<ProcurementItemClassification> getAll() {
        return classificationList;
    }

    // 依据code找出自己的name并与之的父级name
    public static String getNames(String code, char spacer) {
        StringBuilder res = new StringBuilder();
        doGetNames(getByCode(code), res, spacer);
        return res.toString();
    }

    private static void doGetNames(ProcurementItemClassification p, StringBuilder str, char spacer) {
        if (Objects.isNull(p)) {
            return;
        }
        str.append(p.getName());
        if (CollectionUtils.isEmpty(p.getChildren())) {
            return;
        }
        str.append(spacer);
        doGetNames(p.getChildren().stream().findFirst().get(), str, spacer);
    }

    // 依据code找出自己并与之的父级
    public static ProcurementItemClassification getByCode(String code) {
        if (!StringUtils.hasText(code) || Pattern.compile("[\u4E00-\u9FA5]").asPredicate().test(code)) {
            return new ProcurementItemClassification(code, code);
        }
        ProcurementItemClassification vo = new ProcurementItemClassification();
        doGetByCode(classificationList, vo, code, 0, 2);
        return vo;
    }

    private static void doGetByCode(List<ProcurementItemClassification> sources, ProcurementItemClassification vo, String code, int start, int interval) {
        if (CollectionUtils.isEmpty(sources) || (start + interval) > code.length()) {
            return;
        }
        boolean hasSetChildren = false;
        for (ProcurementItemClassification source : sources) {
            if (hasSetChildren || source.getCode().length() != code.length()) {
                return;
            }
            if (start == 0) {
                if (code.charAt(start) != source.getCode().charAt(start)) {
                    continue;
                }
                vo.setCode(source.getCode());
                vo.setName(source.getName());
                doGetByCode(source.getChildren(), vo, code, ++start, interval);
                hasSetChildren = true;
            } else {
                if (source.getCode().substring(0, start + interval).equals(code.substring(0, start + interval))) {
                    ProcurementItemClassification next = new ProcurementItemClassification(source.getName(), source.getCode());
                    vo.getChildren().add(next);
                    doGetByCode(source.getChildren(), next, code, start + interval, interval);
                    hasSetChildren = true;
                }
            }

        }
    }


}