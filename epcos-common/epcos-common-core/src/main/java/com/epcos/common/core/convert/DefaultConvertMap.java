package com.epcos.common.core.convert;

import cn.hutool.core.date.DateUtil;
import com.epcos.common.core.annotation.Export;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.StringUtils;
import com.epcos.common.core.utils.reflect.ReflectUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/16 14:57
 */
public interface DefaultConvertMap {

    default Map<String, String> convert(Object object) {
        LinkedHashMap<String, String> linkedHashMap = Optional.ofNullable(object)
                .map(obj -> Arrays.stream(obj.getClass().getDeclaredFields())
                        .collect(Collectors.toMap(
                                Field::getName,
                                i -> {
                                    ReflectUtils.makeAccessible(i);
                                    Object o;
                                    try {
                                        o = i.get(obj);
                                    } catch (IllegalAccessException e) {
                                        throw new ServiceException(e.getMessage());
                                    }
                                    if (Objects.nonNull(o)) {
                                        if (o instanceof Date) {
                                            o = DateUtil.formatDateTime(((Date) o));
                                        }
                                        return o.toString();
                                    }
                                    return "";
                                },
                                (o, o2) -> o2,
                                LinkedHashMap::new
                        ))).orElse(new LinkedHashMap<>());
        return linkedHashMap;
    }


    default Map<String, String> convertToMap(Object obj) {
        Map<String, String> map = new LinkedHashMap<>();
        Arrays.stream(obj.getClass().getDeclaredFields())
                .forEach(f -> {
                    try {
                        f.setAccessible(true);
                        if (f.isAnnotationPresent(Export.class)) {
                            Export apiModelProperty = f.getAnnotation(Export.class);
                            String annValue = apiModelProperty.value();
                            String value = Objects.isNull(f.get(obj)) ? null : f.get(obj).toString();
                            if (StringUtils.hasText(annValue)) {
                                map.put(annValue, StringUtils.hasText(value) ? value : "");
                            }
                        }
                    } catch (IllegalAccessException e) {
                        throw new ServiceException("获取属性值失败");
                    }
                });
        return map;
    }
}
