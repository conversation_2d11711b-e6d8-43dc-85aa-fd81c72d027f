package com.epcos.common.core.domain.review;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 */

@Data
public class SupplierDetailsVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("包编码")
    private String subpackageCode;

    @ApiModelProperty("供应商id")
    private Long supplierId;

    @ApiModelProperty("供应商单位名字")
    private String supplierCompanyName;

    @ApiModelProperty("价格分[四舍五入]")
    private BigDecimal priceSource;

    @ApiModelProperty("总分")
    private BigDecimal totalSource;

    @ApiModelProperty("平均分")
    private Double scoreResult;

    @ApiModelProperty("合格结果：1-通过,  0-不通过")
    private Boolean qualifiedResult;

    @ApiModelProperty("1-打分,  0-合格  2-投票")
    private Integer isType;

    @ApiModelProperty("评委推荐排名")
    private Integer rank;

    @ApiModelProperty("评委")
    private List<ReviewJudgesVo> judgesVoList;

    @ApiModelProperty("是否投票 true:已投票,false:未投票")
    private boolean whetherRecord;

    @ApiModelProperty("投票人姓名")
    private String votersNames;

    @ApiModelProperty("票数")
    private Integer votesNum;

    @ApiModelProperty("所有行-报价-总价")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal allRowQuotationTotalPrice;

}
