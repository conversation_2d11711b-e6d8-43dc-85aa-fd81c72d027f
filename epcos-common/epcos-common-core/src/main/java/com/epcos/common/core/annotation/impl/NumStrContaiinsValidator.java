package com.epcos.common.core.annotation.impl;

import com.epcos.common.core.annotation.NumContains;
import com.epcos.common.core.constant.SupplierConstants;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.Objects;
import java.util.regex.Pattern;

public class NumStrContaiinsValidator implements ConstraintValidator<NumContains, String> {
    private String[] constraint;

    @Override
    public void initialize(NumContains constraintAnnotation) {
        constraint = constraintAnnotation.value();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (Objects.nonNull(constraint) && constraint.length > 0) {
            if (Pattern.matches(SupplierConstants.Regular.NUMBER, value)) {
                return Arrays.asList(constraint).contains(value);
            }
        }
        return false;
    }
}
