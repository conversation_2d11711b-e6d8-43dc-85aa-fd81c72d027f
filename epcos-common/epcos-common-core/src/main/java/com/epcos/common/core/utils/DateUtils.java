package com.epcos.common.core.utils;

import org.apache.commons.lang3.time.DateFormatUtils;

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {
    public static String YYYY = "yyyy";

    public static String YYYY_MM = "yyyyMM";

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    public static String YYYY年MM月DD日HHmmss = "yyyy年MM月dd日HH:mm:ss";
    public static String YYYY年MM月DD日HHmm = "yyyy年MM月dd日HH:mm";
    public static String YYYY年MM月DD日 = "yyyy年MM月dd日";

    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};


    /**
     * 获取指定分钟之后的时间
     *
     * @return Date() 当前日期
     */
    public static Date getAfterDate(Integer minute) {
        if (minute == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, minute);
        return calendar.getTime();
    }


    /**
     * 获取相对于当前时间的日期时间字符串（默认格式：yyyy-MM-dd HH:mm:ss）
     * <p>
     * 该方法允许灵活调整年、月、日、小时、分钟等时间单位，返回格式化后的日期时间字符串。
     * 默认格式为 "yyyy-MM-dd HH:mm:ss"（例如："2023-08-15 14:30:00"）。
     * <p>
     * 使用示例：
     * getRelativeDateTime(0, -3, 5, 2, 30)
     * → 返回当前时间减去3个月，加上5天和2小时30分钟后的时间
     *
     * @param years   年数偏移量（整数）
     *                - 正数：增加指定年数（如 1 表示一年后）
     *                - 负数：减少指定年数（如 -1 表示一年前）
     *                - 0：不改变年份
     * @param months  月数偏移量（整数）
     *                - 正数：增加指定月数（如 3 表示三个月后）
     *                - 负数：减少指定月数（如 -2 表示两个月前）
     *                - 0：不改变月份
     * @param days    天数偏移量（整数）
     *                - 正数：增加指定天数（如 7 表示一周后）
     *                - 负数：减少指定天数（如 -5 表示五天前）
     *                - 0：不改变天数
     * @param hours   小时偏移量（整数）
     *                - 正数：增加指定小时（如 2 表示两小时后）
     *                - 负数：减少指定小时（如 -3 表示三小时前）
     *                - 0：不改变小时
     * @param minutes 分钟偏移量（整数）
     *                - 正数：增加指定分钟（如 30 表示三十分钟后）
     *                - 负数：减少指定分钟（如 -15 表示十五分钟前）
     *                - 0：不改变分钟
     * @return 格式化后的日期时间字符串（格式：yyyy-MM-dd HH:mm:ss）
     */
    public static String getRelativeDateTime(int years, int months, int days, int hours, int minutes) {
        return getRelativeDateTime(years, months, days, hours, minutes, 0, "yyyy-MM-dd HH:mm:ss");
    }


    /**
     * 获取相对于当前时间的日期时间字符串（可自定义格式）
     * <p>
     * 这是完整版方法，允许调整所有时间单位（包括秒），并支持自定义输出格式。
     * <p>
     * 使用示例：
     * getRelativeDateTime(0, 0, 0, 2, 15, 30, "HH:mm:ss")
     * → 返回当前时间加上2小时15分钟30秒后的时间（格式："14:45:30"）
     * <p>
     * getRelativeDateTime(1, 0, -10, 0, 0, 0, "yyyy年MM月dd日")
     * → 返回一年前10天的日期（格式："2022年08月05日"）
     *
     * @param years   年数偏移量（整数）
     *                - 正数：增加指定年数
     *                - 负数：减少指定年数
     *                - 0：不改变年份
     * @param months  月数偏移量（整数）
     *                - 正数：增加指定月数
     *                - 负数：减少指定月数
     *                - 0：不改变月份
     * @param days    天数偏移量（整数）
     *                - 正数：增加指定天数
     *                - 负数：减少指定天数
     *                - 0：不改变天数
     * @param hours   小时偏移量（整数）
     *                - 正数：增加指定小时
     *                - 负数：减少指定小时
     *                - 0：不改变小时
     * @param minutes 分钟偏移量（整数）
     *                - 正数：增加指定分钟
     *                - 负数：减少指定分钟
     *                - 0：不改变分钟
     * @param seconds 秒数偏移量（整数）
     *                - 正数：增加指定秒数
     *                - 负数：减少指定秒数
     *                - 0：不改变秒数
     * @param format  日期时间格式字符串（遵循DateTimeFormatter模式）
     *                - 常用格式：
     *                "yyyy-MM-dd" → 2023-08-15
     *                "yyyy-MM-dd HH:mm:ss" → 2023-08-15 14:30:00
     *                "HH:mm:ss" → 14:30:00
     *                "yyyy年MM月dd日 HH时mm分" → 2023年08月15日 14时30分
     *                - 如果传入null或空字符串，将使用默认格式"yyyy-MM-dd HH:mm:ss"
     * @return 格式化后的日期时间字符串
     */
    public static String getRelativeDateTime(int years, int months, int days, int hours, int minutes, int seconds, String format) {

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime result = now
                .plusYears(years)
                .plusMonths(months)
                .plusDays(days)
                .plusHours(hours)
                .plusMinutes(minutes)
                .plusSeconds(seconds);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return result.format(formatter);
    }

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM
     *
     * @return String
     */
    public static String getDateToM() {
        return dateTimeNow(YYYY_MM);
    }

    /**
     * 根据想要的时间格式，返回当前时间对应格式的时间
     * <p>例如：传入yyyy-MM-dd HH:dd:ss，则返回 2022-9-09-30 10:40:10</p>
     *
     * @param timeFormat 时间格式，如yyyy-MM-dd HH:dd:ss
     * @return
     */
    public static String getTimeFormat(String timeFormat, Date date) {
        return new SimpleDateFormat(timeFormat).format(date);
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static final String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow() {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(final String format) {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(final Date date) {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String dateTimeCN(final Date date) {
        if (date == null) {
            return null;
        }
        return parseDateToStr(YYYY年MM月DD日HHmmss, date);
    }

    public static final String date(final Date date) {
        if (date == null) {
            return null;
        }
        return parseDateToStr(YYYY年MM月DD日, date);
    }

    public static final String dateM(final Date date) {
        if (date == null) {
            return null;
        }
        return parseDateToStr(YYYY年MM月DD日HHmm, date);
    }

    public static final String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(final String format, final String ts) {
        try {
            return new SimpleDateFormat(format).parse(ts);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate() {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算两个时间差
     */
    public static String getDatePoor(Date endDate, Date nowDate) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    /**
     * 增加 LocalDateTime ==> Date
     */
    public static Date toDate(LocalDateTime temporalAccessor) {
        ZonedDateTime zdt = temporalAccessor.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 增加 LocalDate ==> Date
     */
    public static Date toDate(LocalDate temporalAccessor) {
        LocalDateTime localDateTime = LocalDateTime.of(temporalAccessor, LocalTime.of(0, 0, 0));
        ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    public static Date[] startAndEnd(Date date) {
        if (Objects.isNull(date)) {
            return new Date[]{null, null};
        }
        LocalDate ld = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        Date start = Date.from(ld.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date end = Date.from(ld.plusDays(1).plus(-1, ChronoUnit.SECONDS).atStartOfDay(ZoneId.systemDefault()).toInstant());
        return new Date[]{start, end};
    }

    public static Date start(Date date) {
        if (Objects.isNull(date)) {
            return null;
        }
        LocalDate ld = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return Date.from(ld.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    public static Date end(Date date) {
        if (Objects.isNull(date)) {
            return null;
        }
        LocalDate ld = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return Date.from(ld.plusDays(1).plus(-1, ChronoUnit.SECONDS).atStartOfDay(ZoneId.systemDefault()).toInstant());
    }
}
