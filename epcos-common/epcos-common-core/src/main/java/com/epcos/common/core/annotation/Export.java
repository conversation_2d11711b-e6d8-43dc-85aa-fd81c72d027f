package com.epcos.common.core.annotation;

import org.springframework.core.annotation.Order;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/15 14:03
 */
@Order(369)
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Export {

    String value() default "";

}
