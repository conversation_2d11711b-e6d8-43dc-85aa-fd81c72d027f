package com.epcos.common.core.annotation;


import com.epcos.common.core.annotation.impl.CharacterLengthValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Constraint(validatedBy = CharacterLengthValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface CharacterLength {

    String message() default "字符长度超出限制";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    /**
     * 字符长度
     */
    int length();

    /**
     * 数字范围
     */
    int[] nums() default {};

    /**
     * 数字范围
     */
    int min() default 0;

    int max() default Integer.MAX_VALUE;

}
