package com.epcos.common.core.domain.review;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ReviewSummaryVo implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("评审结果")
    private List<SupplierDetailsVo> supplierDetailsVoList;
    @ApiModelProperty("未完成评审的评委名单")
    private List<IncompleteJudgeVo> IncompleteJudges;
    @ApiModelProperty("评委签名详情")
    private JudgeSignVo JudgesSign;
    @ApiModelProperty("标段code")
    private String subpackageCode;
    @ApiModelProperty("评审类型：  1-评审 ， 0-调研   2-投票")
    private Integer reviewMethod;
}

