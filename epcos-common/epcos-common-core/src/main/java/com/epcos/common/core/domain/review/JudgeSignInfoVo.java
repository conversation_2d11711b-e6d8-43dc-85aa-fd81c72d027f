package com.epcos.common.core.domain.review;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class JudgeSignInfoVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 评委ID
     */
    @ApiModelProperty("评委ID")
    private Long judgeId;
    /**
     * 评委姓名
     */
    @ApiModelProperty("评委姓名")
    private String judgeName;

    /**
     * 证件号码
     */
    @ApiModelProperty("证件号码")
    private String insideIdentity;

    /**
     * 评委类型： 1-组长,  0-组员
     */
    @ApiModelProperty("评委类型： 1-组长,  0-组员")
    private Integer judgeType;

    /**
     * 采购代表：1-代表,  0-非代表
     */
    @ApiModelProperty("采购代表：1-代表,  0-非代表")
    private Integer delegate;

    /**
     * 评委是否签字： 0-未签字，1-已签字
     */
    @ApiModelProperty("评委是否签字： 0-未签字，1-已签字")
    private Integer isSign;


}
