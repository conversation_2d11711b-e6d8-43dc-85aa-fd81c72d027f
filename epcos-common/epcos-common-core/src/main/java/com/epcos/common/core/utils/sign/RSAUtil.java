package com.epcos.common.core.utils.sign;

import com.epcos.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * rsa 工具
 */
@Slf4j
public class RSAUtil {

    // 算法名
    private final static String RSA = "RSA";
    // 公钥key
    public final static String KEY_RSA_PUBLIC = "RSA_PUBLIC_KEY";
    // 私钥key
    public final static String KEY_RSA_PRIVATE = "RSA_PRIVATE_KEY";
    // rsa 最大加密大小
    private final static int MAX_ENCRYPT_BLOCK = 117;
    // rsa 最大解密大小
    private final static int MAX_DECRYPT_BLOCK = 128;
    //定义签名算法
    private final static String KEY_RSA_SIGNATURE = "MD5withRSA";

    /**
     * 生成公私密钥对
     */
    public static Map<String, Object> init() {
        Map<String, Object> res = null;
        try {
            KeyPairGenerator rsaGen = KeyPairGenerator.getInstance(RSA);
            // 设置密钥对的bit数，越大越安全，但速度减慢，一般使用512或1024
            rsaGen.initialize(1024);
            // 生成密钥对
            KeyPair keyPair = rsaGen.generateKeyPair();
            // 公钥
            RSAPublicKey rsaPublicKey = (RSAPublicKey) keyPair.getPublic();
            // 私钥
            RSAPrivateKey rsaPrivateKey = (RSAPrivateKey) keyPair.getPrivate();
            res = new HashMap<>();
            res.put(KEY_RSA_PUBLIC, rsaPublicKey);
            res.put(KEY_RSA_PRIVATE, rsaPrivateKey);
        } catch (NoSuchAlgorithmException e) {
            log.error("e: " + e);
            throw new RuntimeException(e);
        }
        return res;
    }

    /**
     * 获取base64编码的公钥串
     */
    public static String getPublicKey(Map<String, Object> map) {
        if (CollectionUtils.isEmpty(map)) {
            return null;
        }
        Key key = (Key) map.get(KEY_RSA_PUBLIC);
        return encryptBase64(key.getEncoded());
    }

    /**
     * 获取base64编码的私钥串
     */
    public static String getPrivateKey(Map<String, Object> map) {
        if (CollectionUtils.isEmpty(map)) {
            return null;
        }
        Key key = (Key) map.get(KEY_RSA_PRIVATE);
        return encryptBase64(key.getEncoded());
    }

    private static PublicKey getPublicKey(String publicKey) throws NoSuchAlgorithmException, InvalidKeySpecException {
        byte[] decodePublicKey = decryptBase64(publicKey);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(decodePublicKey);
        return KeyFactory.getInstance(RSA).generatePublic(keySpec);
    }

    private static PrivateKey getPrivateKey(String privateKey) throws NoSuchAlgorithmException, InvalidKeySpecException {
        // 解密由base64编码的私钥
        byte[] bytes = decryptBase64(privateKey);
        // 构造PKCS8EncodedKeySpec对象
        PKCS8EncodedKeySpec pkcs = new PKCS8EncodedKeySpec(bytes);
        // 指定的加密算法
        KeyFactory factory = KeyFactory.getInstance(RSA);
        // 取私钥对象
        return factory.generatePrivate(pkcs);
    }

    /**
     * base64 解码
     */
    public static byte[] decryptBase64(String data) {
        return Base64.getDecoder().decode(data);
    }

    /**
     * base64 编码
     */
    public static String encryptBase64(byte[] data) {
        return new String(Base64.getEncoder().encode(data));
    }


    /**
     * 分段加密
     * 编码publicKey
     * 返回编码后的数据
     */
    public static String encrypted(String dataStr, String publicKeyStr) {
        try {
            // 公钥
            PublicKey publicKey = getPublicKey(publicKeyStr);
            // 数据加密
            Cipher cipher = Cipher.getInstance(RSA);
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            // 对据分段处理
            byte[] decryptedData = segmentHandle(dataStr.getBytes(StandardCharsets.UTF_8), cipher, MAX_ENCRYPT_BLOCK);
            return encryptBase64(decryptedData);
        } catch (Exception e) {
            log.error("响应公钥加密失败：dataStr:{}, publicKeyStr:{}", dataStr, publicKeyStr, e);
            throw new ServiceException("响应加密失败");
        }
    }

    // segment
    private static byte[] segmentHandle(byte[] dataBytes, Cipher cipher, int maxHandleLength) throws IllegalBlockSizeException, BadPaddingException {
        // 分段处理
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            int i = 0;
            int offSet = 0;
            int inputLen = dataBytes.length;
            byte[] cache;
            while ((inputLen - offSet) > 0) {
                if (inputLen - offSet > maxHandleLength) {
                    cache = cipher.doFinal(dataBytes, offSet, maxHandleLength);
                } else {
                    cache = cipher.doFinal(dataBytes, offSet, inputLen - offSet);
                }
                out.write(cache, 0, cache.length);
                offSet = ++i * maxHandleLength;
            }
            return out.toByteArray();
        } catch (IOException e) {
            log.error("响应加密分段处理失败！ ", e);
            throw new ServiceException("响应加密分段处理失败！");
        }
    }


    /**
     * 私钥加密
     */
    public static String encryptByPrivateKey(String dataStr, String priKeyStr) {
        try {
            PrivateKey privateKey = getPrivateKey(priKeyStr);
            Cipher cipher = Cipher.getInstance(RSA);
            cipher.init(Cipher.ENCRYPT_MODE, privateKey);
            byte[] out = segmentHandle(dataStr.getBytes(StandardCharsets.UTF_8), cipher, MAX_ENCRYPT_BLOCK);
            return encryptBase64(out);
        } catch (Exception e) {
            log.error("私钥加密失败：dataStr:{}, priKeyStr:{}", dataStr, priKeyStr, e);
            throw new ServiceException("私钥加密失败");
        }
    }

    /**
     * 公钥解密
     */
    public static String decryptByPublicKey(String dataStr, String puKey) {
        try {
            PublicKey publicKey = getPublicKey(puKey);
            Cipher cipher = Cipher.getInstance(RSA);
            cipher.init(Cipher.DECRYPT_MODE, publicKey);
            byte[] out = segmentHandle(decryptBase64(dataStr), cipher, MAX_DECRYPT_BLOCK);
            return new String(out);
        } catch (Exception e) {
            log.error("公钥解密失败：dataStr:{}, puKey:{}", dataStr, puKey, e);
            throw new ServiceException("公钥解密失败");
        }
    }

    /**
     * @param dataStr       待解密数据 （base64编码对象）
     * @param privateKeyStr 私钥
     * @param returnBase64  是否以base64编码返回
     * @return 返回base64编码后对象
     * 解密
     */
    public static String decrypted(String dataStr, String privateKeyStr, boolean returnBase64) {
        byte[] decryptBase64;
        try {
            decryptBase64 = decryptBase64(dataStr);
        } catch (IllegalArgumentException e) {
            log.error("非法请求，解码异常: dataStr={}", dataStr);
            throw new ServiceException("非法请求，解码异常");
        }
        try {
            // 获取私钥
            PrivateKey privateKey = getPrivateKey(privateKeyStr);
            Cipher cipher = Cipher.getInstance(RSA);
            cipher.init(Cipher.DECRYPT_MODE, privateKey);
            byte[] res = segmentHandle(decryptBase64, cipher, MAX_DECRYPT_BLOCK);
            if (returnBase64) {
                return encryptBase64(res);
            }
            return new String(res);
        } catch (Exception e) {
            log.error("非法请求，解密异常：dataStr:{}, publicKeyStr:{}", dataStr, privateKeyStr, e);
            throw new ServiceException("非法请求，解密异常");
        }
    }


    /**
     * 用私钥对加密数据进行签名 * *
     *
     * @param encryptedStr 加密后数据
     * @param privateKey   *
     * @return 返回签名str
     */
    public static String sign(String encryptedStr, String privateKey) {
        try {
            //将私钥加密数据字符串转换为字节数组
            byte[] data = encryptedStr.getBytes();
            // 用私钥对信息生成数字签名
            Signature signature = Signature.getInstance(KEY_RSA_SIGNATURE);
            signature.initSign(getPrivateKey(privateKey));
            signature.update(data);
            return encryptBase64(signature.sign());
        } catch (Exception e) {
            log.error("e: " + e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 校验数字签名 * *
     *
     * @param encryptedStr 加密数据
     * @param publicKey    *
     * @param sign         签名str
     * @return 校验成功返回true，失败返回false
     */
    public static boolean verify(String encryptedStr, String publicKey, String sign) {
        boolean flag = false;
        try {
            //将私钥加密数据字符串转换为字节数组
            byte[] data = encryptedStr.getBytes();
            // 用公钥验证数字签名
            Signature signature = Signature.getInstance(KEY_RSA_SIGNATURE);
            signature.initVerify(getPublicKey(publicKey));
            signature.update(data);
            flag = signature.verify(decryptBase64(sign));
        } catch (Exception e) {
            log.error("e: " + e);
            throw new RuntimeException(e);
        }
        return flag;
    }

    public static void main(String[] args) throws Exception {
        String puKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCC3Lb0O4zgEakDfJ4XJO5zadXep9bQeWyJ6pa0e328PYQYZgLNP7eVrAP7mVZgG+8D4MicIcStTQnBxF8AEyJKrh/M/3WSSK2zDvrZn1paWf4SA8zFIn5cuYlcUH+WuxghQn3kKRUW2qtBY9eaGF5qntascctNgQTHmW3eqQzDBQIDAQAB";
        String priKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAILctvQ7jOARqQN8nhck7nNp1d6n1tB5bInqlrR7fbw9hBhmAs0/t5WsA/uZVmAb7wPgyJwhxK1NCcHEXwATIkquH8z/dZJIrbMO+tmfWlpZ/hIDzMUifly5iVxQf5a7GCFCfeQpFRbaq0Fj15oYXmqe1qxxy02BBMeZbd6pDMMFAgMBAAECgYAeNo07CEC3PvyRpITvfQRcuzO4q3GKQm6PzQyscNKP0ngcFflRoANn2AY3YaiymeBuOci3W4iIJoA5L3hrkP9agBhW8EQIPl/hSm16aq7/m14dtnHPlOdwLL8VmK+lsG2qujmn3DdPROqMorX48UBshnC+2qwa6Z18tFTeX7jkJQJBALzQpkKKI83U1APPiopzZh6dw2tKjm8Fv1iEnig+73XrDU1EHIrB1OQAoSR0/lR5gmOKBCQgDKJFpOaevcctZIMCQQCxbRSOi6DKMdp8u5iTHDW4BcFy30RpAr1I8EPfClIIDxhTJyriRMimsaNSi0U89a11KFPao1NRAppNIjjhV/PXAkBEsVg7jwxVnx9/P2t00WUOsHDfQOGu7JVfu+faVoNCEEDnomcL6FbumHgHznSVARv54MV+6xYNl27V5FzA8PfHAkEAgGZ0bgRWRH13pLUOYcgUiCyCJuO02loFKffW5l7Nps9lxyedPUqR+zCrBV2MqeURtquMo7l23jWH/TI6/7lq2QJAT4CCSX3RAHRz1pk4Z0/lJV1SN5pMfy8Botpko3Bj7qYFImfkZhqacWtJ6X7VxumgCLK3t1953nQSALtAwQ6GvA==";
        String aesKey = "kapnoC182X5Dw89J";

        Map<String, Object> init = RSAUtil.init();
        String pubKey = RSAUtil.getPublicKey(init);
        String riKey = RSAUtil.getPrivateKey(init);
        System.out.println(pubKey);
        System.out.println(riKey);


//        原码base64_私钥加密_公钥解密_ok(aesKey, puKey, priKey);
//        原码使用_私钥加密_公钥解密(aesKey, puKey, priKey);


//        test_1();
//        test();


    }

    private static void 原码使用_私钥加密_公钥解密(String aesKey, String puk, String prik) throws NoSuchAlgorithmException, InvalidKeySpecException, NoSuchPaddingException, InvalidKeyException, IllegalBlockSizeException, BadPaddingException {
        PrivateKey privateKey = getPrivateKey(prik);
        Cipher cipher = Cipher.getInstance(RSA);
        cipher.init(Cipher.ENCRYPT_MODE, privateKey);
        byte[] out = segmentHandle(aesKey.getBytes(StandardCharsets.UTF_8), cipher, MAX_ENCRYPT_BLOCK);
        String 原码使用私钥加密返回结果 = new String(out);

        log.error("原码使用私钥加密返回结果: " + 原码使用私钥加密返回结果);

        PublicKey publicKey = getPublicKey(puk);
        Cipher cipher_2 = Cipher.getInstance(RSA);
        cipher_2.init(Cipher.DECRYPT_MODE, publicKey);
        byte[] de = segmentHandle(原码使用私钥加密返回结果.getBytes(StandardCharsets.UTF_8), cipher_2, MAX_DECRYPT_BLOCK);
        String 使用公钥解密返回结果 = new String(de);
        log.error("使用公钥解密返回结果: " + 使用公钥解密返回结果);

    }

    private static void 原码base64_私钥加密_公钥解密_ok(String aesKey, String puk, String prik) throws Exception {

        PrivateKey privateKey = getPrivateKey(prik);
        Cipher cipher = Cipher.getInstance(RSA);
        cipher.init(Cipher.ENCRYPT_MODE, privateKey);
        byte[] out = segmentHandle(aesKey.getBytes(StandardCharsets.UTF_8), cipher, MAX_ENCRYPT_BLOCK);
        String 使用私钥加密_并base64_返回结果 = encryptBase64(out);
        log.error("使用私钥加密_并base64_返回结果: " + 使用私钥加密_并base64_返回结果);

        PublicKey publicKey = getPublicKey(puk);
        Cipher cipher_2 = Cipher.getInstance(RSA);
        cipher_2.init(Cipher.DECRYPT_MODE, publicKey);
        byte[] o = segmentHandle(decryptBase64(使用私钥加密_并base64_返回结果), cipher_2, MAX_DECRYPT_BLOCK);
        String 先base64解码_公钥解密_并返回结果 = new String(o);
        log.error("先base64解码_公钥解密_并返回结果: " + 先base64解码_公钥解密_并返回结果);
    }


    private static void test_1() {
        String aesKey = "RqJY/qJvIYMLSHPKjjSegq4Mew2OkueqacOYkzb6O1MnOkPrtnCVM1b9JxyW1oIBgxL3d4CFnz8S/m0MrgT8wV1yL+Edvi5FJtOqe9fAobZARfSdwlNGaTXgrsKKzJGb1+6fCGmEpsXPzDQHcBgDuBmoHkZGRel3pUbddBeCDIo=";
        String puKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCC3Lb0O4zgEakDfJ4XJO5zadXep9bQeWyJ6pa0e328PYQYZgLNP7eVrAP7mVZgG+8D4MicIcStTQnBxF8AEyJKrh/M/3WSSK2zDvrZn1paWf4SA8zFIn5cuYlcUH+WuxghQn3kKRUW2qtBY9eaGF5qntascctNgQTHmW3eqQzDBQIDAQAB";
        System.out.println(decryptByPublicKey(aesKey, puKey));


    }

    private static void test() {
        String data = "{\n" +
                "    \"username\": \"132100000011321000000113210000001132100000011321000000113210000001132100000011321000000113210000001132100000011321000000113210000001132100000011321000000113210000001132100000011321000000113210000001132100000011321000000113210000001132100000011321000000113210000001132100000011321000000113210000001132100000011321000000113210000001132100000011321000000113210000001132100000011321000000113210000001132100000011321000000113210000001132100000011321000000113210000001\",\n" +
                "    \"password\": \"epc1688\"\n" +
                "}";

        String puKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCC3Lb0O4zgEakDfJ4XJO5zadXep9bQeWyJ6pa0e328PYQYZgLNP7eVrAP7mVZgG+8D4MicIcStTQnBxF8AEyJKrh/M/3WSSK2zDvrZn1paWf4SA8zFIn5cuYlcUH+WuxghQn3kKRUW2qtBY9eaGF5qntascctNgQTHmW3eqQzDBQIDAQAB";
        String priKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAILctvQ7jOARqQN8nhck7nNp1d6n1tB5bInqlrR7fbw9hBhmAs0/t5WsA/uZVmAb7wPgyJwhxK1NCcHEXwATIkquH8z/dZJIrbMO+tmfWlpZ/hIDzMUifly5iVxQf5a7GCFCfeQpFRbaq0Fj15oYXmqe1qxxy02BBMeZbd6pDMMFAgMBAAECgYAeNo07CEC3PvyRpITvfQRcuzO4q3GKQm6PzQyscNKP0ngcFflRoANn2AY3YaiymeBuOci3W4iIJoA5L3hrkP9agBhW8EQIPl/hSm16aq7/m14dtnHPlOdwLL8VmK+lsG2qujmn3DdPROqMorX48UBshnC+2qwa6Z18tFTeX7jkJQJBALzQpkKKI83U1APPiopzZh6dw2tKjm8Fv1iEnig+73XrDU1EHIrB1OQAoSR0/lR5gmOKBCQgDKJFpOaevcctZIMCQQCxbRSOi6DKMdp8u5iTHDW4BcFy30RpAr1I8EPfClIIDxhTJyriRMimsaNSi0U89a11KFPao1NRAppNIjjhV/PXAkBEsVg7jwxVnx9/P2t00WUOsHDfQOGu7JVfu+faVoNCEEDnomcL6FbumHgHznSVARv54MV+6xYNl27V5FzA8PfHAkEAgGZ0bgRWRH13pLUOYcgUiCyCJuO02loFKffW5l7Nps9lxyedPUqR+zCrBV2MqeURtquMo7l23jWH/TI6/7lq2QJAT4CCSX3RAHRz1pk4Z0/lJV1SN5pMfy8Botpko3Bj7qYFImfkZhqacWtJ6X7VxumgCLK3t1953nQSALtAwQ6GvA==";

        System.out.println("初始数据：" + data);
        // 公钥加密
        String encryptedBytes = encrypted(data, puKey);
        System.out.println("公钥加密后：" + encryptedBytes);
        // 私钥解密
        String decryptedBytes = decrypted(encryptedBytes, priKey, false);
        System.out.println("私钥解密后：" + decryptedBytes);
        // 私钥加密
        String encryptedBytes2 = encryptByPrivateKey(data, priKey);
        System.out.println("私钥加密后：" + encryptedBytes2);
        // 公钥解密
        String decryptedBytes2 = decryptByPublicKey(encryptedBytes2, puKey);
        System.out.println("公钥解密后：" + decryptedBytes2);
    }

}
