package com.epcos.common.core.web.domain.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(description = "用户组织关系")
public class UserOrgVO implements Serializable {
    private static final long serialVersionUID = 2811702456847858016L;


    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "组织编码")
    private String orgCode;

    @ApiModelProperty(value = "组织审核状态,0-等待组织进行审核，1-审核失败，2-审核通过")
    private String orgAuditStatus;

    @ApiModelProperty(value = "审核不通过理由")
    private String reason;

    @ApiModelProperty(value = "组织名称")
    private String orgName;
}
