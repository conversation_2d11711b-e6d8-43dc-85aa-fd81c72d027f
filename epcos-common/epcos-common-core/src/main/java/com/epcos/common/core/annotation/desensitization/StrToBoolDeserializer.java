package com.epcos.common.core.annotation.desensitization;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/21 10:17
 */
@Slf4j
public class StrToBoolDeserializer extends JsonDeserializer<Boolean> {

    @Override
    public Boolean deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        return Objects.equals("1", p.getValueAsString());
    }
}
