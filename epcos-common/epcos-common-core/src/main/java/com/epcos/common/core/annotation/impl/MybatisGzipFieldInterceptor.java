package com.epcos.common.core.annotation.impl;

import com.epcos.common.core.annotation.Gzip;
import com.epcos.common.core.utils.purchase.GzipUtils;
import com.epcos.common.core.utils.reflect.ReflectUtils;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Modifier;
import java.util.Collection;
import java.util.Map;
import java.util.function.Consumer;

/**
 * 针对大文本进行gzip处理
 */
@Component
@Intercepts(value = {
        @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class})}
)
public class MybatisGzipFieldInterceptor implements Interceptor {

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Executor executor = (Executor) invocation.getTarget();
        Object[] args = invocation.getArgs();
        MappedStatement ms = (MappedStatement) args[0];
        Object result;
        if (ms.getSqlCommandType() == SqlCommandType.SELECT) {
            CacheKey cacheKey;
            BoundSql boundSql;
            // 查询
            if (args.length == 6) {
                boundSql = (BoundSql) args[5];
                cacheKey = (CacheKey) args[4];
            } else {
                // 对于没有明确 CacheKey 的查询，创建 CacheKey
                boundSql = ms.getBoundSql(args[1]);
                cacheKey = executor.createCacheKey(ms, args[1], (RowBounds) args[2], boundSql);
            }
            // 是否缓存
            boolean isCached = executor.isCached(ms, cacheKey);
            // 查询操作
            result = invocation.proceed();
            if (!isCached) {
                // 未缓存，处理 gzip 字段
                processGzipFields(result, true);
            }
        } else {
            // 更新操作
            result = args[1];
            processGzipFields(result, false);
            result = invocation.proceed();
        }
        return result;
    }

    private void processGzipFields(Object obj, boolean isSelect) {
        Consumer<Object> handler = o -> gzipHandler(isSelect, o);
        if (obj instanceof Collection) {
            ((Collection<?>) obj).forEach(handler);
        } else if (obj instanceof Map) {
            ((Map<?, ?>) obj).values().stream().limit(1).forEach(handler);
        } else {
            handler.accept(obj);
        }
    }

    private void gzipHandler(boolean select, Object obj) {
        if (obj == null) {
            return;
        }
        Class<?> clazz = obj.getClass();
        if (clazz.getName().startsWith("java.")
                || Modifier.isFinal(clazz.getModifiers()) && clazz.getSuperclass() != null
                || clazz.isInterface()) {
            return;
        }
        ReflectUtils.getAllFields(clazz)
                .stream()
                .filter(f -> f.getAnnotation(Gzip.class) != null
                        && f.getType().equals(String.class))
                .filter(f -> StringUtils.hasText(ReflectUtils.invokeGetter(obj, f.getName())))
                .forEach(f -> {
                    String fieldValue = ReflectUtils.invokeGetter(obj, f.getName());
                    String value = select ? GzipUtils.unDeflate(fieldValue)
                            : GzipUtils.deflate(fieldValue);
                    ReflectUtils.invokeSetter(obj, f.getName(), value);
                });
    }


}
