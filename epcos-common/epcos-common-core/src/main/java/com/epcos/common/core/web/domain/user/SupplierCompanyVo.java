package com.epcos.common.core.web.domain.user;

import com.epcos.common.core.annotation.CharacterLength;
import com.epcos.common.core.annotation.desensitization.BoolToIntSerializer;
import com.epcos.common.core.annotation.valid.Add;
import com.epcos.common.core.annotation.valid.Up;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class SupplierCompanyVo {

    @ApiModelProperty("主键id,更新时必填")
    private Long id;

    @ApiModelProperty(value = "供应商代码类型")
    private String bidderCodeType;

    @ApiModelProperty(value = "统一信用代码或营业执照号码")
    private String licNumber;

    @ApiModelProperty(value = "供应商公司名称")
    private String bidderName;

    @ApiModelProperty(value = "法定代表人联系方式")
    private String contactNumber;

    @ApiModelProperty(value = "联系地址")
    private String contactAddress;

    @ApiModelProperty(value = "行政区域代码")
    private String regionCode;

    @ApiModelProperty(value = "开户银行")
    private String openingBank;

    @ApiModelProperty(value = "基本账户账号")
    private String basicAccount;

    @ApiModelProperty(value = "行业代码")
    private String industryCode;

    @ApiModelProperty(value = "供货范围")
    private String scopeOfSupply;

    @ApiModelProperty(value = "单位性质")
    private String unitNature;

    @ApiModelProperty(value = "营业执照或组织机构代码证件扫描件")
    private String businessLicense;

    @ApiModelProperty(value = "法定代表人身份证明扫描件")
    private String legalRepresentativeIdentityCertificate;

    @ApiModelProperty(value = "是否中小微企业，是=1，0=不是")
    @JsonSerialize(using = BoolToIntSerializer.class)
    private Boolean whetherMicroEnterprises;

    @ApiModelProperty(value = "中小微企业文件")
    private String microEnterprises;

    @ApiModelProperty(value = "电子邮箱")
    private String email;

    @ApiModelProperty(value = "投标授权书")
    private String powerOfAttorney;

    @ApiModelProperty("法人证件类型，1：身份证，2：护照，3：军（警）官证，4：港澳通行证，5：台胞证，6：户口簿，9：其他")
    @CharacterLength(length = 1, nums = {1, 2, 3, 4, 5, 6, 9}, message = "法人证件类型，1：身份证，2：护照，3：军（警）官证，4：港澳通行证，5：台胞证，6：户口簿，9：其他", groups = {Add.class, Up.class})
    private Character certificate;

    @ApiModelProperty(value = "法定代表人证件号码")
    private String certificateCode;

    @ApiModelProperty(value = "法定代表人名称")
    private String certificateName;

    @ApiModelProperty(value = "投标责任人姓名")
    private String infoReporterName;

    @ApiModelProperty("投标责任人证件类型，1：身份证，2：护照，3：军（警）官证，4：港澳通行证，5：台胞证，6：户口簿，9：其他")
    @CharacterLength(length = 1, nums = {1, 2, 3, 4, 5, 6, 9}, message = "投标责任人证件类型，1：身份证，2：护照，3：军（警）官证，4：港澳通行证，5：台胞证，6：户口簿，9：其他", groups = {Add.class, Up.class})
    private Character infoReporter;

    @ApiModelProperty(value = "投标责任人证件号码")
    private String infoReporterCode;

    @ApiModelProperty(value = "投标责任人联系电话")
    private String infoReporterContactNumber;

    @ApiModelProperty(value = "投标类型")
    private String bidType;

    @ApiModelProperty(value = "注册资本")
    private String registeredCapital;

    @ApiModelProperty(value = "营业期限")
    private String operatingPeriod;

    @ApiModelProperty(value = "成立日期")
    private String dateOfEstablishment;

    @ApiModelProperty(value = "登记机关")
    private String registrationAndAuthority;

    @ApiModelProperty(value = "用户id")
    private Long userId;
}
