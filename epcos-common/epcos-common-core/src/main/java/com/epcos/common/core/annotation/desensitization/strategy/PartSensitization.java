package com.epcos.common.core.annotation.desensitization.strategy;

import com.epcos.common.core.annotation.desensitization.DesensitizationStrategy;
import com.epcos.common.core.utils.StringUtils;

import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 取整个串的后 2/3 处理为 *
 */
public class PartSensitization implements DesensitizationStrategy {

    @Override
    public String desensitizationHandle(String oldValue) {
        if (StringUtils.isBlank(oldValue)) {
            return oldValue;
        }
        if (oldValue.length() == 1) {
            return "*";
        }
        int old = oldValue.length() / 3 + 1;
        int replaceN = oldValue.length() - old;
        return oldValue.substring(0, old) + getX(replaceN);
    }

    private String getX(int n) {
        return IntStream.range(0, n).mapToObj(i -> "*").collect(Collectors.joining());
    }

}
