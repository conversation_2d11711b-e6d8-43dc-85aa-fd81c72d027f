package com.epcos.common.core.enums;

import com.epcos.common.core.exception.ServiceException;
import org.springframework.util.StringUtils;

import java.util.Arrays;

/**
 * 短信类型
 */
public enum SmsEnum {

    /**
     * 无模版通用
     */
    NO_TEMPLATE("  %s",1),

    /**
     * 忘记密码验证码 (龙岗特有)
     */
    SMS_FORGET_PASSWORD("您的忘记密码验证码：%s，有效时间%d分钟。请勿泄漏，非本人操作请忽略。", 2),

    /**
     * 注册验证码
     */
    SMS_REGISTER("您正在进行注册操作，验证码：%s，有效时间%d分钟。请勿泄漏，非本人操作请忽略。", 2),
    /**
     * 登录验证码
     */
    SMS_LOGIN("您的登录验证码：%s，有效时间%d分钟。请勿泄漏，非本人操作请忽略。", 2),

    /**
     * 采购人上传招标文件并经过审核后通过，给报名通过的供应商发送短信
     */
    SMS_TENDER_FILE("您报名 %s 项目，%s，已上传招标文件，请尽快下载。", 2),
    /**
     * 采购人点击开标开始按钮，通知供应商解密；
     */
    SMS_OPEN_DECRYPT("您报名的 %s 项目，%s，已开标开始，请尽快对投标文件进行解密。", 2),

    /**
     * 供应商报名并通过审核
     */
    SMS_SUPPLIER_PASS("您已报名 %s 项目，%s ，请于 %s 之前上传投标文件。", 3),

    /**
     * 会议时间（开标时间）
     * 发布公告时发送
     */
    SMS_MEET("您报名的 %s 项目，%s ，于%s 开始会议，请在会议开始时间前半小时登录电子化招标采购平台进行签到。", 3),

    /**
     * 供应商账号禁用
     * 您的电子化招标平台账号因超过xx月未使用，已被停用，如需参与项目，请重新提交审核。
     */
    SMS_SUPPLIER_DISABLE("您的电子化招标平台账号因超过 %d 个月未使用，已被停用，如需参与项目，请重新提交审核。", 1),

    /**
     * 评委抽取
     */
    JUDGE_EXTRACT("%s 项目，%s ，已选择您为评审专家，请登录电子化招标采购平台查看详情。", 2);

    private final String info;
    private final int args;

    SmsEnum(String info, int args) {
        this.info = info;
        this.args = args;
    }

    /**
     * 获取填充后的短信内容
     */
    public String getInfo(Object... objs) {
        int objSize = objs == null ? 0 : objs.length;
        if (args == objSize) {
            return String.format(this.info, objs);
        } else {
            throw new ServiceException(
                    String.format("请正确设置短信参数个数。短信内容【%s】，参数个数【%d】，实际传入参数【%s】",
                            this.info, this.args, Arrays.toString(objs))
            );
        }
    }

    /**
     * 获取短信key
     */
    public String getSmsKey(String mobile) {
        if (StringUtils.hasText(mobile)) {
            return this.name() + ":" + mobile;
        } else {
            throw new ServiceException("请传入手机号码");
        }
    }


}
