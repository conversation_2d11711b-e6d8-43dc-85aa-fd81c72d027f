package com.epcos.common.core.utils.purchase;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @Title:
 * @Description:TODO
 * @author:moyu
 * @version:1.0
 * @since 2022-03-29 11:14
 */
public class GeneratorCodeUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(GeneratorCodeUtil.class);
    /**
     * 项目编号常量
     */
    private static final String PROJECT = "EP";
    /**
     * 招标项目编号常量
     */
    private static final String TENDER_PROJECT = "ET";
    /**
     * 标段编号常量
     */
    private static final String BID_SELECTION = "EB";
    /**
     * 招标公告编号
     */
    private static final String BULLETIN = "EPC_";

    /**
     * 委托项目编号前缀
     */
    private static final String AGENT_PROJECT = "AG_ZP";

    /**
     * 委托标段编号前缀
     */
    private static final String AGENT_SECTION = "AG_EB";

    private GeneratorCodeUtil() {
    }

    public static void main(String[] args) {
    }

    /**
     * 生成项目编号(根据10号令生成的项目编号为17位)
     * 声称规则:    项目常量 + 时间戳 + 【0-9】的随机一位拼接到最后
     * 例如：EP202203291127506
     *
     * @return
     */
    public static  String generatorProjectCode(Long id) {
        return generatorCode(PROJECT, "yyyyMMdd", 8, id);
    }

    /**
     * 生成招标项目编号(根据10号令生成的招标项目编号为20位)
     * 生成规则:  项目常量 + 时间戳 + 【0-9】的随机四位拼接到最后
     *
     * @return
     */
    public static  String generatorTenderProjectCode(Long id) {
        return generatorCode(TENDER_PROJECT, "yyyyMMddHH", 9, id);
    }

    /**
     * 生成标段编号(根据10号令生成的招标项目编号为23位)
     * 生成规则:  项目常量 + 时间戳 + 标段id
     *
     * @return
     */
    public static  String generatorBidSectionCodeByBidId(Long id) {
        return generatorCode(BID_SELECTION, "yyyyMMddHHmmss", 8, id);
    }

    /**
     * 根据传入的数字生成招标公告
     * 生成规则:  公告常量 + 年月日 + 招标项目id
     *
     * @param id
     * @return
     */
    public static  String generatorBulletinCodeByBidCount(Long id) {
        return generatorCode(BULLETIN, "yyyyMMdd", 7, id);
    }

    /**
     * 生成委托招标项目编号：
     * 生成规则:  AG_ZP + 第几个项目
     * AG_ZP-20220928-001
     *
     * @param id 当日项目个数+1
     *           //     * @param codeType 委托招标项目编号前缀
     * @return
     */
    public static  String generatorAgentProjectCode(Long id) {
        return generatorCode(AGENT_PROJECT, "yyyyMMdd", 4, id);
    }

    /**
     * 生成委托标段编号：
     * 生成规则:  AG_EB + 第几个项目
     * AG_EB-20220928-001
     *
     * @param id 当日项目个数+1
     *           //     * @param codeType 委托招标项目编号前缀
     * @return
     */
    public static  String generatorAgentSectionCode(Long id) {
        return generatorCode(AGENT_SECTION, "yyyyMMdd", 4, id);
    }

    /**
     * 生成编号
     *
     * @param constName   类型前缀-常量
     * @param dateForMart 时间格式化样式
     * @param maxLength   最大容量
     * @param id          即将生成的id后缀顺序
     * @return
     */
    private static String generatorCode(String constName, String dateForMart, Integer maxLength, Long id) {

        StringBuilder buffer = new StringBuilder(constName);
        SimpleDateFormat sdf = new SimpleDateFormat(dateForMart);
        String format = sdf.format(new Date());
        buffer.append(format);
        int length = id.toString().length();
        if (length < maxLength) {
            int zero = maxLength - 1 - length;
            for (int i = 0; i < zero; i++) {
                buffer.append(0);
            }
        } else {
            LOGGER.error("GeneratorCodeUtil.GeneratorCode有问题了,已经不能承载数据了:{}", id);
            return null;
        }
        buffer.append(id.toString());
        return buffer.toString();
    }
}