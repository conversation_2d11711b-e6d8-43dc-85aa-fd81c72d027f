package com.epcos.common.core.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.apache.ibatis.session.RowBounds;

import java.io.Serializable;
import java.util.Objects;

/**
 * <p>Description : 分页
 * <p>Date : 2018/9/9/009 17:28
 * <p><AUTHOR> wjq
 */
@Data
public class PagerParam implements Serializable {

    public static final int FIRST_CRITERIA_INDEX = 0;
    private static final long serialVersionUID = 2050179513755739826L;

    /**
     * 从第几页记录开始查询
     */
    @JsonProperty("pageNum")
    private Integer pageNum;

    /**
     * 限制返回记录数
     */
    @JsonProperty("pageSize")
    private Integer pageSize;

    public Integer getPageNum() {
        if (Objects.isNull(pageNum) || pageNum == 0) {
            pageNum = 1;
        }
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        if (Objects.isNull(pageSize) || pageSize == 0) {
            pageSize = 10;
        }
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

//    public RowBounds getRowBounds() {
//        if (this.getPageSize() == null || this.getPageNum() == null) {
//            return new RowBounds();
//        } else {
//            return new RowBounds(
//                    this.getPageNum() == null ? FIRST_CRITERIA_INDEX : (this.getPageNum() - 1) * this.getPageSize(),
//                    this.getPageSize());
//        }
//    }


}
