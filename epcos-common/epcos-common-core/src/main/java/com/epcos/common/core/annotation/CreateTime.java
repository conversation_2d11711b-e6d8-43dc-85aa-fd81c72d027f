package com.epcos.common.core.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @Title:
 * @Description:TODO
 * @author:moyu
 * @version:1.0
 * @since 2022-03-26 7:43
 */
@Retention(value = RetentionPolicy.RUNTIME)
@Target(value = ElementType.FIELD)
public @interface CreateTime {

    String value() default "";
}
