package com.epcos.common.core.constant;

/**
 * 服务名称
 *
 * <AUTHOR>
 */
public class ServiceNameConstants {
    /**
     * 认证服务的serviceId
     */
    public static final String AUTH_SERVICE = "epcos-auth";

    /**
     * 系统模块的serviceId
     */
    public static final String SYSTEM_SERVICE = "epcos-system";

    /**
     * 文件服务的serviceId
     */
    public static final String FILE_SERVICE = "epcos-epcfile";

    /**
     * 签章服务的serviceId
     */
    public static final String SEAL_SERVICE = "epcos-seal";
    /**
     * 投标人服务的serviceId
     */
    public static final String SUPPLIER_SERVICE = "epcos-bidding";
    /**
     * 招标人服务serviceId
     */
    public static final String PURCHASER_SERVICE = "epcos-bidding";
    /**
     * 委托项目服务serviceId
     */
    public static final String AGENT_SERVICE = "epcos-agent";
    /**
     * 专家服务serviceId
     */
    public static final String REVIEW_SERVICE = "epcos-review";

    /**
     * 易建采专家抽取服务serviceId
     */
    public static final String EXTRACT_SERVICE = "epcos-extract";
    /**
     * 支付服务serviceId
     */
    public static final String PAY_SERVICE = "epcos-pay";
}
