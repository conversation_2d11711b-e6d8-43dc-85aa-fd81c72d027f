package com.epcos.common.core.utils;

import com.epcos.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;

import static com.epcos.common.core.enums.ClientEnum.*;

@Slf4j
public class EvUtils {


    /**
     * springboot 环境变量
     */
    private static Environment environment;

    /**
     * 静态存储 @Value 值
     */
    private static String testEv;


    /**
     * 由上层应用在启动时调用，注入环境参数
     */
    public static void init(Environment env, String testEvValue) {
        environment = env;
        testEv = testEvValue;
    }

    /**
     * 获取环境变量
     * 先获取springboot 启动环境变量
     * 如果是测试环境，或者某个本地测试环境 则从testEv中获取
     * testEv则由各个上层微服务中启动的yml环境中的配置项决定
     */
    public static String ev() {
        String ev = activeEv();
        if (TEST.getCode().equals(ev) || DEV.getCode().equals(ev) || TK.getCode().equals(ev)) {
            ev = testEv;
        }
        log.error("环境变量:{}", ev);
        return ev;
    }

    /**
     * 获取spring启动的环境变量
     * 即：application-{ev}.yml
     *
     * @return
     */
    public static String activeEv() {
        if (environment == null || testEv == null) {
            throw new ServiceException("EvUtils 未初始化！请确保在启动类中调用 EvUtils.init()");
        }
        return environment.getActiveProfiles()[0];
    }

}