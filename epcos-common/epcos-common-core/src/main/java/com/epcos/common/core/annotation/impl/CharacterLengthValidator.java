package com.epcos.common.core.annotation.impl;

import com.epcos.common.core.annotation.CharacterLength;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * <AUTHOR>
 */
public class CharacterLengthValidator implements ConstraintValidator<CharacterLength, Character> {
    private int length;
    private int min;
    private int max;

    @Override
    public void initialize(CharacterLength constraintAnnotation) {
        length = constraintAnnotation.length();
        min = constraintAnnotation.min();
        max = constraintAnnotation.max();
    }

    @Override
    public boolean isValid(Character value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }
        String str = value.toString();
        try {
            int i = Integer.parseInt(str);
            return str.length() <= length && i >= min && i <= max;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}
