package com.epcos.common.core.domain.review;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 */
@Data
@NoArgsConstructor
public class IncompleteJudgeVo implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("专家ID")
    private Long judgeId;
    @ApiModelProperty("专家姓名")
    private String judgeName;
    @ApiModelProperty("未评审的供应商")
    private String supplierCompanyName;

    public IncompleteJudgeVo(Long judgeId, String judgeName, String supplierCompanyName) {
        this.judgeId = judgeId;
        this.judgeName = judgeName;
        this.supplierCompanyName = supplierCompanyName;
    }
}
