package com.epcos.common.core.annotation;

import com.epcos.common.core.annotation.impl.NumStrContaiinsValidator;
import com.epcos.common.core.annotation.impl.NumIntContainsValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 数字是否包含
 *
 * <AUTHOR>
 */
@Constraint(validatedBy = {NumStrContaiinsValidator.class, NumIntContainsValidator.class})
@Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER})
@Retention(RUNTIME)
public @interface NumContains {

    /**
     * 字符数字类型
     *
     * @return
     */
    public String[] value() default {};

    /**
     * 数字类型
     *
     * @return
     */
    public int[] num() default {};

    String message() default "当前属性值不在指定数字内";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
