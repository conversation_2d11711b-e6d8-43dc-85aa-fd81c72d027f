package com.epcos.common.core.constant;

/**
 * @Title:投标人常量类
 * @Description:
 * @author:moyu
 * @version:1.0
 * @since 2022-03-26 9:26
 */
public class PurchaseConstants {

    /**
     * 医学装备部—YXZB、
     * 后勤保障部—HQBZ、
     * 基础设施建设管理办公室—JCSS、
     * 信息科—XXK、
     * 基建办—XXK、
     * 招标采购中心院内集中采购—ZBCG
     */
    public interface InnerCodePre {
        String YXZB = "YXZB";
        String HQBZ = "HQBZ";
        String JCSS = "JCSS";
        String XXK = "XXK";
        String JJB = "JJB";
        String ZBCG = "ZBCG";
    }


    /**
     * 环境
     * smart-  epc系统
     * jxzl-  江西省南昌市肿瘤医院
     * syth-  湖北省十堰市太和医院
     * whpr-  湖北省武汉市普仁医院
     * bjxk-  北京市胸科医院
     * hbxy-  湖北省襄阳市襄阳市医院
     * wzlg-  浙江省温州市龙港医院
     */
    public interface Environment {
        String SMART = "smart";
        String JXZL = "zl";
        String SYTH = "th";
        String WHPR = "pr";
        String BJXK = "xk";
        String WZLG = "lg";
        String XYZY = "xy";
        String WHWS = "ws";
        String FJNX = "fj";
    }

    /**
     * 参数状态[0-未推送, 1-等待被推送人提交, 2-待确认, 3-已确认，4-不同意]',
     */
    public interface Param {
        String WAIT_PUSH = "0";
        String WAIT_COMMIT = "1";
        String WAIT_CONFIRM = "2";
        String CONFIRMED = "3";
        String DISAGREE = "4";
    }

    /**
     * 审核状态
     * 0-平台审核不通过,
     * 1-平台审核通过,
     * 2-待审核,
     * 3-撤回
     */
    public interface BulletinAudit {
        String NOT_PASS = "0";
        String PASS = "1";
        String WAIT_VERIFY = "2";
        String BACK = "3";
    }

    /**
     * 公告是可见范围
     * 0-全网可见,
     * 1-仅被邀请供应商可见,
     * 2-不可见,
     */
    public interface BulletinShow {
        String ALL_VISIBLE = "0";
        String INVITE_SUPPLIER = "1";
        String INVISIBLE = "2";
    }

    /**
     * 具体含义 查看  BulletinTypeEnum
     */
    public interface PurchaseTemplate {
        String BIDDING_ANNOUNCEMENT = "bidding_announcement";
        String CLARIFY_ANNOUNCEMENT = "clarify_announcement";
        String CHANGE_ANNOUNCEMENT = "change_announcement";
        String WIN_CANDIDATE_PUBLICITY = "win_candidate_publicity";
        String ABANDON_ANNOUNCEMENT = "abandon_announcement";
        String WIN_RESULT_PUBLICITY = "win_result_publicity";
        String WIN_RESULT_NOTICE = "win_result_notice";
        String NOT_WIN_RESULT_NOTICE = "not_win_result_notice";
        String OTHER = "other";
    }


    /**
     * -1   开标未开始,
     * 0    开标开始，
     * 1    唱标,
     * 2    开标完成
     */
    public interface BidOpenState {
        int WAIT = -1;
        int START = 0;
        int SING = 1;
        int FINISH = 2;
    }


    /**
     * 0-采购人议价，
     * 1-评委议价,
     * 2-没有议价
     */
    public interface Bargain {
        String PURCHASER = "0";
        String Judge = "1";
        String NOT = "2";
    }

    /**
     * 通用状态
     * 此处只提供0和1的常量，具体含义需要看业务使用的地方
     */
    public interface Currency {
        int ZERO = 0;
        int ONE = 1;
        int TWO = 2;

        String LIKE = "%";

        String BUY_ITEM_CODE = "#dto.buyItemCode";
        String BUY_ITEM = "BUYITEM-";
    }

    /**
     * 公告关键字
     */
    public interface Bulletin {
        String KEYWORDS = "（盖章）";
    }

    /**
     * 质疑或答疑人的类型
     * <p>
     * 1-招标办
     * 2-投标人
     * 3-评委
     */
    public interface UserType {
        String PURCHASER = "1";
        String SUPPLIER = "2";
        String EXPERT = "3";
    }
}
