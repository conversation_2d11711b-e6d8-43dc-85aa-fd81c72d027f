package com.epcos.common.core.annotation.desensitization;

import com.epcos.common.core.annotation.Desensitization;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Objects;

@Slf4j
public class DesensitizationSerializer extends JsonSerializer<String> implements ContextualSerializer {

    // 脱敏处理
    private DesensitizationStrategy strategy;

    public DesensitizationSerializer() {
    }

    public DesensitizationSerializer(DesensitizationStrategy strategy) {
        this.strategy = strategy;
    }

    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        String desensitizationValue = strategy.desensitizationHandle(value);
        gen.writeString(desensitizationValue);
    }

    @Override
    public JsonSerializer<?> createContextual(SerializerProvider prov, BeanProperty property) throws JsonMappingException {
        JsonSerializer<?> desensitizationSerializer = null;
        if (null == property) {
            desensitizationSerializer = prov.findNullValueSerializer(property);
        } else {
            boolean stringType = Objects.equals(property.getType().getRawClass(), String.class);
            if (!stringType) {
                desensitizationSerializer = prov.findValueSerializer(property.getType(), property);
            } else {
                // JsonSerializer 设置自定义脱敏策略
                desensitizationSerializer = handleDesensitizationSerializer(property);
            }
        }
        return desensitizationSerializer;
    }

    // 脱敏设置
    private JsonSerializer<?> handleDesensitizationSerializer(BeanProperty beanProperty) {
        // 获取脱敏注解
        Desensitization desensitizationJsonSerializer = beanProperty.getAnnotation(Desensitization.class);
        if (desensitizationJsonSerializer == null) {
            // 当前pojo没有此类型注解，则从上下文中寻找
            desensitizationJsonSerializer = beanProperty.getContextAnnotation(Desensitization.class);
        }
        try {
            // 设置脱敏实例,添加自定义脱敏策略
            return new DesensitizationSerializer(desensitizationJsonSerializer.strategy().newInstance());
        } catch (InstantiationException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }
}
