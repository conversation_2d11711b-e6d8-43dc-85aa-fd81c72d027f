package com.epcos.common.core.constant;

public class FileTypeNameConstants {

    // 招标文件
    public static final String CLAIMS_FILE = "claims_file";
    // 招标文件pdf
    public static final String BIDDING_DOC_ENCLOSURE = "bidding_doc_enclosure";
    // 招标文件章节目录中的附件
    public static final String BIDDING_DOC_ENCLOSURE_APPENDIX = "bidding_doc_enclosure_appendix";
    //项目进行中的文件上传 钉钉审批单号 【202404240944000394994】 由页面定义
    public static final String BUY_ITEM_OTHER_FILE = "buy_item_other_file";


    // 供应商, 投标文件
    public static final String ANSWER_FILE = "answer_file";
    // 投标文件pdf
    public static final String TENDER_DOC_ENCLOSURE = "tender_doc_enclosure";


    // 投标文件章节目录中的附件
    public static final String TENDER_DOC_PDF_ENCLOSURE = "tender_doc_pdf_enclosure";
    // 报价表中附件
    public static final String TENDER_DOC_PDF_QUOTE_FORM = "tender_doc_pdf_quote_form";


    // 招标人，资格预审文件
    public static final String PREQUALIFICATION_DOC = "prequalification_doc";
    // 资格预审文件pdf
    public static final String PREQUALIFICATION_DOC_ENCLOSURE = "prequalification_doc_enclosure";
    // 资格预审文件章节目录中的附件
    public static final String PREQUALIFICATION_DOC_ENCLOSURE_APPENDIX = "prequalification_doc_enclosure_appendix";


    // 供应商，资格预审申请文件
    public static final String PREQUALIFICATION_DOC_RESPONSE = "prequalification_doc_response";
    // 资格预审申请文件pdf
    public static final String PREQUALIFICATION_DOC_RESPONSE_ENCLOSURE = "prequalification_doc_response_enclosure";
    // 资格预审申请文件章节目录中的附件
    public static final String PREQUALIFICATION_DOC_RESPONSE_ENCLOSURE_APPENDIX = "prequalification_doc_response_enclosure_appendix";


    /**
     * 投标委托函 归档接口中上传
     **/
    public static final String TENDER_ENTRUST = "tender_entrust";
    /**
     * 采购公告 归档接口中上传
     **/
    public static final String BUY_NOTICE = "buy_notice";

    /**
     * 采购公告附件 归档接口中上传
     **/
    public static final String BIDDING_PROCLAMATION_ENCLOSURE = "bidding_proclamation_enclosure";

    /**
     * 公告附件 归档接口中上传
     **/
    public static final String PROCLAMATION_ENCLOSURE = "proclamation_enclosure";

    /**
     * 资格预审公告（代招标公告） 归档接口中上传
     **/
    public static final String PREQUALIFICATION_PROCLAMATION = "prequalification_proclamation";

    /**
     * 资格预审通知书 归档接口中上传
     **/
    public static final String PREQUALIFICATION_NOTICE = "prequalification_notice";
    /**
     * 澄清公告 归档接口中上传
     **/
    public static final String CLARIFY_NOTICE = "clarify_notice";

    /**
     * 变更公告 归档接口中上传
     **/
    public static final String CHANGE_NOTICE = "change_notice";

    /**
     * 废标公告 归档接口中上传
     **/
    public static final String DISCARD_PROCLAMATION = "discard_proclamation";

    /**
     * 提出质疑质疑 归档接口中上传
     **/
    public static final String SUBMIT_RESULT_QUESTION = "submit_result_question";

    /**
     * 回复质疑 归档接口中上传
     */
    public static final String ANSWER_RESULT_QUESTION = "answer_result_question";

    /**
     * 中标候选人公示 归档接口中上传
     **/
    public static final String BID_WIN_PROCLAMATION = "bid_win_proclamation";

    /**
     * 中标结果公示 归档接口中上传
     **/
    public static final String WINNING_RESULTS_PROCLAMATION = "winning_results_proclamation";

    /**
     * 中标结果通知书 归档接口中上传
     **/
    public static final String WIN_LETTER = "win_letter";

    /**
     * 未中标结果通知书
     **/
    public static final String NOT_WIN_RESULT_NOTICE = "not_winning_result_notice";

    /**
     * 开标记录表 归档接口中上传
     **/
    public static final String BIDOPENING_SHEET = "bidopening_sheet";

    /**
     * 专家申明书 归档接口中上传
     **/
    public static final String EXPERT_STATEMENT = "expert_statement";

    /**
     * 评审报告 归档接口中上传
     **/
    public static final String BUY_REPORT = "buy_report";

    /**
     * 二次议价文件 归档接口中上传
     */
    public static final String SECOND_NEGOTIATE_PDF = "second_negotiate_pdf";

    /**
     * 2022-12-5 修改注释【经过接口调用传递类型确认此为附件】
     * 质疑附件提问 归档接口中上传
     */
    public static final String QUESTION_ASK = "question_ask";

    /**
     * 2022-12-5 修改注释【经过接口调用传递类型确认此为附件】
     * 质疑附件提问回答 归档接口中上传
     */
    public static final String QUESTION_ANSWER = "question_answer";
    /**
     * 投标MP4 非归档文件
     */
    public static final String TENDER_MP4 = "tender_mp4";

    /**
     * 资格预审申请 MP4 非归档文件
     */
    public static final String TENDER_APPLY_MP4 = "tender_apply_mp4";

    /**
     * 投标授权书 非归档文件
     */
    public static final String TENDER_AUTHORIZATION = "tender_authorization";

    /**
     * 投标报名信息表, excel
     */
    public static final String TENDER_REGISTRATION_INFORMATION_FORM = "tender_registration_information_form";

    /**
     * 专家抽取表, excel
     */
    public static final String EXPERT_EXTRACT_FORM = "expert_extract_form";

    /**
     * 成交合同
     */
    public static final String TRANSACTION_CONTRACT = "TRANSACTION_CONTRACT";

    /**
     * 供应商商品图片
     */
    public static final String SUPPLIER_GOODS_PIC = "SUPPLIER_GOODS_PIC";

    /**
     * 采购资料文件
     */
    public static final String PURCHASE_DATA_FILE = "PURCHASE_DATA_FILE";

    /**
     * 调研生成的文件
     */
    public static final String PURCHASE_RESEARCH_REPORT = "purchase_research_report";

    /**
     * 项目报表excel,只限此项目报表excel使用
     */
    public static final String PROJECT_REPORT_EXCEL = "project_report_excel";

    /**
     * 钉钉审批附件
     */
    public static final String DINGTALK_OA_ATT = "dingtalk_oa_att";

    /**
     * 钉钉苑长办公纪要
     */
    public static final String DING_TALK_MEETING_ATT = "ding_talk_meeting_att";


    /**
     * 创建项目时的附件
     */
    public static final String CREATE_BUY_ITEM = "create_buy_item";

    /**
     * 创建项目时的附件
     */
    public static final String FILE_ZIP = "fileZip";

    /**
     * 创建项目时的附件
     */
    public static final String LG_SUB_INFO_FILE = "lgSubInfoFile";

}
