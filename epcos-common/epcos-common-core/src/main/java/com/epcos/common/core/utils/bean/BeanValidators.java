package com.epcos.common.core.utils.bean;

import com.epcos.common.core.exception.ServiceException;
import org.springframework.util.CollectionUtils;

import javax.validation.*;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * bean对象属性验证
 *
 * <AUTHOR>
 */
public class BeanValidators {
    public static void validateWithException(Validator validator, Object object, Class<?>... groups) throws ConstraintViolationException {
        Set<ConstraintViolation<Object>> constraintViolations = validator.validate(object, groups);
        if (!constraintViolations.isEmpty()) {
            throw new ConstraintViolationException(constraintViolations);
        }
    }

    /**
     * 校验是否符合预期值
     */
    public static <T> Map<String, String> validate(T t, Class... groups) {
        final ValidatorFactory validatorFactory = Validation.buildDefaultValidatorFactory();
        final Map<String, String> res = validatorFactory.getValidator().
                validate(t, groups)
                .stream()
                .collect(Collectors.toMap(
                        i -> i.getPropertyPath().toString(),
                        ConstraintViolation::getMessage));
        validatorFactory.close();
        return res;
    }

    public static <T> void validateEx(T t, Class... groups) {
        Map<String, String> res = validate(t, groups);
        if (!CollectionUtils.isEmpty(res)) {
            throw new ServiceException(res.toString());
        }
    }

    public static <T, R> R verifyField(R obj,
                                       T fieldValue,
                                       Predicate<T> condition,
                                       String errorMsg) {
        if (Objects.isNull(obj)) {
            throw new ServiceException("传入对象" + obj + "不能为 null");
        }
        if (Objects.nonNull(fieldValue) && condition.test(fieldValue)) {
            throw new ServiceException(errorMsg);
        }
        return obj;
    }

}
