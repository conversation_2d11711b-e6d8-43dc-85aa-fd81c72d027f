package com.epcos.common.core.annotation.desensitization.strategy;

import com.epcos.common.core.annotation.desensitization.DesensitizationStrategy;
import com.epcos.common.core.utils.StringUtils;

/**
 * 增加**
 */
public class AddStrSensitization implements DesensitizationStrategy {

    @Override
    public String desensitizationHandle(String oldValue) {
        if (StringUtils.isBlank(oldValue)) {
            return oldValue;
        }
        int start = oldValue.length() / 3;
        String s = oldValue.substring(0, start) + "**";
        int l = oldValue.length() - start;
        String m = oldValue.substring(start, l);
        String e = "**" + oldValue.substring(l);
        return s + m + e;
    }


}
