package com.epcos.common.core.enums;

import com.epcos.common.core.exception.ServiceException;
import lombok.Getter;

/**
 * 不同客户区分
 */
@Getter
public enum FileTypeNameEnum {

    CLAIMS_FILE("claims_file", "招标文件"),
    BIDDING_DOC_ENCLOSURE("bidding_doc_enclosure", "招标文件pdf"),
    BIDDING_DOC_ENCLOSURE_APPENDIX("bidding_doc_enclosure_appendix", "招标文件章节目录中的附件"),
    BUY_ITEM_OTHER_FILE("buy_item_other_file", "项目进行中的文件上传"),
    ANSWER_FILE("answer_file", "供应商,投标文件"),

    TENDER_DOC_ENCLOSURE("tender_doc_enclosure", "投标文件pdf"),
    TENDER_DOC_PDF_ENCLOSURE("tender_doc_pdf_enclosure", "投标文件章节目录中的附件"),
    TENDER_DOC_PDF_QUOTE_FORM("tender_doc_pdf_quote_form", "报价表中附件"),
    PREQUALIFICATION_DOC("prequalification_doc", "招标人，资格预审文件"),
    PREQUALIFICATION_DOC_ENCLOSURE("prequalification_doc_enclosure", "资格预审文件pdf"),

    PREQUALIFICATION_DOC_ENCLOSURE_APPENDIX("prequalification_doc_enclosure_appendix", "资格预审文件章节目录中的附件"),
    PREQUALIFICATION_DOC_RESPONSE("prequalification_doc_response", "供应商，资格预审申请文件"),
    PREQUALIFICATION_DOC_RESPONSE_ENCLOSURE("prequalification_doc_response_enclosure", "资格预审申请文件pdf"),
    PREQUALIFICATION_DOC_RESPONSE_ENCLOSURE_APPENDIX("prequalification_doc_response_enclosure_appendix", "资格预审申请文件章节目录中的附件"),
    TENDER_ENTRUST("tender_entrust", "投标委托函 归档接口中上传"),

    BUY_NOTICE("buy_notice", "采购公告 归档接口中上传"),
    BIDDING_PROCLAMATION_ENCLOSURE("bidding_proclamation_enclosure", "采购公告附件 归档接口中上传"),
    PROCLAMATION_ENCLOSURE("proclamation_enclosure", "公告附件 归档接口中上传"),
    PREQUALIFICATION_PROCLAMATION("prequalification_proclamation", "资格预审公告（代招标公告） 归档接口中上传"),
    PREQUALIFICATION_NOTICE("prequalification_notice", "资格预审通知书 归档接口中上传"),

    CLARIFY_NOTICE("clarify_notice", "澄清公告 归档接口中上传"),
    CHANGE_NOTICE("change_notice", "变更公告 归档接口中上传"),
    DISCARD_PROCLAMATION("discard_proclamation", "废标公告 归档接口中上传"),
    SUBMIT_RESULT_QUESTION("submit_result_question", "提出质疑质疑 归档接口中上传"),
    ANSWER_RESULT_QUESTION("answer_result_question", "回复质疑 归档接口中上传"),

    BID_WIN_PROCLAMATION("bid_win_proclamation", "中标候选人公示 归档接口中上传"),
    WINNING_RESULTS_PROCLAMATION("winning_results_proclamation", "中标结果公示 归档接口中上传"),
    WIN_LETTER("win_letter", "中标结果通知书 归档接口中上传"),
    NOT_WIN_RESULT_NOTICE("not_winning_result_notice", "未中标结果通知书"),
    BIDOPENING_SHEET("bidopening_sheet", "开标记录表 归档接口中上传"),

    EXPERT_STATEMENT("expert_statement", "专家申明书 归档接口中上传"),
    BUY_REPORT("buy_report", "评审报告 归档接口中上传"),
    SECOND_NEGOTIATE_PDF("second_negotiate_pdf", "二次议价文件 归档接口中上传"),
    QUESTION_ASK("question_ask", "质疑附件提问 归档接口中上传"),
    QUESTION_ANSWER("question_answer", "质疑附件提问回答 归档接口中上传"),

    TENDER_MP4("tender_mp4", "投标MP4 非归档文件"),
    TENDER_APPLY_MP4("tender_apply_mp4", "资格预审申请 MP4 非归档文件"),
    TENDER_AUTHORIZATION("tender_authorization", "投标授权书 非归档文件"),
    TENDER_REGISTRATION_INFORMATION_FORM("tender_registration_information_form", "投标报名信息表, excel"),
    EXPERT_EXTRACT_FORM("expert_extract_form", "专家抽取表, excel"),

    TRANSACTION_CONTRACT("TRANSACTION_CONTRACT", "成交合同"),
    SUPPLIER_GOODS_PIC("SUPPLIER_GOODS_PIC", "供应商商品图片"),
    PURCHASE_DATA_FILE("PURCHASE_DATA_FILE", "采购资料文件"),
    PURCHASE_RESEARCH_REPORT("purchase_research_report", "调研生成的文件"),
    PROJECT_REPORT_EXCEL("project_report_excel", "项目报表excel,只限此项目报表excel使用"),

    DINGTALK_OA_ATT("dingtalk_oa_att", "钉钉审批附件"),
    DING_TALK_MEETING_ATT("ding_talk_meeting_att", "钉钉苑长办公纪要"),
    CREATE_BUY_ITEM("create_buy_item", "创建项目时的附件"),
    FILE_ZIP("fileZip", "项目归档文件"),

    SUPPLIER_ANSWER_UP_ATT("supplier_answer_up_att", "投标供应商投标文件附件"),

    PURCHASE_ANSWER_UP_ATT("PURCHASE_answer_up_att", "投标供应商投标文件附件"),

    ;

    final String code;
    final String name;

    @Override
    public String toString() {
        return name + "（" + code + "）";
    }

    /**
     * 根据 code 返回 name
     */
    public static String ofCode(String code) {
        for (FileTypeNameEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item.getName();
            }
        }
        throw new ServiceException(code + "枚举不存在");
    }

    FileTypeNameEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
