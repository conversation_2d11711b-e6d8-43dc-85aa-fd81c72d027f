package com.epcos.common.core.annotation;

import com.epcos.common.core.annotation.impl.GbkLengthValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = GbkLengthValidator.class)
public @interface GbkLength {

    String message() default "GBK-编码后长度不符合要求";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    int maxLength();

}
