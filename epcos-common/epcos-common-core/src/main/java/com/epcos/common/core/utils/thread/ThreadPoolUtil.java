package com.epcos.common.core.utils.thread;

import io.netty.util.concurrent.DefaultThreadFactory;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 线程池工具类
 */
public class ThreadPoolUtil {

    private ThreadPoolUtil() {
    }

    public static ExecutorService fixedThreadPool = Instance.FIXED_THREAD_POOL.fixedThreadPool;

    private enum Instance {
        FIXED_THREAD_POOL;
        private final ExecutorService fixedThreadPool;

        Instance() {
            fixedThreadPool = new ThreadPoolExecutor(10, 20, 30,
                    TimeUnit.MINUTES,
                    new ArrayBlockingQueue<>(20),
                    new DefaultThreadFactory("my_自定义线程池"),
                    new ThreadPoolExecutor.CallerRunsPolicy()
            );
        }
    }


}
