package com.epcos.common.core.domain;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class ProcurementItemClassification implements Serializable {

    private String name;
    private String code;
    private List<ProcurementItemClassification> children = new ArrayList<>();

    public ProcurementItemClassification(String name, String code) {
        this.name = name;
        this.code = code;
    }
}
