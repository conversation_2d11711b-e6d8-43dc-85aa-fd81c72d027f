package com.epcos.common.core.enums;

/**
 * 不同客户区分
 */
public enum ClientEnum {

    DEV("dev", "本地开发"),
    TEST("test", "测试环境"),
    TK("tk", "测试电脑"),
    ZL("zl", "江西省肿瘤医院"),
    TH("th", "十堰市太和医院"),
    PR("pr", "湖北正泓源医疗科技有限公司电子采购平台"),
    XK("xk", "首都医科大学附属北京胸科医院"),
    XY("xy", "襄阳市中医医院电子采购平台"),
    WS("ws", "湖北省武汉市武商集团版本"),
    LG("lg", "龙港城发集团数字化采购管理平台"),
    SMART("smart", "易建采采购管理平台"),
    FJ("fj", "福建农信电子采购管理平台"),
    ;

    String code;
    String name;

    @Override
    public String toString() {
        return name + "（" + code + "）";
    }

    /**
     * 根据 code 返回 this
     */
    public static ClientEnum ofCode(String code) {
        for (ClientEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

    ClientEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
