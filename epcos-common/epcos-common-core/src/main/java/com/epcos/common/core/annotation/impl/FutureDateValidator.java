package com.epcos.common.core.annotation.impl;

import com.epcos.common.core.annotation.FutureDate;

import javax.validation.ConstraintValidatorContext;
import java.time.LocalDate;


public class FutureDateValidator implements javax.validation.ConstraintValidator<FutureDate, LocalDate> {
    @Override
    public boolean isValid(LocalDate value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }
        return value.isAfter(LocalDate.now());
    }

}
