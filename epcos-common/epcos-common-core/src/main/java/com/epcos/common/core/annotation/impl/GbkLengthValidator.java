package com.epcos.common.core.annotation.impl;

import com.epcos.common.core.annotation.GbkLength;
import lombok.extern.slf4j.Slf4j;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

@Slf4j
public class GbkLengthValidator implements ConstraintValidator<GbkLength, String> {

    private int maxLength;

    @Override
    public void initialize(GbkLength constraintAnnotation) {
        maxLength = constraintAnnotation.maxLength();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }
        try {
            byte[] gbks = value.getBytes("GBK");
            return gbks.length <= maxLength;
        } catch (Exception e) {
            log.error("GbkLengthValidator error", e);
            return false;
        }
    }
}
