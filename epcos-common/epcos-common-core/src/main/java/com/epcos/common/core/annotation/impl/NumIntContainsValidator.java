package com.epcos.common.core.annotation.impl;

import com.epcos.common.core.annotation.NumContains;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.Objects;

public class    NumIntContainsValidator implements ConstraintValidator<NumContains, Integer> {

    private int[] ints;

    @Override
    public void initialize(NumContains contains) {
        ints = contains.num();
    }

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        if (Objects.nonNull(value) && Objects.nonNull(ints)) {
            return Arrays.binarySearch(ints, value) >= 0;
        }
        return true;
    }

}
