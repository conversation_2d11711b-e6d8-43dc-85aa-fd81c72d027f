package com.epcos.common.core.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/18 16:08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AttachmentDto implements Serializable {

    @ApiModelProperty(value = "附件名称")
    private String name;

    @ApiModelProperty(value = "附件key")
    private String url;
}
