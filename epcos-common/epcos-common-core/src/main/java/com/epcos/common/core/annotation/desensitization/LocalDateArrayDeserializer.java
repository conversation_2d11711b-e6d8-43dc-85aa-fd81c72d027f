package com.epcos.common.core.annotation.desensitization;

import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class LocalDateArrayDeserializer extends JsonDeserializer<LocalDate[]> {
    @Override
    public LocalDate[] deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JacksonException {
        String[] dates = p.readValueAs(String[].class);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDate[] localDates = new LocalDate[dates.length];
        for (int i = 0; i < dates.length; i++) {
            localDates[i] = LocalDate.parse(dates[i], formatter);
        }
        return localDates;
    }
}
