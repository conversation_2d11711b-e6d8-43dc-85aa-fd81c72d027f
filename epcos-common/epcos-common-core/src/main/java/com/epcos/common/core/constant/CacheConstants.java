package com.epcos.common.core.constant;

/**
 * 缓存的key 常量
 *
 * <AUTHOR>
 */
public class CacheConstants {
    /**
     * 缓存有效期，默认720（分钟）
     */
    public final static long EXPIRATION = 720;

    /**
     * 缓存刷新时间，默认120（分钟）
     */
    public final static long REFRESH_TIME = 120;

    /**
     * 权限缓存前缀
     */
    public final static String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * redis 锁前缀
     */
    public final static String REDIS_LOCK = "redis_lock:";
    /**
     * websocket 用户
     */
    public final static String WEBSOCKET_ONLINE_USER = "websocket:online_user:";
    /**
     * websocket 用户订阅
     */
    public final static String WEBSOCKET_USER_SUBSCRIBE = "websocket:user_subscribe:";
    /**
     * websocket 用户累积消息
     */
    public final static String WEBSOCKET_USER_MSG = "websocket:user_msg:";
    /**
     * websocket 用户消息列表
     */
    public final static String WEBSOCKET_USER_MSG_LIST = "websocket:user_msg:list:";
    /**
     * websocket 用户未读消息
     */
    public final static String WEBSOCKET_USER_MSG_UNREAD = "websocket:user_msg:unread:";
    /**
     * websocket 消息
     */
    public final static String WEBSOCKET_USER_MSG_HASH = "websocket:user_msg:hash:";
    /**
     * websocket  维护一个 msgId：userId 反向关系，后期在监听 hash 过期时方便删除 zset 与 set
     */
    public final static String WEBSOCKET_USER_MSG_REVERSE = "websocket:user_msg:reverse:";
    /**
     * 消息缓存时长
     */
    public final static int expireHours = 1;
    /**
     * 自动开标 key
     */
    public final static String AUTO_BID_OPEN = "auto_bid_open:";
    /**
     * 项目结束通知采购人评价供应商 key
     */
    public final static String COMMENT_SUPPLIER = "sms:supplier:comment";
    /**
     * 自动开标 key lock
     */
    public final static String AUTO_BID_OPEN_LOCK = "auto_bid_open:lock:";
    /**
     * 自动开标
     */
    public final static String AUTO_BID_OPEN_HASH = "auto_bid_open:hash:";

}
