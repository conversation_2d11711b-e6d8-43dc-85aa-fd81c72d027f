package com.epcos.common.core.domain.review;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 */

@Data
public class ReviewDetailsVo implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("评审模块编号uuid")
    @NotBlank(message = "评审模块编号不允许为空")
    private String uuid;

    @ApiModelProperty("是否合格 1合格 0不合格")
    private Integer qualified;

    @ApiModelProperty("分数")
    private Double score;

    @ApiModelProperty("评审模块")
    @NotBlank(message = "评审模块")
    private String reviewItem;

    @ApiModelProperty("评审规则")
    private String reviewCriteria;


}
