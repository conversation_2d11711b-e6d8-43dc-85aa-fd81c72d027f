package com.epcos.common.core.utils.collection;

import org.springframework.util.CollectionUtils;

import java.util.Collection;

public class CollUtil {

    public static boolean isEquals(Collection<?> a, Collection<?> b) {
        boolean emptyP = CollectionUtils.isEmpty(a);
        boolean emptyS = CollectionUtils.isEmpty(b);
        if (emptyP && emptyS) {
            return true;
        }
        if (!emptyP && !emptyS) {
            if (a.size() != b.size()) {
                return false;
            }
            return a.hashCode() == b.hashCode();
        }
        return false;
    }

}
