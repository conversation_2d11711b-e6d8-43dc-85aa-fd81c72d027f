package com.epcos.common.core.web.domain.user;

import com.epcos.common.core.annotation.Excel;
import com.epcos.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
 * 部门表 sys_dept
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DeptJxzl extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 部门ID
     */
    @Excel(name = "DEPT_CODE")
    private Long deptId;

    /**
     * 部门名称
     */
    @Excel(name = "DEPT_NAME")
    private String deptName;

    /**
     * 部门状态:0正常,1停用
     */
    @Excel(name = "VALID_STATE")
    private String status;


}
