package com.epcos.common.core.utils.purchase;

import com.epcos.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Objects;
import java.util.zip.*;

/**
 * @Title:
 * @Description:TODO
 * @author:moyu
 * @version:1.0
 * @since 2022-03-28 10:22
 */
@Slf4j
public class GzipUtils {

    private GzipUtils() {
    }

    /**
     * @param str：正常的字符串
     * @return 压缩字符串 类型为：  ³)°K,NIc i£_`Çe#  c¦%ÂXHòjyIÅÖ`
     * @throws IOException
     */
    public static String compress(String str) throws IOException {
        if (str == null || str.length() == 0) {
            return str;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        GZIPOutputStream gzip = new GZIPOutputStream(out);
        gzip.write(str.getBytes());
        gzip.close();
        return out.toString("ISO-8859-1");
    }


    /**
     * @param str：类型为： ³)°K,NIc i£_`Çe#  c¦%ÂXHòjyIÅÖ`
     * @return 解压字符串  生成正常字符串。
     * @throws IOException
     */
    public static String unCompress(String str) throws IOException {
        if (str == null || str.length() == 0) {
            return str;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ByteArrayInputStream in = new ByteArrayInputStream(str.getBytes(StandardCharsets.ISO_8859_1));
        GZIPInputStream gunzip = new GZIPInputStream(in);
        byte[] buffer = new byte[256];
        int n;
        while ((n = gunzip.read(buffer)) >= 0) {
            out.write(buffer, 0, n);
        }
        String s = out.toString("UTF-8");
        in.close();
        out.close();
        return s;
    }

    /**
     * @param jsUriStr :字符串类型为：%1F%C2%8B%08%00%00%00%00%00%00%03%C2%B3)%C2%B0K%2CNI%03c%20i%C2%A3_%60%C3%87e%03%11%23%C2%82%0Dc%C2%A6%25%C3%82XH%C3%B2jyI%C3%85%05%C3%96%60%1E%00%17%C2%8E%3Dvf%00%00%00
     * @return 生成正常字符串
     * @throws IOException
     */
    public static String unCompressURI(String jsUriStr) throws IOException {
        String decodeJSUri = URLDecoder.decode(jsUriStr, "UTF-8");
        return unCompress(decodeJSUri);
    }

    /**
     * @param strData :字符串类型为： 正常字符串
     * @return 生成字符串类型为：%1F%C2%8B%08%00%00%00%00%00%00%03%C2%B3)%C2%B0K%2CNI%03c%20i%C2%A3_%60%C3%87e%03%11%23%C2%82%0Dc%C2%A6%25%C3%82XH%C3%B2jyI%C3%85%05%C3%96%60%1E%00%17%C2%8E%3Dvf%00%00%00
     * @throws IOException
     */
    public static String compress2Uri(String strData) throws IOException {
        String encodeGzip = compress(strData);
        return URLEncoder.encode(encodeGzip, "UTF-8");
    }

    /**
     * gzip 并 base64
     */
    public static String zip(String str) {
        final ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
        final GZIPOutputStream gzipOut;
        try {
            gzipOut = new GZIPOutputStream(byteOut);
            gzipOut.write(str.getBytes(StandardCharsets.UTF_8));
            gzipOut.close();
            byteOut.close();
            return Base64.getEncoder().encodeToString(byteOut.toByteArray());
        } catch (IOException e) {
            log.error("gzip 并 base64 异常，e:{}, str:{}", e, str);
            throw new RuntimeException("gzip 并 base64 异常");
        }
    }


    /**
     * base64 解码并 unzip
     */
    public static String unzip(String str) {
        final ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
        final GZIPInputStream gzipIn;
        try {
            final byte[] decode = Base64.getDecoder().decode(str);
            final ByteArrayInputStream byteIn = new ByteArrayInputStream(decode);
            gzipIn = new GZIPInputStream(byteIn);
            int n;
            final byte[] buff = new byte[1024];
            while ((n = gzipIn.read(buff)) != -1) {
                byteOut.write(buff, 0, n);
            }
            byteIn.close();
            gzipIn.close();
            byteOut.close();
            return byteOut.toString(StandardCharsets.UTF_8.name());
        } catch (IOException e) {
            log.error("base64 解码并 unzip 异常，e:{}, str:{}", e, str);
            throw new RuntimeException("base64 解码并 unzip 异常");
        }
    }


    /**
     * deflater 压缩
     */
    public static String deflate(String str) {
        if (Objects.isNull(str)) {
            return str;
        }
        //使用指定的压缩级别创建一个新的压缩器。
        Deflater deflater = new Deflater(Deflater.BEST_COMPRESSION);
        //设置压缩输入数据。
        deflater.setInput(str.getBytes(StandardCharsets.UTF_8));
        //当被调用时，表示压缩应该以输入缓冲区的当前内容结束。
        deflater.finish();
        final byte[] bytes = new byte[512];
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream(512);
        while (!deflater.finished()) {
            //压缩输入数据并用压缩数据填充指定的缓冲区。
            int length = deflater.deflate(bytes);
            outputStream.write(bytes, 0, length);
        }
        //关闭压缩器并丢弃任何未处理的输入。
        deflater.end();
        return Base64.getEncoder().encodeToString(outputStream.toByteArray());
    }

    /**
     * inflater 解压缩
     */
    public static String unDeflate(String str) {
        if (StringUtils.isBlank(str)) {
            return str;
        }
        final byte[] decodeBase64 = Base64.getDecoder().decode(str);
        final Inflater inflater = new Inflater();
        inflater.setInput(decodeBase64);
        byte[] buff = new byte[512];
        try (final ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            while (!inflater.finished()) {
                final int inflate = inflater.inflate(buff);
                out.write(buff, 0, inflate);
            }
            return out.toString(StandardCharsets.UTF_8.name());
        } catch (IOException | DataFormatException e) {
            log.error("inflater 解压缩异常，e:{}, str:{}", e, str);
            throw new RuntimeException("inflater 解压缩异常");
        }
    }


}
