package com.epcos.common.core.annotation;

import com.epcos.common.core.annotation.impl.FutureDateValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Constraint(validatedBy = FutureDateValidator.class)
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface FutureDate {

    String message() default "日期必须大于当前日期";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
