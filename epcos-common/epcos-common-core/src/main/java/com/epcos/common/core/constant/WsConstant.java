package com.epcos.common.core.constant;

import org.springframework.util.Assert;

/**
 * ws常量
 */
public class WsConstant {


    /**
     * redis发布地址
     */
    public static interface RedisPublish {
        // 所有用户
        public final static String TO_ALL = "websocket.msg.all";
        // 单个
        public final static String TO_ONE = "websocket.msg.one";
    }

    /**
     * 服务代理地址
     */
    public static interface Broker {
        // 所有用户的订阅地址
        public final static String ALL = "/all";
        // 某个用户的评阅地址
        public final static String QUEUE = "/queue";

        // 用户上线后会必须订阅 /chat/{userId} 地址
        public static String getUserQueue(Long userId) {
            Assert.notNull(userId, "用户id必填");
            return QUEUE + "/" + userId;
        }
    }


}
