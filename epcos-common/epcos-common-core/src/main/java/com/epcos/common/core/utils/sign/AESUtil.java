package com.epcos.common.core.utils.sign;

import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.crypto.generators.SCrypt;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

import static java.nio.charset.StandardCharsets.UTF_8;

@Slf4j
public class AESUtil {

    public static final String AES = "AES";
    // 加密算法
    public static final String AES_ECB = "AES/ECB/PKCS5Padding";
    //  AES 算法的 CBC 模式和 PKCS5Padding 填充方式
    public static final String AES_CBC = "AES/CBC/PKCS5Padding";

    private static final SecureRandom SECURE_RANDOM = new SecureRandom();
    private static final char[] LETTERS = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ".toCharArray();

    /**
     * 解密文件流
     * https://blog.csdn.net/qq_41434431/article/details/136659441
     */
    public static void decryptFile(Path sourceEncryptedPath, Path targetDecryptedPath, String password) {
        try {
            // scrypt参数
            byte[] salt = "salt".getBytes(StandardCharsets.UTF_8);
            int costParameter = 16384;
            int blockSize = 8;
            int parallelizationParameter = 1;
            int keyLength = 32;

            // 使用scrypt生成密钥
            byte[] keyBytes = SCrypt.generate(password.getBytes(StandardCharsets.UTF_8),
                    salt, costParameter, blockSize, parallelizationParameter, keyLength);
            SecretKeySpec secretKeySpec = new SecretKeySpec(keyBytes, AES);

            // 初始化向量
            byte[] iv = new byte[16];

            // 解密过程
            Cipher cipher = Cipher.getInstance(AES_CBC);
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, new IvParameterSpec(iv));

            try (InputStream fileInputStream = Files.newInputStream(sourceEncryptedPath);
                 OutputStream fileOutputStream = Files.newOutputStream(targetDecryptedPath)
            ) {
                byte[] buffer = new byte[1024 * 10];
                int bytesRead;
                while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                    fileOutputStream.write(cipher.update(buffer, 0, bytesRead));
                }
                fileOutputStream.write(cipher.doFinal());
            }
            Files.deleteIfExists(sourceEncryptedPath);
        } catch (Exception e) {
            log.error("解密文件流，异常 sourceEncryptedPath={}, targetDecryptedPath={}, password={}", sourceEncryptedPath, targetDecryptedPath, password, e);
            throw new RuntimeException("解密文件流，异常", e);
        }
    }

    /**
     * 随机生成 AES 密钥
     */
    public static String getAESKey(int len) {
        StringBuilder res = new StringBuilder(len);
        for (int i = 0; i < len; i++) {
            boolean isChar = SECURE_RANDOM.nextBoolean();
            if (isChar) {
                // 生成字母
                int index = SECURE_RANDOM.nextInt(LETTERS.length);
                res.append(LETTERS[index]);
            } else {
                // 生成数字
                res.append(SECURE_RANDOM.nextInt(10));
            }
        }
        return res.toString();
    }

    /**
     * 加密
     *
     * @param data 数据
     * @param key  密码
     */
    private static byte[] encrypt(byte[] data, byte[] key) {
        SecretKeySpec secretKeySpec = new SecretKeySpec(key, AES);
        byte[] encoded = secretKeySpec.getEncoded();
        SecretKeySpec secKeyEncoded = new SecretKeySpec(encoded, AES);
        try {
            Cipher cipher = Cipher.getInstance(AES_ECB);
            cipher.init(Cipher.ENCRYPT_MODE, secKeyEncoded);
            return cipher.doFinal(data);
        } catch (NoSuchAlgorithmException | NoSuchPaddingException | IllegalBlockSizeException | BadPaddingException |
                 InvalidKeyException e) {
            throw new RuntimeException("encrypt fail!", e);
        }
    }

    /**
     * 解密
     *
     * @param data 数据
     * @param key  密码
     */
    public static byte[] decrypt(byte[] data, byte[] key) {
        SecretKeySpec keySpec = new SecretKeySpec(key, AES);
        byte[] encoded = keySpec.getEncoded();
        SecretKeySpec secKeyEncoded = new SecretKeySpec(encoded, AES);
        try {
            Cipher cipher = Cipher.getInstance(AES_ECB);
            cipher.init(Cipher.DECRYPT_MODE, secKeyEncoded);
            return cipher.doFinal(data);
        } catch (NoSuchAlgorithmException | NoSuchPaddingException | IllegalBlockSizeException | BadPaddingException |
                 InvalidKeyException e) {
            throw new RuntimeException("decrypt fail!", e);
        }
    }

    /**
     * 加密并base64编码
     */
    public static String encryptToBase64(String data, String key) {
        byte[] decrypt = encrypt(data.getBytes(UTF_8), key.getBytes(UTF_8));
        byte[] encode = Base64.getEncoder().encode(decrypt);
        return new String(encode, UTF_8);
    }

    /**
     * base64解码并解密
     */
    public static String decryptFromBase64(String data, String key) {
        byte[] decode = Base64.getDecoder().decode(data);
        byte[] decrypt = decrypt(decode, key.getBytes(UTF_8));
        return new String(decrypt, UTF_8);
    }


}
