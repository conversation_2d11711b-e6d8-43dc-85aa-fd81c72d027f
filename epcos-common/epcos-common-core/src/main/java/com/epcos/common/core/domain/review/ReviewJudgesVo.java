package com.epcos.common.core.domain.review;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 */
@Data
public class ReviewJudgesVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("专家ID")
    private Long judgeId;

    @ApiModelProperty("专家姓名")
    private String judgeName;

    @ApiModelProperty("采购代表：1-代表,  0-非代表")
    private Integer judgeDelegate;

    @ApiModelProperty("平均分")
    private Double scoreResult;

    @ApiModelProperty("合格结果：1-完成,  0-未完成")
    private Boolean qualifiedResult;

    @ApiModelProperty("评审详情")
    private List<ReviewDetailsVo> reviewDetailsVoList;

}
