package com.epcos.common.core.constant;

/**
 * 用户常量信息
 *
 * <AUTHOR>
 */
public class UserConstants {

    /**
     * 平台内系统用户的唯一标志
     */
    public static final String SYS_USER = "SYS_USER";

    /**
     * 正常状态
     */
    public static final String NORMAL = "0";

    /**
     * 异常状态
     */
    public static final String EXCEPTION = "1";

    /**
     * 用户封禁状态
     */
    public static final String USER_DISABLE = "1";

    /**
     * 角色封禁状态
     */
    public static final String ROLE_DISABLE = "1";

    /**
     * 部门正常状态
     */
    public static final String DEPT_NORMAL = "0";

    /**
     * 类别正常状态
     */
    public static final String TYPE_NORMAL = "0";


    /**
     * 部门停用状态
     */
    public static final String DEPT_DISABLE = "1";

    /**
     * 组织正常状态
     */
    public static final String ORG_NORMAL = "0";

    /**
     * 组织停用状态
     */
    public static final String ORG_DISABLE = "1";

    /**
     * 字典正常状态
     */
    public static final String DICT_NORMAL = "0";

    /**
     * 是否为系统默认（是）
     */
    public static final String YES = "Y";

    /**
     * 是否菜单外链（是）
     */
    public static final String YES_FRAME = "0";

    /**
     * 是否菜单外链（否）
     */
    public static final String NO_FRAME = "1";

    /**
     * 菜单类型（目录）
     */
    public static final String TYPE_DIR = "M";

    /**
     * 菜单类型（菜单）
     */
    public static final String TYPE_MENU = "C";

    /**
     * 菜单类型（按钮）
     */
    public static final String TYPE_BUTTON = "F";

    /**
     * Layout组件标识
     */
    public final static String LAYOUT = "Layout";

    /**
     * ParentView组件标识
     */
    public final static String PARENT_VIEW = "ParentView";

    /**
     * InnerLink组件标识
     */
    public final static String INNER_LINK = "InnerLink";

    /**
     * 校验返回结果码
     */
    public final static String UNIQUE = "0";

    public final static String NOT_UNIQUE = "1";

    /**
     * 用户名长度限制
     */
    public static final int USERNAME_MIN_LENGTH = 2;

    public static final int USERNAME_MAX_LENGTH = 20;

    /**
     * 密码长度限制
     */
    public static final int PASSWORD_MIN_LENGTH = 5;

    public static final int PASSWORD_MAX_LENGTH = 20;

    /**
     * 密码8-20位,必须包含数字、字母、特殊字符
     */
    public static final String PASSWORD_REGEX = "^(?=.*\\d)(?=.*[a-zA-Z])(?=.*[!@#$%^&*()\\-_=+\\[\\]{}|;:',.<>?/~`\\\\]).{8,20}$";
    public static final String PASSWORD_TIP = "密码8-20位，必须包含数字、字母、英文特殊字符：!@#$%^&*()-_=+[]{}|;:'.<>?/~`\\";

    /**
     * 用户类型
     * 1-采购人，2-供应商，3-专家
     */
    public static final String[] USER_TYPE = {"0", "1", "2"};

    /**
     * 系统配置常量, 调用   /system/config/configKey/ 参数常量
     */
    /**
     * 专家
     */
    public static final String EXPERT_ROLE_ID_PARAM = "sys.expert.roleId";

    /**
     * 供应商
     */
    public static final String SUPPLIER_ROLE_ID_PARAM = "sys.supplier.roleId";

    /**
     * 采购人
     */
    public static final String PURCHASER_ROLE_ID_PARAM = "sys.purchaser.roleId";

    // 后台管理员 = 招标办企业及个人信息 id
    public static final String SYS_ADMIN_USER_ID = "sys.admin.userId";

    /**
     * 用户管理-账号初始密码
     */
    public static final String USER_INIT_PASSWORD = "sys.user.initPassword";

    /**
     * 是否开启电子签章
     */
    public static final String signature = "sys.signature.off";
    /**
     * 印章横向文本
     */
    public static final String sealHorizontalContent = "seal.horizontal.content";
    /**
     * 印章下旋数字
     */
    public static final String sealUnderScript = "seal.under.script";
    /**
     * 委托项目完整项目数量
     */
    public static final String agentFileNumber = "agent.file.number";

    /**
     * 用户账户 启用：0，停用：1
     */
    public static final String USER_ACCOUNT_ENABLE = "0";
    public static final String USER_ACCOUNT_DISABLE = "1";


    /**
     * 医院名字
     */
    public static final String SYSTEM_OWNER = "system.owner";
    /**
     * 所处城市
     */
    public static final String SYSTEM_CITY = "system.city";

    /**
     * 是否允许响应: 0-不允许，1-允许
     */
    public static final String BID_FILE_PERMIT = "bid.file.permit";

    /**
     * 是否合格: 0-不合格，1-合格
     */
    public static final String BID_FILE_QUALIFIED = "bid.file.qualified";

    /**
     * 是否需要报名审核，0-不需要，1-需要
     */
    public static final String SUPPLIER_SIGN_UP_REVIEW_STATUS_ENABLED = "supplier_sign_up.review_status.enabled";

    /**
     * 个人-根据关键字
     */
    public static final String PERSON_KEY_SEAL_PARAMETER = "person_key_seal_parameter";

    /**
     * 企业-根据关键字
     */
    public static final String FIRM_KEY_SEAL_PARAMETER = "firm_key_seal_parameter";

    /**
     * 个人-根据坐标
     */
    public static final String PERSON_COORDINATE_SEAL_PARAMETER = "person_coordinate_seal_parameter";

    /**
     * 企业-根据坐标
     */
    public static final String FIRM_COORDINATE_SEAL_PARAMETER = "firm_coordinate_seal_parameter";

    /**
     * 创建印章参数
     */
    public static final String CREATE_SEAL_PARAMETER = "create_seal_parameter";

    /**
     * e签宝版本
     */
    public static final String SEAL_VERSION = "seal_version";

    /**
     * 文件存储方式
     */
    public static final String FILE_STORAGE_METHOD = "file_storage_method";


    //供应商等级
    public static final String A = "A";
    public static final String B = "B";
    public static final String C = "C";
    public static final String D = "D";
    /**
     * 专家类型
     */
    public static final String EXPERT_DEPT = "expert_dept";

    /**
     * 供应商类型
     */
    public static final String SUPPLIER_BID_TYPE = "supplier_bid_type";

    /**
     * 供应商类型(龙岗特殊使用)
     */
    public static final String SUPPLIER_TYPE = "supplier_type";

    /**
     * 采购流程中的节点时间
     */
    public static final String SYS_PURCHASE_TIME = "sys_purchase_time";

    /**
     * 项目完成后多少天可以评价供应商
     */
    public static final String PURCHASER_COMMENT_SUPPLIER_TIME = "purchaser.comment.supplier.time";

    /**
     * 开标前是否能看到供应商投标文件
     */
    public static final String WHETHER_DISPLAY_SUPPLIER_FILE = "whether.display.supplier.file";

    /**
     * 龙岗短信参数
     */
    public static final String LG_SMS_PARAMETER = "lg.sms.parameter";

    /**
     * 供应商证书功能
     */
    public static final String SUPPLIER_CERTIFICATE_FUNCTION = "supplier.certificate.function";

    /**
     * 龙岗短信模版
     */
    public static final String LG_SMS_TEMPLATE_ID = "lg_sms_template_id";

    /**
     * 龙岗短信模版(忘记密码)
     */
    public static final String FORGET_PASSWORD = "forget_password";

    /**
     * 龙岗短信模版(项目名称)可自定义20字符之内
     */
    public static final String PROJECT_NAME = "project_name";

    /**
     * 字典
     * 供应商中标通知书下载费用
     */
    public static final String SUPPLIER_NOTICE_DOWNLOAD_FEE = "supplier_notice_download_fee";

    /**
     * 参数
     * 供应商下载中标通知书是否收费
     * 1=开启 0=关闭
     */
    public static final String SUPPLIER_NOTICE_DOWNLOAD_FEE_ENABLED = "supplier.notice.download.fee.enabled";


    /**
     * 字典
     * <p>
     * 标的类型（采购分类）
     */
    public static final String PROJECT_CATEGORY = "project_category";


}
