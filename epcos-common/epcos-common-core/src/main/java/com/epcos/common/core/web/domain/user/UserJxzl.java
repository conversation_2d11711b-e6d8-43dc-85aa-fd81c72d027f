package com.epcos.common.core.web.domain.user;

import com.epcos.common.core.annotation.Excel;
import com.epcos.common.core.annotation.Excel.Type;
import com.epcos.common.core.annotation.Excels;
import com.epcos.common.core.annotation.valid.Add;
import com.epcos.common.core.annotation.valid.Up;
import com.epcos.common.core.web.domain.BaseEntity;
import com.epcos.common.core.xss.Xss;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * 用户对象 sys_user
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class UserJxzl extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门编号")
    @Excel(name = "DEPT_CODE")
    private Long deptId;

    /**
     * 用户账号
     */
    @ApiModelProperty(value = "登录名称", hidden = true)
    @Xss(message = "用户账号不能包含脚本字符")
    @Size(max = 30, message = "用户账号长度不能超过30个字符")
    @Excel(name = "EMPL_CODE")
    @NotBlank(message = "用户登录名称必填", groups = Add.class)
    private String userName;

    /**
     * 用户昵称
     */
    @ApiModelProperty(value = "用户名称")
    @Xss(message = "用户昵称不能包含脚本字符")
    @Size(max = 100, message = "用户昵称长度不能超过100个字符")
    @Excel(name = "EMPL_NAME")
    private String nickName;


    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码", hidden = true)
    @Size(max = 30, message = "手机号码长度不能超过30个字符")
    @Excel(name = "TEL")
    private String phonenumber;

    /**
     * 用户性别
     */
    @ApiModelProperty(value = "用户性别", hidden = true)
    @Excel(name = "SEX_CODE")
    private String sex;

    /**
     * 帐号状态（0正常 1停用）
     */
    @ApiModelProperty(value = "帐号状态", hidden = true)
    @Excel(name = "VALID_STATE")
    private String status;


    @ApiModelProperty("供应商：统一信用代码，专家：身份证号")
    @Excel(name = "IDENNO")
    @Length(max = 25)
    private String idNumber;
}
