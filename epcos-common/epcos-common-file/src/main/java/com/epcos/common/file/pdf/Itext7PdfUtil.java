package com.epcos.common.file.pdf;

import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import com.itextpdf.layout.properties.VerticalAlignment;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.util.stream.IntStream;

@Slf4j
public class Itext7PdfUtil {


    // 设置默认字体-新宋体
    public static PdfFont pdfFont() throws IOException {
        byte[] bytes = StreamUtils.copyToByteArray(Itext7PdfUtil.class.getClassLoader().getResourceAsStream("fonts/simhei.ttf"));
        return PdfFontFactory.createFont(bytes, PdfEncodings.IDENTITY_H);
//        return PdfFontFactory.createFont(fontsPath + File.separator + "simsun.ttc,1", PdfEncodings.IDENTITY_H);
    }

    public static Table initTable(int columnSize) {
        Table table = new Table(columnSize);
        table.setWidth(UnitValue.createPercentValue(100))
                .setTextAlignment(TextAlignment.CENTER)
                .setVerticalAlignment(VerticalAlignment.MIDDLE)
                .setAutoLayout();
        return table;
    }

    // 空行
    public static void blankLinesInTheDocument(Document document) {
        blankLinesInTheDocument(document, 3);
    }

    public static void blankLinesInTheDocument(Document document, int blankLines) {
        IntStream.range(0, blankLines).forEach(i ->
                document.add(Itext7PdfUtil.paragraphLeft(""))
        );
    }

    // 合并，并设置内容
    public static Cell cellCenter(int rowspan, int colspan, Paragraph paragraph) {
        return new Cell(rowspan, colspan)
                .add(paragraph)
                .setTextAlignment(TextAlignment.CENTER)
                .setVerticalAlignment(VerticalAlignment.MIDDLE)
                ;
    }

    public static Cell cellCenter(int rowspan, int colspan, int height, Paragraph paragraph) {
        return new Cell(rowspan, colspan)
                .add(paragraph)
                .setTextAlignment(TextAlignment.CENTER)
                .setVerticalAlignment(VerticalAlignment.MIDDLE)
                .setHeight(height);
    }

    public static Cell cellCenter(Paragraph paragraph) {
        return cellCenter(1, 1, paragraph);
    }

    public static Cell cellLeft(Paragraph paragraph) {
        return cellCenter(1, 1, paragraph).setTextAlignment(TextAlignment.LEFT);
    }

    public static Cell cellLeft(int rowspan, int colspan, int height, Paragraph paragraph) {
        return cellCenter(rowspan, colspan, paragraph)
                .setTextAlignment(TextAlignment.LEFT)
                .setVerticalAlignment(VerticalAlignment.BOTTOM)
                .setHeight(height);
    }

    public static Cell cellRight(Paragraph paragraph) {
        return cellCenter(1, 1, paragraph).setTextAlignment(TextAlignment.RIGHT);
    }


    // 无边框格子
    public static Cell cellCenterAndNoBorder(Paragraph paragraph) {
        return cellCenter(paragraph).setBorder(Border.NO_BORDER);
    }

    // 顶部框格子
    public static Cell cellCenterAndNoBorder(int rowspan, int colspan, int height, Paragraph paragraph) {
        return cellCenter(rowspan, colspan, paragraph)
                .setBorderTop(Border.NO_BORDER)
                .setVerticalAlignment(VerticalAlignment.BOTTOM)
                .setHeight(height);
    }

    // 居中，无底部线
    public static Cell cellCenterAndNoBorderBottom(Paragraph paragraph) {
        return cellCenter(paragraph).setBorderBottom(Border.NO_BORDER);
    }

    // 居中，无底部线
    public static Cell cellCenterAndNoBorderBottom(int rowspan, int colspan, int height, Paragraph paragraph) {
        return cellCenter(rowspan, colspan, paragraph).setBorderBottom(Border.NO_BORDER).setHeight(height);
    }


    // 居左，无底部线
    public static Cell cellLeftAndNoBorderBottom(Paragraph paragraph) {
        return cellLeft(paragraph).setBorderBottom(Border.NO_BORDER);
    }

    // 段落
    public static Paragraph paragraph(String text, float fontSize, TextAlignment textAlignment, VerticalAlignment verticalAlignment) {
        return new Paragraph(text).setFontSize(fontSize).setTextAlignment(textAlignment).setVerticalAlignment(verticalAlignment);
    }

    public static Paragraph paragraph(String text, float fontSize) {
        return paragraph(text, fontSize, TextAlignment.CENTER, VerticalAlignment.MIDDLE);
    }


    // 字体
    public static Paragraph paragraphTitle(String text) {
        return paragraph(text, 20);
    }

    public static Paragraph paragraphSmallTitle(String text) {
        return paragraph(text, 16);
    }

    // 字体
    public static Paragraph paragraphHead(String text, TextAlignment textAlignment) {
        return paragraph(text, 14, textAlignment, VerticalAlignment.MIDDLE);
    }

    public static Paragraph paragraphHeadCenter(String text) {
        return paragraphHead(text, TextAlignment.CENTER);
    }

    public static Paragraph paragraphHeadLeft(String text) {
        return paragraphHead(text, TextAlignment.LEFT);
    }

    public static Paragraph paragraphHeadRight(String text) {
        return paragraphHead(text, TextAlignment.RIGHT);
    }

    // 字体8
    public static Paragraph paragraph(String text, TextAlignment textAlignment) {
        return paragraph(text, 12, textAlignment, VerticalAlignment.MIDDLE);
    }

    // 内容靠左
    public static Paragraph paragraphLeft(String text) {
        return paragraph(text, TextAlignment.LEFT);
    }

    // 内容靠右
    public static Paragraph paragraphRight(String text) {
        return paragraph(text, TextAlignment.RIGHT);
    }

    // 内容居中
    public static Paragraph paragraphCenter(String text) {
        return paragraph(text, TextAlignment.CENTER);
    }

    // 字体6
    public static Paragraph paragraphSmall(String text, TextAlignment textAlignment) {
        return paragraph(text, 12, textAlignment, VerticalAlignment.MIDDLE);
    }

}
