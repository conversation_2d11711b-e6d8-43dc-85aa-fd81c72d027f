## 湖北省武汉市武商集团版本

## 初始化项目信息表

```sql
CREATE TABLE `purchase_whws_buy_item`
(
    `id`                BIGINT ( 11 ) NOT NULL AUTO_INCREMENT COMMENT '采购项目-湖北省武汉市武商集团版本主健id',
    `allocate_id`       BIGINT ( 11 ) DEFAULT NULL COMMENT '项目外资料分配之后的主键id',
    `buy_item_code`     VARCHAR(64)    NOT NULL COMMENT '采购项目code',
    `buy_item_name`     VARCHAR(128)   NOT NULL COMMENT '采购项目名字',
    `apply_dept`        VARCHAR(64)    NOT NULL COMMENT '申报单位',
    `inner_code`        VARCHAR(64)    NOT NULL COMMENT '采购编号(院方使用)',
    `apply_person`      VARCHAR(64)    NOT NULL COMMENT '申请人',
    `apply_time`        datetime       NOT NULL COMMENT '申报时间',
    `buy_control_price` decimal(18, 5) NOT NULL COMMENT '招标控制价【18位整数，5位小数】',
    `buy_class`         VARCHAR(64)    NOT NULL COMMENT '项目类型',
    `annex_file`        text COMMENT '项目附件',
    `buy_remark`        VARCHAR(128) DEFAULT NULL COMMENT '备注',
    `deleted`           bit(1)       DEFAULT b'0' COMMENT '删除标志（0代表存在 代表删除)',
    `create_by`         VARCHAR(64)  DEFAULT '' COMMENT '创建者',
    `create_at`         datetime     DEFAULT NULL COMMENT '创建时间',
    `update_by`         VARCHAR(64)  DEFAULT '' COMMENT '更新者',
    `update_at`         datetime     DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `buy_item_code` ( `buy_item_code` )
) ENGINE = INNODB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8 COMMENT = '采购项目-湖北省武汉市武商集团版本';
````

## 2025-5-26 线上全部服务发布

## 2025-6-5 修改采购项目表

```sql
ALTER TABLE purchase_whws_buy_item
    modify COLUMN `inner_code` VARCHAR (64) NOT NULL COMMENT '采购编号(院方使用)';
ALTER TABLE `purchase_whws_buy_item`
    ADD UNIQUE KEY `inner_code_unique` (`inner_code`);
````

## 2025-6-11 15：38打包，线上全部服务发布

## 2025-6-13 修改采购项目表

```sql
ALTER TABLE purchase_whws_buy_item
    ADD COLUMN bid_whether_free TINYINT(1) DEFAULT 0 comment '标书文件是否收费[0-不收费，1-收费]' AFTER `annex_file`;
ALTER TABLE purchase_whws_buy_item
    ADD COLUMN bid_book_amount decimal(18, 5) DEFAULT NULL comment '收费金额' AFTER `bid_whether_free`;
```

## 2025-6-24 12：00 线上发布

## 2025-6-26 13：00 线上发布

## 2025-6-27 修改采购项目表

```sql
ALTER TABLE purchase_whws_buy_item
    ADD COLUMN account_name VARCHAR(32) DEFAULT NULL comment '账户名称' AFTER `bid_book_amount`;
ALTER TABLE purchase_whws_buy_item
    ADD COLUMN bank_account VARCHAR(32) DEFAULT NULL comment '银行账户' AFTER `account_name`;
ALTER TABLE purchase_whws_buy_item
    ADD COLUMN opening_bank VARCHAR(32) DEFAULT NULL comment '开户行' AFTER `bank_account`;
```

## 2025-7-14 武商增加供应商投标文件额外上传附件
```sql
create table answer_file_att
(
    `id`              bigint       not null auto_increment comment '主键',
    `subpackage_code` char(64)     not null comment '包编码',
    `supplier_id`     bigint       not null comment '供应商id',
    `file_key`        varchar(100) not null comment '文件key',
    `file_name`       varchar(100) not null comment '文件名',
    `create_by`       varchar(64) DEFAULT '' COMMENT '创建者',
    `create_at`       datetime    DEFAULT NULL COMMENT '创建时间',
    `update_by`       varchar(64) DEFAULT '' COMMENT '更新者',
    `update_at`       datetime    DEFAULT NULL COMMENT '更新时间',
    primary key (`id`),
    key               `idx_subpackage_supplier` (`subpackage_code`, `supplier_id`)
) engine=innodb DEFAULT CHARSET=utf8mb4 COMMENT='武商-供应商投标文件附件';
```

## 2025-7-16 线上服务全发布