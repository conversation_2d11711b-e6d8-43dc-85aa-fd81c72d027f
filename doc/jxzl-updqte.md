# 江西省肿瘤医院

## 修改记录

# 采购人供应商库中 attribute 表：通用属性配置，不能删除

### 2024-5-14 17:00 发布线上

### 2024-6-19 17:00 采购人库 新增

### 2024-6-20 10:00 发布线上

### 2024-7-23 发布线上

### 2024-7-31 发布线上

### 2024-8-1 发布线上

### 2024-8-13 修改江西创建项目表

```sql
CREATE TABLE `purchase_jxzl_buy_item`
(
    `id`                  bigint(11)   NOT NULL AUTO_INCREMENT COMMENT '采购项目-江西肿瘤版本主健id',
    `buy_item_code`       varchar(64)  NOT NULL COMMENT '采购项目code',
    `buy_item_name`       varchar(128) NOT NULL COMMENT '采购项目名字',
    `buy_class`           varchar(64)  NOT NULL COMMENT '标的属性',
    `buy_person`          varchar(64)  NOT NULL COMMENT '项目负责人',
    `concat_number`       varchar(18)  NOT NULL COMMENT '联系电话',
    `organize_type`       varchar(64)    DEFAULT NULL COMMENT '组织形式',
    `apply_time`          datetime       DEFAULT NULL COMMENT '申报时间',
    `buy_budget`          decimal(18, 5) DEFAULT NULL COMMENT '预算金额【18位整数，5位小数】',
    `project_budget`      decimal(18, 5) DEFAULT NULL COMMENT '项目预算【18位整数，5位小数】',
    `buy_fixe_price`      decimal(18, 5) DEFAULT NULL COMMENT '采购限价【18位整数，5位小数】',
    `inner_code`          varchar(64)  NOT NULL COMMENT '采购编号(院方使用)',
    `apply_dept`          varchar(64)  NOT NULL COMMENT '申请部门',
    `management_dept`     varchar(64)    DEFAULT NULL COMMENT '归口管理',
    `organize_dept`       varchar(64)    DEFAULT NULL COMMENT '组织部门',
    `entrust_agent`       varchar(32)  NOT NULL COMMENT '委托代理',
    `purchase_limit_time` varchar(32)    DEFAULT NULL COMMENT '采购时限',
    `buy_remark`          varchar(128)   DEFAULT NULL COMMENT '备注',
    `deleted`             bit(1)         DEFAULT b'0' COMMENT '删除标志（0代表存在 代表删除)',
    `create_by`           varchar(64)    DEFAULT '' COMMENT '创建者',
    `create_at`           datetime       DEFAULT NULL COMMENT '创建时间',
    `update_by`           varchar(64)    DEFAULT '' COMMENT '更新者',
    `update_at`           datetime       DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `buy_item_code` (`buy_item_code`),
    UNIQUE KEY `inner_code` (`inner_code`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4 COMMENT ='采购项目-江西肿瘤版本';
```

### 2024-9-10 发布线上

### 2024-9-18 修改字段

```sql
ALTER TABLE purchase_jxzl_buy_item
    modify COLUMN `inner_code` varchar (64) DEFAULT NULL COMMENT '采购编号(院方使用)';
```

### 2024-9-18 发布线上

```sql
-- 会议室
create table `meeting_room`
(
    `id`         bigint              not null auto_increment primary key comment 'id',
    `name`       varchar(100) unique not null comment '会议室名称',
    `start_time` datetime            not null comment '开放时间',
    `end_time`   datetime            not null comment '结束时间',
    `status`     tinyint      default 0 comment '0-正常，1-停用',
    `remark`     varchar(500) default null comment '备注'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='会议室';
-- 会议室日程
create table `meeting_room_schedule`
(
    `id`         bigint       not null auto_increment primary key comment 'id',
    `room_id`    bigint       not null comment '会议室id',
    `user_id`    bigint       not null comment '用户id',
    `nike_name`  varchar(50)  not null comment '用户名',
    `phone`      varchar(20)  not null comment '联系电话',
    `start_time` datetime     not null comment '使用时间',
    `end_time`   datetime     not null comment '结束时间',
    `reason`     varchar(500) not null comment '说明',
    `audit`      tinyint unsigned default 0 comment '0-待审核，1-通过，2-拒绝',
    `statement`  varchar(500) default null comment '审核说明'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='会议室日程';

```

### 2024-9-19 21.56 发布线上

### 2024-10-10 发布线上[bidding,system]，评审模板替换，供应商注册异常修复

### 2024-10-14 增加唯一约束

```sql
ALTER TABLE `purchase_jxzl_buy_item`
    MODIFY COLUMN `inner_code` varchar (64) NOT NULL COMMENT '采购编号(院方使用)';
ALTER TABLE `purchase_jxzl_buy_item`
    MODIFY COLUMN `entrust_agent` varchar (32) DEFAULT NULL COMMENT '委托代理';
ALTER TABLE `purchase_jxzl_buy_item`
    ADD CONSTRAINT `inner_code` UNIQUE (`inner_code`);
```

### 2024-10-15 发布上线，webservice 接口公布，接收外部项目数据

### 2024-10-19 江西肿瘤医院 采购项目表增加字段

```sql
ALTER TABLE purchase_jxzl_buy_item
    ADD COLUMN allocate_id bigint(11) DEFAULT NULL comment '项目外资料分配之后的主键id' AFTER `id`;
```

### 2024-10-31 江西肿瘤医院 会议室表 修改字段

```sql
ALTER TABLE meeting_room
    MODIFY COLUMN start_time datetime DEFAULT NULL comment '开放时间';
ALTER TABLE meeting_room
    MODIFY COLUMN end_time datetime DEFAULT NULL comment '结束时间';
ALTER TABLE meeting_room_schedule
    MODIFY COLUMN reason varchar (500) DEFAULT NULL COMMENT '说明';
```

### 2024-11-2 11:00 江西肿瘤医院发布线上

### 2024-11-14 9:30 江西肿瘤医院发布线上

### 2024-12-27 11:00 江西肿瘤医院发布线上,应钉钉审批中修复安全漏洞

### 2025-1-9 12:00 江西肿瘤医院发布线上,pdf文件xss攻击漏洞修复

### 2025-2-7 12:00 线上发布，数据统计相关功能上线，所有包都已发布

### 2025-2-10 江西肿瘤医院  采购人表 修改字段

```sql
ALTER TABLE purchase_jxzl_buy_item MODIFY project_budget varchar (64) DEFAULT NULL COMMENT '项目预算来源';
```

### 2025-6-20 18:00 线上发布，所有包都已发布