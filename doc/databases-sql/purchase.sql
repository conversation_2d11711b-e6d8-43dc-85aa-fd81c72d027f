CREATE TABLE `purchase_tender_info`
(
    `id`                                        bigint(11)   NOT NULL AUTO_INCREMENT COMMENT 'id',
    `org_code`                                  varchar(64)  NOT NULL COMMENT '组织编码',
    `tenderer_code_type`                        char(2)      NOT NULL DEFAULT '97' COMMENT '(默认97:统一信用代码)招标人代码类型:参看3.19主体机构代码类型',
    `tenderer_code`                             char(18)     NOT NULL COMMENT '(公司统一信用代码18位)招标人代码:参看3.20主体机构代码',
    `tenderer_name`                             varchar(100) NOT NULL COMMENT '(公司名字)招标人名称:',
    `artificial_person`                         char(10)     NOT NULL COMMENT '法人代表/负责人姓名:',
    `artificial_person_code_type`               char(2)      NOT NULL DEFAULT '01' COMMENT '(01:身份证)法人代表/负责人证件类型:参看3.19主体机构代码类型',
    `artificial_person_code`                    char(18)     NOT NULL COMMENT '(身份证号码)法人代表/负责人身份证件号:参看3.20主体机构代码',
    `contact_number`                            char(12)              DEFAULT NULL COMMENT '机构联系电话: 座机或手机',
    `country_region`                            char(3)               DEFAULT NULL COMMENT '156-中国,(中国)国别/地区:采用GB/T 2659-2000 中的3位数字码',
    `unit_nature`                               char(25)              DEFAULT NULL COMMENT '(3位数字码)单位性质:GB/T 12402《经济类型分类与代码》。',
    `region_code`                               char(6)               DEFAULT NULL COMMENT '(char6)行政区域代码:采用GB/T 2260-2007中的市级代码',
    `region_code_detail`                        varchar(125)          DEFAULT NULL COMMENT '公司详细地址',
    `industry_code`                             char(30)              DEFAULT NULL COMMENT '(A01)行业代码:采用GB/T 4754《国民经济行业分类》中的门类和大类',
    `credit_rate`                               char(10)              DEFAULT NULL COMMENT '资信等级:',
    `opening_bank`                              char(30)              DEFAULT NULL COMMENT '开户银行:',
    `basic_account`                             char(30)              DEFAULT NULL COMMENT '基本账户账号:',
    `reg_capital`                               decimal(18, 2)        DEFAULT NULL COMMENT '注册资本:',
    `reg_cap_currency`                          char(3)               DEFAULT NULL COMMENT '注册资本币种:采用GB/T 12406-2008《表示货币和资金的代码》的数字码',
    `reg_unit`                                  char(1)               DEFAULT NULL COMMENT '(1:元,2:万元)注册资本单位:《电子招投标系统技术规范》附录B.3.25价格单位代码',
    `info_reporter_name`                        char(10)              DEFAULT NULL COMMENT '信息申报责任人姓名:',
    `info_reporter_code_type`                   char(2)               DEFAULT NULL COMMENT '(01:身份证)信息申报责任人证件类型:参看3.19主体机构代码类型',
    `info_reporter_code`                        char(18)              DEFAULT NULL COMMENT '(身份证号码)信息申报责任人证件号码:参看3.20主体机构代码',
    `info_reporter_contact_number`              char(11)              DEFAULT NULL COMMENT '信息申报责任人联系电话:信息申报责任人的电话请填写手机号',
    `contact_address`                           varchar(125)          DEFAULT NULL COMMENT '联系地址:',
    `zip_code`                                  char(6)               DEFAULT NULL COMMENT '邮政编码:',
    `email`                                     varchar(100)          DEFAULT NULL COMMENT '电子邮箱:',
    `business_license`                          mediumtext COMMENT '营业执照或组织机构代码证件扫描件(2m以内) base64',
    `legal_representative_identity_certificate` mediumtext COMMENT '法定代表人身份证明扫描件(2m以内) base64(非必须)',
    `user_id`                                   bigint(11)   NOT NULL COMMENT '创建人id',
    `company_id`                                bigint(11)   NOT NULL COMMENT '公司id',
    `platform_status`                           tinyint(1)   NOT NULL DEFAULT '0' COMMENT '0-等待平台进行审核，1-审核失败，2-审核通过',
    `verify_time`                               datetime              DEFAULT NULL COMMENT '交易平台数据验证时间: ',
    `platform_code`                             char(11)              DEFAULT NULL COMMENT '交易平台标识码:《电子招投标系统技术规范》附录B.3.1交易平台标识代码； ',
    `platform_name`                             varchar(100)          DEFAULT NULL COMMENT '交易平台名称: ',
    `examiner_name`                             char(10)              DEFAULT NULL COMMENT '交易平台数据验证责任人姓名:S由交易平台人员在国家公共服务平台审核时形成',
    `examiner_code_type`                        char(2)               DEFAULT NULL COMMENT '01-身份证，交易平台数据验证责任人证件类型:参看3.19主体机构代码类型由交易平台人员在国家公共服务平台审核时形成 ',
    `examiner_code`                             char(18)              DEFAULT NULL COMMENT '420102197710231419 交易平台数据验证责任人证件号码:参看3.20主体机构代码由交易平台人员在国家公共服务平台审核时形成 ',
    `deleted`                                   bit(1)                DEFAULT false COMMENT '删除标志（false代表存在 true代表删除)',
    `create_by`                                 varchar(64)           DEFAULT '' COMMENT '创建者',
    `create_time`                               datetime              DEFAULT NULL COMMENT '创建时间',
    `update_by`                                 varchar(64)           DEFAULT '' COMMENT '更新者',
    `update_time`                               datetime              DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8 COMMENT ='招标人信息';

-- 采购项目 基础信息表
CREATE TABLE `purchase_buy_item`
(
    `id`                     bigint(11)   NOT NULL AUTO_INCREMENT COMMENT '采购项目主健id',
    `item_code`              varchar(64)  NOT NULL DEFAULT '' COMMENT '项目code 暂时用不到',
    `buy_item_code`          varchar(64)  NOT NULL DEFAULT '' COMMENT '采购项目code',
    `buy_item_name`          varchar(128) NOT NULL DEFAULT '' COMMENT '采购项目名字',
    `inner_buy_item_code`    varchar(64)  NOT NULL DEFAULT '' COMMENT '院内采购项目编号',
    `monitor_bid_person`     varchar(32)           DEFAULT '' COMMENT '监标人',
    `monitor_bid_person_id`  bigint(11)            DEFAULT NULL COMMENT '监标人id',
    `purchase_method_code`   varchar(32)  NOT NULL COMMENT '采购方式code[存储 purchase_method表中的 purchase_method_code字段]',
    `purchase_method_type`   varchar(128) NOT NULL COMMENT '采购方式类型[存储 purchase_method表中的 purchase_method_type字段]',
    `purchase_method_name`   varchar(128) NOT NULL COMMENT '采购方式名称[存储 purchase_method表中的 purchase_method_name字段]',
    `year_month_split`       varchar(32)           DEFAULT NULL COMMENT '招标项目创建时间的年月',
    `purchase_time_json`     text         NOT NULL COMMENT '采购时间点json',
    `purchase_bulletin_json` text         NOT NULL COMMENT '采购公示公告json',
    `purchase_function_json` text         NOT NULL COMMENT '采购功能点json',
    `user_id`                bigint(11)   NOT NULL COMMENT '创建人id',
    `org_code`               varchar(64)  NOT NULL COMMENT '创建人的组织代码',
    `deleted`                bit(1)                DEFAULT 0 COMMENT '删除标志（0代表存在 代表删除)',
    `create_by`              varchar(64)           DEFAULT '' COMMENT '创建者',
    `create_at`              datetime              DEFAULT NULL COMMENT '创建时间',
    `update_by`              varchar(64)           DEFAULT '' COMMENT '更新者',
    `update_at`              datetime              DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `buy_item_code` (`buy_item_code`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8 COMMENT ='采购项目 基础信息表';


-- 采购项目-医疗版本
CREATE TABLE `purchase_jxzl_buy_item`
(
    `id`                bigint(11)   NOT NULL AUTO_INCREMENT COMMENT '采购项目-  江西肿瘤版本主健id',
    `buy_item_code`     varchar(64)  NOT NULL COMMENT '采购项目code',
    `buy_item_name`     varchar(128) NOT NULL COMMENT '采购项目名字',
    `buy_content`       text           DEFAULT NULL COMMENT '招标内容与范围',
    `buy_college`       varchar(128)   DEFAULT NULL COMMENT '采购院校',
    `function_dept`     varchar(64)  NOT NULL COMMENT '职能科室',
    `budget_amount`     decimal(18, 5) DEFAULT NULL COMMENT '预算金额【18位整数，5位小数】',
    `buy_budget`        decimal(18, 5) DEFAULT NULL COMMENT '采购预算【18位整数，5位小数】',
    `buy_control_price` decimal(18, 5) DEFAULT NULL COMMENT '招标控制价【18位整数，5位小数】',
    `use_dept`          varchar(64)  NOT NULL COMMENT '使用科室',
    `use_dept_person`   varchar(64)    DEFAULT NULL COMMENT '使用科室负责人',
    `buy_class`         varchar(64)  NOT NULL COMMENT '采购分类',
    `annex_key`         varchar(64)    DEFAULT '' COMMENT '项目附件key',
    `deleted`           bit(1)         DEFAULT 0 COMMENT '删除标志（0代表存在 代表删除)',
    `create_by`         varchar(64)    DEFAULT '' COMMENT '创建者',
    `create_at`         datetime       DEFAULT NULL COMMENT '创建时间',
    `update_by`         varchar(64)    DEFAULT '' COMMENT '更新者',
    `update_at`         datetime       DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `buy_item_code` (`buy_item_code`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8 COMMENT ='采购项目-医疗版本';


-- 包 基础信息表
CREATE TABLE `purchase_subpackage`
(
    `id`                          bigint(11)  NOT NULL AUTO_INCREMENT COMMENT '主健id',
    `buy_item_code`               varchar(64) NOT NULL COMMENT '采购项目编号(业务使用代码)',
    `subpackage_code`             varchar(64) NOT NULL COMMENT '标段编号（业务使用代码）',
    `subpackage_name`             varchar(64) NOT NULL COMMENT '标段名称',
    `inner_buy_item_package_code` varchar(64)    DEFAULT NULL COMMENT '院内采购项目标段编号',
    `invited_supplier_json`       text COMMENT '被邀请的供应商Json',
    `claims_file_pdf_key`         varchar(100)   DEFAULT NULL COMMENT '采购文件pdf key.txt',
    `abandon`                     char(1)        DEFAULT '0' COMMENT '是否终止采购 [0-正常，1-终止] 默认为0',
    `label_json`                  text COMMENT '标签',
    `contract_price`              decimal(18, 5) DEFAULT NULL COMMENT '控制价【18位整数，5位小数】',
    `subpackage_content`          varchar(256)   DEFAULT NULL COMMENT '标段内容',
    `registration_end_time`       datetime       DEFAULT NULL COMMENT '报名截止时间',
    `meeting_time`                datetime       DEFAULT NULL COMMENT '响应截止时间,也是开标时间',
    `deleted`                     bit(1)         DEFAULT 0 COMMENT '删除标志（0代表存在 代表删除)',
    `create_by`                   varchar(64)    DEFAULT '' COMMENT '创建者',
    `create_at`                   datetime       DEFAULT NULL COMMENT '创建时间',
    `update_by`                   varchar(64)    DEFAULT '' COMMENT '更新者',
    `update_at`                   datetime       DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `subpackage_code` (`subpackage_code`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8 COMMENT ='包 基础信息表';


CREATE TABLE `purchase_bulletin`
(
    `id`                   bigint(11)   NOT NULL AUTO_INCREMENT COMMENT 'id',
    `buy_item_code`        varchar(64)  NOT NULL COMMENT '采购项目编号',
    `subpackage_code`      varchar(64)           DEFAULT NULL COMMENT '标段code',
    `bulletin_name`        varchar(128) NOT NULL COMMENT '公告名称',
    `bulletin_type`        varchar(64)  NOT NULL COMMENT '公告类型',
    `bulletin_type_name`   varchar(64)  NOT NULL COMMENT '公告类型中文名',
    `annex_key`            text COMMENT '公告附件key',
    `bulletin_content_key` varchar(64)  NOT NULL COMMENT '公告的pdf_key',
    `org_code`             varchar(32)           DEFAULT NULL COMMENT '组织code',
    `invite_supplier`      text                  DEFAULT NULL COMMENT '邀请的供应商id信息',
    `supplier_addition`    text                  DEFAULT NULL COMMENT '供应商附加条件',
    `review_time`          datetime              DEFAULT NULL COMMENT '公告审核时间',
    `audit_status`         char(1)      NOT NULL DEFAULT '0' COMMENT '0-待审核, 1-撤回, 2-平台审核不通过, 3-平台审核通过',
    `audit_desc`           varchar(256)          DEFAULT '' COMMENT '审核说明',
    `audit_id`             bigint(11)            DEFAULT NULL COMMENT '审核人id',
    `audit_name`           varchar(20)           DEFAULT '' COMMENT '审核人名字',
    `whether_show`         char(1)      NOT NULL DEFAULT '0' COMMENT '是否展示 0-全网展示 1-供应商可见 2-不展示',
    `deleted`              bit(1)                DEFAULT 0 COMMENT '删除标志（0代表存在 代表删除)',
    `create_by`            varchar(64)           DEFAULT '' COMMENT '创建者',
    `create_at`            datetime              DEFAULT NULL COMMENT '创建时间',
    `update_by`            varchar(64)           DEFAULT '' COMMENT '更新者',
    `update_at`            datetime              DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8 COMMENT ='公告表';



CREATE TABLE `purchase_bulletin_time`
(
    `id`              bigint(11)  NOT NULL AUTO_INCREMENT COMMENT 'id',
    `bulletin_id`     bigint(11)  NOT NULL COMMENT '公告信息表主键id',
    `buy_item_code`   varchar(64) NOT NULL COMMENT '招标项目编号',
    `subpackage_code` varchar(64) NOT NULL COMMENT '标段code',
    `audit_status`    char(1)     NOT NULL DEFAULT '0' COMMENT '0-待审核, 1-撤回, 2-平台审核不通过, 3-平台审核通过',
    `time_name`       varchar(64) NOT NULL COMMENT '时间名称',
    `time_type`       varchar(64) NOT NULL COMMENT '时间类型',
    `time`            datetime             DEFAULT NULL COMMENT '公告时间',
    `deleted`         bit(1)               DEFAULT 0 COMMENT '删除标志（0代表存在 代表删除)',
    `create_by`       varchar(64)          DEFAULT '' COMMENT '创建者',
    `create_at`       datetime             DEFAULT NULL COMMENT '创建时间',
    `update_by`       varchar(64)          DEFAULT '' COMMENT '更新者',
    `update_at`       datetime             DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8 COMMENT ='公告时间表';


CREATE TABLE `purchase_extract_judge_external`
(
    `id`                bigint(11)   NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `extract_long_name` varchar(128) NOT NULL COMMENT '抽取记录名称',
    `judge_id`          bigint(11)   NOT NULL COMMENT '评委id',
    `remark`            varchar(500) DEFAULT '' COMMENT '备注',
    `deleted`           bit(1)       DEFAULT 0 COMMENT '删除标志（0代表存在 代表删除)',
    `create_by`         varchar(64)  DEFAULT '' COMMENT '创建者',
    `create_at`         datetime     DEFAULT NULL COMMENT '创建时间',
    `update_by`         varchar(64)  DEFAULT '' COMMENT '更新者',
    `update_at`         datetime     DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8 COMMENT ='院外--评委抽取表';


CREATE TABLE `purchase_extract_judge_inner`
(
    `id`              bigint(11)   NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `buy_item_code`   varchar(64)  NOT NULL COMMENT '采购项目编号',
    `subpackage_code` varchar(64)  NOT NULL COMMENT '标段code',
    `judge_id`        bigint(11)   NOT NULL COMMENT '评委id',
    `judge_name`      varchar(100) NOT NULL COMMENT '评委名称',
    `password`        varchar(50)  NOT NULL COMMENT '评审密码',
    `dept_represent`  char(1)     DEFAULT '0' COMMENT '是否是科室代表 [0-不是 1-是]',
    `whether_group`   char(1)     DEFAULT '0' COMMENT '是否是组长 [0-不是 1-是]',
    `whether_sign`    char(1)     DEFAULT '0' COMMENT '是否签到 [0-未签到 1-签到成功]',
    `sign_time`       datetime    DEFAULT NULL COMMENT '签到时间',
    `status`          char(1)     DEFAULT '0' COMMENT '评委状态[0-未评标 1-评标 2-评标完成]',
    `deleted`         bit(1)      DEFAULT 0 COMMENT '删除标志（0代表存在 代表删除)',
    `create_by`       varchar(64) DEFAULT '' COMMENT '创建者',
    `create_at`       datetime    DEFAULT NULL COMMENT '创建时间',
    `update_by`       varchar(64) DEFAULT '' COMMENT '更新者',
    `update_at`       datetime    DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8 COMMENT ='项目内--评委抽取表';



CREATE TABLE `purchase_doubt_time`
(
    `id`              bigint(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `buy_item_code`   char(64)            NOT NULL DEFAULT '' COMMENT '采购项目编号',
    `subpackage_code` char(64)            NOT NULL DEFAULT '' COMMENT '包编码',
    `subpackage_name` varchar(128)        NOT NULL DEFAULT '' COMMENT '包名称',
    `doubt_type`      char(1)             NOT NULL COMMENT '质疑类型  1-开标之前 （供应商向招标人） 2 - 开标过程中（供应商向招标人） 3 -评标过程中（专家向供应商 澄清 ） 4- 中标公示期（供应商向招标人）',
    `start_time`      datetime            NOT NULL COMMENT '发起质疑开始时间',
    `end_time`        datetime            NOT NULL COMMENT '质疑回复截止时间',
    `deleted`         bit(1)                       DEFAULT 0 COMMENT '删除标志（0代表存在 代表删除)',
    `version`         int(9)                       DEFAULT 0 COMMENT '版本信息',
    `create_by`       varchar(64)                  DEFAULT '' COMMENT '创建者',
    `create_at`       datetime                     DEFAULT NULL COMMENT '创建时间',
    `update_by`       varchar(64)                  DEFAULT '' COMMENT '更新者',
    `update_at`       datetime                     DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 100
  DEFAULT CHARSET = utf8 COMMENT ='质疑时间信息表';

CREATE TABLE `purchase_doubt`
(
    `id`                   bigint(11)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `doubt_time_id`        bigint(11)  NOT NULL COMMENT '质疑时间信息表主键',
    `subpackage_code`      char(64)    NOT NULL COMMENT '包编号',
    `doubt_user_type`      char(1)     NOT NULL COMMENT '质疑人类型[1-投标人, 2-采购办, 3-评委]',
    `doubt_user_id`        bigint(11)  NOT NULL COMMENT '质疑人id',
    `doubt_user_name`      varchar(64) NOT NULL COMMENT '提问名称[投标人公司名称/采购办操作人姓名/评委姓名]',
    `doubt_file_key`       varchar(64) DEFAULT NULL COMMENT '质疑文件地址key',
    `reply_file_key`       varchar(64) DEFAULT NULL COMMENT '质疑回复文件地址key',
    `doubt_annex_file_key` varchar(64) DEFAULT NULL COMMENT '发起质疑附件文件地址key',
    `reply_annex_file_key` varchar(64) DEFAULT NULL COMMENT '回复质疑附件文件地址key',
    `reply_user_id`        bigint(11)  DEFAULT NULL COMMENT '质疑答复人id',
    `deleted`              bit(1)      DEFAULT 0 COMMENT '删除标志（0代表存在 代表删除)',
    `create_by`            varchar(64) DEFAULT '' COMMENT '创建者',
    `create_at`            datetime    DEFAULT NULL COMMENT '创建时间',
    `update_by`            varchar(64) DEFAULT '' COMMENT '更新者',
    `update_at`            datetime    DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8 COMMENT ='质疑表信息列表';


CREATE TABLE `purchase_ask_answer`
(
    `id`               bigint(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `subpackage_code`  varchar(64)         NOT NULL DEFAULT '' COMMENT '标段编码',
    `ask_user_type`    char(1)             NOT NULL COMMENT '提问人类型[1-投标人, 2-采购办, 3-评委]',
    `ask_user_id`      bigint(11)          NOT NULL COMMENT ' 提问人id',
    `ask_user_name`    varchar(64)         NOT NULL COMMENT '提问人名称[投标人公司名称/采购办操作人姓名/评委姓名]',
    `answer_user_id`   bigint(11)                   DEFAULT NULL COMMENT ' 答复人id',
    `answer_user_name` varchar(64)                  DEFAULT NULL COMMENT '答复人名称[投标人公司名称/采购办操作人姓名/评委姓名]',
    `answer_user_type` char(1)                      DEFAULT NULL COMMENT '回复人类型[1-投标人, 2-采购办, 3-评委]',
    `ask_content`      varchar(256)        NOT NULL COMMENT '提问内容',
    `answer_content`   varchar(256)                 DEFAULT NULL COMMENT '答复内容',
    `deleted`          bit(1)                       DEFAULT 0 COMMENT '删除标志（0代表存在 代表删除)',
    `create_by`        varchar(64)                  DEFAULT '' COMMENT '创建者',
    `create_at`        datetime                     DEFAULT NULL COMMENT '创建时间',
    `update_by`        varchar(64)                  DEFAULT '' COMMENT '更新者',
    `update_at`        datetime                     DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='答疑模块答疑表';



CREATE TABLE `purchase_win_bid_result`
(
    `id`                    bigint(11)   NOT NULL AUTO_INCREMENT COMMENT 'id',
    `subpackage_code`       varchar(64)  NOT NULL DEFAULT '' COMMENT '标段编号',
    `subpackage_name`       varchar(200) NOT NULL DEFAULT '' COMMENT '标段名称',
    `supplier_id`           bigint(11)   NOT NULL COMMENT '投标人id',
    `supplier_company_name` varchar(64)  NOT NULL COMMENT '投标单位',
    `recommend_candidate`   char(1)               DEFAULT NULL COMMENT '评委推荐候选人[1-第一候选人，2-第二...，3-第三...]',
    `win_bid`               char(1)               DEFAULT '0' COMMENT '设定中标人【1-中标，2-未中标】',
    `win_bid_price`         decimal(18, 2)        DEFAULT NULL COMMENT '中标价格',
    `remark`                varchar(256)          DEFAULT '' COMMENT '说明',
    `is_win`                char(1)               DEFAULT NULL COMMENT '中标通知书类型[1-中标,2-未中标]',
    `send_time`             datetime              DEFAULT NULL COMMENT '通知书发送时间',
    `result_notice`         varchar(64)           DEFAULT '' COMMENT '中标结果通知书pdf_key---供应商',
    `again_send`            char(1)               DEFAULT '0' COMMENT '结果通知书的状态【0-未发送, 1-第一次已发送, 2-已重新发送】',
    `deleted`               bit(1)                DEFAULT 0 COMMENT '删除标志（0代表存在 代表删除)',
    `create_by`             varchar(64)           DEFAULT '' COMMENT '创建者',
    `create_at`             datetime              DEFAULT NULL COMMENT '创建时间',
    `update_by`             varchar(64)           DEFAULT '' COMMENT '更新者',
    `update_at`             datetime              DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8 COMMENT ='中标结果表';



CREATE TABLE `purchase_review_before`
(
    `id`                   bigint(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `subpackage_code`      char(64)            NOT NULL DEFAULT '' COMMENT '包编码',
    `supplier_id`          bigint(11)          NOT NULL COMMENT '供应商id',
    `bid_enter_expert`     char(1)                      DEFAULT NULL COMMENT '采购人确认评标结果[0-未确认，1-确认]',
    `confirm_counter_sign` char(1)                      DEFAULT NULL COMMENT '是否确认会签[0-未确认，1-已确认]',
    `confirm_review`       char(1)                      DEFAULT NULL COMMENT '是否确认评审[0-未确认，1-已确认]',
    `whether_show_judge`   char(1)                      DEFAULT NULL COMMENT '是否允许在评委列表上显示[0-不显示，1-显示]',
    `enter_the_review`     char(1)                      DEFAULT NULL COMMENT '是否允许进入评审,0-不允许，1-允许',
    `is_bargaining`        char(1)                      DEFAULT '0' COMMENT '议价人[0-采购人，1-评委，2-没有议价]',
    `deleted`              bit(1)                       DEFAULT 0 COMMENT '删除标志（0代表存在 代表删除)',
    `create_by`            varchar(64)                  DEFAULT '' COMMENT '创建者',
    `create_at`            datetime                     DEFAULT NULL COMMENT '创建时间',
    `update_by`            varchar(64)                  DEFAULT '' COMMENT '更新者',
    `update_at`            datetime                     DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8 COMMENT ='评审之前标段状态表';


CREATE TABLE `purchase_bargain`
(
    `id`              bigint(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `subpackage_code` char(64)   NOT NULL DEFAULT '' COMMENT '包编码',
    `initiate_id`     bigint(11) NOT NULL COMMENT '发起/结束议价人的id',
    `supplier_id`     bigint(11) NOT NULL COMMENT '供应商id',
    `round`           char(1)    NOT NULL DEFAULT '1' COMMENT '发起议价轮数： 1 一轮议价  2 二轮议价  3 三轮议价',
    `launch_status`   char(1)    NOT NULL DEFAULT '1' COMMENT '议价状态：1 发起  2 结束',
    `countdown`       datetime            DEFAULT NULL COMMENT '倒计时',
    `service_require` varchar(255)        DEFAULT NULL COMMENT '服务需求',
    `deleted`         bit(1)              DEFAULT 0 COMMENT '删除标志（0代表存在 代表删除)',
    `create_by`       varchar(64)         DEFAULT '' COMMENT '创建者',
    `create_at`       datetime            DEFAULT NULL COMMENT '创建时间',
    `update_by`       varchar(64)         DEFAULT '' COMMENT '更新者',
    `update_at`       datetime            DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8 COMMENT ='采购人/评委发起议价表';

CREATE TABLE `purchase_buy_item_param`
(
    `id`               bigint(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `subpackage_code`  char(64)   NOT NULL DEFAULT '' COMMENT '包编码',
    `pushed_user_id`   bigint(11)          DEFAULT NULL COMMENT '被推送人的id',
    `pushed_user_name` varchar(32)         DEFAULT NULL COMMENT '被推送人的名称',
    `param_state`      char(1)             DEFAULT '0' COMMENT '参数状态[0-未推送, 1-等待被推送人提交, 2-待确认, 3-已确认，4-不同意]',
    `param_reason`     varchar(255)        DEFAULT NULL COMMENT '驳回理由',
    `push_time`        datetime            DEFAULT NULL COMMENT '参数推送时间',
    `response_time`    datetime            DEFAULT NULL COMMENT '参数响应时间',
    `confirm_time`     datetime            DEFAULT NULL COMMENT '参数确认时间',
    `param_remark`     varchar(255)        DEFAULT NULL COMMENT '备注',
    `param_desc`       text                DEFAULT NULL COMMENT '参数描述',
    `param_attachment` text                DEFAULT NULL COMMENT '附件',
    `deleted`          bit(1)              DEFAULT 0 COMMENT '删除标志（0代表存在 代表删除)',
    `create_by`        varchar(64)         DEFAULT '' COMMENT '创建者',
    `create_at`        datetime            DEFAULT NULL COMMENT '创建时间',
    `update_by`        varchar(64)         DEFAULT '' COMMENT '更新者',
    `update_at`        datetime            DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `subpackage_code` (`subpackage_code`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8 COMMENT ='项目参数';

CREATE TABLE `tender_file_requirement`
(
    `id`               bigint        NOT NULL AUTO_INCREMENT COMMENT 'id',
    `bid_section_code` char(23)      NOT NULL COMMENT '标段code',
    `attribute`        varchar(500)  NOT NULL COMMENT '属性',
    `description`      varchar(1000) NOT NULL COMMENT '描述',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8 COMMENT ='采购文件要求';

CREATE TABLE `tender_file_rejection`
(
    `requirement_id` bigint       NOT NULL COMMENT '采购文件要求id',
    `supplier_id`    bigint       NOT NULL COMMENT '供应商id',
    `reason`         varchar(500) NOT NULL COMMENT '理由',
    `bid_file_key`   varchar(100) NOT NULL COMMENT '响应文件key',
    `create_at`      datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '日期',
    `create_id`      bigint       NOT NULL COMMENT '操作人id',
    `create_name`    varchar(100) NOT NULL COMMENT '操作人',
    KEY `fk_requirement_id` (`requirement_id`),
    CONSTRAINT `fk_requirement_id` FOREIGN KEY (`requirement_id`) REFERENCES `tender_file_requirement` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='采购文件要求驳回记录';

CREATE TABLE `bid_file_requirement`
(
    `id`               bigint       NOT NULL AUTO_INCREMENT COMMENT 'id',
    `bid_section_code` char(23)     NOT NULL COMMENT '标段code',
    `supplier_id`      bigint       NOT NULL COMMENT '供应商id',
    `requirement_id`   bigint       NOT NULL COMMENT '采购文件要求id',
    `bid_file_key`     varchar(100) NOT NULL COMMENT '响应文件key',
    `status`           tinyint unsigned DEFAULT '0' COMMENT '0-待上传、1-已上传、2-被驳回',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8 COMMENT ='投标人对采购文件具体响应';

