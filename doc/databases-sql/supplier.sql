CREATE TABLE `attribute`
(
    `id`            bigint(20)       NOT NULL AUTO_INCREMENT COMMENT '主键',
    `group_type`    char(3)          NOT NULL COMMENT '分组类型',
    `key_group`     varchar(255) DEFAULT NULL COMMENT '字段组',
    `key_name`      varchar(255)     NOT NULL COMMENT '字段中文名',
    `key_val`       varchar(255)     NOT NULL COMMENT '字段英文名',
    `key_type`      varchar(255)     NOT NULL COMMENT '字段类型',
    `key_java_type` varchar(255) DEFAULT NULL COMMENT 'java类型',
    `required`      bit(1)           NOT NULL COMMENT '必填=1',
    `regex`         varchar(255)     NOT NULL COMMENT '正则',
    `remark`        varchar(255) DEFAULT NULL COMMENT '备注',
    `displayed`     bit(1)           NOT NULL COMMENT '显示=1',
    `disabled`      bit(1)           NOT NULL COMMENT '禁用=1',
    `sort`          int(11) unsigned NOT NULL COMMENT '排序',
    `deleted`       bit(1)       DEFAULT b'0' COMMENT '删除=1',
    `create_at`     datetime     DEFAULT NULL COMMENT '创建时间',
    `create_by`     varchar(100) DEFAULT NULL COMMENT '创建者',
    `update_at`     datetime     DEFAULT NULL COMMENT '更新时间',
    `update_by`     varchar(100) DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_groupType_keyVal` (`group_type`, `key_val`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='通用属性配置';

CREATE TABLE `claims_file`
(
    `id`              bigint(20)          NOT NULL AUTO_INCREMENT COMMENT '主键',
    `subpackage_code` char(50)            NOT NULL COMMENT '包编码',
    `epc_file`        varchar(100)        NOT NULL COMMENT '采购文件包key',
    `pdf_file`        varchar(100)        NOT NULL COMMENT '采购文件pdf key.txt',
    `release_status`  tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '采购文件发布状态,0已上传 1已提交',
    `release_time`    datetime                     DEFAULT NULL COMMENT '采购文件提交时间',
    `app_name`        varchar(100)        NOT NULL COMMENT 'app名称',
    `app_version`     varchar(100)        NOT NULL COMMENT 'app版本',
    `zepc_key`        varchar(100)        NOT NULL COMMENT 'app文件验证key',
    `deleted`         bit(1)                       DEFAULT b'0' COMMENT '删除=1',
    `create_at`       datetime                     DEFAULT NULL COMMENT '创建时间',
    `create_by`       varchar(100)                 DEFAULT NULL COMMENT '创建者',
    `update_at`       datetime                     DEFAULT NULL COMMENT '更新时间',
    `update_by`       varchar(100)                 DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_subpackageCode` (`subpackage_code`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='采购文件信息';


CREATE TABLE `claims_file_evaluation_method`
(
    `id`              bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `subpackage_code` char(50)            NOT NULL COMMENT '包编码',
    `review_type`     tinyint(1) unsigned NOT NULL COMMENT '评审类型：1-符合性评审,评分表:(2-技术,3-商务,4-价格)',
    `review_item`     varchar(500)        NOT NULL COMMENT '评审模块',
    `review_criteria` text COMMENT '评审规则',
    `subjective`      tinyint(1) unsigned NOT NULL COMMENT '1-主观分,0-客观分',
    `review_score`    double(10, 2) unsigned DEFAULT NULL COMMENT '分值',
    `uuid`            char(50)            NOT NULL COMMENT 'uuid',
    `deleted`         bit(1)                 DEFAULT b'0' COMMENT '删除=1',
    `create_at`       datetime               DEFAULT NULL COMMENT '创建时间',
    `create_by`       varchar(100)           DEFAULT NULL COMMENT '创建者',
    `update_at`       datetime               DEFAULT NULL COMMENT '更新时间',
    `update_by`       varchar(100)           DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_subpackageCode` (`subpackage_code`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='采购文件评审办法';

CREATE TABLE `claims_file_evaluation_method_remark`
(
    `id`              bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `subpackage_code` char(50)            NOT NULL COMMENT '包编码',
    `remark`          text COMMENT '备注内容',
    `deleted`         bit(1)       DEFAULT b'0' COMMENT '删除=1',
    `create_at`       datetime     DEFAULT NULL COMMENT '创建时间',
    `create_by`       varchar(100) DEFAULT NULL COMMENT '创建者',
    `update_at`       datetime     DEFAULT NULL COMMENT '更新时间',
    `update_by`       varchar(100) DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_subpackageCode` (`subpackage_code`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='采购文件评审办法备注';

CREATE TABLE `claims_file_menu`
(
    `id`              bigint(20)          NOT NULL AUTO_INCREMENT COMMENT '主键',
    `subpackage_code` char(50)            NOT NULL COMMENT '包编码',
    `pid`             bigint(20) unsigned DEFAULT NULL COMMENT '父主键',
    `order_num`       tinyint(1) unsigned NOT NULL COMMENT '目录顺序',
    `chapter_name`    varchar(100)        NOT NULL COMMENT '目录名字',
    `chapter_context` mediumtext COMMENT '目录内容，压缩并base64转码存储',
    `attach_key`      varchar(5000)       DEFAULT NULL COMMENT '目录中文件keys',
    `chapter_type`    tinyint(1) unsigned DEFAULT NULL COMMENT '目录类型',
    `deleted`         bit(1)              DEFAULT b'0' COMMENT '删除=1',
    `create_at`       datetime            DEFAULT NULL COMMENT '创建时间',
    `create_by`       varchar(100)        DEFAULT NULL COMMENT '创建者',
    `update_at`       datetime            DEFAULT NULL COMMENT '更新时间',
    `update_by`       varchar(100)        DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_subpackageCode` (`subpackage_code`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='采购文件章节及章节子内容';

CREATE TABLE `claims_file_quote_form`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `subpackage_code` char(50)   NOT NULL COMMENT '包编码',
    `head_json`       text       NOT NULL COMMENT '表头',
    `body_json`       text COMMENT '表体',
    `deleted`         bit(1)       DEFAULT b'0' COMMENT '删除=1',
    `create_at`       datetime     DEFAULT NULL COMMENT '创建时间',
    `create_by`       varchar(100) DEFAULT NULL COMMENT '创建者',
    `update_at`       datetime     DEFAULT NULL COMMENT '更新时间',
    `update_by`       varchar(100) DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`),
    KEY `idx_subpackageCode` (`subpackage_code`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='采购人报价表单';

-- 供应商投标人信息
CREATE TABLE `supplier_bidder`
(
    `id`                                        bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
    `subpackage_code`                           char(50)            NOT NULL COMMENT '包编码',
    `bidder_code_type`                          char(2)             NOT NULL DEFAULT '97' COMMENT '供应商代码类型,97：公司',
    `lic_number`                                char(18)            NOT NULL COMMENT '统一信用代码，营业执照号码: ',
    `bidder_name`                               varchar(100)        NOT NULL COMMENT '供应商公司名称 [1,100]',
    `contact_number`                            varchar(20)         NOT NULL COMMENT '法定代表人联系方式',
    `contact_address`                           varchar(125)        NOT NULL COMMENT '联系地址: 必须加上省市区加详细地址',
    `region_code`                               char(6)             NOT NULL COMMENT '6位数字码，行政区域代码:采用GB/T 2260-2007中的市级代码 ',
    `opening_bank`                              varchar(30)         NOT NULL COMMENT '开户银行: ',
    `basic_account`                             varchar(30)         NOT NULL COMMENT '基本账户账号: ',
    `industry_code`                             varchar(30)                  DEFAULT NULL COMMENT '例：E47（建筑业，房屋建筑业），行业代码:采用GB/T 4754《国民经济行业分类》中的门类和大类 ',
    `scope_of_supply`                           varchar(200)                 DEFAULT NULL COMMENT '供货范围',
    `unit_nature`                               varchar(100)        NOT NULL COMMENT '生产厂家，产品代理商',
    `business_license`                          varchar(100)        NOT NULL COMMENT '营业执照或组织机构代码证件扫描件',
    `legal_representative_identity_certificate` varchar(100)        NOT NULL COMMENT '法定代表人身份证明扫描件',
    `whether_micro_enterprises`                 tinyint(1) unsigned          DEFAULT NULL COMMENT '是否中小微企业，0：不是，1：是',
    `micro_enterprises`                         varchar(100)                 DEFAULT NULL COMMENT '中小微企业文件',
    `email`                                     varchar(100)                 DEFAULT null COMMENT '电子邮箱',
    `power_of_attorney`                         varchar(100)                 DEFAULT NULL COMMENT '投标授权书',
    `certificate_code`                          varchar(50)         NOT NULL COMMENT '法定代表人证件号码',
    `certificate_name`                          varchar(50)         NOT NULL COMMENT '法定代表人名称',
    `info_reporter_name`                        varchar(50)                  DEFAULT NULL COMMENT '投标责任人姓名: ',
    `info_reporter_code`                        varchar(50)                  DEFAULT NULL COMMENT '投标责任人证件号码',
    `info_reporter_contact_number`              varchar(20)                  DEFAULT NULL COMMENT '投标责任人联系电话',
    `bid_type`                                  varchar(100)        NOT NULL COMMENT '投标类型',
    `registered_capital`                        varchar(100)        NOT NULL COMMENT '注册资本',
    `operating_period`                          varchar(100)        NOT NULL COMMENT '营业期限',
    `date_of_establishment`                     varchar(100)        NOT NULL COMMENT '成立日期',
    `registration_and_authority`                varchar(100)        NOT NULL COMMENT '登记机关',
    `supplier_id`                               bigint(20)          NOT NULL COMMENT '供应商公司id',
    `deleted`                                   bit(1)                       DEFAULT 0 comment '删除=1',
    `create_at`                                 datetime                     DEFAULT NULL comment '创建时间',
    `create_by`                                 varchar(100)                 DEFAULT NULL comment '创建者',
    `update_at`                                 datetime                     DEFAULT NULL comment '更新时间',
    `update_by`                                 varchar(100)                 DEFAULT NULL comment '更新者',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_subpackageCode_supplierId` (`subpackage_code`, `supplier_id`)
) ENGINE = InnoDB
  ROW_FORMAT = DYNAMIC
  DEFAULT CHARSET = utf8mb4 COMMENT ='供应商投标人信息';

-- 供应商报名表
CREATE TABLE `supplier_sign_up`
(
    `id`                     bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `buy_item_name`          varchar(200)        NOT NULL COMMENT '采购项目名称',
    `buy_item_code`          char(50)            NOT NULL COMMENT '采购项目编码',
    `subpackage_code`        char(50)            NOT NULL COMMENT '包编码',
    `subpackage_name`        varchar(128)        NOT NULL COMMENT '包名称',
    `permit`                 bit(1)       DEFAULT null COMMENT '是否允许响应: 0-不允许，1-允许',
    `qualified`              bit(1)       DEFAULT null COMMENT '是否合格: 0-不合格，1-合格',
    `bid_opening_sign`       bit(1)       DEFAULT null COMMENT '开标签到，0-未签到，1-已签到',
    `additional_information` json comment '附加信息',
    `supplier_id`            bigint(20) unsigned NOT NULL COMMENT '供应商id',
    `deleted`                bit(1)       DEFAULT 0 comment '删除=1',
    `create_at`              datetime     DEFAULT NULL comment '创建时间',
    `create_by`              varchar(100) DEFAULT NULL comment '创建者',
    `update_at`              datetime     DEFAULT NULL comment '更新时间',
    `update_by`              varchar(100) DEFAULT NULL comment '更新者',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_subpackageCode_supplierId` (`subpackage_code`, `supplier_id`)
) ENGINE = InnoDB
  ROW_FORMAT = DYNAMIC
  DEFAULT CHARSET = utf8mb4 COMMENT ='供应商报名表';

-- 响应文件
CREATE TABLE `supplier_bidding`
(
    `id`                      bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `subpackage_code`         char(50)            NOT NULL COMMENT '包编码',
    `supplier_id`             bigint(20) unsigned NOT NULL COMMENT '供应商id',
    `epc_file`                varchar(100)        not null COMMENT '响应文件压缩包url',
    `pdf_file`                varchar(100)        not null COMMENT '响应文件url',
    `ip`                      bigint(20) unsigned not null COMMENT '响应文件上传时ip',
    `video_url`               varchar(100)                 DEFAULT NULL COMMENT '演示视频url',
    `release_status`          tinyint(1)          NOT NULL DEFAULT '0' COMMENT '0已上传 1已确认',
    `answer_file_key`         varchar(100)                 DEFAULT NULL COMMENT '响应文件密钥',
    `submission_time`         datetime                     DEFAULT NULL COMMENT '响应文件递交盖章时间',
    `bid_opening_decrypt`     bit(1)                       DEFAULT null COMMENT '开标解密，0-未解密，1-解密',
    `bid_opening_record_form` bit(1)                       DEFAULT null COMMENT '开标记录表，0-未签字，1-已签字',
    `app_name`                varchar(100)        NOT NULL COMMENT 'app名称',
    `app_version`             varchar(100)        NOT NULL COMMENT 'app版本',
    `zepc_key`                varchar(100)        not null COMMENT 'app文件验证key',
    `deleted`                 bit(1)                       DEFAULT 0 comment '删除=1',
    `create_at`               datetime                     DEFAULT NULL comment '创建时间',
    `create_by`               varchar(100)                 DEFAULT NULL comment '创建者',
    `update_at`               datetime                     DEFAULT NULL comment '更新时间',
    `update_by`               varchar(100)                 DEFAULT NULL comment '更新者',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_subpackageCode_supplierId` (`subpackage_code`, `supplier_id`)
) ENGINE = InnoDB
  ROW_FORMAT = DYNAMIC
  DEFAULT CHARSET = utf8mb4 COMMENT ='响应文件';

-- 供应商演示视频
CREATE TABLE `supplier_demo_video`
(
    `id`              bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `subpackage_code` char(50)            NOT NULL COMMENT '包编码',
    `supplier_id`     bigint(20) unsigned NOT NULL COMMENT '供应商id',
    `video_url`       varchar(100)        not null COMMENT '演示视频url',
    `deleted`         bit(1)       DEFAULT 0 comment '删除=1',
    `create_at`       datetime     DEFAULT NULL comment '创建时间',
    `create_by`       varchar(100) DEFAULT NULL comment '创建者',
    `update_at`       datetime     DEFAULT NULL comment '更新时间',
    `update_by`       varchar(100) DEFAULT NULL comment '更新者',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_subpackageCode_supplierId` (`subpackage_code`, `supplier_id`)
) ENGINE = InnoDB
  ROW_FORMAT = DYNAMIC
  DEFAULT CHARSET = utf8mb4 COMMENT ='供应商演示视频';

-- 响应文件评审办法
CREATE TABLE `answer_file_evaluation_method`
(
    `id`              bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `subpackage_code` char(50)            NOT NULL COMMENT '包编码[23]',
    `supplier_id`     bigint(20) unsigned NOT NULL COMMENT '供应商id',
    `score_chapter`   varchar(500) DEFAULT NULL COMMENT '响应文件中得分点定位',
    `uuid`            char(50)            NOT NULL COMMENT 'uuid',
    `deleted`         bit(1)       DEFAULT 0 comment '删除=1',
    `create_at`       datetime     DEFAULT NULL comment '创建时间',
    `create_by`       varchar(100) DEFAULT NULL comment '创建者',
    `update_at`       datetime     DEFAULT NULL comment '更新时间',
    `update_by`       varchar(100) DEFAULT NULL comment '更新者',
    PRIMARY KEY (`id`),
    KEY `idx_subpackageCode_supplierId` (`subpackage_code`, `supplier_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='响应文件评审办法';

-- 响应文件章节及章节子内容
CREATE TABLE `answer_file_menu`
(
    `id`              bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `subpackage_code` char(50)            NOT NULL COMMENT '包编码[23]',
    `supplier_id`     bigint(20) unsigned NOT NULL COMMENT '供应商id',
    `pid`             bigint(20) unsigned DEFAULT NULL COMMENT '父主键',
    `order_num`       tinyint(1) unsigned NOT NULL COMMENT '目录顺序',
    `chapter_name`    varchar(100)        NOT NULL COMMENT '目录名字',
    `chapter_context` mediumtext COMMENT '目录内容，压缩并base64转码存储',
    `attach_key`      varchar(5000) COMMENT '目录中文件keys',
    `chapter_type`    tinyint(1) unsigned DEFAULT NULL COMMENT '目录类型',
    `deleted`         bit(1)              DEFAULT 0 comment '删除=1',
    `create_at`       datetime            DEFAULT NULL comment '创建时间',
    `create_by`       varchar(100)        DEFAULT NULL comment '创建者',
    `update_at`       datetime            DEFAULT NULL comment '更新时间',
    `update_by`       varchar(100)        DEFAULT NULL comment '更新者',
    PRIMARY KEY (`id`),
    KEY `idx_subpackageCode_supplierId` (`subpackage_code`, `supplier_id`)
) ENGINE = InnoDB
  ROW_FORMAT = DYNAMIC
  DEFAULT CHARSET = utf8mb4 COMMENT ='响应文件章节及章节子内容';

-- 响应文件评审办法备注
CREATE TABLE `answer_file_evaluation_method_remark`
(
    `id`              bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `subpackage_code` char(50)            NOT NULL COMMENT '包编码',
    `supplier_id`     bigint(20) unsigned NOT NULL COMMENT '供应商id',
    `remark`          text COMMENT '备注内容',
    `deleted`         bit(1)       DEFAULT 0 comment '删除=1',
    `create_at`       datetime     DEFAULT NULL comment '创建时间',
    `create_by`       varchar(100) DEFAULT NULL comment '创建者',
    `update_at`       datetime     DEFAULT NULL comment '更新时间',
    `update_by`       varchar(100) DEFAULT NULL comment '更新者',
    PRIMARY KEY (`id`),
    KEY `idx_subpackageCode_supplierId` (`subpackage_code`, `supplier_id`)
) ENGINE = InnoDB
  ROW_FORMAT = DYNAMIC
  DEFAULT CHARSET = utf8mb4 COMMENT ='响应文件评审办法备注';

-- 供应商报价表
CREATE TABLE `answer_file_quote_form`
(
    `id`              bigint(20)          NOT NULL AUTO_INCREMENT COMMENT '报价表单id',
    `subpackage_code` char(50)            NOT NULL COMMENT '包编码',
    `supplier_id`     bigint(20)          NOT NULL COMMENT '供应商id',
    `round`           tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '轮数，递交响应文件中默认是0轮',
    `body_json`       json                not null COMMENT '报价表单内容',
    `deleted`         bit(1)                       DEFAULT 0 comment '删除=1',
    `create_at`       datetime                     DEFAULT NULL comment '创建时间',
    `create_by`       varchar(100)                 DEFAULT NULL comment '创建者',
    `update_at`       datetime                     DEFAULT NULL comment '更新时间',
    `update_by`       varchar(100)                 DEFAULT NULL comment '更新者',
    PRIMARY KEY (`id`),
    KEY `idx_subpackageCode` (`subpackage_code`),
    KEY `idx_supplierId` (`supplier_id`)
) ENGINE = InnoDB
  ROW_FORMAT = DYNAMIC
  DEFAULT CHARSET = utf8mb4 COMMENT ='供应商报价表';

-- 供应商议价
CREATE TABLE `supplier_bargain`
(
    `id`               bigint(20)          NOT NULL AUTO_INCREMENT COMMENT '报价表单id',
    `subpackage_code`  char(50)            NOT NULL COMMENT '包编码',
    `supplier_id`      bigint(20)          NOT NULL COMMENT '供应商id',
    `round`            tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '轮数',
    `bargain_time`     datetime            NOT NULL COMMENT '报价时间',
    `bargain_document` varchar(100)                 DEFAULT NULL COMMENT '议价文件url',
    `deleted`          bit(1)                       DEFAULT 0 comment '删除=1',
    `create_at`        datetime                     DEFAULT NULL comment '创建时间',
    `create_by`        varchar(100)                 DEFAULT NULL comment '创建者',
    `update_at`        datetime                     DEFAULT NULL comment '更新时间',
    `update_by`        varchar(100)                 DEFAULT NULL comment '更新者',
    PRIMARY KEY (`id`),
    KEY `idx_subpackageCode` (`subpackage_code`),
    KEY `idx_supplierId` (`supplier_id`)
) ENGINE = InnoDB
  ROW_FORMAT = DYNAMIC
  DEFAULT CHARSET = utf8mb4 COMMENT ='供应商议价';

-- 供应商流程节点
CREATE table supplier_process_node
(
    id                    bigint(20)   not null primary key auto_increment comment 'id',
    subpackage_code       char(50)     NOT NULL COMMENT '包编码',
    supplier_id           bigint(20)   NOT NULL COMMENT '供应商id',
    nodes                 json         not null comment '采购方式的完整流程与下一节点的触发点',
    purchase_function_key varchar(128) not null comment '当前节点key',
    `deleted`             bit(1)       DEFAULT 0 comment '删除=1',
    `create_at`           datetime     DEFAULT NULL comment '创建时间',
    `create_by`           varchar(100) DEFAULT NULL comment '创建者',
    `update_at`           datetime     DEFAULT NULL comment '更新时间',
    `update_by`           varchar(100) DEFAULT NULL comment '更新者',
    unique key uk_subpackageCode_supplierId (subpackage_code, supplier_id)
) ENGINE = InnoDB
  ROW_FORMAT = DYNAMIC
  DEFAULT CHARSET = utf8mb4 COMMENT ='供应商流程节点';

-- 审批信息
CREATE TABLE `audit_info`
(
    `id`              bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `buy_item_code`   varchar(64)         NOT NULL COMMENT '采购项目code',
    `subpackage_code` varchar(64)         default NULL COMMENT '标段编号',
    `audit_code`      varchar(64)         NOT NULL COMMENT '审批code',
    `audit_title`     varchar(200)        NOT NULL COMMENT '审批名',
    `audit_type`      varchar(64)         NOT NULL COMMENT '类型',
    `content`         json                NOT NULL COMMENT '内容',
    `remark`          varchar(500)        DEFAULT NULL COMMENT '备注',
    `status`          tinyint(1) unsigned DEFAULT '2' COMMENT '审核状态，0-不同意，1-同意，2-待审批，3-撤回',
    `create_id`       bigint(20) unsigned NOT NULL COMMENT '创建人id',
    `deleted`         bit(1)              DEFAULT b'0' COMMENT '删除=1',
    `create_at`       datetime            NOT NULL COMMENT '创建时间',
    `create_by`       varchar(100)        DEFAULT NULL COMMENT '创建者',
    `update_at`       datetime            DEFAULT NULL COMMENT '更新时间',
    `update_by`       varchar(100)        DEFAULT NULL COMMENT '更新者',
    `create_at_date`  date GENERATED ALWAYS AS (cast(`create_at` as date)) STORED,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `audit_code` (`audit_code`),
    KEY `idx_create_at_date` (`create_at_date`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='审批信息';

-- 审批人
CREATE TABLE `audit_person`
(
    `id`            bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `audit_info_id` bigint(20) unsigned NOT NULL COMMENT '审批信息id',
    `user_id`       bigint(20) unsigned NOT NULL COMMENT '审核人id',
    `user_name`     varchar(100)        NOT NULL COMMENT '审核人姓名',
    `requirement`   varchar(500) DEFAULT NULL COMMENT '要求',
    `atts`          json         DEFAULT NULL COMMENT '多附件',
    `deleted`       bit(1)       DEFAULT b'0' COMMENT '删除=1',
    `create_at`     datetime     DEFAULT NULL COMMENT '创建时间',
    `create_by`     varchar(100) DEFAULT NULL COMMENT '创建者',
    `update_at`     datetime     DEFAULT NULL COMMENT '更新时间',
    `update_by`     varchar(100) DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_audit_info_id` (`audit_info_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='审批人';

-- 审核评论
CREATE TABLE `audit_comment`
(
    `id`              bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `audit_record_id` bigint(20) unsigned NOT NULL COMMENT '审批人审核记录id',
    `user_id`         bigint(20) unsigned NOT NULL COMMENT '用户id',
    `user_name`       varchar(100)        NOT NULL COMMENT '用户名',
    `comment`         varchar(500)        NOT NULL COMMENT '评论',
    `deleted`         bit(1)       DEFAULT b'0' COMMENT '删除=1',
    `create_at`       datetime     DEFAULT NULL COMMENT '创建时间',
    `create_by`       varchar(100) DEFAULT NULL COMMENT '创建者',
    `update_at`       datetime     DEFAULT NULL COMMENT '更新时间',
    `update_by`       varchar(100) DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_audit_record_id` (`audit_record_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='审核评论';

-- 抄送人
CREATE TABLE `audit_recipient`
(
    `id`            bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `audit_info_id` bigint(20) unsigned NOT NULL COMMENT '审批信息id',
    `copy_id`       bigint(20) unsigned NOT NULL COMMENT '抄送人id',
    `copy_name`     varchar(100)        NOT NULL COMMENT '抄送人',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_audit_info_id` (`audit_info_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='抄送人';

-- 审核记录
CREATE TABLE `audit_record`
(
    `id`              bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `audit_person_id` bigint(20) unsigned NOT NULL COMMENT '审批人信息id',
    `status`          tinyint(1) unsigned NOT NULL COMMENT '审核状态，0-不同意，1-同意',
    `remark`          varchar(500) DEFAULT NULL COMMENT '审核备注',
    `deleted`         bit(1)       DEFAULT b'0' COMMENT '删除=1',
    `create_at`       datetime     DEFAULT NULL COMMENT '创建时间',
    `create_by`       varchar(100) DEFAULT NULL COMMENT '创建者',
    `update_at`       datetime     DEFAULT NULL COMMENT '更新时间',
    `update_by`       varchar(100) DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_audit_person_id` (`audit_person_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='审核记录';

-- 审批关系
CREATE TABLE `biz_audit_relation`
(
    `id`              bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `audit_code`      varchar(64)         NOT NULL COMMENT '审批code',
    `audit_type`      varchar(64)         NOT NULL COMMENT '审批类型',
    `buy_item_code`   varchar(64)         NOT NULL COMMENT '采购项目code',
    `user_id`         bigint(20)          not NULL COMMENT '用户id',
    `data`            JSON                not null comment '业务数据',
    `className`       varchar(200)        not null COMMENT '业务数据类名',
    `business_id`     bigint(20)   default null comment '业务id',
    `subpackage_code` varchar(64)  default NULL COMMENT '标段编号',
    `business_type`   varchar(100) default NULL COMMENT '业务类型',
    `create_at`       datetime     default NULL COMMENT '创建时间',
    `create_by`       varchar(100) default NULL COMMENT '创建者',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `audit_code` (`audit_code`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='审批关系';

-- 供应商合同
CREATE TABLE `supplier_contract`
(
    `id`                    bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
    `supplier_id`           bigint(20)          NOT NULL COMMENT '供应商id',
    `supplier_company_name` varchar(100)        NOT NULL COMMENT '供应商公司名',
    `buy_item_name`         varchar(128)        NOT NULL COMMENT '项目名称',
    `buy_item_code`         varchar(64)         NOT NULL COMMENT '项目code',
    `subpackage_code`       varchar(64)         NOT NULL COMMENT '包code',
    `subpackage_name`       varchar(64)         NOT NULL COMMENT '包名字',
    `submission_time`       datetime            DEFAULT NULL COMMENT '模板提交审核时间',
    `audit_time`            datetime            DEFAULT NULL COMMENT '模板采购人审核时间',
    `audit_status`          tinyint(1) unsigned DEFAULT '0' COMMENT '模板，0-完成模板,1-提交至采购人确认,2-采购人确认，3-供应商确认',
    `html_template`         mediumtext          NOT NULL COMMENT '模板',
    `contract_url`          varchar(100)        DEFAULT NULL COMMENT '模板pdf',
    `attachment`            varchar(100)        DEFAULT NULL COMMENT '合同pdf',
    `deleted`               bit(1)              DEFAULT b'0' COMMENT '删除=1',
    `create_at`             datetime            DEFAULT NULL COMMENT '创建时间',
    `create_by`             varchar(100)        DEFAULT NULL COMMENT '创建者',
    `update_at`             datetime            DEFAULT NULL COMMENT '更新时间',
    `update_by`             varchar(100)        DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_subpackageCode_supplierId` (`subpackage_code`, `supplier_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='供应商合同';

-- 委托公告
CREATE TABLE `entrust_bulletin`
(
    `id`                  bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '委托id',
    `create_user_id`      bigint(20)   NOT NULL COMMENT '创建人id',
    `create_user_name`    varchar(100) NOT NULL COMMENT '创建人名称',
    `project_name`        varchar(500) NOT NULL COMMENT '项目名称',
    `bulletin_name`       varchar(500) NOT NULL COMMENT '公告名称',
    `bulletin_type`       varchar(100)          DEFAULT NULL COMMENT '公告类型',
    `bulletin_text`       text COMMENT '委托公告文本',
    `create_time`         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `audit_status`        tinyint(4)   NOT NULL COMMENT '审核状态，0-待审核，1-审核成功，2-审核失败',
    `show_status`         tinyint(4)   NOT NULL COMMENT '展示状态，1-展示，2-隐藏',
    `failure_description` varchar(500)          DEFAULT NULL COMMENT '审核失败时说明',
    `audit_user_id`       bigint(20)            DEFAULT NULL COMMENT '审核人id',
    `audit_user_name`     varchar(100)          DEFAULT NULL COMMENT '审核人名称',
    `audit_time`          datetime              DEFAULT NULL COMMENT '审核时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='委托公告';
