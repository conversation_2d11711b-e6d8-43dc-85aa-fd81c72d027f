```sql
CREATE TABLE `purchase_fjnx_buy_item`
(
    `id`              bigint(11) NOT NULL AUTO_INCREMENT COMMENT '采购项目-福建农信版本主健id',
    `allocate_id`     bigint(11) DEFAULT NULL COMMENT '项目外资料分配之后的主键id',
    `buy_item_code`   varchar(64)  NOT NULL COMMENT '采购项目code',
    `buy_item_name`   varchar(128) NOT NULL COMMENT '采购项目名字',
    `apply_dept`      varchar(64)    DEFAULT NULL COMMENT '申报科室',
    `inner_code`      varchar(64)    DEFAULT NULL COMMENT '采购编号(院方使用)',
    `apply_person`    varchar(64)    DEFAULT NULL COMMENT '申请人',
    `apply_time`      datetime       DEFAULT NULL COMMENT '申报时间',
    `buy_cnt`         int(11) DEFAULT NULL COMMENT '需购数量',
    `buy_fixe_price`  decimal(18, 5) DEFAULT NULL COMMENT '单价限价',
    `buy_total_price` decimal(18, 5) DEFAULT NULL COMMENT '总价限价【18位整数，5位小数】',
    `buy_class`       varchar(64)  NOT NULL COMMENT '项目类型',
    `whether_import`  tinyint(1) DEFAULT NULL COMMENT '是否可采购进口产品[0-否，1-是]',
    `whether_consume` tinyint(1) NOT NULL COMMENT '是否有专机专用或耗材或试剂[0-否, 1-是]',
    `annex_file`      text COMMENT '项目附件',
    `buy_remark`      varchar(128)   DEFAULT NULL COMMENT '备注',
    `deleted`         bit(1)         DEFAULT b'0' COMMENT '删除标志（0代表存在 代表删除)',
    `create_by`       varchar(64)    DEFAULT '' COMMENT '创建者',
    `create_at`       datetime       DEFAULT NULL COMMENT '创建时间',
    `update_by`       varchar(64)    DEFAULT '' COMMENT '更新者',
    `update_at`       datetime       DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `buy_item_code` (`buy_item_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='采购项目-福建农信版本';
```