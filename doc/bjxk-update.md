# 北京胸科医院

## 2024-12-11 上线

### 采购项目表-北京胸科版本

```sql

CREATE TABLE `purchase_bjxk_buy_item`
(
    `id`               bigint(11)   NOT NULL AUTO_INCREMENT COMMENT '采购项目-北京胸科版本主健id',
    `ding_talk_id`     bigint(11) DEFAULT NULL COMMENT '钉钉项目表id',
    `buy_item_code`    varchar(64)  NOT NULL COMMENT '采购项目code',
    `buy_item_name`    varchar(128) NOT NULL COMMENT '采购项目名字',
    `inner_code`       varchar(64)    DEFAULT NULL COMMENT '采购编号(院方使用)',
    `apply`            varchar(32)  NOT NULL COMMENT '申请人',
    `apply_dept`       varchar(32)  NOT NULL COMMENT '申请科室',
    `budget_number`    varchar(100) NOT NULL COMMENT '预算号/课题号',
    `buy_purpose`      varchar(100) NOT NULL COMMENT '采购目的',
    `buy_class`        varchar(100) NOT NULL COMMENT '采购类型：货物/服务',
    `procurement_type` varchar(32)  NOT NULL COMMENT '采购标的',
    `goods_type`       varchar(32)    DEFAULT NULL COMMENT '货物采购类型：医用设备采购/医用设备维修配件及维修服务采购/信息类办公设备及维修配件采购/其他',
    `middle_amount`    varchar(32)    DEFAULT NULL COMMENT '是否超过5万',
    `params_att`       json           DEFAULT NULL COMMENT '详细参数附件',
    `buy_budget`       decimal(18, 5) DEFAULT NULL COMMENT '预算总金额（元）',
    `large_amount`     varchar(32)  NOT NULL COMMENT '是否涉及大额资金（20万元以上）',
    `capital_source`   varchar(32)  NOT NULL COMMENT '资金来源：研究所/医院/工会',
    `budget_type`      varchar(32)  NOT NULL COMMENT '预算类型：财政项目/科研项目/自有资金',
    `buy_person`       varchar(32)    DEFAULT NULL COMMENT '项目/课题负责人',
    `class_num`        varchar(32)    DEFAULT NULL COMMENT '课题号',
    `meeting_content`  json COMMENT '院长办公纪要',
    `buy_remark`       varchar(128)   DEFAULT NULL COMMENT '备注',
    `deleted`          bit(1)         DEFAULT b'0' COMMENT '删除标志（0代表存在 代表删除)',
    `create_by`        varchar(64)    DEFAULT '' COMMENT '创建者',
    `create_at`        datetime       DEFAULT NULL COMMENT '创建时间',
    `update_by`        varchar(64)    DEFAULT '' COMMENT '更新者',
    `update_at`        datetime       DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `buy_item_code` (`buy_item_code`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4 COMMENT ='采购项目-北京胸科版本';
```

### 2024-12-12 以上已发布上线

```sql
create table dingtalk_project_request
(
    `id`                  bigint(11)          NOT NULL primary key AUTO_INCREMENT COMMENT 'id',
    `business_id`         varchar(100) unique not null comment '审批编号',
    `process_instance_id` varchar(100)        not null comment '审批实例id',
    `buy_item_name`       varchar(128)   default null comment '采购项目名称',
    `apply`               varchar(32)    DEFAULT null comment '申请人',
    `apply_dept`          varchar(32)    DEFAULT null comment '申请科室',
    `budget_number`       varchar(100)   DEFAULT null comment '预算号/课题号',
    `buy_purpose`         varchar(100)   DEFAULT null comment '采购目的',
    `buy_class`           varchar(100)   DEFAULT null comment '采购类型：货物/服务',
    `procurement_type`    varchar(32)    DEFAULT null comment '采购标的',
    `goods_type`          varchar(32)    default null comment '货物采购类型：医用设备采购/医用设备维修配件及维修服务采购/信息类办公设备及维修配件采购/其他',
    `details`             json comment '采购明细',
    `params_att`          json comment '详细参数附件',
    `meeting_content`     json COMMENT '院长办公纪要',
    `buy_budget`          decimal(18, 5) DEFAULT NULL COMMENT '预算总金额（元）',
    `large_amount`        varchar(32)    DEFAULT null comment '是否涉及大额资金（20万元以上）',
    `capital_source`      varchar(32)    DEFAULT null comment '资金来源：研究所/医院/工会',
    `budget_type`         varchar(32)    DEFAULT null comment '预算类型：财政项目/科研项目/自有资金',
    `buy_person`          varchar(32)    default null comment '项目/课题负责人',
    `buy_remark`          varchar(255)   default null comment '备注',
    `creator`             varchar(32)         not null comment '创建人',
    `creator_dept`        varchar(32)         not null comment '创建人部门',
    `confirm`             tinyint(1)     default 0 comment '内容审批：0-待审核，1-已确认',
    `status`              tinyint(1)     default 0 comment '项目状态：0-待创建，1-已创建',
    `biz_data`            json                not null comment '审批数据',
    `deleted`             bit(1)         DEFAULT b'0' COMMENT '删除=1',
    `create_at`           datetime       DEFAULT NULL COMMENT '创建时间',
    `create_by`           varchar(100)   DEFAULT NULL COMMENT '创建者',
    `update_at`           datetime       DEFAULT NULL COMMENT '更新时间',
    `update_by`           varchar(100)   DEFAULT NULL COMMENT '更新者'
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
    COMMENT ='钉钉项目申请';

create table dingtalk_failed
(
    `id`                  bigint(11)          NOT NULL primary key AUTO_INCREMENT COMMENT 'id',
    `process_code`        varchar(100)        not null comment '审批模板code',
    `business_id`         varchar(100) unique not null comment '审批编号',
    `process_instance_id` varchar(100)        not null comment '审批实例id',
    `event_data`          json                not null comment '事件数据',
    `retry_count`         tinyint  default 0 comment '重试次数',
    `error_msg`           text comment '失败原因',
    `last_retry_time`     DATETIME default null comment '最后重试时间',
    `create_at`           datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
    COMMENT ='钉钉失败记录表';
```

### 2024-12-20 发布上线

### 2024-12-25 发布上线


### 2024-12-28 新增字段
```sql
ALTER TABLE `dingtalk_project_request`
    ADD COLUMN `middle_amount` varchar(32) DEFAULT NULL COMMENT '是否超过5万' AFTER `buy_budget`;
ALTER TABLE `dingtalk_project_request`
    ADD COLUMN `class_num` varchar(32) DEFAULT NULL COMMENT '课题号' AFTER `buy_person`;
```

### 2024-12-31 发布上线

### 2025-2-17 12：00更新上线

### 2025-2-19 钉钉服务发布

### 2025-2-20 全服务，发布上线

### 2025-3-15 全服务，发布上线

### 2025-5-15 全服务，发布上线