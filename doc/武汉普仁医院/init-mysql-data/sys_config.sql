-- ----------------------------
-- Records of sys_config
-- ----------------------------
INSERT INTO `sys_config` VALUES (1, '主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-blue', 'Y', 'admin', '2022-03-22 18:49:20', 'service', '2024-05-17 15:25:25', '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');
INSERT INTO `sys_config` VALUES (2, '用户管理-账号初始密码', 'sys.user.initPassword', 'Epc16888', 'Y', 'admin', '2022-03-22 18:49:20', 'service', '2022-11-24 11:26:32', '初始化密码 Epc16888');
INSERT INTO `sys_config` VALUES (3, '主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-dark', 'Y', 'admin', '2022-03-22 18:49:20', 'service', '2024-05-17 15:26:10', '深色主题theme-dark，浅色主题theme-light');
INSERT INTO `sys_config` VALUES (4, '账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'true', 'Y', 'admin', '2022-03-22 18:49:20', 'admin', '2022-03-29 19:43:22', '是否开启注册用户功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (11, '客户端阿里云盘链接', 'sys.aliyun.url', 'https://www.aliyundrive.com/s/JCTYLbqeNGf', 'Y', 'service', '2022-09-08 17:29:40', 'service', '2022-11-24 11:26:03', NULL);
INSERT INTO `sys_config` VALUES (12, '客户端百度网盘链接', 'sys.baidu.url', 'https://pan.baidu.com/s/1huS-4W2UfhuXtxjcYGS5Mg?pwd=bd7x', 'Y', 'service', '2022-09-08 17:30:21', 'service', '2022-11-24 11:26:18', NULL);
INSERT INTO `sys_config` VALUES (15, '后台超级管理员adminID', 'sys.admin.userId', '1', 'Y', 'service', '2022-12-02 14:50:35', '', NULL, NULL);
INSERT INTO `sys_config` VALUES (16, '使用科室角色ID', 'sys.useDept.roleId', '112', 'Y', 'service', '2023-03-06 14:17:22', '', NULL, NULL);
INSERT INTO `sys_config` VALUES (18, '供应商角色ID', 'sys.supplier.roleId', '101', 'Y', 'service', '2023-03-07 11:18:11', '', NULL, NULL);
INSERT INTO `sys_config` VALUES (19, '评审专家角色ID', 'sys.expert.roleId', '102', 'Y', 'service', '2023-03-07 11:43:46', '', NULL, NULL);
INSERT INTO `sys_config` VALUES (20, '采购人角色ID', 'sys.purchaser.roleId', '100', 'Y', 'service', '2023-03-11 10:49:07', '', NULL, NULL);
INSERT INTO `sys_config` VALUES (21, '职能科室角色ID', 'sys.funcDept.roleId', '110', 'Y', 'service', '2023-03-11 10:49:20', '', NULL, NULL);
INSERT INTO `sys_config` VALUES (22, '印章横向文本', 'seal.horizontal.content', '招标采购处', 'Y', 'service', '2023-03-14 13:56:21', 'service', '2023-04-04 11:22:01', NULL);
INSERT INTO `sys_config` VALUES (23, '印章下旋数字', 'seal.under.script', '7899088901', 'Y', 'service', '2023-03-14 13:56:40', 'service', '2023-03-15 10:33:48', NULL);
INSERT INTO `sys_config` VALUES (24, '控制开标开始是否校验投标人签到满足3家（1：打开校验，0：关闭校验）', 'bid.opening.start.valid.numberOfCheckIn', '1', 'Y', 'service', '2023-03-15 09:29:15', 'service', '2024-05-15 19:25:01', '（1：打开校验，0：关闭校验）');
INSERT INTO `sys_config` VALUES (25, '是否支持电子签章', 'sys.signature.off', '1', 'Y', 'service', '2023-03-15 16:42:01', 'service', '2024-07-01 08:58:02', '1关闭 ，0开启');
INSERT INTO `sys_config` VALUES (26, '委托项目标段必传文件', 'agent.file.number', '7', 'Y', 'service', '2023-03-17 15:42:04', '', NULL, NULL);
INSERT INTO `sys_config` VALUES (27, '是否允许投标', 'bid.file.permit', '1', 'Y', 'service', '2023-04-07 09:09:42', 'service', '2024-04-25 16:32:10', '是否允许投标: 0-不允许(报名之后。默认不允许投标)，1-允许（报名之后，默认允许投标）');
INSERT INTO `sys_config` VALUES (28, '医院名字', 'system.owner', '请输入医院名字', 'Y', 'service', '2023-04-07 09:10:25', '', NULL, '医院名字');
INSERT INTO `sys_config` VALUES (29, '医院所处城市', 'system.city', '请输入医院所处城市', 'Y', 'service', '2023-04-07 09:10:58', '', NULL, '医院所处城市');
INSERT INTO `sys_config` VALUES (30, '超级管理员角色ID', 'sys.admin.roleId', '1', 'Y', 'service', '2024-02-22 16:40:45', '', NULL, NULL);
INSERT INTO `sys_config` VALUES (31, '管理员查询所有用户列表，排除的角色ID', 'sys.exclude.roleIds', '1,101', 'Y', 'service', '2024-02-22 16:41:27', 'service', '2024-05-16 16:00:00', NULL);
INSERT INTO `sys_config` VALUES (33, '招标办电话', 'sys.purchaser.telephone', '1321000036', 'Y', 'service', '2024-04-24 10:46:34', 'service', '2024-04-25 08:28:26', NULL);
INSERT INTO `sys_config` VALUES (34, '采购人查询所有用户列表，排除的角色ID', 'purchaser.exclude.roleIds', '1,2,101', 'Y', 'service', '2024-04-25 13:59:21', 'service', '2024-05-16 15:59:28', '采购人组织中包含的角色，排查的角色，admin、zbbAdmin、supplier');
INSERT INTO `sys_config` VALUES (35, '网站备案号', 'sys.website.ICP', '鄂ICP备18022772号-1', 'Y', 'service', '2024-04-30 15:32:56', 'service', '2024-06-17 13:58:22', NULL);
INSERT INTO `sys_config` VALUES (37, '智能客服功能开关', 'sys.chatMsg.show', '0', 'Y', 'service', '2024-05-14 14:32:31', 'service', '2024-05-16 14:17:59', '1开，0关');
INSERT INTO `sys_config` VALUES (38, '采购文件属性', 'sys.purchase.attribute', '[   {     \"id\": \"1\",     \"groupName\": \"医疗设备及服务\",     \"children\": [       {         \"id\": \"1\",         \"attribute\": \"封面\",         \"description\": \"计划编号、采购科室、产品名称、品牌型号、注册证号、设备厂家设计使用期限、有无专机专用耗材/试剂，公司名称、联系人姓名及联系方式等信息\"       },       {         \"id\": \"2\",         \"attribute\": \"企业信用承诺书\",         \"description\": \"模板详见《医疗设备院内比选、调研材料目录》附件1\"       },       {         \"id\": \"3\",         \"attribute\": \"产品资质\",         \"description\": \"包括注册证、国际认证等及产品简介，附一份查询注册证时的药监部门网站截图。如无注册证，请上传国药监发布的分类界定文件\"       },       {         \"id\": \"4\",         \"attribute\": \"设备生产厂家对产品的设计使用期限\",         \"description\": \"如说明书、注册证、铭牌等复印件或照片，并提供设备超出使用期限后使用可能存在潜在临床风险及法规问题。如无明确要求则请出具原厂说明文件并加盖原厂及报名公司公章\"       },       {         \"id\": \"5\",         \"attribute\": \"产品铭牌照片\",         \"description\": \"\"       },       {         \"id\": \"6\",         \"attribute\": \"配置清单\",         \"description\": \"报名文件中一份配置，另需单独打印一份，二者须一致；配置清单中不得有价格显示模板详见《医疗设备院内比选、调研材料目录》附件2\"       },       {         \"id\": \"7\",         \"attribute\": \"产品技术参数\",         \"description\": \"\"       },       {         \"id\": \"8\",         \"attribute\": \"产品安装场地等要求\",         \"description\": \"模板详见《医疗设备院内比选、调研材料目录》附件3\"       },       {         \"id\": \"9\",         \"attribute\": \"市场同类同档次产品的性能对比表\",         \"description\": \"\"       },       {         \"id\": \"10\",         \"attribute\": \"生产厂家和代理公司资质及简介\",         \"description\": \"生产厂家及各级代理公司资质均须提供\"       },       {         \"id\": \"11\",         \"attribute\": \"生产厂家授权书\",         \"description\": \"要求报名公司为产品区域代理，不接受鼓楼医院专项授权，经销人员身份证复印件、经销人员在投标公司所缴纳社保证明（3个月以上）\"       },       {         \"id\": \"12\",         \"attribute\": \"售后服务条款\",         \"description\": \"模板详见《医疗设备院内比选、调研材料目录》附件4\"       },       {         \"id\": \"13\",         \"attribute\": \"业绩\",         \"description\": \"其他医院（以三甲医院为主）中标通知书或合同及相应配置（如我院一年内采购过，提供我院采购合同和相应配置）\"       },       {         \"id\": \"14\",         \"attribute\": \"用户名单\",         \"description\": \"用户名单、采购时间及联系人\"       },       {         \"id\": \"15\",         \"attribute\": \"宣传彩页\",         \"description\": \"纸质版需要提供印刷版，打印和复印版无效；pdf版需扫描彩页\"       },       {         \"id\": \"16\",         \"attribute\": \"比选、调研材料真实性及购销廉洁声明\",         \"description\": \"模板详见《医疗设备院内比选、调研材料目录》附件5\"       }     ]   } ]', 'Y', 'service', '2024-06-29 11:51:43', 'service', '2024-06-29 15:23:38', NULL);
INSERT INTO `sys_config` VALUES (39, '发起审批页面选择自定义项', 'sys.approval.attr', '[{\"id\":\"1\",\"groupName\":\"立项审批\",\"children\":[{\"id\":\"1\",\"title\":\"采购项目名称\"},{\"id\":\"2\",\"title\":\"采购方式\"},{\"id\":\"3\",\"title\":\"采购预算\"},{\"id\":\"4\",\"title\":\"使用科室\"},{\"id\":\"5\",\"title\":\"标的类型\"},{\"id\":\"6\",\"title\":\"资金来源\"},{\"id\":\"7\",\"title\":\"项目负责人\"},{\"id\":\"8\",\"title\":\"联系电话\"},{\"id\":\"9\",\"title\":\"组织形式\"},{\"id\":\"10\",\"title\":\"备注\"}]},{\"id\":\"2\",\"groupName\":\"合同审批\",\"children\":[{\"id\":\"1\",\"title\":\"采购项目名称\"},{\"id\":\"2\",\"title\":\"采购方式\"},{\"id\":\"3\",\"title\":\"采购预算\"},{\"id\":\"4\",\"title\":\"使用科室\"},{\"id\":\"5\",\"title\":\"标的类型\"},{\"id\":\"6\",\"title\":\"资金来源\"},{\"id\":\"7\",\"title\":\"项目负责人\"},{\"id\":\"8\",\"title\":\"联系电话\"},{\"id\":\"9\",\"title\":\"组织形式\"},{\"id\":\"10\",\"title\":\"备注\"}]}]', 'Y', 'service', '2024-07-05 10:26:13', 'service', '2024-07-05 10:26:40', NULL);
INSERT INTO `sys_config` VALUES (40, 'ip地址显示开关', 'sys.ip.show', '1', 'Y', 'service', '2024-07-09 10:02:37', 'service', '2024-07-09 11:13:43', '参数键值：0或1，开1，关0');

