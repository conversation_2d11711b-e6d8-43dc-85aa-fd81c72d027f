/*
 Navicat Premium Data Transfer

 Source Server         : ************
 Source Server Type    : MySQL
 Source Server Version : 50712
 Source Host           : ************:3307
 Source Schema         : epcos-smart-system

 Target Server Type    : MySQL
 Target Server Version : 50712
 File Encoding         : 65001

 Date: 25/07/2024 15:28:19
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for feedback
-- ----------------------------
DROP TABLE IF EXISTS `feedback`;
CREATE TABLE `feedback`
(
    `id`             bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
    `content`        text                NULL COMMENT '内容',
    `img_key`        varchar(100)        NULL     DEFAULT NULL COMMENT '图片key',
    `contact`        char(12)            NOT NULL COMMENT '联系方式，座机：^d{3}-d{8}$|^d{4}-d{7}$ 手机：^1d{10}$',
    `platform`       varchar(100)        NOT NULL COMMENT '平台',
    `handler_status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '处理状态：0-未处理，1-已处理',
    `handler`        varchar(100)        NULL     DEFAULT NULL COMMENT '处理人',
    `question_type`  tinyint(1) UNSIGNED NULL     DEFAULT NULL COMMENT '问题类型：0-非系统问题无需处理，1-用户操作问题，2-系统需要优化问题',
    `remark`         text                NULL COMMENT '备注',
    `create_time`    datetime(0)         NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    `update_time`    datetime(0)         NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最后修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '意见反馈'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for process_node
-- ----------------------------
DROP TABLE IF EXISTS `process_node`;
CREATE TABLE `process_node`
(
    `purchase_function_id` bigint(11) UNSIGNED NOT NULL COMMENT '采购流程节点id',
    `purchase_method_id`   bigint(11) UNSIGNED NOT NULL COMMENT '采购方式id',
    `ranking`              int(10) UNSIGNED    NOT NULL COMMENT '排名',
    `trigger_point`        varchar(200)        NULL DEFAULT NULL COMMENT '触发点',
    INDEX `fk_purchase_function_id` (`purchase_function_id`) USING BTREE,
    INDEX `fk_purchase_method_id` (`purchase_method_id`) USING BTREE,
    CONSTRAINT `fk_purchase_function` FOREIGN KEY (`purchase_function_id`) REFERENCES `purchase_function` (`purchase_function_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
    CONSTRAINT `fk_purchase_method` FOREIGN KEY (`purchase_method_id`) REFERENCES `purchase_method` (`purchase_method_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '流程节点顺序'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_announcement
-- ----------------------------
DROP TABLE IF EXISTS `purchase_announcement`;
CREATE TABLE `purchase_announcement`
(
    `announcement_id`      bigint(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `purchase_method_code` varchar(32)         NOT NULL COMMENT '采购方式code',
    `tag`                  varchar(32)         NULL DEFAULT NULL COMMENT '公告标签',
    `an_name`              varchar(60)         NOT NULL COMMENT '公告名称',
    `an_type`              varchar(100)        NOT NULL COMMENT '公告类型',
    PRIMARY KEY (`announcement_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '公告模板'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_function
-- ----------------------------
DROP TABLE IF EXISTS `purchase_function`;
CREATE TABLE `purchase_function`
(
    `purchase_function_id`   bigint(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `parent_id`              bigint(11)          NULL     DEFAULT NULL COMMENT '父功能id',
    `parent_module`          varchar(64)         NULL     DEFAULT NULL COMMENT '上级模块名',
    `purchase_function_name` varchar(128)        NOT NULL COMMENT '采购功能名称【页面对应键值——中文】',
    `purchase_function_key`  varchar(128)        NOT NULL COMMENT '采购功能英文名称【页面对应键名——英文，不可重复】',
    `purchase_function_code` varchar(32)         NOT NULL COMMENT '采购功能编码',
    `sort`                   int(9)              NOT NULL COMMENT '显示排序',
    `whether_node`           char(1)             NOT NULL DEFAULT '0' COMMENT '是否属于流程节点[0-否，1-是]',
    `belong_role`            char(1)             NOT NULL COMMENT '所属角色[1-采购人，2-供应商，3-专家]',
    `whether_must`           char(1)             NOT NULL DEFAULT '0' COMMENT '是否必须[0-否，1-是]',
    `belong_level`           char(1)             NOT NULL COMMENT '所属级别[1-模块，2-页面，3-功能]',
    `whether_disabled`       char(1)             NOT NULL DEFAULT '0' COMMENT '是否禁用[0-否，1-是]',
    `remark`                 varchar(256)        NOT NULL DEFAULT '' COMMENT '备注',
    `create_time`            datetime(0)         NULL     DEFAULT NULL COMMENT '创建时间',
    `update_time`            datetime(0)         NULL     DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`purchase_function_id`) USING BTREE,
    UNIQUE INDEX `purchase_function_code` (`purchase_function_code`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '采购功能点'
  ROW_FORMAT = Compressed;

-- ----------------------------
-- Table structure for purchase_method
-- ----------------------------
DROP TABLE IF EXISTS `purchase_method`;
CREATE TABLE `purchase_method`
(
    `purchase_method_id`     bigint(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `purchase_method_name`   varchar(128)        NOT NULL COMMENT '采购方式名称',
    `purchase_method_type`   varchar(128)        NOT NULL COMMENT '采购方式类型',
    `purchase_method_code`   varchar(32)         NOT NULL COMMENT '采购方式编码',
    `status`                 char(1)             NOT NULL DEFAULT '0' COMMENT '状态[0-关，1-开]',
    `purchase_method_json`   varchar(32)         NOT NULL DEFAULT '' COMMENT '采购方式json',
    `purchase_bulletin_json` varchar(32)         NOT NULL DEFAULT '' COMMENT '采购公告json',
    `remark`                 varchar(256)        NOT NULL DEFAULT '' COMMENT '备注',
    `create_time`            datetime(0)         NULL     DEFAULT NULL COMMENT '创建时间',
    `update_time`            datetime(0)         NULL     DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`purchase_method_id`) USING BTREE,
    UNIQUE INDEX `purchase_method_code` (`purchase_method_code`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '采购方式表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_method_function
-- ----------------------------
DROP TABLE IF EXISTS `purchase_method_function`;
CREATE TABLE `purchase_method_function`
(
    `purchase_method_function_id` bigint(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `purchase_method_id`          bigint(11)          NOT NULL COMMENT '采购方式表主键id',
    `purchase_function_id`        bigint(11)          NOT NULL COMMENT '采购功能表主键id',
    `create_time`                 datetime(0)         NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`                 datetime(0)         NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`purchase_method_function_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '采购方式_采购功能关联表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_personalized_dictionary
-- ----------------------------
DROP TABLE IF EXISTS `purchase_personalized_dictionary`;
CREATE TABLE `purchase_personalized_dictionary`
(
    `purchase_personalized_dictionary_id` bigint(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `purchase_method_code`                varchar(32)         NOT NULL COMMENT '采购方式编码',
    `key_name`                            varchar(64)         NOT NULL COMMENT '英文名称',
    `value_name`                          varchar(64)         NOT NULL COMMENT '中文名称',
    `create_time`                         datetime(0)         NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`                         datetime(0)         NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`purchase_personalized_dictionary_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '采购方式下的名词对照表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_tender_info
-- ----------------------------
DROP TABLE IF EXISTS `purchase_tender_info`;
CREATE TABLE `purchase_tender_info`
(
    `id`                                        bigint(11)     NOT NULL AUTO_INCREMENT COMMENT 'id',
    `org_code`                                  varchar(64)    NOT NULL COMMENT '组织编码',
    `tenderer_code_type`                        char(2)        NOT NULL DEFAULT '97' COMMENT '(默认97:统一信用代码)招标人代码类型:参看3.19主体机构代码类型',
    `tenderer_code`                             char(18)       NOT NULL COMMENT '(公司统一信用代码18位)招标人代码:参看3.20主体机构代码',
    `tenderer_name`                             varchar(100)   NOT NULL COMMENT '(公司名字)招标人名称:',
    `artificial_person`                         char(10)       NOT NULL COMMENT '法人代表/负责人姓名:',
    `artificial_person_code_type`               char(2)        NOT NULL DEFAULT '01' COMMENT '(01:身份证)法人代表/负责人证件类型:参看3.19主体机构代码类型',
    `artificial_person_code`                    char(18)       NOT NULL COMMENT '(身份证号码)法人代表/负责人身份证件号:参看3.20主体机构代码',
    `contact_number`                            char(12)       NULL     DEFAULT NULL COMMENT '机构联系电话: 座机或手机',
    `country_region`                            char(3)        NULL     DEFAULT NULL COMMENT '156-中国,(中国)国别/地区:采用GB/T 2659-2000 中的3位数字码',
    `unit_nature`                               char(25)       NULL     DEFAULT NULL COMMENT '(3位数字码)单位性质:GB/T 12402《经济类型分类与代码》。',
    `region_code`                               char(6)        NULL     DEFAULT NULL COMMENT '(char6)行政区域代码:采用GB/T 2260-2007中的市级代码',
    `region_code_detail`                        varchar(125)   NULL     DEFAULT NULL COMMENT '公司详细地址',
    `industry_code`                             char(30)       NULL     DEFAULT NULL COMMENT '(A01)行业代码:采用GB/T 4754《国民经济行业分类》中的门类和大类',
    `credit_rate`                               char(10)       NULL     DEFAULT NULL COMMENT '资信等级:',
    `opening_bank`                              char(30)       NULL     DEFAULT NULL COMMENT '开户银行:',
    `basic_account`                             char(30)       NULL     DEFAULT NULL COMMENT '基本账户账号:',
    `reg_capital`                               decimal(18, 2) NULL     DEFAULT NULL COMMENT '注册资本:',
    `reg_cap_currency`                          char(3)        NULL     DEFAULT NULL COMMENT '注册资本币种:采用GB/T 12406-2008《表示货币和资金的代码》的数字码',
    `reg_unit`                                  char(1)        NULL     DEFAULT NULL COMMENT '(1:元,2:万元)注册资本单位:《电子招投标系统技术规范》附录B.3.25价格单位代码',
    `info_reporter_name`                        char(10)       NULL     DEFAULT NULL COMMENT '信息申报责任人姓名:',
    `info_reporter_code_type`                   char(2)        NULL     DEFAULT NULL COMMENT '(01:身份证)信息申报责任人证件类型:参看3.19主体机构代码类型',
    `info_reporter_code`                        char(18)       NULL     DEFAULT NULL COMMENT '(身份证号码)信息申报责任人证件号码:参看3.20主体机构代码',
    `info_reporter_contact_number`              char(11)       NULL     DEFAULT NULL COMMENT '信息申报责任人联系电话:信息申报责任人的电话请填写手机号',
    `contact_address`                           varchar(125)   NULL     DEFAULT NULL COMMENT '联系地址:',
    `zip_code`                                  char(6)        NULL     DEFAULT NULL COMMENT '邮政编码:',
    `email`                                     varchar(100)   NULL     DEFAULT NULL COMMENT '电子邮箱:',
    `business_license`                          mediumtext     NULL COMMENT '营业执照或组织机构代码证件扫描件(2m以内) base64',
    `legal_representative_identity_certificate` mediumtext     NULL COMMENT '法定代表人身份证明扫描件(2m以内) base64(非必须)',
    `user_id`                                   bigint(11)     NOT NULL COMMENT '创建人id',
    `company_id`                                bigint(11)     NOT NULL COMMENT '公司id',
    `platform_status`                           tinyint(1)     NOT NULL DEFAULT 0 COMMENT '0-等待平台进行审核，1-审核失败，2-审核通过',
    `verify_time`                               datetime(0)    NULL     DEFAULT NULL COMMENT '交易平台数据验证时间: ',
    `platform_code`                             char(11)       NULL     DEFAULT NULL COMMENT '交易平台标识码:《电子招投标系统技术规范》附录B.3.1交易平台标识代码； ',
    `platform_name`                             varchar(100)   NULL     DEFAULT NULL COMMENT '交易平台名称: ',
    `examiner_name`                             char(10)       NULL     DEFAULT NULL COMMENT '交易平台数据验证责任人姓名:S由交易平台人员在国家公共服务平台审核时形成',
    `examiner_code_type`                        char(2)        NULL     DEFAULT NULL COMMENT '01-身份证，交易平台数据验证责任人证件类型:参看3.19主体机构代码类型由交易平台人员在国家公共服务平台审核时形成 ',
    `examiner_code`                             char(18)       NULL     DEFAULT NULL COMMENT '420102197710231419 交易平台数据验证责任人证件号码:参看3.20主体机构代码由交易平台人员在国家公共服务平台审核时形成 ',
    `deleted`                                   bit(1)         NULL     DEFAULT b'0' COMMENT '删除标志（false代表存在 true代表删除)',
    `create_by`                                 varchar(64)    NULL     DEFAULT '' COMMENT '创建者',
    `create_time`                               datetime(0)    NULL     DEFAULT NULL COMMENT '创建时间',
    `update_by`                                 varchar(64)    NULL     DEFAULT '' COMMENT '更新者',
    `update_time`                               datetime(0)    NULL     DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '招标人信息'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_time_node
-- ----------------------------
DROP TABLE IF EXISTS `purchase_time_node`;
CREATE TABLE `purchase_time_node`
(
    `purchase_time_node_id` bigint(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `purchase_method_code`  varchar(32)         NOT NULL COMMENT '采购方式编码',
    `time_key_name`         varchar(64)         NOT NULL COMMENT '公告时间英文名称',
    `create_time`           datetime(0)         NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`           datetime(0)         NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`purchase_time_node_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '采购方式对应的公告时间关系表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for seal
-- ----------------------------
DROP TABLE IF EXISTS `seal`;
CREATE TABLE `seal`
(
    `seal_id`       bigint(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
    `only_sign`     varbinary(64)       NOT NULL COMMENT '唯一标识',
    `psn_seal_id`   varchar(60)         NULL DEFAULT NULL COMMENT 'e签宝个人印章id',
    `psn_seal_blob` blob                NULL COMMENT '个人印章',
    `org_seal_id`   varchar(60)         NULL DEFAULT NULL COMMENT 'e签宝企业印章id',
    `org_seal_blob` blob                NULL COMMENT '企业印章',
    PRIMARY KEY (`seal_id`) USING BTREE,
    UNIQUE INDEX `user_id` (`only_sign`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '用户印章'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for supplier_company
-- ----------------------------
DROP TABLE IF EXISTS `supplier_company`;
CREATE TABLE `supplier_company`
(
    `id`                                        bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
    `bidder_code_type`                          char(2)             NOT NULL DEFAULT '97' COMMENT '供应商代码类型,97：公司',
    `lic_number`                                char(18)            NOT NULL COMMENT '统一信用代码，营业执照号码: ',
    `bidder_name`                               varchar(100)        NOT NULL COMMENT '供应商公司名称 [1,100]',
    `contact_number`                            varchar(20)         NOT NULL COMMENT '法定代表人联系方式',
    `contact_address`                           varchar(125)        NOT NULL COMMENT '联系地址: 必须加上省市区加详细地址',
    `region_code`                               char(6)             NOT NULL COMMENT '6位数字码，行政区域代码:采用GB/T 2260-2007中的市级代码 ',
    `opening_bank`                              varchar(30)         NOT NULL COMMENT '开户银行: ',
    `basic_account`                             varchar(30)         NOT NULL COMMENT '基本账户账号: ',
    `industry_code`                             varchar(30)         NULL     DEFAULT NULL COMMENT '例：E47（建筑业，房屋建筑业），行业代码:采用GB/T 4754《国民经济行业分类》中的门类和大类 ',
    `scope_of_supply`                           varchar(200)        NULL     DEFAULT NULL COMMENT '供货范围',
    `unit_nature`                               varchar(100)        NOT NULL COMMENT '生产厂家，产品代理商',
    `business_license`                          varchar(100)        NOT NULL COMMENT '营业执照或组织机构代码证件扫描件',
    `legal_representative_identity_certificate` varchar(100)        NOT NULL COMMENT '法定代表人身份证明扫描件',
    `whether_micro_enterprises`                 tinyint(1) UNSIGNED NULL     DEFAULT NULL COMMENT '是否中小微企业，0：不是，1：是',
    `micro_enterprises`                         varchar(100)        NULL     DEFAULT NULL COMMENT '中小微企业文件',
    `email`                                     varchar(100)        NULL     DEFAULT NULL COMMENT '电子邮箱',
    `power_of_attorney`                         varchar(100)        NULL     DEFAULT NULL COMMENT '投标授权书',
    `certificate_code`                          varchar(50)         NOT NULL COMMENT '法定代表人证件号码',
    `certificate_name`                          varchar(50)         NOT NULL COMMENT '法定代表人名称',
    `info_reporter_name`                        varchar(50)         NULL     DEFAULT NULL COMMENT '投标责任人姓名: ',
    `info_reporter_code`                        varchar(50)         NULL     DEFAULT NULL COMMENT '投标责任人证件号码',
    `info_reporter_contact_number`              varchar(20)         NULL     DEFAULT NULL COMMENT '投标责任人联系电话',
    `bid_type`                                  varchar(100)        NOT NULL COMMENT '投标类型',
    `registered_capital`                        varchar(100)        NOT NULL COMMENT '注册资本',
    `operating_period`                          varchar(100)        NOT NULL COMMENT '营业期限',
    `date_of_establishment`                     varchar(100)        NOT NULL COMMENT '成立日期',
    `registration_and_authority`                varchar(100)        NOT NULL COMMENT '登记机关',
    `user_id`                                   bigint(20)          NOT NULL COMMENT '用户id',
    `create_at`                                 datetime(6)         NULL     DEFAULT NULL COMMENT '创建时间',
    `create_by`                                 varchar(100)        NULL     DEFAULT NULL COMMENT '创建者',
    `update_at`                                 datetime(6)         NULL     DEFAULT NULL COMMENT '更新时间',
    `update_by`                                 varchar(100)        NULL     DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_userId` (`user_id`) USING BTREE,
    UNIQUE INDEX `uk_licNumber` (`lic_number`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '供应商公司信息'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config`
(
    `config_id`    int(5)       NOT NULL AUTO_INCREMENT COMMENT '参数主键',
    `config_name`  varchar(100) NULL DEFAULT '' COMMENT '参数名称',
    `config_key`   varchar(100) NULL DEFAULT '' COMMENT '参数键名',
    `config_value` text         NULL COMMENT '参数键值',
    `config_type`  char(1)      NULL DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
    `create_by`    varchar(64)  NULL DEFAULT '' COMMENT '创建者',
    `create_time`  datetime(0)  NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`    varchar(64)  NULL DEFAULT '' COMMENT '更新者',
    `update_time`  datetime(0)  NULL DEFAULT NULL COMMENT '更新时间',
    `remark`       varchar(500) NULL DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`config_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '参数配置表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept`
(
    `dept_id`     bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '部门id',
    `parent_id`   bigint(20)  NULL DEFAULT 0 COMMENT '父部门id',
    `ancestors`   varchar(50) NULL DEFAULT '' COMMENT '祖级列表',
    `dept_name`   varchar(30) NULL DEFAULT '' COMMENT '部门名称',
    `order_num`   int(4)      NULL DEFAULT 0 COMMENT '显示顺序',
    `leader`      varchar(20) NULL DEFAULT NULL COMMENT '负责人',
    `phone`       varchar(11) NULL DEFAULT NULL COMMENT '联系电话',
    `email`       varchar(50) NULL DEFAULT NULL COMMENT '邮箱',
    `status`      char(1)     NULL DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
    `del_flag`    char(1)     NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    `create_by`   varchar(64) NULL DEFAULT '' COMMENT '创建者',
    `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`   varchar(64) NULL DEFAULT '' COMMENT '更新者',
    `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`dept_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '部门表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data`
(
    `dict_code`   bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '字典编码',
    `dict_sort`   int(4)       NULL DEFAULT 0 COMMENT '字典排序',
    `dict_label`  varchar(100) NULL DEFAULT '' COMMENT '字典标签',
    `dict_value`  varchar(100) NULL DEFAULT '' COMMENT '字典键值',
    `dict_type`   varchar(100) NULL DEFAULT '' COMMENT '字典类型',
    `css_class`   varchar(100) NULL DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
    `list_class`  varchar(100) NULL DEFAULT NULL COMMENT '表格回显样式',
    `is_default`  char(1)      NULL DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
    `status`      char(1)      NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
    `create_by`   varchar(64)  NULL DEFAULT '' COMMENT '创建者',
    `create_time` datetime(0)  NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`   varchar(64)  NULL DEFAULT '' COMMENT '更新者',
    `update_time` datetime(0)  NULL DEFAULT NULL COMMENT '更新时间',
    `remark`      varchar(500) NULL DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`dict_code`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '字典数据表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`
(
    `dict_id`     bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '字典主键',
    `dict_name`   varchar(100) NULL DEFAULT '' COMMENT '字典名称',
    `dict_type`   varchar(100) NULL DEFAULT '' COMMENT '字典类型',
    `status`      char(1)      NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
    `create_by`   varchar(64)  NULL DEFAULT '' COMMENT '创建者',
    `create_time` datetime(0)  NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`   varchar(64)  NULL DEFAULT '' COMMENT '更新者',
    `update_time` datetime(0)  NULL DEFAULT NULL COMMENT '更新时间',
    `remark`      varchar(500) NULL DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`dict_id`) USING BTREE,
    UNIQUE INDEX `dict_type` (`dict_type`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '字典类型表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_expert_info
-- ----------------------------
DROP TABLE IF EXISTS `sys_expert_info`;
CREATE TABLE `sys_expert_info`
(
    `id`             bigint(20)          NOT NULL AUTO_INCREMENT COMMENT 'id',
    `user_id`        bigint(20)          NOT NULL COMMENT '用户id',
    `region`         varchar(100)        NOT NULL COMMENT '地区',
    `id_photo`       varchar(100)        NULL DEFAULT NULL COMMENT '身份证照片',
    `title_photo`    varchar(100)        NULL DEFAULT NULL COMMENT '职称证照片',
    `job_photo`      varchar(100)        NULL DEFAULT NULL COMMENT '职业资格证书照片',
    `platformStatus` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '待完善0，待审核1，审核通过2，审核拒绝3',
    `reason`         varchar(200)        NULL DEFAULT NULL COMMENT '审核不通过理由',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `user_id` (`user_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '注册专家信息'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS `sys_job`;
CREATE TABLE `sys_job`
(
    `job_id`          bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '任务ID',
    `job_name`        varchar(64)  NOT NULL DEFAULT '' COMMENT '任务名称',
    `job_group`       varchar(64)  NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
    `invoke_target`   varchar(500) NOT NULL COMMENT '调用目标字符串',
    `cron_expression` varchar(255) NULL     DEFAULT '' COMMENT 'cron执行表达式',
    `misfire_policy`  varchar(20)  NULL     DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
    `concurrent`      char(1)      NULL     DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
    `status`          char(1)      NULL     DEFAULT '0' COMMENT '状态（0正常 1暂停）',
    `create_by`       varchar(64)  NULL     DEFAULT '' COMMENT '创建者',
    `create_time`     datetime(0)  NULL     DEFAULT NULL COMMENT '创建时间',
    `update_by`       varchar(64)  NULL     DEFAULT '' COMMENT '更新者',
    `update_time`     datetime(0)  NULL     DEFAULT NULL COMMENT '更新时间',
    `remark`          varchar(500) NULL     DEFAULT '' COMMENT '备注信息',
    PRIMARY KEY (`job_id`, `job_name`, `job_group`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '定时任务调度表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_job_log`;
CREATE TABLE `sys_job_log`
(
    `job_log_id`     bigint(20)    NOT NULL AUTO_INCREMENT COMMENT '任务日志ID',
    `job_name`       varchar(64)   NOT NULL COMMENT '任务名称',
    `job_group`      varchar(64)   NOT NULL COMMENT '任务组名',
    `invoke_target`  varchar(500)  NOT NULL COMMENT '调用目标字符串',
    `job_message`    varchar(500)  NULL DEFAULT NULL COMMENT '日志信息',
    `status`         char(1)       NULL DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
    `exception_info` varchar(2000) NULL DEFAULT '' COMMENT '异常信息',
    `create_time`    datetime(0)   NULL DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`job_log_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '定时任务调度日志表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_logininfor
-- ----------------------------
DROP TABLE IF EXISTS `sys_logininfor`;
CREATE TABLE `sys_logininfor`
(
    `info_id`     bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '访问ID',
    `user_name`   varchar(50)  NULL DEFAULT '' COMMENT '用户账号',
    `ipaddr`      varchar(128) NULL DEFAULT '' COMMENT '登录IP地址',
    `status`      char(1)      NULL DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
    `msg`         varchar(255) NULL DEFAULT '' COMMENT '提示信息',
    `access_time` datetime(0)  NULL DEFAULT NULL COMMENT '访问时间',
    PRIMARY KEY (`info_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '系统访问记录'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`
(
    `menu_id`     bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
    `menu_name`   varchar(50)  NOT NULL COMMENT '菜单名称',
    `parent_id`   bigint(20)   NULL DEFAULT 0 COMMENT '父菜单ID',
    `order_num`   int(4)       NULL DEFAULT 0 COMMENT '显示顺序',
    `path`        varchar(200) NULL DEFAULT '' COMMENT '路由地址',
    `component`   varchar(255) NULL DEFAULT NULL COMMENT '组件路径',
    `query`       varchar(255) NULL DEFAULT NULL COMMENT '路由参数',
    `is_frame`    int(1)       NULL DEFAULT 1 COMMENT '是否为外链（0是 1否）',
    `is_cache`    int(1)       NULL DEFAULT 0 COMMENT '是否缓存（0缓存 1不缓存）',
    `menu_type`   char(1)      NULL DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
    `visible`     char(1)      NULL DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
    `status`      char(1)      NULL DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
    `perms`       varchar(100) NULL DEFAULT NULL COMMENT '权限标识',
    `icon`        varchar(100) NULL DEFAULT '#' COMMENT '菜单图标',
    `create_by`   varchar(64)  NULL DEFAULT '' COMMENT '创建者',
    `create_time` datetime(0)  NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`   varchar(64)  NULL DEFAULT '' COMMENT '更新者',
    `update_time` datetime(0)  NULL DEFAULT NULL COMMENT '更新时间',
    `remark`      varchar(500) NULL DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '菜单权限表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice`;
CREATE TABLE `sys_notice`
(
    `notice_id`      int(4)       NOT NULL AUTO_INCREMENT COMMENT '公告ID',
    `notice_title`   varchar(50)  NOT NULL COMMENT '公告标题',
    `notice_type`    char(1)      NOT NULL COMMENT '公告类型（1通知 2公告）',
    `notice_content` longblob     NULL COMMENT '公告内容',
    `status`         char(1)      NULL DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
    `create_by`      varchar(64)  NULL DEFAULT '' COMMENT '创建者',
    `create_time`    datetime(0)  NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`      varchar(64)  NULL DEFAULT '' COMMENT '更新者',
    `update_time`    datetime(0)  NULL DEFAULT NULL COMMENT '更新时间',
    `remark`         varchar(255) NULL DEFAULT NULL COMMENT '备注',
    `files`          text         NULL COMMENT '文件名与文件key',
    PRIMARY KEY (`notice_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '通知公告表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_oper_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log`
(
    `oper_id`        bigint(20)    NOT NULL AUTO_INCREMENT COMMENT '日志主键',
    `title`          varchar(50)   NULL DEFAULT '' COMMENT '模块标题',
    `business_type`  int(2)        NULL DEFAULT 0 COMMENT '业务类型（0其它 1新增 2修改 3删除）',
    `method`         varchar(200)  NULL DEFAULT '' COMMENT '方法名称',
    `request_method` varchar(10)   NULL DEFAULT '' COMMENT '请求方式',
    `operator_type`  int(1)        NULL DEFAULT 0 COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
    `oper_name`      varchar(50)   NULL DEFAULT '' COMMENT '操作人员',
    `dept_name`      varchar(50)   NULL DEFAULT '' COMMENT '部门名称',
    `oper_url`       varchar(255)  NULL DEFAULT '' COMMENT '请求URL',
    `oper_ip`        varchar(128)  NULL DEFAULT '' COMMENT '主机地址',
    `oper_location`  varchar(255)  NULL DEFAULT '' COMMENT '操作地点',
    `oper_param`     varchar(2000) NULL DEFAULT '' COMMENT '请求参数',
    `json_result`    varchar(2000) NULL DEFAULT '' COMMENT '返回参数',
    `status`         int(1)        NULL DEFAULT 0 COMMENT '操作状态（0正常 1异常）',
    `error_msg`      varchar(2000) NULL DEFAULT '' COMMENT '错误消息',
    `oper_time`      datetime(0)   NULL DEFAULT NULL COMMENT '操作时间',
    PRIMARY KEY (`oper_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '操作日志记录'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_organize
-- ----------------------------
DROP TABLE IF EXISTS `sys_organize`;
CREATE TABLE `sys_organize`
(
    `organize_id` bigint(20)    NOT NULL AUTO_INCREMENT COMMENT '组织id',
    `parent_id`   bigint(20)    NULL DEFAULT 0 COMMENT '父组织id',
    `org_code`    varchar(64)   NULL DEFAULT '0' COMMENT '组织编码',
    `ancestors`   varchar(50)   NULL DEFAULT '' COMMENT '祖级列表',
    `org_name`    varchar(30)   NULL DEFAULT '' COMMENT '组织名称',
    `order_num`   int(4)        NULL DEFAULT 0 COMMENT '显示顺序',
    `status`      char(1)       NULL DEFAULT '0' COMMENT '组织状态（0正常 1停用）',
    `psn_id`      varchar(60)   NULL DEFAULT NULL COMMENT '组织法人id',
    `org_id`      varbinary(60) NULL DEFAULT NULL COMMENT '组织企业id',
    `del_flag`    char(1)       NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    `create_by`   varchar(64)   NULL DEFAULT '' COMMENT '创建者',
    `create_time` datetime(0)   NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`   varchar(64)   NULL DEFAULT '' COMMENT '更新者',
    `update_time` datetime(0)   NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`organize_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '组织表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_post`;
CREATE TABLE `sys_post`
(
    `post_id`     bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
    `post_code`   varchar(64)  NOT NULL COMMENT '岗位编码',
    `post_name`   varchar(50)  NOT NULL COMMENT '岗位名称',
    `post_sort`   int(4)       NOT NULL COMMENT '显示顺序',
    `status`      char(1)      NOT NULL COMMENT '状态（0正常 1停用）',
    `create_by`   varchar(64)  NULL DEFAULT '' COMMENT '创建者',
    `create_time` datetime(0)  NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`   varchar(64)  NULL DEFAULT '' COMMENT '更新者',
    `update_time` datetime(0)  NULL DEFAULT NULL COMMENT '更新时间',
    `remark`      varchar(500) NULL DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`post_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '岗位信息表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`
(
    `role_id`             bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '角色ID',
    `role_name`           varchar(30)  NOT NULL COMMENT '角色名称',
    `role_key`            varchar(100) NOT NULL COMMENT '角色权限字符串',
    `role_sort`           int(4)       NOT NULL COMMENT '显示顺序',
    `data_scope`          char(1)      NULL DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
    `menu_check_strictly` tinyint(1)   NULL DEFAULT 1 COMMENT '菜单树选择项是否关联显示',
    `dept_check_strictly` tinyint(1)   NULL DEFAULT 1 COMMENT '部门树选择项是否关联显示',
    `status`              char(1)      NOT NULL COMMENT '角色状态（0正常 1停用）',
    `del_flag`            char(1)      NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    `create_by`           varchar(64)  NULL DEFAULT '' COMMENT '创建者',
    `create_time`         datetime(0)  NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`           varchar(64)  NULL DEFAULT '' COMMENT '更新者',
    `update_time`         datetime(0)  NULL DEFAULT NULL COMMENT '更新时间',
    `remark`              varchar(500) NULL DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '角色信息表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_dept`;
CREATE TABLE `sys_role_dept`
(
    `role_id` bigint(20) NOT NULL COMMENT '角色ID',
    `dept_id` bigint(20) NOT NULL COMMENT '部门ID',
    PRIMARY KEY (`role_id`, `dept_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '角色和部门关联表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`
(
    `role_id` bigint(20) NOT NULL COMMENT '角色ID',
    `menu_id` bigint(20) NOT NULL COMMENT '菜单ID',
    PRIMARY KEY (`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '角色和菜单关联表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`
(
    `user_id`         bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `dept_id`         bigint(20)   NULL DEFAULT NULL COMMENT '部门ID',
    `user_name`       varchar(30)  NOT NULL COMMENT '用户账号',
    `nick_name`       varchar(100) NOT NULL COMMENT '用户昵称',
    `user_type`       varchar(2)   NULL DEFAULT '00' COMMENT '用户类型（00系统用户）',
    `email`           varchar(50)  NULL DEFAULT '' COMMENT '用户邮箱',
    `phonenumber`     varchar(30)  NULL DEFAULT '' COMMENT '手机号码',
    `sex`             char(1)      NULL DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
    `avatar`          varchar(100) NULL DEFAULT '' COMMENT '头像地址',
    `password`        varchar(100) NULL DEFAULT '' COMMENT '密码',
    `status`          char(1)      NULL DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
    `del_flag`        char(1)      NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    `login_ip`        varchar(128) NULL DEFAULT '' COMMENT '最后登录IP',
    `login_date`      datetime(0)  NULL DEFAULT NULL COMMENT '最后登录时间',
    `create_by`       varchar(64)  NULL DEFAULT '' COMMENT '创建者',
    `create_time`     datetime(0)  NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`       varchar(64)  NULL DEFAULT '' COMMENT '更新者',
    `update_time`     datetime(0)  NULL DEFAULT NULL COMMENT '更新时间',
    `remark`          varchar(500) NULL DEFAULT NULL COMMENT '备注',
    `id_number`       char(30)     NULL DEFAULT NULL COMMENT '供应商：统一信用代码，专家：身体证号',
    `type`            varchar(100) NULL DEFAULT NULL COMMENT '专家类型',
    `department`      varchar(100) NULL DEFAULT NULL COMMENT '专家科室',
    `inside_identity` varchar(100) NULL DEFAULT NULL COMMENT '院内身份（评审专家）',
    `psn_id`          varchar(60)  NULL DEFAULT NULL COMMENT 'e签宝个人账户id',
    `org_id`          varchar(60)  NULL DEFAULT NULL COMMENT 'e签宝企业账户id',
    PRIMARY KEY (`user_id`) USING BTREE,
    UNIQUE INDEX `idx_uk_username` (`user_name`) USING BTREE,
    INDEX `dept_id` (`dept_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '用户信息表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_user_org
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_org`;
CREATE TABLE `sys_user_org`
(
    `user_id`          bigint(20)   NOT NULL COMMENT '用户ID',
    `org_code`         varchar(64)  NOT NULL COMMENT '组织编码',
    `org_audit_status` char(1)      NULL DEFAULT NULL COMMENT '组织审核用户状态,0-等待组织进行审核，1-审核失败，2-审核通过',
    `reason`           varchar(500) NULL DEFAULT NULL COMMENT '审核不通过理由',
    UNIQUE INDEX `unique_key` (`user_id`, `org_code`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '用户和组织关联表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_user_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_post`;
CREATE TABLE `sys_user_post`
(
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `post_id` bigint(20) NOT NULL COMMENT '岗位ID',
    PRIMARY KEY (`user_id`, `post_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '用户与岗位关联表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`
(
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `role_id` bigint(20) NOT NULL COMMENT '角色ID',
    PRIMARY KEY (`user_id`, `role_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '用户和角色关联表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for template
-- ----------------------------
DROP TABLE IF EXISTS `template`;
CREATE TABLE `template`
(
    `tmp_id`      bigint(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tmp_type`    varchar(64)         NOT NULL COMMENT '模板类型',
    `tmp_key`     varchar(32)         NOT NULL COMMENT '模板英文名',
    `tmp_name`    varchar(32)         NOT NULL COMMENT '模板中文名',
    `tmp_content` text                NULL COMMENT '模板内容',
    PRIMARY KEY (`tmp_id`) USING BTREE,
    UNIQUE INDEX `tmp_key` (`tmp_key`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '模板管理'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for undo_log
-- ----------------------------
DROP TABLE IF EXISTS `undo_log`;
CREATE TABLE `undo_log`
(
    `branch_id`     bigint(20)   NOT NULL COMMENT 'branch transaction id',
    `xid`           varchar(128) NOT NULL COMMENT 'global transaction id',
    `context`       varchar(128) NOT NULL COMMENT 'undo_log context,such as serialization',
    `rollback_info` longblob     NOT NULL COMMENT 'rollback info',
    `log_status`    int(11)      NOT NULL COMMENT '0:normal status,1:defense status',
    `log_created`   datetime(6)  NOT NULL COMMENT 'create datetime',
    `log_modified`  datetime(6)  NOT NULL COMMENT 'modify datetime',
    UNIQUE INDEX `ux_undo_log` (`xid`, `branch_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = 'AT transaction mode undo table'
  ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
