/*
 Navicat Premium Data Transfer

 Source Server         : ************
 Source Server Type    : MySQL
 Source Server Version : 50712
 Source Host           : ************:3307
 Source Schema         : smart-bidding

 Target Server Type    : MySQL
 Target Server Version : 50712
 File Encoding         : 65001

 Date: 25/07/2024 15:22:11
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for agent_bid_section
-- ----------------------------
DROP TABLE IF EXISTS `agent_bid_section`;
CREATE TABLE `agent_bid_section`  (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '标段id',
  `project_code` char(20)  NOT NULL DEFAULT '' COMMENT '项目编号',
  `bid_section_name` varchar(128)  NOT NULL DEFAULT '' COMMENT '标段名称',
  `bid_section_code` varchar(20)  NOT NULL DEFAULT '' COMMENT '标段编号',
  `announcement_time` datetime(0) NULL DEFAULT NULL COMMENT '公告时间',
  `registration_deadline` datetime(0) NULL DEFAULT NULL COMMENT '报名截止时间',
  `bid_opening_time` datetime(0) NULL DEFAULT NULL COMMENT '开标时间',
  `deadline_bid_evaluation` datetime(0) NULL DEFAULT NULL COMMENT '评标截止时间',
  `result_publicity_time` datetime(0) NULL DEFAULT NULL COMMENT '结果公示时间',
  `create_by` varchar(64)  NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64)  NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '代理项目标段' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for agent_project
-- ----------------------------
DROP TABLE IF EXISTS `agent_project`;
CREATE TABLE `agent_project`  (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `year_month_split` varchar(64)  NOT NULL COMMENT '创建时间的年月',
  `project_code` char(20)  NOT NULL DEFAULT '' COMMENT '项目编号',
  `project_name` varchar(200)  NOT NULL DEFAULT '' COMMENT '代理项目名称',
  `industries_type` varchar(32)  NOT NULL DEFAULT '0' COMMENT '项目行业分类- 0 其它、1工程、2货物、3服务、4医疗设备 、',
  `fund_source` varchar(32)  NOT NULL DEFAULT '0' COMMENT '0 其它资金、1科研资金、2财政资金、3自筹资金、4工会资金',
  `project_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '预算金额,单位万元',
  `use_department` varchar(36)  NULL DEFAULT NULL COMMENT '使用科室',
  `representative` varchar(128)  NOT NULL DEFAULT '' COMMENT '使用科室代表',
  `evaluation_method` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否采用评定分离法 1 是  0 否',
  `im_port` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否进口 1 是  0 否',
  `procurement_method` varchar(2)  NOT NULL DEFAULT '0' COMMENT '采购方式 1 公开招标  2竞争性谈判 3单一来源 4竞价 5 根标采购 6 其它',
  `audit_status` tinyint(2) NULL DEFAULT NULL COMMENT '项目审核状态 1-待审核, 2-审核通过, 3-审核未通过',
  `project_status` tinyint(2) NOT NULL DEFAULT 1 COMMENT '项目完成状态 1-未开始, 2-进行中, 3-已完成',
  `complete_time` datetime(0) NULL DEFAULT NULL COMMENT '项目完成时间',
  `reason` varchar(128)  NULL DEFAULT NULL COMMENT '审核拒绝的理由',
  `agency_id` varchar(5)  NOT NULL DEFAULT '' COMMENT '代理机构id',
  `approval_documents_file_key` varchar(60)  NULL DEFAULT NULL COMMENT '批复文件key',
  `requirements_file_key` varchar(60)  NULL DEFAULT NULL COMMENT '需求文件key',
  `other_file_key` varchar(60)  NULL DEFAULT NULL COMMENT '其它文件key',
  `enter_prepare` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否保存招标准备[0-未保存，1-已保存]',
  `save_result` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否保存中标结果[0-未保存，1-已保存]',
  `del_flag` char(1)  NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `intention_time` datetime(0) NULL DEFAULT NULL COMMENT '意向公开时间',
  `intention_url` varchar(200)  NOT NULL DEFAULT '' COMMENT '意向公开地址',
  `reviewed_by_name` varchar(64)  NULL DEFAULT '' COMMENT '审核人',
  `reviewed_user_id` bigint(11) NULL DEFAULT NULL COMMENT '审核人ID',
  `reviewed_time` datetime(0) NULL DEFAULT NULL COMMENT '审核时间',
  `create_by_name` varchar(64)  NULL DEFAULT '' COMMENT '创建者',
  `create_user_id` bigint(11) NULL DEFAULT NULL COMMENT '创建人id',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '代理项目' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for agent_project_operator
-- ----------------------------
DROP TABLE IF EXISTS `agent_project_operator`;
CREATE TABLE `agent_project_operator`  (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '标段id',
  `project_code` varchar(32)  NOT NULL DEFAULT '' COMMENT '项目编号',
  `operator_name` varchar(32)  NOT NULL DEFAULT '' COMMENT '项目参与人姓名',
  `operator_id` bigint(11) NULL DEFAULT NULL COMMENT '项目参与人ID',
  `operator_category` varchar(2)  NOT NULL DEFAULT '0' COMMENT '项目参与人类别 0 代理机构 1-暂时不确定',
  `create_by` varchar(64)  NULL DEFAULT '' COMMENT '添加人',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64)  NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '项目与参与使用科室代表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for agent_supplier
-- ----------------------------
DROP TABLE IF EXISTS `agent_supplier`;
CREATE TABLE `agent_supplier`  (
  `bidder_id` bigint(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `bid_section_code` char(23)  NOT NULL COMMENT '标段编号',
  `bidder_name` varchar(100)  NOT NULL COMMENT '投标人公司名称 ',
  `info_reporter_name` varchar(50)  NOT NULL COMMENT '投标责任人姓名: ',
  `info_reporter_contact_number` char(11)  NOT NULL COMMENT '投标责任人联系电话',
  `brand_model` varchar(64)  NULL DEFAULT NULL COMMENT '品牌型号',
  `candidate_ranking` tinyint(2) NOT NULL DEFAULT 0 COMMENT '候选人名次',
  `winning_the_bid` tinyint(2) NOT NULL DEFAULT 0 COMMENT '是否中标 1中标  0未中标',
  `bid_winning_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '1中标金额',
  `create_by` varchar(64)  NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64)  NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`bidder_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '代理项目供应商' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for answer_file_evaluation_method
-- ----------------------------
DROP TABLE IF EXISTS `answer_file_evaluation_method`;
CREATE TABLE `answer_file_evaluation_method`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `subpackage_code` char(50)  NOT NULL COMMENT '包编码[23]',
  `supplier_id` bigint(20) UNSIGNED NOT NULL COMMENT '供应商id',
  `score_chapter` varchar(500)  NULL DEFAULT NULL COMMENT '响应文件中得分点定位',
  `uuid` char(50)  NOT NULL COMMENT 'uuid',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除=1',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(100)  NULL DEFAULT NULL COMMENT '创建者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(100)  NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_subpackageCode_supplierId`(`subpackage_code`, `supplier_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '响应文件评审办法' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for answer_file_evaluation_method_remark
-- ----------------------------
DROP TABLE IF EXISTS `answer_file_evaluation_method_remark`;
CREATE TABLE `answer_file_evaluation_method_remark`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `subpackage_code` char(50)  NOT NULL COMMENT '包编码',
  `supplier_id` bigint(20) UNSIGNED NOT NULL COMMENT '供应商id',
  `remark` text  NULL COMMENT '备注内容',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除=1',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(100)  NULL DEFAULT NULL COMMENT '创建者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(100)  NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_subpackageCode_supplierId`(`subpackage_code`, `supplier_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '响应文件评审办法备注' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for answer_file_menu
-- ----------------------------
DROP TABLE IF EXISTS `answer_file_menu`;
CREATE TABLE `answer_file_menu`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `subpackage_code` char(50)  NOT NULL COMMENT '包编码[23]',
  `supplier_id` bigint(20) UNSIGNED NOT NULL COMMENT '供应商id',
  `pid` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '父主键',
  `order_num` tinyint(1) UNSIGNED NOT NULL COMMENT '目录顺序',
  `chapter_name` varchar(100)  NOT NULL COMMENT '目录名字',
  `chapter_context` mediumtext  NULL COMMENT '目录内容，压缩并base64转码存储',
  `attach_key` varchar(5000)  NULL DEFAULT NULL COMMENT '目录中文件keys',
  `chapter_type` tinyint(1) UNSIGNED NULL DEFAULT NULL COMMENT '目录类型',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除=1',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(100)  NULL DEFAULT NULL COMMENT '创建者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(100)  NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_subpackageCode_supplierId`(`subpackage_code`, `supplier_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '响应文件章节及章节子内容' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for answer_file_quote_form
-- ----------------------------
DROP TABLE IF EXISTS `answer_file_quote_form`;
CREATE TABLE `answer_file_quote_form`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '报价表单id',
  `subpackage_code` char(50)  NOT NULL COMMENT '包编码',
  `supplier_id` bigint(20) NOT NULL COMMENT '供应商id',
  `round` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '轮数，递交响应文件中默认是0轮',
  `body_json` json NOT NULL COMMENT '报价表单内容',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除=1',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(100)  NULL DEFAULT NULL COMMENT '创建者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(100)  NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_subpackageCode`(`subpackage_code`) USING BTREE,
  INDEX `idx_supplierId`(`supplier_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商报价表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for attribute
-- ----------------------------
DROP TABLE IF EXISTS `attribute`;
CREATE TABLE `attribute`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `group_type` char(3)  NOT NULL COMMENT '分组类型',
  `key_group` varchar(255)  NULL DEFAULT NULL COMMENT '字段组',
  `key_name` varchar(255)  NOT NULL COMMENT '字段中文名',
  `key_val` varchar(255)  NOT NULL COMMENT '字段英文名',
  `key_type` varchar(255)  NOT NULL COMMENT '字段类型',
  `key_java_type` varchar(255)  NULL DEFAULT NULL COMMENT 'java类型',
  `required` bit(1) NOT NULL COMMENT '必填=1',
  `regex` varchar(255)  NOT NULL COMMENT '正则',
  `remark` varchar(255)  NULL DEFAULT NULL COMMENT '备注',
  `displayed` bit(1) NOT NULL COMMENT '显示=1',
  `disabled` bit(1) NOT NULL COMMENT '禁用=1',
  `sort` int(11) UNSIGNED NOT NULL COMMENT '排序',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除=1',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(100)  NULL DEFAULT NULL COMMENT '创建者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(100)  NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_groupType_keyVal`(`group_type`, `key_val`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '通用属性配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for audit_comment
-- ----------------------------
DROP TABLE IF EXISTS `audit_comment`;
CREATE TABLE `audit_comment`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `audit_record_id` bigint(20) UNSIGNED NOT NULL COMMENT '审批人审核记录id',
  `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '用户id',
  `user_name` varchar(100)  NOT NULL COMMENT '用户名',
  `comment` varchar(500)  NOT NULL COMMENT '评论',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除=1',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(100)  NULL DEFAULT NULL COMMENT '创建者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(100)  NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_audit_record_id`(`audit_record_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '审核评论' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for audit_info
-- ----------------------------
DROP TABLE IF EXISTS `audit_info`;
CREATE TABLE `audit_info`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `buy_item_code` varchar(64)  NOT NULL COMMENT '采购项目code',
  `subpackage_code` varchar(64)  NULL DEFAULT NULL COMMENT '标段code',
  `audit_code` varchar(64)  NOT NULL COMMENT '审批code',
  `audit_title` varchar(200)  NOT NULL COMMENT '审批名',
  `audit_type` varchar(64)  NOT NULL COMMENT '类型',
  `content` json NOT NULL COMMENT '内容',
  `remark` varchar(500)  NULL DEFAULT NULL COMMENT '备注',
  `status` tinyint(1) UNSIGNED NULL DEFAULT 2 COMMENT '审核状态，0-不同意，1-同意，2-待审批，3-撤回',
  `create_id` bigint(20) UNSIGNED NOT NULL COMMENT '创建人id',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除=1',
  `create_at` datetime(0) NOT NULL COMMENT '创建时间',
  `create_by` varchar(100)  NULL DEFAULT NULL COMMENT '创建者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(100)  NULL DEFAULT NULL COMMENT '更新者',
  `create_at_date` date GENERATED ALWAYS AS (cast(`create_at` as date)) STORED NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `audit_code`(`audit_code`) USING BTREE,
  INDEX `idx_create_at_date`(`create_at_date`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '审批信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for audit_person
-- ----------------------------
DROP TABLE IF EXISTS `audit_person`;
CREATE TABLE `audit_person`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `audit_info_id` bigint(20) UNSIGNED NOT NULL COMMENT '审批信息id',
  `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '审核人id',
  `user_name` varchar(100)  NOT NULL COMMENT '审核人姓名',
  `requirement` varchar(500)  NULL DEFAULT NULL COMMENT '要求',
  `atts` json NULL COMMENT '多附件',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除=1',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(100)  NULL DEFAULT NULL COMMENT '创建者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(100)  NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_audit_info_id`(`audit_info_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '审批人' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for audit_recipient
-- ----------------------------
DROP TABLE IF EXISTS `audit_recipient`;
CREATE TABLE `audit_recipient`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `audit_info_id` bigint(20) UNSIGNED NOT NULL COMMENT '审批信息id',
  `copy_id` bigint(20) UNSIGNED NOT NULL COMMENT '抄送人id',
  `copy_name` varchar(100)  NOT NULL COMMENT '抄送人',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_audit_info_id`(`audit_info_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '抄送人' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for audit_record
-- ----------------------------
DROP TABLE IF EXISTS `audit_record`;
CREATE TABLE `audit_record`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `audit_person_id` bigint(20) UNSIGNED NOT NULL COMMENT '审批人信息id',
  `status` tinyint(1) UNSIGNED NOT NULL COMMENT '审核状态，0-不同意，1-同意',
  `remark` varchar(500)  NULL DEFAULT NULL COMMENT '审核备注',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除=1',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(100)  NULL DEFAULT NULL COMMENT '创建者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(100)  NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_audit_person_id`(`audit_person_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '审核记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for bid_file_requirement
-- ----------------------------
DROP TABLE IF EXISTS `bid_file_requirement`;
CREATE TABLE `bid_file_requirement`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `subpackage_code` char(64)  NOT NULL COMMENT '标段code',
  `supplier_id` bigint(20) NOT NULL COMMENT '供应商id',
  `requirement_id` bigint(20) NOT NULL COMMENT '采购文件要求id',
  `bid_file_key` varchar(100)  NOT NULL COMMENT '响应文件key',
  `status` tinyint(3) UNSIGNED NULL DEFAULT 0 COMMENT '0-待上传、1-已上传、2-被驳回',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '投标人对采购文件具体响应' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for biz_audit_relation
-- ----------------------------
DROP TABLE IF EXISTS `biz_audit_relation`;
CREATE TABLE `biz_audit_relation`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `audit_code` varchar(64)  NOT NULL COMMENT '审批code',
  `audit_type` varchar(64)  NOT NULL COMMENT '审批类型',
  `buy_item_code` varchar(64)  NOT NULL COMMENT '采购项目code',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `data` json NOT NULL COMMENT '业务数据',
  `class_name` varchar(200)  NOT NULL COMMENT '业务数据类名',
  `business_id` bigint(20) NULL DEFAULT NULL COMMENT '业务id',
  `subpackage_code` varchar(64)  NULL DEFAULT NULL COMMENT '标段编号',
  `business_type` varchar(100)  NULL DEFAULT NULL COMMENT '业务类型',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(100)  NULL DEFAULT NULL COMMENT '创建者',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `audit_code`(`audit_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '审批关系' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for claims_file
-- ----------------------------
DROP TABLE IF EXISTS `claims_file`;
CREATE TABLE `claims_file`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `subpackage_code` char(50)  NOT NULL COMMENT '包编码',
  `epc_file` varchar(100)  NOT NULL COMMENT '采购文件包key',
  `pdf_file` varchar(100)  NOT NULL COMMENT '采购文件pdf key.txt',
  `release_status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '采购文件发布状态,0已上传 1已提交',
  `release_time` datetime(0) NULL DEFAULT NULL COMMENT '采购文件提交时间',
  `app_name` varchar(100)  NOT NULL COMMENT 'app名称',
  `app_version` varchar(100)  NOT NULL COMMENT 'app版本',
  `zepc_key` varchar(100)  NOT NULL COMMENT 'app文件验证key',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除=1',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(100)  NULL DEFAULT NULL COMMENT '创建者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(100)  NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_subpackageCode`(`subpackage_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '采购文件信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for claims_file_evaluation_method
-- ----------------------------
DROP TABLE IF EXISTS `claims_file_evaluation_method`;
CREATE TABLE `claims_file_evaluation_method`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `subpackage_code` char(50)  NOT NULL COMMENT '包编码',
  `review_type` tinyint(1) UNSIGNED NOT NULL COMMENT '评审类型：1-符合性评审,评分表:(2-技术,3-商务,4-价格)',
  `review_item` varchar(500)  NOT NULL COMMENT '评审模块',
  `review_criteria` text  NULL COMMENT '评审规则',
  `subjective` tinyint(1) UNSIGNED NOT NULL COMMENT '1-主观分,0-客观分',
  `review_score` double(10, 2) UNSIGNED NULL DEFAULT NULL COMMENT '分值',
  `uuid` char(50)  NOT NULL COMMENT 'uuid',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除=1',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(100)  NULL DEFAULT NULL COMMENT '创建者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(100)  NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_subpackageCode`(`subpackage_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '采购文件评审办法' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for claims_file_evaluation_method_remark
-- ----------------------------
DROP TABLE IF EXISTS `claims_file_evaluation_method_remark`;
CREATE TABLE `claims_file_evaluation_method_remark`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `subpackage_code` char(50)  NOT NULL COMMENT '包编码',
  `remark` text  NULL COMMENT '备注内容',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除=1',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(100)  NULL DEFAULT NULL COMMENT '创建者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(100)  NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_subpackageCode`(`subpackage_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '采购文件评审办法备注' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for claims_file_menu
-- ----------------------------
DROP TABLE IF EXISTS `claims_file_menu`;
CREATE TABLE `claims_file_menu`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `subpackage_code` char(50)  NOT NULL COMMENT '包编码',
  `pid` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '父主键',
  `order_num` tinyint(1) UNSIGNED NOT NULL COMMENT '目录顺序',
  `chapter_name` varchar(100)  NOT NULL COMMENT '目录名字',
  `chapter_context` mediumtext  NULL COMMENT '目录内容，压缩并base64转码存储',
  `attach_key` varchar(5000)  NULL DEFAULT NULL COMMENT '目录中文件keys',
  `chapter_type` tinyint(1) UNSIGNED NULL DEFAULT NULL COMMENT '目录类型',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除=1',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(100)  NULL DEFAULT NULL COMMENT '创建者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(100)  NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_subpackageCode`(`subpackage_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '采购文件章节及章节子内容' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for claims_file_quote_form
-- ----------------------------
DROP TABLE IF EXISTS `claims_file_quote_form`;
CREATE TABLE `claims_file_quote_form`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `subpackage_code` char(50)  NOT NULL COMMENT '包编码',
  `head_json` text  NOT NULL COMMENT '表头',
  `body_json` text  NULL COMMENT '表体',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除=1',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(100)  NULL DEFAULT NULL COMMENT '创建者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(100)  NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_subpackageCode`(`subpackage_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '采购人报价表单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for entrust_bulletin
-- ----------------------------
DROP TABLE IF EXISTS `entrust_bulletin`;
CREATE TABLE `entrust_bulletin`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '委托id',
  `create_user_id` bigint(20) NOT NULL COMMENT '创建人id',
  `create_user_name` varchar(100)  NOT NULL COMMENT '创建人名称',
  `project_name` varchar(500)  NOT NULL COMMENT '项目名称',
  `bulletin_name` varchar(500)  NOT NULL COMMENT '公告名称',
  `bulletin_type` varchar(100)  NULL DEFAULT NULL COMMENT '公告类型',
  `bulletin_text` text  NULL COMMENT '委托公告文本',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `audit_status` tinyint(4) NOT NULL COMMENT '审核状态，0-待审核，1-审核成功，2-审核失败',
  `show_status` tinyint(4) NOT NULL COMMENT '展示状态，1-展示，2-隐藏',
  `failure_description` varchar(500)  NULL DEFAULT NULL COMMENT '审核失败时说明',
  `audit_user_id` bigint(20) NULL DEFAULT NULL COMMENT '审核人id',
  `audit_user_name` varchar(100)  NULL DEFAULT NULL COMMENT '审核人名称',
  `audit_time` datetime(0) NULL DEFAULT NULL COMMENT '审核时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '委托公告' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_ask_answer
-- ----------------------------
DROP TABLE IF EXISTS `purchase_ask_answer`;
CREATE TABLE `purchase_ask_answer`  (
  `id` bigint(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `subpackage_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '标段编码',
  `ask_user_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提问人类型[1-投标人, 2-采购办, 3-评委]',
  `ask_user_id` bigint(11) NOT NULL COMMENT ' 提问人id',
  `ask_user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提问人名称[投标人公司名称/采购办操作人姓名/评委姓名]',
  `answer_user_id` bigint(11) NULL DEFAULT NULL COMMENT ' 答复人id',
  `answer_user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '答复人名称[投标人公司名称/采购办操作人姓名/评委姓名]',
  `answer_user_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '回复人类型[1-投标人, 2-采购办, 3-评委]',
  `ask_content` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提问内容',
  `answer_content` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '答复内容',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除标志（0代表存在 代表删除)',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '答疑模块答疑表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_bargain
-- ----------------------------
DROP TABLE IF EXISTS `purchase_bargain`;
CREATE TABLE `purchase_bargain`  (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `subpackage_code` char(64)  NOT NULL DEFAULT '' COMMENT '包编码',
  `initiate_id` bigint(11) NOT NULL COMMENT '发起/结束议价人的id',
  `supplier_id` bigint(11) NOT NULL COMMENT '供应商id',
  `round` char(1)  NOT NULL DEFAULT '1' COMMENT '发起议价轮数： 1 一轮议价  2 二轮议价  3 三轮议价',
  `launch_status` char(1)  NOT NULL DEFAULT '1' COMMENT '议价状态：1 发起  2 结束',
  `countdown` datetime(0) NULL DEFAULT NULL COMMENT '倒计时',
  `service_require` varchar(255)  NULL DEFAULT NULL COMMENT '服务需求',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除标志（0代表存在 代表删除)',
  `create_by` varchar(64)  NULL DEFAULT '' COMMENT '创建者',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64)  NULL DEFAULT '' COMMENT '更新者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '采购人发起议价表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_bid_opening
-- ----------------------------
DROP TABLE IF EXISTS `purchase_bid_opening`;
CREATE TABLE `purchase_bid_opening`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `subpackage_code` char(50)  NOT NULL COMMENT '包编码',
  `status` tinyint(1) UNSIGNED NOT NULL COMMENT '0-开标开始，1-唱标，2-开标完成',
  `record_file` varchar(100)  NULL DEFAULT NULL COMMENT '开标记录文件',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除=1',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(100)  NULL DEFAULT NULL COMMENT '创建者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(100)  NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_subpackageCode`(`subpackage_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '采购人开标' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_bulletin
-- ----------------------------
DROP TABLE IF EXISTS `purchase_bulletin`;
CREATE TABLE `purchase_bulletin`  (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `buy_item_code` varchar(64)  NOT NULL COMMENT '采购项目编号',
  `subpackage_code` varchar(64)  NULL DEFAULT NULL COMMENT '标段code',
  `bulletin_audit_code` varchar(64)  NOT NULL COMMENT '公告审批单号',
  `bulletin_name` varchar(128)  NOT NULL COMMENT '公告名称',
  `bulletin_type` varchar(64)  NOT NULL COMMENT '公告类型',
  `bulletin_type_name` varchar(64)  NOT NULL COMMENT '公告类型中文名',
  `annex_key` text  NULL COMMENT '公告附件key',
  `bulletin_content_html` text  NOT NULL COMMENT '公告的html',
  `bulletin_content_key` varchar(64)  NOT NULL COMMENT '公告的pdf_key',
  `org_code` varchar(32)  NULL DEFAULT NULL COMMENT '组织code',
  `invite_supplier` json NULL COMMENT '邀请的供应商id信息',
  `supplier_addition` text  NULL COMMENT '供应商附加条件',
  `review_time` datetime(0) NULL DEFAULT NULL COMMENT '公告审核时间',
  `audit_status` char(1)  NOT NULL DEFAULT '2' COMMENT '审核状态，0-不同意，1-同意，2-待审批，3-撤回',
  `audit_desc` varchar(256)  NULL DEFAULT '' COMMENT '审核说明',
  `audit_id` bigint(11) NULL DEFAULT NULL COMMENT '审核人id',
  `audit_name` varchar(20)  NULL DEFAULT '' COMMENT '审核人名字',
  `whether_show` char(1)  NOT NULL DEFAULT '0' COMMENT '是否展示 0-全网展示 1-供应商可见 2-不展示',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除标志（0代表存在 代表删除)',
  `create_by` varchar(64)  NULL DEFAULT '' COMMENT '创建者',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64)  NULL DEFAULT '' COMMENT '更新者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '公告表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_bulletin_time
-- ----------------------------
DROP TABLE IF EXISTS `purchase_bulletin_time`;
CREATE TABLE `purchase_bulletin_time`  (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `bulletin_id` bigint(11) NOT NULL COMMENT '公告信息表主键id',
  `buy_item_code` varchar(64)  NOT NULL COMMENT '招标项目编号',
  `subpackage_code` varchar(64)  NOT NULL COMMENT '标段code',
  `audit_status` char(1)  NOT NULL DEFAULT '0' COMMENT '0-待审核, 1-撤回, 2-平台审核不通过, 3-平台审核通过',
  `time_name` varchar(64)  NOT NULL COMMENT '时间名称',
  `time_type` varchar(64)  NOT NULL COMMENT '时间类型',
  `time` datetime(0) NULL DEFAULT NULL COMMENT '公告时间',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除标志（0代表存在 代表删除)',
  `create_by` varchar(64)  NULL DEFAULT '' COMMENT '创建者',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64)  NULL DEFAULT '' COMMENT '更新者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '公告时间表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_buy_item
-- ----------------------------
DROP TABLE IF EXISTS `purchase_buy_item`;
CREATE TABLE `purchase_buy_item`  (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '采购项目主健id',
  `item_code` varchar(64)  NOT NULL DEFAULT '' COMMENT '项目code 暂时用不到',
  `buy_item_code` varchar(64)  NOT NULL DEFAULT '' COMMENT '采购项目code',
  `buy_item_name` varchar(128)  NOT NULL DEFAULT '' COMMENT '采购项目名字',
  `purchase_method_code` varchar(32)  NOT NULL COMMENT '采购方式code[存储 purchase_method表中的 purchase_method_code字段]',
  `purchase_method_type` varchar(128)  NOT NULL COMMENT '采购方式类型[存储 purchase_method表中的 purchase_method_type字段]',
  `purchase_method_name` varchar(128)  NOT NULL COMMENT '采购方式名称[存储 purchase_method表中的 purchase_method_name字段]',
  `year_month_split` varchar(32)  NULL DEFAULT NULL COMMENT '招标项目创建时间的年月',
  `end` tinyint(1) NULL DEFAULT 0 COMMENT '完成并归档[0-未完成,1-已完成，未归档,2-已归档]',
  `purchase_time_json` text  NOT NULL COMMENT '采购时间点json',
  `purchase_bulletin_json` text  NOT NULL COMMENT '采购公示公告json',
  `purchase_function_json` text  NOT NULL COMMENT '采购功能点json',
  `user_id` bigint(11) NOT NULL COMMENT '创建人id',
  `dept_id` bigint(11) NULL DEFAULT NULL COMMENT '部门id',
  `org_code` varchar(64)  NOT NULL COMMENT '创建人的组织代码',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除标志（0代表存在 代表删除)',
  `create_by` varchar(64)  NULL DEFAULT '' COMMENT '创建者',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64)  NULL DEFAULT '' COMMENT '更新者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `buy_item_code`(`buy_item_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '采购项目 基础信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_buy_item_param
-- ----------------------------
DROP TABLE IF EXISTS `purchase_buy_item_param`;
CREATE TABLE `purchase_buy_item_param`  (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `subpackage_code` char(64)  NOT NULL DEFAULT '' COMMENT '包编码',
  `pushed_user_id` bigint(11) NULL DEFAULT NULL COMMENT '被推送人的id',
  `pushed_user_name` varchar(32)  NULL DEFAULT NULL COMMENT '被推送人的名称',
  `param_state` char(1)  NULL DEFAULT '0' COMMENT '参数状态[0-未推送, 1-等待被推送人提交, 2-待确认, 3-已确认，4-不同意]',
  `param_reason` varchar(255)  NULL DEFAULT NULL COMMENT '驳回理由',
  `push_time` datetime(0) NULL DEFAULT NULL COMMENT '参数推送时间',
  `response_time` datetime(0) NULL DEFAULT NULL COMMENT '参数响应时间',
  `confirm_time` datetime(0) NULL DEFAULT NULL COMMENT '参数确认时间',
  `param_remark` varchar(255)  NULL DEFAULT NULL COMMENT '备注',
  `param_desc` text  NULL COMMENT '参数描述',
  `param_attachment` text  NULL COMMENT '附件',
  `create_id` bigint(11) NOT NULL COMMENT '创建者id',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除标志（0代表存在 代表删除)',
  `create_by` varchar(64)  NULL DEFAULT '' COMMENT '创建者',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64)  NULL DEFAULT '' COMMENT '更新者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `subpackage_code`(`subpackage_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '项目参数' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_doubt
-- ----------------------------
DROP TABLE IF EXISTS `purchase_doubt`;
CREATE TABLE `purchase_doubt`  (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `doubt_time_id` bigint(11) NOT NULL COMMENT '质疑时间信息表主键',
  `subpackage_code` char(64)  NOT NULL COMMENT '包编号',
  `doubt_user_type` char(1)  NOT NULL COMMENT '质疑人类型[1-投标人, 2-采购办, 3-评委]',
  `doubt_user_id` bigint(11) NOT NULL COMMENT '质疑人id',
  `doubt_user_name` varchar(64)  NOT NULL COMMENT '提问名称[投标人公司名称/采购办操作人姓名/评委姓名]',
  `doubt_file_key` varchar(64)  NULL DEFAULT NULL COMMENT '质疑文件地址key',
  `reply_file_key` varchar(64)  NULL DEFAULT NULL COMMENT '质疑回复文件地址key',
  `doubt_annex_file_key` varchar(64)  NULL DEFAULT NULL COMMENT '发起质疑附件文件地址key',
  `reply_annex_file_key` varchar(64)  NULL DEFAULT NULL COMMENT '回复质疑附件文件地址key',
  `reply_user_id` bigint(11) NULL DEFAULT NULL COMMENT '质疑答复人id',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除标志（0代表存在 代表删除)',
  `create_by` varchar(64)  NULL DEFAULT '' COMMENT '创建者',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64)  NULL DEFAULT '' COMMENT '更新者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '质疑表信息列表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_doubt_time
-- ----------------------------
DROP TABLE IF EXISTS `purchase_doubt_time`;
CREATE TABLE `purchase_doubt_time`  (
  `id` bigint(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `buy_item_code` char(64)  NOT NULL DEFAULT '' COMMENT '采购项目编号',
  `subpackage_code` char(64)  NOT NULL DEFAULT '' COMMENT '包编码',
  `subpackage_name` varchar(128)  NOT NULL DEFAULT '' COMMENT '包名称',
  `doubt_type` char(1)  NOT NULL COMMENT '质疑类型  1-开标之前 （供应商向招标人） 2 - 开标过程中（供应商向招标人） 3 -评标过程中（专家向供应商 澄清 ） 4- 中标公示期（供应商向招标人）',
  `start_time` datetime(0) NOT NULL COMMENT '发起质疑开始时间',
  `end_time` datetime(0) NOT NULL COMMENT '质疑回复截止时间',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除标志（0代表存在 代表删除)',
  `version` int(9) NULL DEFAULT 0 COMMENT '版本信息',
  `create_by` varchar(64)  NULL DEFAULT '' COMMENT '创建者',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64)  NULL DEFAULT '' COMMENT '更新者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '质疑时间信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_extract_judge_external
-- ----------------------------
DROP TABLE IF EXISTS `purchase_extract_judge_external`;
CREATE TABLE `purchase_extract_judge_external`  (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `extract_long_name` varchar(128)  NOT NULL COMMENT '抽取记录名称',
  `judge_id` bigint(11) NOT NULL COMMENT '评委id',
  `remark` varchar(500)  NULL DEFAULT '' COMMENT '备注',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除标志（0代表存在 代表删除)',
  `create_by` varchar(64)  NULL DEFAULT '' COMMENT '创建者',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64)  NULL DEFAULT '' COMMENT '更新者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '院外--评委抽取表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_extract_judge_inner
-- ----------------------------
DROP TABLE IF EXISTS `purchase_extract_judge_inner`;
CREATE TABLE `purchase_extract_judge_inner`  (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `buy_item_code` varchar(64)  NOT NULL COMMENT '采购项目编号',
  `subpackage_code` varchar(64)  NOT NULL COMMENT '标段code',
  `judge_id` bigint(11) NOT NULL COMMENT '评委id',
  `judge_name` varchar(100)  NOT NULL COMMENT '评委名称',
  `password` varchar(50)  NOT NULL COMMENT '评审密码',
  `dept_represent` char(1)  NULL DEFAULT '0' COMMENT '是否是科室代表 [0-不是 1-是]',
  `whether_group` char(1)  NULL DEFAULT '0' COMMENT '是否是组长 [0-不是 1-是]',
  `whether_sign` char(1)  NULL DEFAULT '0' COMMENT '是否签到 [0-未签到 1-签到成功]',
  `sign_time` datetime(0) NULL DEFAULT NULL COMMENT '签到时间',
  `status` char(1)  NULL DEFAULT '0' COMMENT '评委状态[0-未评标 1-评标 2-评标完成]',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除标志（0代表存在 代表删除)',
  `create_by` varchar(64)  NULL DEFAULT '' COMMENT '创建者',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64)  NULL DEFAULT '' COMMENT '更新者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '项目内--评委抽取表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_jxzl_buy_item
-- ----------------------------
DROP TABLE IF EXISTS `purchase_jxzl_buy_item`;
CREATE TABLE `purchase_jxzl_buy_item`  (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '采购项目-江西肿瘤版本主健id',
  `buy_item_code` varchar(64)  NOT NULL COMMENT '采购项目code',
  `buy_item_name` varchar(128)  NOT NULL COMMENT '采购项目名字',
  `monitor_bid_person` varchar(32)  NULL DEFAULT '' COMMENT '监标人',
  `monitor_bid_person_id` bigint(11) NULL DEFAULT NULL COMMENT '监标人id',
  `buy_budget` decimal(18, 5) NULL DEFAULT NULL COMMENT '采购预算【18位整数，5位小数】',
  `use_dept` varchar(64)  NOT NULL COMMENT '使用科室',
  `buy_class` varchar(64)  NOT NULL COMMENT '标的类型',
  `capital_source` varchar(64)  NULL DEFAULT NULL COMMENT '资金来源',
  `buy_person` varchar(64)  NOT NULL COMMENT '项目负责人',
  `organize_type` varchar(64)  NULL DEFAULT NULL COMMENT '组织形式',
  `buy_remark` varchar(128)  NULL DEFAULT NULL COMMENT '备注',
  `apply_time` datetime(0) NULL DEFAULT NULL COMMENT '申报时间',
  `purchase_dept` varchar(32)  NOT NULL COMMENT '采购科室',
  `concat_number` varchar(18)  NOT NULL COMMENT '联系电话',
  `purchase_limit_time` varchar(32)  NULL DEFAULT NULL COMMENT '采购时限',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除标志（0代表存在 代表删除)',
  `create_by` varchar(64)  NULL DEFAULT '' COMMENT '创建者',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64)  NULL DEFAULT '' COMMENT '更新者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `buy_item_code`(`buy_item_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '采购项目-江西肿瘤版本' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_review_before
-- ----------------------------
DROP TABLE IF EXISTS `purchase_review_before`;
CREATE TABLE `purchase_review_before`  (
  `id` bigint(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `subpackage_code` char(64)  NOT NULL DEFAULT '' COMMENT '包编码',
  `supplier_id` bigint(11) NOT NULL COMMENT '供应商id',
  `bid_enter_expert` char(1)  NULL DEFAULT NULL COMMENT '采购人确认评标结果[0-未确认，1-确认]',
  `confirm_counter_sign` char(1)  NULL DEFAULT NULL COMMENT '是否确认会签[0-未确认，1-已确认]',
  `confirm_review` char(1)  NULL DEFAULT NULL COMMENT '是否确认评审[0-未确认，1-已确认]',
  `confirm_review_time` datetime(0) NULL DEFAULT NULL COMMENT '确认评审时间',
  `review_end_time` datetime(0) NULL DEFAULT NULL COMMENT '评审完成时间',
  `whether_show_judge` char(1)  NULL DEFAULT NULL COMMENT '是否允许在评委列表上显示[0-不显示，1-显示]',
  `enter_the_review` char(1)  NULL DEFAULT NULL COMMENT '是否允许进入评审,0-不允许，1-允许',
  `is_bargaining` char(1)  NULL DEFAULT NULL COMMENT '议价人[0-采购人，1-评委，2-没有议价(2状态是传给评委的)]',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除标志（0代表存在 代表删除)',
  `create_by` varchar(64)  NULL DEFAULT '' COMMENT '创建者',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64)  NULL DEFAULT '' COMMENT '更新者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '评审之前标段状态表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_smart_buy_item
-- ----------------------------
DROP TABLE IF EXISTS `purchase_smart_buy_item`;
CREATE TABLE `purchase_smart_buy_item`  (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '采购项目-易建采版本主健id',
  `buy_item_code` varchar(64)  NOT NULL COMMENT '采购项目code',
  `buy_item_name` varchar(128)  NOT NULL COMMENT '采购项目名字',
  `buy_budget` decimal(18, 5) NULL DEFAULT NULL COMMENT '采购预算【18位整数，5位小数】',
  `use_dept` varchar(64)  NOT NULL COMMENT '使用科室',
  `buy_class` varchar(64)  NOT NULL COMMENT '标的类型',
  `capital_source` varchar(64)  NULL DEFAULT NULL COMMENT '资金来源',
  `buy_person` varchar(64)  NOT NULL COMMENT '项目负责人',
  `organize_type` varchar(64)  NULL DEFAULT NULL COMMENT '组织形式',
  `buy_remark` varchar(128)  NULL DEFAULT NULL COMMENT '备注',
  `concat_number` varchar(18)  NOT NULL COMMENT '联系电话',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除标志（0代表存在 代表删除)',
  `create_by` varchar(64)  NULL DEFAULT '' COMMENT '创建者',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64)  NULL DEFAULT '' COMMENT '更新者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `buy_item_code`(`buy_item_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '采购项目-易建采版本' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_subpackage
-- ----------------------------
DROP TABLE IF EXISTS `purchase_subpackage`;
CREATE TABLE `purchase_subpackage`  (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '主健id',
  `buy_item_code` varchar(64)  NOT NULL COMMENT '采购项目编号(业务使用代码)',
  `subpackage_code` varchar(64)  NOT NULL COMMENT '标段编号（业务使用代码）',
  `subpackage_name` varchar(64)  NOT NULL COMMENT '标段名称',
  `inner_buy_item_package_code` varchar(64)  NULL DEFAULT NULL COMMENT '院内采购项目标段编号',
  `invited_supplier_json` text  NULL COMMENT '被邀请的供应商Json',
  `claims_file_pdf_key` varchar(100)  NULL DEFAULT NULL COMMENT '采购文件pdf key.txt',
  `abandon` char(1)  NULL DEFAULT '0' COMMENT '是否终止采购 [0-正常，1-终止] 默认为0',
  `label_json` text  NULL COMMENT '标签',
  `contract_price` decimal(18, 5) NULL DEFAULT NULL COMMENT '控制价【18位整数，5位小数】',
  `subpackage_content` varchar(256)  NULL DEFAULT NULL COMMENT '标段内容',
  `registration_end_time` datetime(0) NULL DEFAULT NULL COMMENT '报名截止时间',
  `meeting_time` datetime(0) NULL DEFAULT NULL COMMENT '响应截止时间,也是开标时间',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除标志（0代表存在 代表删除)',
  `create_by` varchar(64)  NULL DEFAULT '' COMMENT '创建者',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64)  NULL DEFAULT '' COMMENT '更新者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `subpackage_code`(`subpackage_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '包 基础信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_syth_buy_item
-- ----------------------------
DROP TABLE IF EXISTS `purchase_syth_buy_item`;
CREATE TABLE `purchase_syth_buy_item`  (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '采购项目-十堰太和版本主健id',
  `buy_item_code` varchar(64)  NOT NULL COMMENT '采购项目code',
  `buy_item_name` varchar(128)  NOT NULL COMMENT '采购项目名字',
  `inner_code` varchar(32)  NOT NULL COMMENT '院内项目编号',
  `buy_budget` decimal(18, 5) NULL DEFAULT NULL COMMENT '采购预算【18位整数，5位小数】',
  `use_dept` varchar(64)  NOT NULL COMMENT '使用科室',
  `buy_class` varchar(64)  NOT NULL COMMENT '标的类型',
  `capital_source` varchar(64)  NULL DEFAULT NULL COMMENT '资金来源',
  `buy_person` varchar(64)  NOT NULL COMMENT '项目负责人',
  `organize_type` varchar(64)  NULL DEFAULT NULL COMMENT '组织形式',
  `buy_remark` varchar(128)  NULL DEFAULT NULL COMMENT '备注',
  `apply_time` datetime(0) NULL DEFAULT NULL COMMENT '申报时间',
  `purchase_dept` varchar(32)  NOT NULL COMMENT '采购科室',
  `concat_number` varchar(18)  NOT NULL COMMENT '联系电话',
  `purchase_limit_time` varchar(32)  NULL DEFAULT NULL COMMENT '采购时限',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除标志（0代表存在 代表删除)',
  `create_by` varchar(64)  NULL DEFAULT '' COMMENT '创建者',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64)  NULL DEFAULT '' COMMENT '更新者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `buy_item_code`(`buy_item_code`) USING BTREE,
  UNIQUE INDEX `inner_code`(`inner_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '采购项目-十堰太和版本' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_whpr_buy_item
-- ----------------------------
DROP TABLE IF EXISTS `purchase_whpr_buy_item`;
CREATE TABLE `purchase_whpr_buy_item`  (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '采购项目-武汉市普仁医院id',
  `buy_item_code` varchar(64)  NOT NULL COMMENT '采购项目code',
  `buy_item_name` varchar(128)  NOT NULL COMMENT '采购项目名字',
  `inner_code` varchar(32)  NOT NULL COMMENT '院内项目编号',
  `buy_budget` decimal(18, 5) NULL DEFAULT NULL COMMENT '采购预算【18位整数，5位小数】',
  `use_dept` varchar(64)  NOT NULL COMMENT '使用科室',
  `buy_class` varchar(64)  NOT NULL COMMENT '标的类型',
  `capital_source` varchar(64)  NULL DEFAULT NULL COMMENT '资金来源',
  `buy_person` varchar(64)  NOT NULL COMMENT '项目负责人',
  `organize_type` varchar(64)  NULL DEFAULT NULL COMMENT '组织形式',
  `buy_remark` varchar(128)  NULL DEFAULT NULL COMMENT '备注',
  `concat_number` varchar(18)  NOT NULL COMMENT '联系电话',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除标志（0代表存在 代表删除)',
  `create_by` varchar(64)  NULL DEFAULT '' COMMENT '创建者',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64)  NULL DEFAULT '' COMMENT '更新者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `buy_item_code`(`buy_item_code`) USING BTREE,
  UNIQUE INDEX `inner_code`(`inner_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '采购项目-武汉市普仁医院' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchase_win_bid_result
-- ----------------------------
DROP TABLE IF EXISTS `purchase_win_bid_result`;
CREATE TABLE `purchase_win_bid_result`  (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `subpackage_code` varchar(64)  NOT NULL DEFAULT '' COMMENT '标段编号',
  `subpackage_name` varchar(200)  NOT NULL DEFAULT '' COMMENT '标段名称',
  `supplier_id` bigint(11) NOT NULL COMMENT '投标人id',
  `supplier_company_name` varchar(64)  NOT NULL COMMENT '投标单位',
  `recommend_candidate` char(1)  NULL DEFAULT NULL COMMENT '评委推荐候选人[1-第一候选人，2-第二...，3-第三...]',
  `win_bid` char(1)  NULL DEFAULT '0' COMMENT '设定中标人【1-中标，2-未中标】',
  `win_bid_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '中标价格',
  `remark` varchar(256)  NULL DEFAULT '' COMMENT '说明',
  `is_win` char(1)  NULL DEFAULT NULL COMMENT '中标通知书类型[1-中标,2-未中标]',
  `send_time` datetime(0) NULL DEFAULT NULL COMMENT '通知书发送时间',
  `result_notice` varchar(64)  NULL DEFAULT '' COMMENT '中标结果通知书pdf_key---供应商',
  `again_send` char(1)  NULL DEFAULT '0' COMMENT '结果通知书的状态【0-未发送, 1-第一次已发送, 2-已重新发送】',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除标志（0代表存在 代表删除)',
  `create_by` varchar(64)  NULL DEFAULT '' COMMENT '创建者',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64)  NULL DEFAULT '' COMMENT '更新者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '中标结果表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for supplier_bargain
-- ----------------------------
DROP TABLE IF EXISTS `supplier_bargain`;
CREATE TABLE `supplier_bargain`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '报价表单id',
  `subpackage_code` char(50)  NOT NULL COMMENT '包编码',
  `supplier_id` bigint(20) NOT NULL COMMENT '供应商id',
  `round` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '轮数',
  `bargain_time` datetime(0) NOT NULL COMMENT '报价时间',
  `bargain_document` varchar(100)  NULL DEFAULT NULL COMMENT '议价文件url',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除=1',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(100)  NULL DEFAULT NULL COMMENT '创建者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(100)  NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_subpackageCode`(`subpackage_code`) USING BTREE,
  INDEX `idx_supplierId`(`supplier_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商议价' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for supplier_bidder
-- ----------------------------
DROP TABLE IF EXISTS `supplier_bidder`;
CREATE TABLE `supplier_bidder`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `subpackage_code` char(50)  NOT NULL COMMENT '包编码',
  `bidder_code_type` char(2)  NOT NULL DEFAULT '97' COMMENT '供应商代码类型,97：公司',
  `lic_number` char(18)  NOT NULL COMMENT '统一信用代码，营业执照号码: ',
  `bidder_name` varchar(100)  NOT NULL COMMENT '供应商公司名称 [1,100]',
  `contact_number` varchar(20)  NOT NULL COMMENT '法定代表人联系方式',
  `contact_address` varchar(125)  NOT NULL COMMENT '联系地址: 必须加上省市区加详细地址',
  `region_code` char(6)  NOT NULL COMMENT '6位数字码，行政区域代码:采用GB/T 2260-2007中的市级代码 ',
  `opening_bank` varchar(30)  NOT NULL COMMENT '开户银行: ',
  `basic_account` varchar(30)  NOT NULL COMMENT '基本账户账号: ',
  `industry_code` varchar(30)  NULL DEFAULT NULL COMMENT '例：E47（建筑业，房屋建筑业），行业代码:采用GB/T 4754《国民经济行业分类》中的门类和大类 ',
  `scope_of_supply` varchar(200)  NULL DEFAULT NULL COMMENT '供货范围',
  `unit_nature` varchar(100)  NOT NULL COMMENT '生产厂家，产品代理商',
  `business_license` varchar(100)  NOT NULL COMMENT '营业执照或组织机构代码证件扫描件',
  `legal_representative_identity_certificate` varchar(100)  NOT NULL COMMENT '法定代表人身份证明扫描件',
  `whether_micro_enterprises` tinyint(1) UNSIGNED NULL DEFAULT NULL COMMENT '是否中小微企业，0：不是，1：是',
  `micro_enterprises` varchar(100)  NULL DEFAULT NULL COMMENT '中小微企业文件',
  `email` varchar(100)  NULL DEFAULT NULL COMMENT '电子邮箱',
  `power_of_attorney` varchar(100)  NULL DEFAULT NULL COMMENT '投标授权书',
  `certificate_code` varchar(50)  NOT NULL COMMENT '法定代表人证件号码',
  `certificate_name` varchar(50)  NOT NULL COMMENT '法定代表人名称',
  `info_reporter_name` varchar(50)  NULL DEFAULT NULL COMMENT '投标责任人姓名: ',
  `info_reporter_code` varchar(50)  NULL DEFAULT NULL COMMENT '投标责任人证件号码',
  `info_reporter_contact_number` varchar(20)  NULL DEFAULT NULL COMMENT '投标责任人联系电话',
  `bid_type` varchar(100)  NOT NULL COMMENT '投标类型',
  `registered_capital` varchar(100)  NOT NULL COMMENT '注册资本',
  `operating_period` varchar(100)  NOT NULL COMMENT '营业期限',
  `date_of_establishment` varchar(100)  NOT NULL COMMENT '成立日期',
  `registration_and_authority` varchar(100)  NOT NULL COMMENT '登记机关',
  `supplier_id` bigint(20) NOT NULL COMMENT '供应商公司id',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除=1',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(100)  NULL DEFAULT NULL COMMENT '创建者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(100)  NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_subpackageCode_supplierId`(`subpackage_code`, `supplier_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商投标人信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for supplier_bidding
-- ----------------------------
DROP TABLE IF EXISTS `supplier_bidding`;
CREATE TABLE `supplier_bidding`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `subpackage_code` char(50)  NOT NULL COMMENT '包编码',
  `supplier_id` bigint(20) UNSIGNED NOT NULL COMMENT '供应商id',
  `epc_file` varchar(100)  NOT NULL COMMENT '响应文件压缩包url',
  `pdf_file` varchar(100)  NOT NULL COMMENT '响应文件url',
  `ip` bigint(20) UNSIGNED NOT NULL COMMENT '响应文件上传时ip',
  `video_url` varchar(100)  NULL DEFAULT NULL COMMENT '演示视频url',
  `release_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0已上传 1已确认',
  `answer_file_key` varchar(100)  NULL DEFAULT NULL COMMENT '响应文件密钥',
  `submission_time` datetime(0) NULL DEFAULT NULL COMMENT '响应文件递交盖章时间',
  `bid_opening_decrypt` bit(1) NULL DEFAULT NULL COMMENT '开标解密，0-未解密，1-解密',
  `bid_opening_record_form` bit(1) NULL DEFAULT NULL COMMENT '开标记录表，0-未签字，1-已签字',
  `app_name` varchar(100)  NOT NULL COMMENT 'app名称',
  `app_version` varchar(100)  NOT NULL COMMENT 'app版本',
  `zepc_key` varchar(100)  NOT NULL COMMENT 'app文件验证key',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除=1',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(100)  NULL DEFAULT NULL COMMENT '创建者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(100)  NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_subpackageCode_supplierId`(`subpackage_code`, `supplier_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '响应文件' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for supplier_contract
-- ----------------------------
DROP TABLE IF EXISTS `supplier_contract`;
CREATE TABLE `supplier_contract`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `supplier_id` bigint(20) NOT NULL COMMENT '供应商id',
  `supplier_company_name` varchar(100)  NOT NULL COMMENT '供应商公司名',
  `buy_item_name` varchar(128)  NOT NULL COMMENT '项目名称',
  `buy_item_code` varchar(64)  NOT NULL COMMENT '项目code',
  `subpackage_code` varchar(64)  NOT NULL COMMENT '包code',
  `subpackage_name` varchar(64)  NOT NULL COMMENT '包名字',
  `submission_time` datetime(0) NULL DEFAULT NULL COMMENT '模板提交审核时间',
  `audit_time` datetime(0) NULL DEFAULT NULL COMMENT '模板采购人审核时间',
  `audit_status` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '模板，0-完成模板,1-提交至采购人确认,2-采购人确认，3-供应商确认',
  `html_template` mediumtext  NOT NULL COMMENT '模板',
  `contract_url` varchar(100)  NULL DEFAULT NULL COMMENT '模板pdf',
  `attachment` varchar(100)  NULL DEFAULT NULL COMMENT '合同pdf',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除=1',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(100)  NULL DEFAULT NULL COMMENT '创建者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(100)  NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_subpackageCode_supplierId`(`subpackage_code`, `supplier_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商合同' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for supplier_demo_video
-- ----------------------------
DROP TABLE IF EXISTS `supplier_demo_video`;
CREATE TABLE `supplier_demo_video`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `subpackage_code` char(50)  NOT NULL COMMENT '包编码',
  `supplier_id` bigint(20) UNSIGNED NOT NULL COMMENT '供应商id',
  `video_url` varchar(100)  NOT NULL COMMENT '演示视频url',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除=1',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(100)  NULL DEFAULT NULL COMMENT '创建者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(100)  NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_subpackageCode_supplierId`(`subpackage_code`, `supplier_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商演示视频' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for supplier_process_node
-- ----------------------------
DROP TABLE IF EXISTS `supplier_process_node`;
CREATE TABLE `supplier_process_node`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `subpackage_code` char(50)  NOT NULL COMMENT '包编码',
  `supplier_id` bigint(20) NOT NULL COMMENT '供应商id',
  `nodes` json NOT NULL COMMENT '采购方式的完整流程与下一节点的触发点',
  `purchase_function_key` varchar(128)  NOT NULL COMMENT '当前节点key',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除=1',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(100)  NULL DEFAULT NULL COMMENT '创建者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(100)  NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_subpackageCode_supplierId`(`subpackage_code`, `supplier_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商流程节点' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for supplier_sign_up
-- ----------------------------
DROP TABLE IF EXISTS `supplier_sign_up`;
CREATE TABLE `supplier_sign_up`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `buy_item_name` varchar(200)  NOT NULL COMMENT '采购项目名称',
  `buy_item_code` char(50)  NOT NULL COMMENT '采购项目编码',
  `subpackage_code` char(50)  NOT NULL COMMENT '包编码',
  `subpackage_name` varchar(128)  NOT NULL COMMENT '包名称',
  `permit` bit(1) NULL DEFAULT NULL COMMENT '是否允许响应: 0-不允许，1-允许',
  `qualified` bit(1) NULL DEFAULT NULL COMMENT '是否合格: 0-不合格，1-合格',
  `bid_opening_sign` bit(1) NULL DEFAULT NULL COMMENT '开标签到，0-未签到，1-已签到',
  `additional_information` json NULL COMMENT '附加信息',
  `supplier_id` bigint(20) UNSIGNED NOT NULL COMMENT '供应商id',
  `deleted` bit(1) NULL DEFAULT b'0' COMMENT '删除=1',
  `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(100)  NULL DEFAULT NULL COMMENT '创建者',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(100)  NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_subpackageCode_supplierId`(`subpackage_code`, `supplier_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商报名表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tender_file_rejection
-- ----------------------------
DROP TABLE IF EXISTS `tender_file_rejection`;
CREATE TABLE `tender_file_rejection`  (
  `requirement_id` bigint(20) NOT NULL COMMENT '采购文件要求id',
  `supplier_id` bigint(20) NOT NULL COMMENT '供应商id',
  `reason` varchar(500)  NOT NULL COMMENT '理由',
  `bid_file_key` varchar(100)  NOT NULL COMMENT '响应文件key',
  `create_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '日期',
  `create_id` bigint(20) NOT NULL COMMENT '操作人id',
  `create_name` varchar(100)  NOT NULL COMMENT '操作人',
  INDEX `fk_requirement_id`(`requirement_id`) USING BTREE,
  CONSTRAINT `fk_requirement_id` FOREIGN KEY (`requirement_id`) REFERENCES `tender_file_requirement` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '采购文件要求驳回记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tender_file_requirement
-- ----------------------------
DROP TABLE IF EXISTS `tender_file_requirement`;
CREATE TABLE `tender_file_requirement`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `subpackage_code` char(64)  NOT NULL COMMENT '标段code',
  `attribute` varchar(500)  NOT NULL COMMENT '属性',
  `description` varchar(1000)  NOT NULL COMMENT '描述',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '采购文件要求' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
