```sql
CREATE TABLE `purchase_wzlg_buy_item`
(
    `id`                BIGINT ( 11 ) NOT NULL AUTO_INCREMENT COMMENT '采购项目-温州龙港医院版本主健id',
    `buy_item_code`     VARCHAR(64)  NOT NULL COMMENT '采购项目code',
    `buy_item_name`     VARCHAR(128) NOT NULL COMMENT '采购项目名字',
    `buy_class`         varchar(32)  NOT NULL COMMENT '采购类型',
    `apply_time`        datetime     DEFAULT NULL COMMENT '申请时间',
    `apply_dept`        VARCHAR(64)  DEFAULT NULL COMMENT '申请单位',
    `apply_unit_person` VARCHAR(32)  DEFAULT NULL COMMENT '申请单位负责人',
    `handle_person`     VARCHAR(32)  DEFAULT NULL COMMENT '经办人',
    `buy_purpose_time`  VARCHAR(128) DEFAULT NULL COMMENT '采购用途及需求时间',
    `inner_code`        VARCHAR(64)  DEFAULT NULL COMMENT '采购编号(院方使用)',
    `buy_header`        json         NOT NULL COMMENT '项目采购内容表头',
    `buy_body`          json         NOT NULL COMMENT '项目采购内容',
    `buy_remark`        VARCHAR(128) DEFAULT NULL COMMENT '备注',
    `deleted`           bit(1)       DEFAULT b'0' COMMENT '删除标志（0代表存在 代表删除)',
    `create_by`         VARCHAR(64)  DEFAULT '' COMMENT '创建者',
    `create_at`         datetime     DEFAULT NULL COMMENT '创建时间',
    `update_by`         VARCHAR(64)  DEFAULT '' COMMENT '更新者',
    `update_at`         datetime     DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `buy_item_code` ( `buy_item_code` )
) ENGINE = INNODB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8 COMMENT = '采购项目-温州龙港医院版本';
```

### 2024-12-31 12：00更新上线

### 2025-2-20 12:00 更新线上

### 2025-2-21 14:00 bidding服务更新线上

### 2025-3-17 新增字段

```sql
CREATE TABLE `purchase_wzlg_subpackage`
(
    `id`                 BIGINT ( 11 ) NOT NULL AUTO_INCREMENT COMMENT '标段信息拓展-温州龙港医院版本主健id',
    `subpackage_code`    VARCHAR(64)  NOT NULL COMMENT '标段编号',
    `bid_type`           VARCHAR(100) NOT NULL COMMENT '供应商类型（对应供应商企业信息管理中的投标类型）',
    `sub_server_type`    VARCHAR(32)    DEFAULT NULL COMMENT '服务类别',
    `sub_machinery_type` VARCHAR(32)    DEFAULT NULL COMMENT '机械种类',
    `sub_move_type`      VARCHAR(32)    DEFAULT NULL COMMENT '实施方式',
    `sub_move_amount`    DECIMAL(18, 5) DEFAULT NULL COMMENT '实施金额',
    `sub_project_person` VARCHAR(32)    DEFAULT NULL COMMENT '项目负责人',
    `sub_phone`          VARCHAR(32)    DEFAULT NULL COMMENT '联系方式',
    `sub_move_evidence`  VARCHAR(32)    DEFAULT NULL COMMENT '实施依据',
    `sub_agent_info`     VARCHAR(32)    DEFAULT NULL COMMENT '委托项目基本概况',
    `sub_info_file`      json           DEFAULT NULL COMMENT '标段扩展信息以及项目信息生成的pdf',
    `deleted`            bit(1)         DEFAULT b'0' COMMENT '删除标志（0代表存在 代表删除)',
    `create_by`          VARCHAR(64)    DEFAULT '' COMMENT '创建者',
    `create_at`          datetime       DEFAULT NULL COMMENT '创建时间',
    `update_by`          VARCHAR(64)    DEFAULT '' COMMENT '更新者',
    `update_at`          datetime       DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `subpackage_code` ( `subpackage_code` )
) ENGINE = INNODB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8 COMMENT = '标段拓展信息-温州龙港医院版本';
```

### 2025-3-29 10:00 所有服务更新线上

### 2025-4-2 10:00 更新

### 2025-4-3 15:00 auth bidding epcfile review 服务发布

### 2025-5-30 16:00 前后端所有服务均上线

### 2025-7-14 16:00 前后端所有服务均上线

