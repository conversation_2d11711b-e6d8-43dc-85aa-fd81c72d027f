# 十堰太和医院
## 修改记录

# 采购人供应商库中 attribute 表：通用属性配置，不能删除

### 2024-6-20 10:00 发布线上

## 2024-7-10 十堰太和项目扩展表增加字段
```sql
ALTER TABLE purchase_syth_buy_item
ADD COLUMN inner_code varchar(32) NOT NULL comment '院内项目编号' AFTER `buy_item_name`;

ALTER TABLE `purchase_syth_buy_item`
    ADD CONSTRAINT `inner_code` UNIQUE (`inner_code`);


ALTER TABLE purchase_syth_buy_item ADD COLUMN `apply_time` datetime DEFAULT NULL COMMENT '申报on时间' AFTER `buy_remark`;
ALTER TABLE purchase_syth_buy_item ADD COLUMN `purchase_dept` varchar(32) NOT NULL COMMENT '采购科室' AFTER `apply_time`;
ALTER TABLE purchase_syth_buy_item ADD COLUMN `purchase_limit_time` varchar(32) DEFAULT NULL COMMENT '采购时限' AFTER `concat_number`;
```

### 2024-7-23 发布线上

### 2024-8-22 发布线上

### 2024-9-5 18:26 上线，文件服务迁移

### 2024-9-25 17:10 上线

### 2024-10-11 上线，修复数据库连接时区参数修改为中国时区

### 2024-11-2 太和上线

### 2025-1-9 16:00 太和上线,签章优化

### 2025-1-10 18:00 太和上线,个人签章样式改为无框

### 2025-2-5 11:00 太和上线，所有服务都发布

### 2025-2-7 11:00 太和上线，bidding,review服务

### 2025-3-12 18:00 太和上线，全部服务

### 2025-6-10 18:00 太和上线，全部服务

### 2025-6-11 10:00 太和上线，修复短信发送依赖环境配置问题，auth\system\bidding
