package com.epcos.agent.api.domain;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.epcos.common.core.annotation.CreateTime;
import com.epcos.common.core.annotation.UpdateTime;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;
import java.io.Serializable;
import java.util.Date;

/**
 * 代理项目标段
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BidSection对象", description = "代理项目标段")
public class AgentBidSection implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "标段id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "标段名称")
    private String bidSectionName;

    @ApiModelProperty(value = "标段code")
    private String bidSectionCode;

    @ApiModelProperty(value = "公告时间")
    private Date announcementTime;

    @ApiModelProperty(value = "报名截止时间")
    private Date registrationDeadline;

    @ApiModelProperty(value = "开标时间")
    private Date bidOpeningTime;

    @ApiModelProperty(value = "评标截止时间")
    private Date deadlineBidEvaluation;

    @ApiModelProperty(value = "结果公示时间")
    private Date resultPublicityTime;

    @ApiModelProperty(value = "创建者", hidden = true)
    @Length(max = 64)
    @JsonIgnore
    private String createBy;


    @ApiModelProperty(value = "创建时间", hidden = true)
    @CreateTime
    private Date createTime;

    @Length(max = 64)
    @ApiModelProperty(value = "更新者", hidden = true)
    @JsonIgnore
    private String updateBy;

    @ApiModelProperty(value = "更新时间", hidden = true)
    @UpdateTime
    private Date updateTime;
}
