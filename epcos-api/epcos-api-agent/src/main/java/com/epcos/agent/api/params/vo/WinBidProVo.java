package com.epcos.agent.api.params.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Title:
 * @Description:TODO
 * @author:moyu
 * @version:1.0
 * @since 2023-02-14 9:34
 */
@Data
public class WinBidProVo implements Serializable {

    private static final long serialVersionUID = -5559807249980918404L;

    @ApiModelProperty(value = "代理项目名称")
    private String projectName;

    @ApiModelProperty(value = "代理项目code")
    private String projectCode;

    @ApiModelProperty(value = "是否保存中标结果[0-未保存，1-已保存]")
    private Integer saveResult;


    @ApiModelProperty(value = "标段信息")
    private List<WinBidSectionVo> winBidSectionVoList;
}
