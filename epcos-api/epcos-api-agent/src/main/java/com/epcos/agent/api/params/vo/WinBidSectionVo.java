package com.epcos.agent.api.params.vo;

import com.epcos.agent.api.domain.AgentSupplier;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Title:
 * @Description:TODO
 * @author:moyu
 * @version:1.0
 * @since 2023-02-14 9:34
 */
@Data
public class WinBidSectionVo implements Serializable {

    @ApiModelProperty(value = "代理标段code")
    private String bidSectionCode;

    @ApiModelProperty(value = "代理标段名称")
    private String bidSectionName;

    @ApiModelProperty(value = "中标人信息")
    private List<AgentSupplier> supplierList;
}
