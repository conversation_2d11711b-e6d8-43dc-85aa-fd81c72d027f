package com.epcos.agent.api.params.vo;

import com.epcos.epcfile.api.domain.vo.AgencyProjectFileVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Title:
 * @Description:TODO
 * @author:moyu
 * @version:1.0
 * @since 2023-02-16 9:56
 */
@Data
public class AgentFileSectionVo implements Serializable {

    private static final long serialVersionUID = 1887784010318512467L;

    @ApiModelProperty(value = "代理标段code")
    private String bidSectionCode;

    @ApiModelProperty(value = "代理标段名称")
    private String bidSectionName;

    @ApiModelProperty(value = "标段下的文件列表")
    private List<AgencyProjectFileVo> sectionFileList;
}
