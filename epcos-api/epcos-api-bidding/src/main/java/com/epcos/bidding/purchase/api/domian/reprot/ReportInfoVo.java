package com.epcos.bidding.purchase.api.domian.reprot;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/29 15:07
 */
@Data
public class ReportInfoVo implements Serializable {

    private static final long serialVersionUID = -2225898000455397654L;

    @ApiModelProperty(value = "标段编号")
    private String subpackageCode;

    @ApiModelProperty(value = "标段名称")
    private String subpackageName;

    @ApiModelProperty(value = "调研报告key")
    private String researchReportKey;

    @ApiModelProperty(value = "调研报告文本html")
    private String researchReportText;

    @ApiModelProperty(value = "签字信息")
    private List<SignInfoVo> signInfoVoList;
}
