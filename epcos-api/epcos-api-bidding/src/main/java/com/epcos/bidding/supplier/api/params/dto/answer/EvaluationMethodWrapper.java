package com.epcos.bidding.supplier.api.params.dto.answer;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
public class EvaluationMethodWrapper implements Serializable {
    private static final long serialVersionUID = 3818748533729516559L;

    @ApiModelProperty("项目code")
    @NotBlank(message = "项目code不能为空")
    private String buyItemCode;

    @ApiModelProperty("包code")
    @NotBlank(message = "包code不能为空")
    private String subpackageCode;

    @ApiModelProperty("评审办法")
    @NotEmpty(message = "评审办法不能为空")
    private List<EvaluationMethodDto> evaluationMethodList;
}
