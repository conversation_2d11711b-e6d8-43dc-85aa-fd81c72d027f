package com.epcos.bidding.purchase.api.params.vo.buyitem;

import com.epcos.bidding.audit.api.AuditProcessDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/10 11:47
 */
@Data
@NoArgsConstructor
public class SuperPackageVo implements Serializable {
    private static final long serialVersionUID = -1825034182739641858L;

    public SuperPackageVo(String subpackageCode, String subpackageName) {
        this.subpackageCode = subpackageCode;
        this.subpackageName = subpackageName;
    }

    @ApiModelProperty(value = "标段编号")
    @Length(max = 64, message = "标段名称 超出长度限制")
    private String subpackageCode;

    @ApiModelProperty(value = "标段名称")
    @Length(max = 64, message = "标段名称 超出长度限制")
    private String subpackageName;

    @ApiModelProperty(value = "是否终止采购 [0-正常，1-终止] 默认为0")
    private String abandon;

    @ApiModelProperty(value = "商品关联系信息")
    private String shopJson;

    @ApiModelProperty(value = "审批信息")
    private AuditProcessDto auditProcessDto;
}
