package com.epcos.bidding.purchase.api.params;

import lombok.Data;

import java.io.Serializable;
/**
 * <AUTHOR>
 * @version V1.0
 */

@Data
public class SubpackageVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 包编码
     */
    private String subpackageCode;
    /**
     * 包名称
     */
    private String subpackageName;
    /**
     * 采购项目编号
     */
    private String buyItemCode;

    /**
     * '采购方式名称-英文标识
     */
    private String purchaseMethodType;
    /**
     * 采购方式名称-中文名称
     */
    private String purchaseMethodName;
    /**
     * 采购项目名称
     */
    private String buyItemName;
    /**
     * 1-合格制评审 2-打分制评审  3-合格与打分制并存
     */
    private Integer reviewMode;
    /**
     * '采购文件地址key
     */
    private String claimsFileKey;

    /**
     * 评审报告key
     */
    private String reportFileKey;




    /**
     * 变更文件地址key
     */
    private String changeFileKeys;
    /**
     * 功能点json
     */
    private String purchaseFunctionJson;
    /**
     *监督人 ID
     */
    private Long  monitorBidPersonId;
    /**
     *监督人姓名
     */
    private String monitorBidPerson;

}
