package com.epcos.bidding.purchase.api.params;

import com.epcos.bidding.purchase.api.params.dto.DoubtDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/9 11:15
 */
@Data
public class DoubtVo extends DoubtDto {

    private static final long serialVersionUID = -6452974939422005038L;

    @ApiModelProperty(value = "质疑文件地址key")
    private String doubtFileKey;

    @ApiModelProperty(value = "质疑回复文件地址key")
    private String replyFileKey;

    @ApiModelProperty(value = "质疑提问时间")
    protected Date createAt;

    @ApiModelProperty(value = "质疑回复时间")
    protected Date updateAt;
}
