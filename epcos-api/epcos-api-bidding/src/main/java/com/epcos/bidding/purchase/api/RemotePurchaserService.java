package com.epcos.bidding.purchase.api;


import com.epcos.bidding.purchase.api.domian.cliams.SupplierCommentVo;
import com.epcos.bidding.purchase.api.domian.judge.judgeProjectVo;
import com.epcos.bidding.purchase.api.domian.reprot.ReportInfoVo;
import com.epcos.bidding.purchase.api.factory.RemotePurchaserFallbackFactory;
import com.epcos.bidding.purchase.api.params.DoubtVo;
import com.epcos.bidding.purchase.api.params.EpcFileContentVo;
import com.epcos.bidding.purchase.api.params.EvaluationMethodVo;
import com.epcos.bidding.purchase.api.params.JudgesVo;
import com.epcos.bidding.purchase.api.params.PurchaseBidOpeningVo;
import com.epcos.bidding.purchase.api.params.PurchaseQuoteFormVo;
import com.epcos.bidding.purchase.api.params.SubpackageVo;
import com.epcos.bidding.purchase.api.params.dto.AskDto;
import com.epcos.bidding.purchase.api.params.dto.DoubtDto;
import com.epcos.bidding.purchase.api.params.dto.QueryDoubtDto;
import com.epcos.bidding.purchase.api.params.dto.ReplyDto;
import com.epcos.bidding.purchase.api.params.dto.SubSupplier;
import com.epcos.bidding.purchase.api.params.dto.bargin.StartOrEndBargainDto;
import com.epcos.bidding.purchase.api.params.dto.monitor.CreateMonitorDto;
import com.epcos.bidding.purchase.api.params.vo.answer.AnswerVo;
import com.epcos.bidding.purchase.api.params.vo.buyitem.BuyItemVo;
import com.epcos.bidding.supplier.api.params.PurchaseBargainVo;
import com.epcos.common.core.constant.SecurityConstants;
import com.epcos.common.core.constant.ServiceNameConstants;
import com.epcos.common.core.domain.AuditStatusDto;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.domain.review.ReviewSummaryVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import java.util.List;
import java.util.Map;

/**
 * @Title:
 * @Description:对外暴露的接口
 * @author:moyu
 * @version:1.0
 * @since 2022-04-01 9:00
 */
@FeignClient(contextId = "RemotePurchaserService", value = ServiceNameConstants.PURCHASER_SERVICE, fallbackFactory = RemotePurchaserFallbackFactory.class)
public interface RemotePurchaserService {

    /**
     * 可根据采
     * <采购项目编号>
     * 或者
     * <包编号>
     * 查询某个角色的 采购项目信息 以及 采购方式与功能
     * 角色参数若不传，则默认返回所有觉得的功能
     *
     * @param buyItemCode    采购项目编号
     * @param subpackageCode 包编号
     * @param belongRole     所属角色[1-采购人，2-供应商，3-专家]，如需要供应商角色则传入"2"
     * @return
     */
    @GetMapping(value = "other/to/queryBuyItemInfo")
    R<BuyItemVo> getInnerCode(@RequestHeader(SecurityConstants.FROM_SOURCE) String source,
                              @RequestParam(value = "buyItemCode", required = false) String buyItemCode,
                              @RequestParam(value = "subpackageCode", required = false) String subpackageCode,
                              @RequestParam(value = "belongRole", required = false) String belongRole);

    @ApiOperation("采购文件所有内容查询")
    @GetMapping("/purchase/claims/remote/queryAll")
    R<EpcFileContentVo> queryAll(@RequestHeader(SecurityConstants.FROM_SOURCE) String source,
                                 @RequestParam("subpackageCode") String subpackageCode);

    @ApiOperation("采购文件评审办法,单包code查询")
    @GetMapping("/purchase/claims/remote/evaluationMethod")
    R<EvaluationMethodVo> evaluationMethod(@RequestHeader(SecurityConstants.FROM_SOURCE) String source,
                                           @RequestParam("subpackageCode") String subpackageCode);

    @ApiOperation("采购文件评审办法,多包code查询")
    @GetMapping("/purchase/claims/remote/evaluationMethods")
    R<List<EvaluationMethodVo>> evaluationMethods(@RequestHeader(SecurityConstants.FROM_SOURCE) String source,
                                                  @RequestParam("subpackageCodes") String[] subpackageCodes);

    @ApiOperation("采购文件报价表单,使用多包code查询")
    @GetMapping("/purchase/claims/remote/quoteForms")
    R<Map<String, PurchaseQuoteFormVo>> quoteForms(@RequestHeader(SecurityConstants.FROM_SOURCE) String source,
                                                   @RequestParam("subpackageCodes") String[] subpackageCodes);

    @ApiOperation("修改采购文件审批状态")
    @PostMapping("/purchase/claims/updateAuditStatus")
    R<Boolean> updateAuditStatus(@RequestHeader(SecurityConstants.FROM_SOURCE) String source, @RequestBody AuditStatusDto dto);

    /**
     * 查询采购项目、采购项目对应的包、公告等部分信息
     *
     * @param subpackageCode 包编号
     * @return
     */
    @GetMapping(value = "/other/to/buyItemInfo")
    R<SubpackageVo> buyItemInfo(@RequestHeader(SecurityConstants.FROM_SOURCE) String source,
                                @RequestParam(value = "subpackageCode") String subpackageCode);

    /**
     * 查询包下的信息
     *
     * @param source
     * @param subpackageCode 包编号
     * @return
     */
    @GetMapping(value = "/other/to/getJudge")
    R<List<JudgesVo>> getJudge(@RequestHeader(SecurityConstants.FROM_SOURCE) String source,
                               @RequestParam(value = "subpackageCode") String subpackageCode);

    /**
     * 根据标段code获取供应商信息
     *
     * @param source
     * @param subpackageCode
     * @return
     */
    @GetMapping(value = "/other/to/getSupplier")
    R<SubSupplier> getSupplier(@RequestHeader(SecurityConstants.FROM_SOURCE) String source,
                               @RequestParam(value = "subpackageCode") @NotBlank String subpackageCode);

    /**
     * 答疑
     * 提问
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/other/to/submitAsk")
    R<Boolean> submitAsk(@RequestHeader(SecurityConstants.FROM_SOURCE) String source,
                         @RequestBody @Validated AskDto dto);

    /**
     * 答疑
     * 回复
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/other/to/replyAsk")
    R<Boolean> replyAsk(@RequestHeader(SecurityConstants.FROM_SOURCE) String source,
                        @RequestBody @Validated ReplyDto dto);

    /**
     * 答疑列表
     *
     * @param subpackageCode 包编号
     * @param userId         当前登录人id
     * @return
     */
    @GetMapping(value = "/other/to/judgeAnswerList")
    R<List<AnswerVo>> judgeAnswerList(@RequestHeader(SecurityConstants.FROM_SOURCE) String source,
                                      @RequestParam(value = "subpackageCode") @NotBlank String subpackageCode,
                                      @RequestParam(value = "userId") @NotNull Long userId);

    /**
     * 提出/回复质疑
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/other/to/askDoubt")
    R<Boolean> askDoubt(@RequestHeader(SecurityConstants.FROM_SOURCE) String source, @RequestBody DoubtDto dto);

    /**
     * 质疑列表
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/other/to/doubtList")
    R<List<DoubtVo>> doubtList(@RequestHeader(SecurityConstants.FROM_SOURCE) String source,
                               @RequestBody QueryDoubtDto dto);

    /**
     * 查询是否确认评审
     * 若已经确认评审则返回 true
     * 没有确认评审则返回 false
     *
     * @param subpackageCode 包code
     * @return
     */
    @GetMapping(value = "/other/to/queryWhetherConfirmReview")
    R<Boolean> queryWhetherConfirmReview(@RequestHeader(SecurityConstants.FROM_SOURCE) String source,
                                         @RequestParam(value = "subpackageCode") @NotBlank String subpackageCode);

    /**
     * 评委 发起/结束议价
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/other/to/startOrEndBargain")
    R<Boolean> startOrEndBargain(@RequestBody @Validated StartOrEndBargainDto dto);

    /**
     * 评委 议价列表
     *
     * @param buyItemCode
     * @return
     */
    @GetMapping(value = "/other/to/list")
    R<List<PurchaseBargainVo>> bargainList(@RequestHeader(SecurityConstants.FROM_SOURCE) String source,
                                           @RequestParam("buyItemCode") String buyItemCode);

    @ApiOperation(value = "查询供应商评论信息")
    @GetMapping(value = "/other/to/getSupplierCommentInfo")
    R<List<SupplierCommentVo>> getSupplierCommentInfo(@RequestHeader(SecurityConstants.FROM_SOURCE) String source,
                                                      @RequestParam(value = "supplierId") Long supplierId);

    @ApiOperation(value = "修改签字状态")
    @GetMapping(value = "/other/to/editSignStatus")
    R<Boolean> editSignStatus(@RequestHeader(SecurityConstants.FROM_SOURCE) String source,
                              @RequestParam(value = "subpackageCode") String subpackageCode,
                              @RequestParam(value = "judgeId") Long judgeId);

    @ApiOperation(value = "查询调研信息")
    @GetMapping(value = "/other/to/judgeView")
    R<ReportInfoVo> judgeView(@RequestHeader(SecurityConstants.FROM_SOURCE) String source,
                              @RequestParam(value = "subpackageCode") String subpackageCode);

    @ApiOperation(value = "添加监标信息")
    @GetMapping(value = "/other/to/addMonitorInfo")
    R<Boolean> addMonitorInfo(@RequestHeader(SecurityConstants.FROM_SOURCE) String source,
                              @RequestBody CreateMonitorDto dto);

    @ApiOperation(value = "查询项目院内编号")
    @GetMapping(value = "/other/to/queryInnerCode")
    R<String> queryInnerCode(@RequestHeader(SecurityConstants.FROM_SOURCE) String source,
                             @RequestParam(value = "subpackageCode") String subpackageCode);

    /**
     * 查询开标信息
     */
    @GetMapping("/purchase/bidOpening/remote/query")
    R<PurchaseBidOpeningVo> remoteQuery(@RequestHeader(SecurityConstants.FROM_SOURCE) String source,
                                        @RequestParam("subpackageCode") String subpackageCode);

    /**
     * 計算價格分數
     *
     * @param reviewInfo
     * @return
     */
    @PostMapping(value = "/other/to/calculatePriceScore")
    R<ReviewSummaryVo> calculatePriceScore(@RequestHeader(SecurityConstants.FROM_SOURCE) String source,
                                           @RequestBody ReviewSummaryVo reviewInfo);

    /**
     * 统计供应商中标次数
     *
     * @param supplierIds 供应商id集合
     * @return
     */
    @PostMapping(value = "/other/to/calculateWinCnt")
    R<Map<Long, Long>> calculateWinCnt(@RequestHeader(SecurityConstants.FROM_SOURCE) String source,
                                       @RequestBody List<Long> supplierIds);

    /**
     * 计算邀请了该供应商但未报名的次数
     *
     * @param supplierIds 供应商id集合
     * @return
     */
    @PostMapping(value = "/other/to/calculateInviteNum")
    R<Map<Long, Long>> calculateInviteNum(@RequestHeader(SecurityConstants.FROM_SOURCE) String source,
                                       @RequestBody List<Long> supplierIds);

    /**
     * 统计专家参与项目次数
     *
     * @param source
     * @param judgeIdList
     * @return
     */
    @ApiOperation("统计专家参与项目次数")
    @GetMapping("/extract/judge/getProjectNum")
    R<Map<Long, judgeProjectVo>> getProjectNum(@RequestHeader(SecurityConstants.FROM_SOURCE) String source,
                                               @RequestParam("judgeIdList") List<Long> judgeIdList);


    /**
     * 查询议价最大轮数
     *
     * @param source
     * @param subpackageCode
     * @return
     */
    @GetMapping("/purchase/bargain/findMaxRound")
    R<Integer> findMaxRound(@RequestHeader(SecurityConstants.FROM_SOURCE) String source,
                                        @RequestParam("subpackageCode") String subpackageCode);

    /**
     * 采购项目与标段--湖北省襄阳市中医医院版本--查询流程编号
     *
     * @param source
     * @param buyItemCode
     * @return
     */
    @GetMapping("/xyzy.buyItem/getInnerCode")
    R<String> getInnerCode(@RequestHeader(SecurityConstants.FROM_SOURCE) String source,
                           @RequestParam(value = "buyItemCode") String buyItemCode);


}
