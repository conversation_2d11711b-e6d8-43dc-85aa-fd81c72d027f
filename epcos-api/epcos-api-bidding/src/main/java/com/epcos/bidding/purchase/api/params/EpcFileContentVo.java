package com.epcos.bidding.purchase.api.params;

import com.epcos.common.core.annotation.desensitization.BoolToStrSerializer;
import com.epcos.common.core.annotation.desensitization.StrToBoolDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * 采购文件内容
 */
@Data
@ApiModel(description = "客户端文件内容")
public class EpcFileContentVo implements Serializable {
    private static final long serialVersionUID = -1696942760102045321L;

    @ApiModelProperty("目录及子目录")
    private List<MenuDataVo> menuData;
    @ApiModelProperty("报价表头")
    private List<AttributeVo> heads;
    @ApiModelProperty("采购人报价内容")
    private List<LinkedHashMap<String, String>> purchaseBodyMaps;
    @ApiModelProperty("供应商报价内容")
    private List<LinkedHashMap<String, String>> bodyMaps;
    @ApiModelProperty("评审方法")
    private EvaluationMethodVo evaluationMethod;
    @ApiModelProperty("app名字")
    private String appName;
    @ApiModelProperty("版本")
    private String version;
    @ApiModelProperty("文件验证key")
    private String zepcKey;
    @ApiModelProperty("文件压缩包key")
    private String epcFile;
    @ApiModelProperty("文件pdf key")
    private String pdfFile;
    @ApiModelProperty("文件发布状态,0已上传 1已确认")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer releaseStatus;
    @ApiModelProperty(value = "响应文件上传时ip")
    private String ip;
    @ApiModelProperty(value = "响应演示视频url")
    private String videoUrl;

    @ApiModelProperty(value = "报价总价")
    private BigDecimal priceTotalSource;

    @ApiModelProperty("采购人设置是否允许响应: 0-不允许，1-允许")
    @JsonSerialize(using = BoolToStrSerializer.class)
    @JsonDeserialize(using = StrToBoolDeserializer.class)
    private Boolean permit;

    @ApiModelProperty("供应商支付凭证")
    private String paymentVoucher;

    @ApiModelProperty("附件信息")
    private List<ClaimsFileAttVo> claimsFileAttVoList;

    /**
     * whws
     */
    @ApiModelProperty(value = "0-不收費，1-收费")
    private Integer bidWhetherFree;

    /**
     * whws
     */
    @ApiModelProperty(value = "收费金额")
    private BigDecimal bidBookAmount;

    /**
     * whws
     */
    @ApiModelProperty(value = "账户名称")
    private String accountName;

    /**
     * whws
     */
    @ApiModelProperty(value = "银行账户")
    private String bankAccount;

    /**
     * whws
     */
    @ApiModelProperty(value = "开户行")
    private String openingBank;


}
